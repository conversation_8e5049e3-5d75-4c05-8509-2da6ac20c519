# SIMILE Documentation Index
**Last Updated**: July 4, 2025 (Post Documentation Cleanup)  
**Purpose**: Navigation guide for all project documentation

## 📋 **Current Documentation (Active)**

### **Essential Project Information**
| Document | Purpose | Last Updated |
|----------|---------|--------------|
| [`README.md`](../README.md) | Main project documentation, setup instructions | June 2025 |
| [`PLAN.md`](../PLAN.md) | Product requirements document | Active |
| [`CLAUDE.md`](../CLAUDE.md) | AI assistant instructions and project guidelines | Active |
| [`20250704-Current-Plan.md`](../20250704-Current-Plan.md) | **CURRENT production plan** (Test completion focused) | July 4, 2025 |

### **Development Status**
| Document | Purpose | Last Updated |
|----------|---------|--------------|
| [`development-status.md`](development-status.md) | Current development status and progress | July 4, 2025 |

### **Technical Specifications**
| Document | Purpose | Last Updated |
|----------|---------|--------------|
| [`technical-specifications/validation-behavior-specification.md`](technical-specifications/validation-behavior-specification.md) | Form validation patterns and behavior | June 2025 |
| [`technical-specifications/QA-CONNECTION-TEST-ANALYSIS.md`](technical-specifications/QA-CONNECTION-TEST-ANALYSIS.md) | Connection test analysis and fixes | June 23, 2025 |
| [`technical-specifications/qa-demo-script.md`](technical-specifications/qa-demo-script.md) | QA validation demo procedures | June 2025 |

### **Infrastructure Documentation**
| Document | Purpose | Last Updated |
|----------|---------|--------------|
| [`BACKEND-TEST-FIX-DOCUMENTATION.md`](BACKEND-TEST-FIX-DOCUMENTATION.md) | Critical test infrastructure knowledge | June 13, 2025 |
| [`DEVELOPMENT_SETUP.md`](DEVELOPMENT_SETUP.md) | Development environment setup guide | June 20, 2025 |
| [`PLAYWRIGHT-E2E-TESTING-PLAN.md`](PLAYWRIGHT-E2E-TESTING-PLAN.md) | E2E testing strategy and implementation | June 2025 |

### **Test Documentation**
| Document | Purpose | Last Updated |
|----------|---------|--------------|
| [`../backend/tests/README.md`](../backend/tests/README.md) | Backend test suite documentation | Active |
| [`../frontend/e2e/README.md`](../frontend/e2e/README.md) | Frontend E2E test documentation | Active |

---

## 📦 **Historical Documentation (Archived)**

### **June 2025 Archive**: [`docs/archived/june-2025/`](archived/june-2025/)

**Purpose**: Complete June 2025 documentation including test resolution and planning
- [`20250629-Current-Plan.md`](archived/june-2025/20250629-Current-Plan.md) - Previous current plan (superseded)
- [`reports/`](archived/june-2025/reports/) - Test resolution reports from June 29
- [`E2E-TEST-STATUS-COMPREHENSIVE-REPORT-20250628.md`](archived/june-2025/E2E-TEST-STATUS-COMPREHENSIVE-REPORT-20250628.md) - Historical test status

### **Development Archive**: [`docs/archive/`](archive/)

**Purpose**: Historical development phases and completed work documentation

| Archive Category | Location | Contents |
|------------------|----------|----------|
| **E2E Testing Resolution** | `e2e-testing-resolution-june-2025/` | June test crisis resolution |
| **June Test Resolution** | `june-2025-test-resolution/` | Test resolution detailed docs |
| **Phase Completion** | `phase-1-2-complete/` | Completed phases documentation |
| **Production Planning** | `production-planning-june-2025/` | June production planning docs |

---

## 🎯 **Quick Navigation**

### **For New Team Members**
1. Start with [`README.md`](../README.md) - Setup and overview
2. Review [`20250704-Current-Plan.md`](../20250704-Current-Plan.md) - Current production plan
3. Check [`CLAUDE.md`](../CLAUDE.md) - Development guidelines

### **For Developers**
1. [`CLAUDE.md`](../CLAUDE.md) - Development guidelines
2. [`technical-specifications/validation-behavior-specification.md`](technical-specifications/validation-behavior-specification.md) - Form validation patterns
3. [`BACKEND-TEST-FIX-DOCUMENTATION.md`](BACKEND-TEST-FIX-DOCUMENTATION.md) - Test infrastructure

### **For QA Engineers**
1. [`20250704-Current-Plan.md`](../20250704-Current-Plan.md) - Current test completion plan
2. [`technical-specifications/QA-CONNECTION-TEST-ANALYSIS.md`](technical-specifications/QA-CONNECTION-TEST-ANALYSIS.md) - Test analysis
3. [`PLAYWRIGHT-E2E-TESTING-PLAN.md`](PLAYWRIGHT-E2E-TESTING-PLAN.md) - E2E testing strategy

### **For Project Managers**
1. [`20250704-Current-Plan.md`](../20250704-Current-Plan.md) - Current production plan
2. [`development-status.md`](development-status.md) - Development status
3. [`archived/june-2025/`](archived/june-2025/) - Historical context

### **For DevOps Engineers**
1. [`20250704-Current-Plan.md`](../20250704-Current-Plan.md) - CI/CD integration requirements
2. [`BACKEND-TEST-FIX-DOCUMENTATION.md`](BACKEND-TEST-FIX-DOCUMENTATION.md) - Test infrastructure
3. [`DEVELOPMENT_SETUP.md`](DEVELOPMENT_SETUP.md) - Development environment

---

## 📊 **Documentation Health Status**

### ✅ **Current & Organized**
- **Production Plan**: Test completion focused (July 4, 2025)
- **Documentation**: Cleaned and consolidated
- **Technical Specs**: Organized and accessible
- **Archives**: Historical context preserved

### 📋 **Current Status**
- [x] Text input truncation bug fixed
- [x] E2E tests at 73% pass rate
- [x] Documentation consolidated and archived
- [x] Current plan updated for test completion
- [x] Ready for Phase 1 test stabilization (starting July 5)

---

## 🔄 **Clean Documentation Structure**

```
/
├── CLAUDE.md (project guidance)
├── PLAN.md (requirements)  
├── README.md (overview)
├── 20250704-Current-Plan.md (CURRENT plan)
└── docs/
    ├── DOCUMENTATION-INDEX.md (this file)
    ├── development-status.md (current status)
    ├── BACKEND-TEST-FIX-DOCUMENTATION.md
    ├── DEVELOPMENT_SETUP.md
    ├── PLAYWRIGHT-E2E-TESTING-PLAN.md
    ├── technical-specifications/
    │   ├── validation-behavior-specification.md
    │   ├── QA-CONNECTION-TEST-ANALYSIS.md
    │   └── qa-demo-script.md
    ├── archived/
    │   └── june-2025/ (June 2025 documentation)
    └── archive/
        ├── e2e-testing-resolution-june-2025/
        ├── june-2025-test-resolution/
        ├── phase-1-2-complete/
        └── production-planning-june-2025/
```

---

## 🔄 **Maintenance Guidelines**

### **Documentation Standards**
- **Single Source of Truth**: Use designated authoritative documents
- **Date Standards**: Include last updated dates prominently
- **Status Indicators**: Clear current vs historical distinctions
- **Consolidation**: Prevent document proliferation through regular cleanup

### **Update Responsibilities**
- **Development Team**: Update technical specifications with code changes
- **QA Team**: Maintain test status and validation documentation
- **DevOps Team**: Update infrastructure and deployment documentation
- **Project Management**: Keep production plan and status documentation current
- **All Team Members**: Report outdated or conflicting information

### **Archive Policy**
- **Phase Completion**: Archive detailed phase documents when consolidated
- **Superseded Documents**: Move replaced documents to appropriate archive location
- **Historical Value**: Preserve detailed history while maintaining current clarity
- **Regular Cleanup**: Perform documentation cleanup at phase boundaries

---

## 📞 **Documentation Support**

### **For Questions About**
- **Current Status**: See [`development-status.md`](development-status.md) or [`20250704-Current-Plan.md`](../20250704-Current-Plan.md)
- **Technical Implementation**: Check [`technical-specifications/`](technical-specifications/) directory
- **Historical Context**: Browse [`docs/archive/`](archive/) directories
- **Missing Information**: Report via project channels

### **For Documentation Updates**
- **Production Plan Changes**: Update main production plan document
- **Technical Changes**: Update relevant technical specification
- **Status Changes**: Update current status documents
- **New Documentation**: Follow established naming and structure patterns

---

**Last Maintained**: July 4, 2025 (Post Documentation Cleanup)  
**Next Review**: After Phase 1 completion (Test stabilization)  
**Cleanup Policy**: Regular consolidation at phase boundaries  
**Maintainer**: Team collective responsibility with PM oversight