{"permissions": {"allow": ["Bash(find:*)", "mcp__sequential-thinking__sequentialthinking", "mcp__allpepper-memory-bank__memory_bank_write", "mcp__allpepper-memory-bank__memory_bank_update", "Bash(git flow init:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npx create-react-app:*)", "<PERSON><PERSON>(podman-compose up:*)", "Bash(npm install)", "Bash(ln:*)", "<PERSON><PERSON>(podman-compose:*)", "Bash(ls:*)", "<PERSON><PERSON>(podman pod rm:*)", "<PERSON><PERSON>(podman ps:*)", "Bash(podman logs:*)", "<PERSON><PERSON>(curl:*)", "mcp__allpepper-memory-bank__list_projects", "mcp__allpepper-memory-bank__list_project_files", "mcp__allpepper-memory-bank__memory_bank_read", "Bash(git flow feature start:*)", "<PERSON><PERSON>(podman exec:*)", "<PERSON><PERSON>(podman inspect:*)", "Bash(npm run lint)", "Bash(npm run typecheck:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(source:*)", "Bash(pip install:*)", "<PERSON><PERSON>(podman restart:*)", "Bash(npm run build:*)", "Bash(grep:*)", "Bash(npm test:*)", "<PERSON><PERSON>(timeout 30s npm start)", "<PERSON><PERSON>(podman cp:*)", "<PERSON><PERSON>(podman stop:*)", "<PERSON><PERSON>(podman rm:*)", "Bash(npm start)", "Bash(git add:*)", "<PERSON><PERSON>(docker exec:*)", "<PERSON><PERSON>(docker cp:*)", "<PERSON><PERSON>(podman start:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./backend/run_comprehensive_tests.sh:*)", "Bash(pytest:*)", "Bash(podman system connection:*)", "Bash(./run_comprehensive_tests.sh:*)", "Bash(rg:*)", "mcp__ide__getDiagnostics", "<PERSON><PERSON>(podman build:*)", "<PERSON><PERSON>(podman run:*)", "<PERSON><PERSON>(pkill:*)", "Bash(TEST_DATABASE_HOST=localhost python -m pytest tests/test_endpoints.py -v)", "Bash(TEST_DATABASE_HOST=localhost python -m pytest tests/test_integration_simple.py -v)", "<PERSON><PERSON>(mypy:*)", "Bash(TEST_DATABASE_HOST=localhost python -m pytest --tb=no -q)", "Bash(TEST_DATABASE_HOST=localhost pytest -v)", "Bash(export:*)", "Bash(TEST_DATABASE_HOST=localhost pytest -v --tb=short)", "Bash(TEST_DATABASE_HOST=localhost pytest --tb=short)", "Bash(PYTHONPATH=/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend TEST_DATABASE_HOST=localhost pytest --tb=short)", "Bash(TEST_DATABASE_HOST=localhost pytest --no-header -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest --no-header -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_endpoints.py -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_endpoints.py -v -s)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_integration_simple.py::TestBasicIntegration::test_units_endpoint -v -s)", "<PERSON><PERSON>(make test-setup:*)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_success -xvs)", "<PERSON><PERSON>(make test:*)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest -x --tb=short)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_integration_simple.py::TestBasicIntegration::test_units_endpoint -xvs)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest --tb=short --no-header -rN)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest --tb=no -q)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_concurrent_connection_creation -xvs)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest -xvs -k \"test_create_entity_success\")", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost python -c \"\nimport asyncio\nfrom httpx import AsyncClient\nfrom src.main import app\nfrom src.database import get_db\nfrom tests.conftest import db_session\n\nasync def test_entity():\n    from tests.conftest import test_client\n    async with AsyncClient(app=app, base_url=''http://test'') as client:\n        # Override db\n        async def get_test_db():\n            from tests.conftest import test_engine, setup_test_database\n            from sqlalchemy.ext.asyncio import async_sessionmaker, AsyncSession\n            engine = test_engine()\n            await setup_test_database(engine)\n            session_factory = async_sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)\n            async with session_factory() as session:\n                yield session\n        \n        app.dependency_overrides[get_db] = get_test_db\n        \n        response = await client.post(''/api/v1/entities/'', json={''name'': ''Test''})\n        print(f''Status: {response.status_code}'')\n        print(f''Response: {response.text}'')\n\nasyncio.run(test_entity())\n\")", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_concurrent_connection_creation -xvs -k concurrent_connection)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_concurrent_connection_creation -xvs --tb=short)", "Bash(rm:*)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest --no-header -v --tb=short)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_decimal_precision -xvs)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_decimal_precision -xvs --tb=long)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_connections.py -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest --tb=no)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_entities.py::TestEntityCRUD::test_create_entity_case_insensitive -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_entities.py::TestEntityCRUD::test_create_entity_max_length -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_entities.py::TestEntityCRUD::test_create_entity_exceeds_max_length -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_entities.py::TestEntityCRUD::test_create_entity_numeric_name -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_entities.py -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_auto_inverse -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_auto_inverse -v -s)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_entities.py::TestEntityCRUD::test_create_entity_success -v -s)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest --tb=short -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost python -m pytest --tb=short -v)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase1_demo.py::TestPhase1Demo::test_units_endpoint_works -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase1_demo.py::TestPhase1Demo::test_entity_creation_pattern -v)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase1_demo.py::TestPhase1Demo::test_entity_creation_pattern -v)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase1_demo.py -v)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_endpoints.py -v)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase1_demo.py::TestPhase1Demo::test_units_endpoint_works -v -s)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase1_demo.py::TestPhase1Demo::test_entity_creation_pattern -v --no-cov)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase1_demo.py::TestPhase1Demo::test_entity_creation_pattern -v -s --no-cov)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase2_verification.py -v --no-cov)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase2_verification.py::TestPhase2Verification::test_entity_creation_success -v -s --no-cov)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase2_verification.py::TestPhase2Verification::test_units_endpoint_working -v --no-cov)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase2_verification.py::TestPhase2Verification::test_entity_validation_errors -v --no-cov)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost make test)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_connections.py --collect-only)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase2_verification.py tests/test_endpoints.py -v --no-cov)", "Bash(git commit:*)", "Bash(git push:*)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest --tb=no -v)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase2_verification.py::TestPhase2Verification::test_entity_validation_errors -v)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase2_verification.py::TestPhase2Verification::test_entity_creation_success -v -s)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase2_verification.py -v)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase2_verification.py::TestPhase2Verification::test_units_endpoint_working -v)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase2_verification.py::TestPhase2Verification::test_entity_validation_errors -v -s)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase3_connection_validation.py::TestPhase3ConnectionValidation::test_units_and_entities_endpoints -v -s)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase3_connection_validation.py::TestPhase3ConnectionValidation::test_connection_creation_basic -v -s)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase3_connection_validation.py::TestPhase3ConnectionValidation::test_pathfinding_api_basic -v -s)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase3_connection_validation.py -v)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase3_inverse_connections.py::TestInverseConnectionCreation::test_automatic_inverse_creation -v -s)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase3_inverse_connections.py::TestInverseConnectionCreation::test_decimal_rounding_in_inverse -v -s)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase3_inverse_connections.py::TestInverseConnectionCreation::test_connection_deletion_removes_inverse -v -s)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase3_pathfinding.py::TestPathfindingAlgorithm::test_direct_path_comparison -v -s)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase3_pathfinding.py::TestPathfindingAlgorithm::test_two_hop_pathfinding -v -s)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase3_pathfinding.py::TestPathfindingAlgorithm::test_no_path_exists -v -s)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase3_pathfinding.py::TestPathfindingAlgorithm::test_same_entity_comparison -v -s)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost python -c \"\nimport subprocess\nimport re\n\n# List of test cases that should work individually\ntest_cases = [\n    ''tests/test_endpoints.py'',\n    ''tests/test_phase2_verification.py::TestPhase2Verification::test_entity_validation_errors'',\n    ''tests/test_phase3_connection_validation.py::TestPhase3ConnectionValidation::test_connection_creation_basic'',\n    ''tests/test_phase3_connection_validation.py::TestPhase3ConnectionValidation::test_pathfinding_api_basic'', \n    ''tests/test_phase3_inverse_connections.py::TestInverseConnectionCreation::test_automatic_inverse_creation'',\n    ''tests/test_phase3_inverse_connections.py::TestInverseConnectionCreation::test_decimal_rounding_in_inverse'',\n    ''tests/test_phase3_inverse_connections.py::TestInverseConnectionCreation::test_connection_deletion_removes_inverse'',\n    ''tests/test_phase3_pathfinding.py::TestPathfindingAlgorithm::test_direct_path_comparison'',\n    ''tests/test_phase3_pathfinding.py::TestPathfindingAlgorithm::test_two_hop_pathfinding'',\n    ''tests/test_phase3_pathfinding.py::TestPathfindingAlgorithm::test_no_path_exists'',\n    ''tests/test_phase3_pathfinding.py::TestPathfindingAlgorithm::test_same_entity_comparison''\n]\n\npassed = 0\ntotal = len(test_cases)\n\nprint(''Phase 3 Individual Test Results:'')\nprint(''='' * 50)\n\nfor test in test_cases:\n    try:\n        result = subprocess.run([''pytest'', test, ''-q''], capture_output=True, text=True, timeout=30)\n        if result.returncode == 0:\n            status = ''✅ PASS''\n            passed += 1\n        else:\n            status = ''❌ FAIL''\n        print(f''{status} - {test}'')\n    except Exception as e:\n        print(f''❌ ERROR - {test}: {e}'')\n\nprint(''='' * 50)\nprint(f''Individual Test Pass Rate: {passed}/{total} ({passed/total*100:.1f}%)'')\n\")", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase2_verification.py::TestPhase2Verification::test_entity_creation_success -v)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase3_connection_apis.py::TestConnectionAPIs::test_connection_creation_with_auto_inverse -v)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase3_inverse_connections.py::TestInverseConnections::test_connection_creation_creates_inverse -v)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase3_inverse_connections.py -k test_automatic_inverse_creation -v)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase3_pathfinding.py::TestPathfinding::test_simple_direct_comparison -v)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase3_pathfinding.py -k test_direct_connection_comparison -v)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase3_pathfinding.py::TestPathfindingAPI -v --tb=short)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase3_pathfinding.py -v)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase3_pathfinding.py::TestPathfindingAlgorithm::test_direct_pathfinding -v)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase3_pathfinding.py::TestPathfindingAlgorithm::test_simple_pathfinding -v)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase3_pathfinding.py::TestPathfindingAlgorithm::test_direct_path_comparison -v)", "Bash(make lint)", "<PERSON><PERSON>(make:*)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/ -v --tb=short -x)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" python test_single_working.py)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_endpoints.py tests/test_phase2_verification.py -n 2 --dist loadfile -v)", "Bash(git checkout:*)", "<PERSON><PERSON>(mv:*)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost python -m pytest tests/test_integration.py -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_pathfinding.py::TestPathfinding::test_direct_comparison -v)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_pathfinding.py::TestPathfinding::test_direct_comparison -v)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. python3 -c \"\nimport asyncio\nimport httpx\nfrom src.app_factory import create_app\n\nasync def test_comparison():\n    app = create_app()\n    \n    async with httpx.AsyncClient(app=app, base_url=''http://test'') as client:\n        # Create test entities and connection first\n        entity1 = await client.post(''/api/v1/entities/'', json={''name'': ''TestA''})\n        entity2 = await client.post(''/api/v1/entities/'', json={''name'': ''TestB''})\n        \n        units = await client.get(''/api/v1/units/'')\n        unit_id = units.json()[0][''id'']\n        \n        # Create connection\n        await client.post(''/api/v1/connections/'', json={\n            ''from_entity_id'': entity1.json()[''id''],\n            ''to_entity_id'': entity2.json()[''id''],\n            ''unit_id'': unit_id,\n            ''multiplier'': 2.0\n        })\n        \n        # Try comparison - check exact error\n        response = await client.get(''/api/v1/compare/'', params={\n            ''from_entity_id'': entity1.json()[''id''],\n            ''to_entity_id'': entity2.json()[''id''], \n            ''unit_id'': unit_id\n        })\n        \n        print(f''Status: {response.status_code}'')\n        print(f''Response: {response.text}'')\n        \nasyncio.run(test_comparison())\n\")", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. python3 -c \"\nimport asyncio\nimport httpx\nfrom src.app_factory import create_app\n\nasync def test_comparison_fixed():\n    app = create_app()\n    \n    async with httpx.AsyncClient(app=app, base_url=''http://test'') as client:\n        # Create test entities and connection first\n        entity1 = await client.post(''/api/v1/entities/'', json={''name'': ''TestA''})\n        entity2 = await client.post(''/api/v1/entities/'', json={''name'': ''TestB''})\n        \n        units = await client.get(''/api/v1/units/'')\n        unit_id = units.json()[0][''id'']\n        \n        # Create connection\n        await client.post(''/api/v1/connections/'', json={\n            ''from_entity_id'': entity1.json()[''id''],\n            ''to_entity_id'': entity2.json()[''id''],\n            ''unit_id'': unit_id,\n            ''multiplier'': 2.0\n        })\n        \n        # Try comparison with CORRECT parameter names\n        response = await client.get(''/api/v1/compare/'', params={\n            ''from'': entity1.json()[''id''],\n            ''to'': entity2.json()[''id''], \n            ''unit'': unit_id\n        })\n        \n        print(f''Status: {response.status_code}'')\n        print(f''Response: {response.json()}'')\n        \nasyncio.run(test_comparison_fixed())\n\")", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. python3 -c \"\nimport asyncio\nimport httpx\nfrom src.app_factory import create_app\n\nasync def test_comparison_fixed():\n    app = create_app()\n    \n    async with httpx.AsyncClient(app=app, base_url=''http://test'') as client:\n        # Create test entities and connection first\n        entity1_resp = await client.post(''/api/v1/entities/'', json={''name'': ''TestA''})\n        print(f''Entity1 creation: {entity1_resp.status_code} - {entity1_resp.text}'')\n        \n        entity2_resp = await client.post(''/api/v1/entities/'', json={''name'': ''TestB''})\n        print(f''Entity2 creation: {entity2_resp.status_code} - {entity2_resp.text}'')\n        \n        units_resp = await client.get(''/api/v1/units/'')\n        print(f''Units fetch: {units_resp.status_code} - {units_resp.text}'')\n        \n        if entity1_resp.status_code == 200 and entity2_resp.status_code == 200 and units_resp.status_code == 200:\n            unit_id = units_resp.json()[0][''id'']\n            \n            # Create connection\n            conn_resp = await client.post(''/api/v1/connections/'', json={\n                ''from_entity_id'': entity1_resp.json()[''id''],\n                ''to_entity_id'': entity2_resp.json()[''id''],\n                ''unit_id'': unit_id,\n                ''multiplier'': 2.0\n            })\n            print(f''Connection creation: {conn_resp.status_code} - {conn_resp.text}'')\n            \n            # Try comparison with CORRECT parameter names\n            compare_resp = await client.get(''/api/v1/compare/'', params={\n                ''from'': entity1_resp.json()[''id''],\n                ''to'': entity2_resp.json()[''id''], \n                ''unit'': unit_id\n            })\n            \n            print(f''Comparison Status: {compare_resp.status_code}'')\n            print(f''Comparison Response: {compare_resp.text}'')\n        \nasyncio.run(test_comparison_fixed())\n\")", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. python3 -c \"\nimport asyncio\nimport httpx\nimport uuid\nfrom src.app_factory import create_app\n\nasync def test_comparison_fixed():\n    app = create_app()\n    \n    async with httpx.AsyncClient(app=app, base_url=''http://test'') as client:\n        # Create test entities with unique names\n        suffix = str(uuid.uuid4())[:8]\n        entity1_resp = await client.post(''/api/v1/entities/'', json={''name'': f''TestA_{suffix}''})\n        entity2_resp = await client.post(''/api/v1/entities/'', json={''name'': f''TestB_{suffix}''})\n        \n        units_resp = await client.get(''/api/v1/units/'')\n        unit_id = units_resp.json()[0][''id'']\n        \n        # Create connection\n        conn_resp = await client.post(''/api/v1/connections/'', json={\n            ''from_entity_id'': entity1_resp.json()[''id''],\n            ''to_entity_id'': entity2_resp.json()[''id''],\n            ''unit_id'': unit_id,\n            ''multiplier'': 2.0\n        })\n        print(f''Connection created: {conn_resp.status_code}'')\n        \n        # Try comparison with CORRECT parameter names\n        compare_resp = await client.get(''/api/v1/compare/'', params={\n            ''from'': entity1_resp.json()[''id''],\n            ''to'': entity2_resp.json()[''id''], \n            ''unit'': unit_id\n        })\n        \n        print(f''Comparison Status: {compare_resp.status_code}'')\n        if compare_resp.status_code == 200:\n            result = compare_resp.json()\n            print(f''Multiplier: {result[\"\"multiplier\"\"]}'')\n            print(f''Path length: {len(result[\"\"path\"\"]) if result[\"\"path\"\"] else 0}'')\n            print(''SUCCESS: Comparison API working!'')\n        else:\n            print(f''Error: {compare_resp.text}'')\n        \nasyncio.run(test_comparison_fixed())\n\")", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. python3 -c \"\nimport asyncio\nimport httpx\nimport uuid\nfrom src.app_factory import create_app\n\nasync def test_step_by_step():\n    app = create_app()\n    \n    async with httpx.AsyncClient(app=app, base_url=''http://test'') as client:\n        # Test entity creation\n        suffix = str(uuid.uuid4())[:8]\n        entity1_resp = await client.post(''/api/v1/entities/'', json={''name'': f''TestA_{suffix}''})\n        print(f''Entity1: {entity1_resp.status_code} - {entity1_resp.text}'')\n        \n        if entity1_resp.status_code != 200:\n            print(''Entity creation failed, stopping test'')\n            return\n            \n        entity2_resp = await client.post(''/api/v1/entities/'', json={''name'': f''TestB_{suffix}''})\n        print(f''Entity2: {entity2_resp.status_code} - {entity2_resp.text}'')\n        \n        # Test units\n        units_resp = await client.get(''/api/v1/units/'')\n        print(f''Units: {units_resp.status_code}'')\n        \nasyncio.run(test_step_by_step())\n\")", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. python3 -c \"\nimport asyncio\nimport httpx\nimport uuid\nfrom src.app_factory import create_app\n\nasync def test_step_by_step():\n    app = create_app()\n    \n    async with httpx.AsyncClient(app=app, base_url=''http://test'') as client:\n        # Test entity creation\n        suffix = str(uuid.uuid4())[:8]\n        entity1_resp = await client.post(''/api/v1/entities/'', json={''name'': f''TestA_{suffix}''})\n        print(f''Entity1: {entity1_resp.status_code} - {entity1_resp.text}'')\n        \n        if entity1_resp.status_code != 200:\n            print(''Entity creation failed'')\n            return\n            \n        entity2_resp = await client.post(''/api/v1/entities/'', json={''name'': f''TestB_{suffix}''})\n        print(f''Entity2: {entity2_resp.status_code} - {entity2_resp.text}'')\n        \n        # Test units\n        units_resp = await client.get(''/api/v1/units/'')\n        print(f''Units: {units_resp.status_code}'')\n\nasyncio.run(test_step_by_step())\n\")", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. python3 -c \"\nimport asyncio\nimport httpx\nimport uuid\nfrom src.app_factory import create_app\n\nasync def test_step_by_step():\n    app = create_app()\n    \n    async with httpx.AsyncClient(app=app, base_url=''http://test'') as client:\n        # Test entity creation\n        suffix = str(uuid.uuid4())[:8]\n        entity1_resp = await client.post(''/api/v1/entities/'', json={''name'': f''TestA_{suffix}''})\n        print(f''Entity1: {entity1_resp.status_code} - {entity1_resp.text}'')\n\nasyncio.run(test_step_by_step())\n\")", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. python3 -c \"\nimport asyncio\nimport httpx\nimport uuid\nfrom src.app_factory import create_app\n\nasync def test_comparison_working():\n    app = create_app()\n    \n    async with httpx.AsyncClient(app=app, base_url=''http://test'') as client:\n        # Create test entities with valid names (no underscores)\n        suffix = str(uuid.uuid4())[:8].replace(''-'', '''')  # Remove dashes too\n        entity1_resp = await client.post(''/api/v1/entities/'', json={''name'': f''TestA {suffix}''})\n        entity2_resp = await client.post(''/api/v1/entities/'', json={''name'': f''TestB {suffix}''})\n        \n        print(f''Entity1: {entity1_resp.status_code}'')\n        print(f''Entity2: {entity2_resp.status_code}'')\n        \n        if entity1_resp.status_code == 200 and entity2_resp.status_code == 200:\n            units_resp = await client.get(''/api/v1/units/'')\n            unit_id = units_resp.json()[0][''id'']\n            \n            # Create connection\n            conn_resp = await client.post(''/api/v1/connections/'', json={\n                ''from_entity_id'': entity1_resp.json()[''id''],\n                ''to_entity_id'': entity2_resp.json()[''id''],\n                ''unit_id'': unit_id,\n                ''multiplier'': 2.0\n            })\n            print(f''Connection created: {conn_resp.status_code}'')\n            \n            # Try comparison with CORRECT parameter names (from, to, unit)\n            compare_resp = await client.get(''/api/v1/compare/'', params={\n                ''from'': entity1_resp.json()[''id''],\n                ''to'': entity2_resp.json()[''id''], \n                ''unit'': unit_id\n            })\n            \n            print(f''Comparison Status: {compare_resp.status_code}'')\n            if compare_resp.status_code == 200:\n                result = compare_resp.json()\n                print(f''SUCCESS! Multiplier: {result[\"\"multiplier\"\"]}'')\n                print(''Comparison API is working with correct parameters!'')\n            else:\n                print(f''Comparison Error: {compare_resp.text}'')\n\nasyncio.run(test_comparison_working())\n\")", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. python3 -c \"\nimport asyncio\nimport httpx\nimport uuid\nfrom src.app_factory import create_app\n\nasync def debug_path_response():\n    app = create_app()\n    \n    async with httpx.AsyncClient(app=app, base_url=''http://test'') as client:\n        # Create entities\n        suffix = str(uuid.uuid4())[:8].replace(''-'', '''')\n        entity1_resp = await client.post(''/api/v1/entities/'', json={''name'': f''PathA {suffix}''})\n        entity2_resp = await client.post(''/api/v1/entities/'', json={''name'': f''PathB {suffix}''})\n        \n        units_resp = await client.get(''/api/v1/units/'')\n        unit_id = units_resp.json()[0][''id'']\n        \n        # Create connection\n        await client.post(''/api/v1/connections/'', json={\n            ''from_entity_id'': entity1_resp.json()[''id''],\n            ''to_entity_id'': entity2_resp.json()[''id''],\n            ''unit_id'': unit_id,\n            ''multiplier'': 2.0\n        })\n        \n        # Test comparison\n        response = await client.get(''/api/v1/compare/'', params={\n            ''from'': entity1_resp.json()[''id''],\n            ''to'': entity2_resp.json()[''id''], \n            ''unit'': unit_id\n        })\n        \n        result = response.json()\n        print(f''Multiplier: {result[\"\"multiplier\"\"]}'')\n        print(f''Path length: {len(result[\"\"path\"\"])}'')\n        print(f''Path contents: {result[\"\"path\"\"]}'')\n\nasyncio.run(debug_path_response())\n\")", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_pathfinding.py::TestPathfinding::test_two_hop_comparison -v)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_pathfinding.py::TestPathfinding::test_self_comparison -v)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_pathfinding.py -v)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_integration.py::TestConnectionIntegration::test_connection_with_inverse_creation -v)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. python3 -c \"\nimport asyncio\nimport httpx\nimport uuid\nfrom src.app_factory import create_app\n\nasync def test_status_code_fix():\n    app = create_app()  # Fresh app instance with updated code\n    \n    async with httpx.AsyncClient(app=app, base_url=''http://test'') as client:\n        # Create entities with unique names\n        suffix = str(uuid.uuid4())[:8].replace(''-'', '''')\n        entity1_resp = await client.post(''/api/v1/entities/'', json={''name'': f''Entity1 {suffix}''})\n        entity2_resp = await client.post(''/api/v1/entities/'', json={''name'': f''Entity2 {suffix}''})\n        \n        if entity1_resp.status_code == 200 and entity2_resp.status_code == 200:\n            units_resp = await client.get(''/api/v1/units/'')\n            unit_id = units_resp.json()[0][''id'']\n            \n            # Test connection creation status code\n            conn_resp = await client.post(''/api/v1/connections/'', json={\n                ''from_entity_id'': entity1_resp.json()[''id''],\n                ''to_entity_id'': entity2_resp.json()[''id''],\n                ''unit_id'': unit_id,\n                ''multiplier'': 2.5\n            })\n            \n            print(f''Connection creation status: {conn_resp.status_code}'')\n            if conn_resp.status_code == 201:\n                print(''SUCCESS: Connection now returns 201 (Created)!'')\n            else:\n                print(f''Still getting: {conn_resp.status_code}'')\n        else:\n            print(f''Entity creation failed: {entity1_resp.status_code}, {entity2_resp.status_code}'')\n\nasyncio.run(test_status_code_fix())\n\")", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. python3 -c \"\nimport asyncio\nimport httpx\nfrom src.app_factory import create_app\n\nasync def debug_entity_creation():\n    app = create_app()\n    \n    async with httpx.AsyncClient(app=app, base_url=''http://test'') as client:\n        # Test entity creation\n        entity1_resp = await client.post(''/api/v1/entities/'', json={''name'': ''Entity One''})\n        print(f''Entity1: {entity1_resp.status_code} - {entity1_resp.text}'')\n        \n        entity2_resp = await client.post(''/api/v1/entities/'', json={''name'': ''Entity Two''})\n        print(f''Entity2: {entity2_resp.status_code} - {entity2_resp.text}'')\n\nasyncio.run(debug_entity_creation())\n\")", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_entities.py::TestEntityCRUD::test_create_entity_success -v)", "Bash(DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_integration_simple.py::TestEntityBasicWorkflow::test_entity_creation_and_retrieval -v)", "Bash(DATABASE_URL:*)", "<PERSON><PERSON>(timeout 60 make test)", "<PERSON><PERSON>(timeout 120 make test)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_integration_simple.py::TestEntityBasicWorkflow::test_entity_validation -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest -v --tb=short)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_integration_simple.py::TestEntityBasicWorkflow::test_entity_validation -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost python -c \"\nimport asyncio\nfrom httpx import AsyncClient\nfrom src.app_factory import create_app\n\nasync def test_connection_validation():\n    app = create_app()\n    async with AsyncClient(app=app, base_url=''http://test'') as client:\n        # First create valid entities and unit\n        response1 = await client.post(''/api/v1/entities/'', json={''name'': ''EntityA''})\n        entity_a_id = response1.json()[''id'']\n        \n        response2 = await client.post(''/api/v1/entities/'', json={''name'': ''EntityB''})\n        entity_b_id = response2.json()[''id'']\n        \n        # Get a unit (should already exist)\n        units_response = await client.get(''/api/v1/units/'')\n        unit_id = units_response.json()[0][''id'']\n        \n        # Test self-connection\n        response = await client.post(''/api/v1/connections/'', json={\n            ''from_entity_id'': entity_a_id,\n            ''to_entity_id'': entity_a_id,\n            ''unit_id'': unit_id,\n            ''multiplier'': 2.0\n        })\n        print(f''Self-connection Status: {response.status_code}'')\n        if response.status_code != 201:\n            print(f''Self-connection Response: {response.json()}'')\n        \n        # Test negative multiplier\n        response = await client.post(''/api/v1/connections/'', json={\n            ''from_entity_id'': entity_a_id,\n            ''to_entity_id'': entity_b_id,\n            ''unit_id'': unit_id,\n            ''multiplier'': -2.0\n        })\n        print(f''Negative multiplier Status: {response.status_code}'')\n        if response.status_code != 201:\n            print(f''Negative multiplier Response: {response.json()}'')\n\nasyncio.run(test_connection_validation())\n\")", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost python -c \"\nimport asyncio\nfrom httpx import AsyncClient\nfrom src.app_factory import create_app\n\nasync def test_connection_validation():\n    app = create_app()\n    async with AsyncClient(app=app, base_url=''http://test'') as client:\n        # First create valid entities and unit\n        response1 = await client.post(''/api/v1/entities/'', json={''name'': ''EntityA''})\n        entity_a_id = response1.json()[''id'']\n        \n        response2 = await client.post(''/api/v1/entities/'', json={''name'': ''EntityB''})\n        entity_b_id = response2.json()[''id'']\n        \n        # Get a unit (should already exist)\n        units_response = await client.get(''/api/v1/units/'')\n        unit_id = units_response.json()[0][''id'']\n        \n        # Test self-connection\n        response = await client.post(''/api/v1/connections/'', json={\n            ''from_entity_id'': entity_a_id,\n            ''to_entity_id'': entity_a_id,\n            ''unit_id'': unit_id,\n            ''multiplier'': 2.0\n        })\n        print(f''Self-connection Status: {response.status_code}'')\n        if response.status_code != 201:\n            print(f''Self-connection Response: {response.json()}'')\n        \n        # Test negative multiplier\n        response = await client.post(''/api/v1/connections/'', json={\n            ''from_entity_id'': entity_a_id,\n            ''to_entity_id'': entity_b_id,\n            ''unit_id'': unit_id,\n            ''multiplier'': -2.0\n        })\n        print(f''Negative multiplier Status: {response.status_code}'')\n        if response.status_code != 201:\n            print(f''Negative multiplier Response: {response.json()}'')\n\nasyncio.run(test_connection_validation())\n\")", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost python -c \"\nimport asyncio\nfrom httpx import AsyncClient\nfrom src.app_factory import create_app\n\nasync def test_connection_validation():\n    app = create_app()\n    async with AsyncClient(app=app, base_url=''http://test'') as client:\n        # First create valid entities and unit\n        response1 = await client.post(''/api/v1/entities/'', json={''name'': ''EntityA''})\n        entity_a_id = response1.json()[''id'']\n        \n        response2 = await client.post(''/api/v1/entities/'', json={''name'': ''EntityB''})\n        entity_b_id = response2.json()[''id'']\n        \n        # Get a unit (should already exist)\n        units_response = await client.get(''/api/v1/units/'')\n        unit_id = units_response.json()[0][''id'']\n        \n        # Test self-connection\n        response = await client.post(''/api/v1/connections/'', json={\n            ''from_entity_id'': entity_a_id,\n            ''to_entity_id'': entity_a_id,\n            ''unit_id'': unit_id,\n            ''multiplier'': 2.0\n        })\n        print(f''Self-connection Status: {response.status_code}'')\n        if response.status_code != 201:\n            print(f''Self-connection Response: {response.json()}'')\n        \n        # Test negative multiplier\n        response = await client.post(''/api/v1/connections/'', json={\n            ''from_entity_id'': entity_a_id,\n            ''to_entity_id'': entity_b_id,\n            ''unit_id'': unit_id,\n            ''multiplier'': -2.0\n        })\n        print(f''Negative multiplier Status: {response.status_code}'')\n        if response.status_code != 201:\n            print(f''Negative multiplier Response: {response.json()}'')\n\nasyncio.run(test_connection_validation())\n\")", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost python test_fixes.py)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_same_entity -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost python -c \"\nimport asyncio\nimport uuid\nfrom httpx import AsyncClient\nfrom src.app_factory import create_app\n\nasync def test():\n    app = create_app()\n    async with AsyncClient(app=app, base_url=''http://test'') as client:\n        suffix = str(uuid.uuid4())[:8]\n        entity_name = f''Test Source Entity {suffix}''\n        print(f''Trying to create entity with name: {entity_name}'')\n        \n        response = await client.post(''/api/v1/entities/'', json={''name'': entity_name})\n        print(f''Status: {response.status_code}'')\n        print(f''Response: {response.json()}'')\n\nasyncio.run(test())\n\")", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost python -c \"\nimport asyncio\nimport string\nimport random\nfrom httpx import AsyncClient\nfrom src.app_factory import create_app\n\nasync def test_self_connection():\n    app = create_app()\n    async with AsyncClient(app=app, base_url=''http://test'') as client:\n        # Create an entity\n        suffix = ''''.join(random.choices(string.ascii_letters, k=8))\n        entity_response = await client.post(''/api/v1/entities/'', json={''name'': f''Test Entity {suffix}''})\n        entity_id = entity_response.json()[''id'']\n        \n        # Get a unit\n        units_response = await client.get(''/api/v1/units/'')\n        unit_id = units_response.json()[0][''id'']\n        \n        # Try to create self-connection\n        response = await client.post(''/api/v1/connections/'', json={\n            ''from_entity_id'': entity_id,\n            ''to_entity_id'': entity_id,\n            ''unit_id'': unit_id,\n            ''multiplier'': 1.0\n        })\n        \n        print(f''Status: {response.status_code}'')\n        print(f''Response: {response.json()}'')\n\nasyncio.run(test_self_connection())\n\")", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_negative_multiplier -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost python:*)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_negative_multiplier tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_zero_multiplier -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_pathfinding.py::TestPathfinding::test_no_path_exists -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_pathfinding.py::TestPathfinding::test_different_unit_no_path -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_integration_simple.py -x)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_integration_simple.py -v)", "Bash(./run_tests.sh)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest -xvs --tb=short)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest -k \"test_update_entity_duplicate_name or test_delete_entity_success\" -vvs)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest -k \"test_update_entity_duplicate_name\" -vvs)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest -k \"test_create_connection_very_large_multiplier\" -vvs)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest -k \"test_update_entity_duplicate_name\" -vvs --tb=short)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest -k \"test_connection_with_different_units\" -vvs --tb=short)", "<PERSON><PERSON>(./test-run.sh:*)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase1_demo.py tests/test_integration.py::TestEntityIntegration::test_entity_crud_workflow -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_integration.py::TestEntityIntegration::test_entity_crud_workflow -v -s)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_pathfinding.py::TestPathfinding::test_reverse_comparison -v -s)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_pathfinding.py -v --tb=short)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_pathfinding.py::TestPathfinding::test_complex_decimal_calculations -v -s)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_pathfinding.py -q)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_very_large_multiplier tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_extreme_multiplier_values -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_unit_operations -v)", "Bash(npm install:*)", "<PERSON><PERSON>(npx playwright:*)", "Bash(npm run test:e2e:*)", "Bash(./run_all_tests.sh:*)", "<PERSON><PERSON>(afplay:*)", "<PERSON><PERSON>(timeout 30s npm run test:e2e:headed -- --grep \"should load homepage successfully\")", "Bash(node:*)", "<PERSON><PERSON>(docker:*)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(gtimeout:*)", "<PERSON><PERSON>(docker-compose:*)", "Bash(timeout 30s npm run test:e2e -- --grep \"should create a new entity successfully\" --workers=1)", "Bash(gtimeout 30s npm run test:e2e -- --grep \"should create a new entity successfully\" --workers=1)", "Bash(open http://localhost:3000/entities)", "mcp__playwright__browser_navigate", "<PERSON><PERSON>(time curl:*)", "<PERSON><PERSON>(jq:*)", "Bash(/dev/null)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(sed:*)", "Bash(open http://localhost:3000/connections)", "Bash(git restore:*)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_duplicate -v)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_connections.py -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_connection_regression.py -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_connection_regression.py::TestConnectionRegression::test_cors_headers_on_success -v -s)", "Bash(npx tsc:*)", "Bash(npx eslint:*)", "Bash(npm run:*)", "<PERSON><PERSON>(cat:*)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest -xvs tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_connection_with_decimal_inverse --tb=short)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest -xvs tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_connection_with_decimal_inverse --tb=short -s)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_connection_with_decimal_inverse -xvs)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_edge_cases.py -xvs)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_connection_with_decimal_inverse tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_delete_entity_in_use -xvs)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_connection_with_decimal_inverse -v)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_connection_with_decimal_inverse -v)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_edge_cases.py -v)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_connection_with_decimal_inverse -vvs)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/ -x --tb=no -q)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/ --tb=short -v)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_success -v -s)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_success -v)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/ --tb=no -q)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/ --tb=no)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_auto_inverse -xvs)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_entities.py::TestEntityCRUD::test_create_entity_success -xvs)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_concurrent_entity_creation_same_name -xvs)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_integration.py::TestConnectionIntegration::test_connection_with_inverse_creation -xvs)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_entities.py::TestEntityCRUD::test_create_entity_success -xvs --tb=long)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_entity_name_validation -xvs)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_entity_name_validation_special_chars -xvs)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_edge_cases.py --collect-only)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_special_characters_in_entity_names -xvs)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_database_transaction_rollback -xvs)", "Bash(TEST_DATABASE_HOST=localhost python -c \"\nfrom src.config import settings\nprint(''Database URL:'', settings.database_url)\nimport os\nprint(''TEST_DATABASE_HOST:'', os.environ.get(''TEST_DATABASE_HOST''))\n\")", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_integration.py::TestConnectionIntegration::test_connection_with_inverse_creation -xvs --tb=short)", "Bash(PYTHONPATH=. python debug_test.py)", "Bash(psql:*)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_entities.py::TestEntityCRUD::test_create_entity_success -xvs --tb=short --log-cli-level=DEBUG)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_auto_inverse -xvs --tb=short)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest --collect-only -q)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_entities.py::TestEntityCRUD::test_create_entity_success -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_entities.py::TestEntityCRUD::test_create_entity_with_spaces -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_entities.py::TestEntityCRUD::test_create_entity_success tests/test_comprehensive_entities.py::TestEntityCRUD::test_create_entity_with_spaces -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_entities.py::TestEntityCRUD::test_entity_pagination -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_entities.py::TestEntityCRUD::test_entity_pagination -v -s)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/ -v --tb=short)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase3_inverse_connections.py::TestInverseConnectionCreation::test_automatic_inverse_creation -xvs)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase3_inverse_connections.py::TestInverseConnectionCreation::test_connection_deletion_removes_inverse -xvs)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/ -k \"connection\" -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/ --tb=short)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_auto_inverse tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_decimal_precision tests/test_integration.py::TestConnectionIntegration::test_connection_with_inverse_creation tests/test_phase3_inverse_connections.py::TestInverseConnectionCreation::test_automatic_inverse_creation tests/test_phase3_inverse_connections.py::TestInverseConnectionCreation::test_connection_deletion_removes_inverse -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_concurrent_entity_creation_same_name -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_entities.py::TestEntityCRUD::test_entity_concurrent_creation -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_update_entity_concurrent_requests -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_concurrent_connection_creation -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_database_transaction_rollback -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_concurrent_entity_creation_same_name tests/test_comprehensive_entities.py::TestEntityCRUD::test_entity_concurrent_creation tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_update_entity_concurrent_requests tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_concurrent_connection_creation tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_database_transaction_rollback -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest --cov=src --cov-report=term-missing --tb=short)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest -v tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_concurrent_entity_creation_same_name tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_concurrent_connection_creation tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_database_transaction_rollback --tb=short)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/ --tb=no -v)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_entities.py::TestEntityCRUD::test_create_entity_success tests/test_comprehensive_entities.py::TestEntityCRUD::test_create_entity_with_spaces tests/test_comprehensive_entities.py::TestEntityCRUD::test_update_entity_success -v)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_auto_inverse tests/test_phase3_inverse_connections.py::TestInverseConnectionCreation::test_automatic_inverse_creation -v)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_concurrent_entity_creation_same_name tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_concurrent_connection_creation -v)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_database_transaction_rollback tests/test_integration.py::TestConnectionIntegration::test_connection_with_inverse_creation -v)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_connection_regression.py::TestConnectionRegression::test_connection_with_decimal_inverse -v)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_connection_regression.py -v)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/ --collect-only -q)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/ --collect-only)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/ -v)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_entities.py::TestEntityCRUD::test_create_entity_success tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_auto_inverse tests/test_phase3_inverse_connections.py::TestInverseConnectionCreation::test_automatic_inverse_creation -v)", "Bash(TEST_DATABASE_HOST=localhost python -m pytest tests/test_comprehensive_entities.py::TestEntityCRUD::test_create_entity_success -v -s)", "Bash(TEST_DATABASE_HOST=localhost python -m pytest tests/test_comprehensive_entities.py::TestEntityCRUD::test_get_all_entities_with_data -v -s)", "Bash(TEST_DATABASE_HOST=localhost DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=\".\" python -m pytest -v --tb=short)", "Bash(TEST_DATABASE_HOST=localhost DATABASE_URL=\"postgresql+asyncpg://postgres:postgres@localhost:5432/simile_test\" PYTHONPATH=\".\" python -m pytest --cov=src --cov-report=term-missing --cov-report=html --tb=short)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest --cov=src --cov-report=term-missing --cov-report=html)", "Bash(PYTHONPATH=.. TEST_DATABASE_HOST=localhost pytest test_data_validation.py -v)", "Bash(PYTHONPATH=.. TEST_DATABASE_HOST=localhost pytest test_data_validation.py::TestDataValidationFramework::test_cleanup_verification -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_connections_advanced_scenarios.py::TestConnectionsAdvancedScenarios::test_connection_integrity_constraints -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_connections_advanced_scenarios.py tests/test_connections_comprehensive_crud.py -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_connections_comprehensive_crud.py::TestConnectionsCRUDComprehensive::test_create_connection_decimal_precision -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_connections_comprehensive_crud.py tests/test_connections_advanced_scenarios.py -v --tb=no)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_entities.py --cov=src/routes/entities --cov-report=term-missing -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_endpoints.py --cov=src/routes/units --cov-report=term-missing -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_entities_comprehensive_coverage.py --cov=src/routes/entities --cov-report=term-missing -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_units_comprehensive_coverage.py --cov=src/routes/units --cov-report=term-missing -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_endpoints.py -k \"units\" -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_entities_comprehensive_coverage.py --cov=src/routes/entities --cov-report=term-missing)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest --cov=src --cov-report=term-missing --tb=no -q)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest --cov=src --cov-report=term-missing --cov-report=html -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_units_comprehensive_coverage.py::TestUnitsComprehensiveCoverage::test_create_unit_success -xvs)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_units_comprehensive_coverage.py::TestUnitsComprehensiveCoverage::test_create_unit_success -xvs --capture=no)", "Bash(PYTHONPATH=. python -c \"\nimport asyncio\nfrom src.database import get_async_session\nfrom src.models import Unit\nfrom sqlalchemy import select\n\nasync def check_units():\n    async with get_async_session() as session:\n        result = await session.execute(select(Unit))\n        units = result.scalars().all()\n        print(f''Found {len(units)} units:'')\n        for unit in units:\n            print(f''  - {unit.name} ({unit.symbol})'')\n\nasyncio.run(check_units())\n\")", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_units_comprehensive_coverage.py -xvs)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest --cov=src --cov-report=term-missing --tb=short -q)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_units_comprehensive_coverage.py::TestUnitsCRUD::test_create_unit_duplicate_symbol -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_units_comprehensive_coverage.py::TestUnitsCRUD::test_create_unit_duplicate_symbol tests/test_units_comprehensive_coverage.py::TestUnitsCRUD::test_create_unit_validation_errors tests/test_units_comprehensive_coverage.py::TestUnitsCRUD::test_get_unit_by_invalid_id tests/test_units_comprehensive_coverage.py::TestUnitsCRUD::test_get_unit_by_negative_id -v --tb=short)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_units_comprehensive_coverage.py::TestUnitsComprehensiveCoverage::test_create_unit_duplicate_symbol tests/test_units_comprehensive_coverage.py::TestUnitsComprehensiveCoverage::test_create_unit_validation_errors tests/test_units_comprehensive_coverage.py::TestUnitsComprehensiveCoverage::test_get_unit_by_invalid_id tests/test_units_comprehensive_coverage.py::TestUnitsComprehensiveCoverage::test_get_unit_by_negative_id -v --tb=short)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/ -k \"test_connection_multiplier_boundary_values or test_connection_error_recovery or test_connection_with_malformed_data or test_connection_list_pagination_edge_cases or test_create_connection_automatic_inverse or test_create_connection_validation_errors or test_create_connection_duplicate_updates_existing or test_update_connection\" -v --tb=short)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/ -k \"test_get_entities_invalid_pagination\" -v --tb=short)", "Bash(TEST_DATABASE_HOST=localhost pytest -xvs)", "Bash(TEST_DATABASE_HOST=localhost pytest --tb=short -x)", "Bash(TEST_DATABASE_HOST=localhost pytest --tb=no -q)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_connections_advanced_scenarios.py::TestConnectionsAdvancedScenarios::test_connection_multiplier_boundary_values -xvs)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_units_comprehensive_coverage.py::TestUnitsComprehensiveCoverage::test_get_unit_by_negative_id -xvs)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_units_comprehensive_coverage.py::TestUnitsComprehensiveCoverage::test_get_unit_by_invalid_id -xvs)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_units_comprehensive_coverage.py::TestUnitsComprehensiveCoverage::test_create_unit_validation_errors -xvs)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_entities_comprehensive_coverage.py::TestEntitiesComprehensiveCoverage::test_get_entities_invalid_pagination -xvs)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_connections_advanced_scenarios.py::TestConnectionsAdvancedScenarios::test_connection_list_pagination_edge_cases -xvs)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_connections_comprehensive_crud.py::TestConnectionsCRUDComprehensive::test_create_connection_automatic_inverse -xvs)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_connections_comprehensive_crud.py::TestConnectionsCRUDComprehensive::test_create_connection_duplicate_updates_existing -xvs)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_connections_comprehensive_crud.py::TestConnectionsCRUDComprehensive::test_update_connection -xvs)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_connections_comprehensive_crud.py::TestConnectionsCRUDComprehensive::test_create_connection_validation_errors -xvs)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_connections_advanced_scenarios.py::TestConnectionsAdvancedScenarios::test_connection_error_recovery -xvs)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_connections_advanced_scenarios.py::TestConnectionsAdvancedScenarios::test_connection_with_malformed_data -xvs)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_connections_advanced_scenarios.py::TestConnectionsAdvancedScenarios::test_connection_multiplier_boundary_values tests/test_connections_advanced_scenarios.py::TestConnectionsAdvancedScenarios::test_connection_error_recovery tests/test_connections_advanced_scenarios.py::TestConnectionsAdvancedScenarios::test_connection_with_malformed_data tests/test_connections_advanced_scenarios.py::TestConnectionsAdvancedScenarios::test_connection_list_pagination_edge_cases tests/test_connections_comprehensive_crud.py::TestConnectionsCRUDComprehensive::test_create_connection_automatic_inverse tests/test_connections_comprehensive_crud.py::TestConnectionsCRUDComprehensive::test_create_connection_validation_errors tests/test_connections_comprehensive_crud.py::TestConnectionsCRUDComprehensive::test_create_connection_duplicate_updates_existing tests/test_connections_comprehensive_crud.py::TestConnectionsCRUDComprehensive::test_update_connection tests/test_entities_comprehensive_coverage.py::TestEntitiesComprehensiveCoverage::test_get_entities_invalid_pagination tests/test_units_comprehensive_coverage.py::TestUnitsComprehensiveCoverage::test_create_unit_validation_errors tests/test_units_comprehensive_coverage.py::TestUnitsComprehensiveCoverage::test_get_unit_by_invalid_id tests/test_units_comprehensive_coverage.py::TestUnitsComprehensiveCoverage::test_get_unit_by_negative_id -v)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_same_entity -xvs)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_very_large_multiplier -xvs)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_connections_advanced_scenarios.py::TestConnectionsAdvancedScenarios::test_connection_multiplier_boundary_values tests/test_connections_advanced_scenarios.py::TestConnectionsAdvancedScenarios::test_connection_error_recovery tests/test_connections_advanced_scenarios.py::TestConnectionsAdvancedScenarios::test_connection_with_malformed_data tests/test_connections_advanced_scenarios.py::TestConnectionsAdvancedScenarios::test_connection_list_pagination_edge_cases tests/test_connections_comprehensive_crud.py::TestConnectionsCRUDComprehensive::test_create_connection_automatic_inverse tests/test_connections_comprehensive_crud.py::TestConnectionsCRUDComprehensive::test_create_connection_validation_errors tests/test_connections_comprehensive_crud.py::TestConnectionsCRUDComprehensive::test_create_connection_duplicate_updates_existing tests/test_connections_comprehensive_crud.py::TestConnectionsCRUDComprehensive::test_update_connection tests/test_entities_comprehensive_coverage.py::TestEntitiesComprehensiveCoverage::test_get_entities_invalid_pagination tests/test_units_comprehensive_coverage.py::TestUnitsComprehensiveCoverage::test_create_unit_validation_errors tests/test_units_comprehensive_coverage.py::TestUnitsComprehensiveCoverage::test_get_unit_by_invalid_id tests/test_units_comprehensive_coverage.py::TestUnitsComprehensiveCoverage::test_get_unit_by_negative_id tests/test_units_comprehensive_coverage.py::TestUnitsComprehensiveCoverage::test_get_unit_by_zero_id --tb=no -q)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_units_comprehensive_coverage.py::TestUnitsComprehensiveCoverage::test_get_unit_by_zero_id -xvs)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_compare_comprehensive.py::TestCompareRouteDirectConnections::test_compare_direct_connection_reverse_direction -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_very_large_multiplier -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest -v --tb=line)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_phase3_inverse_connections.py::TestInverseConnectionCreation::test_automatic_inverse_creation -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_compare_comprehensive.py::TestCompareRouteDirectConnections::test_compare_direct_connection_reverse_direction tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_auto_inverse tests/test_phase3_inverse_connections.py::TestInverseConnectionCreation::test_automatic_inverse_creation tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_extreme_multiplier_values tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_very_large_multiplier tests/test_services_comprehensive.py::TestFindShortestPathEdgeCases::test_find_path_with_very_large_multiplier -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_extreme_multiplier_values -v -s)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_extreme_multiplier_values -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_very_large_multiplier tests/test_services_comprehensive.py::TestFindShortestPathEdgeCases::test_find_path_with_very_large_multiplier -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_services_comprehensive.py::TestFindShortestPathEdgeCases::test_find_path_with_very_large_multiplier -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_integration.py::TestErrorHandling::test_self_referencing_connection tests/test_integration_simple.py::TestConnectionBasicWorkflow::test_connection_validation -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_duplicate -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_compare_comprehensive.py::TestCompareRouteDirectConnections::test_compare_direct_connection_reverse_direction tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_auto_inverse tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_same_entity tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_duplicate tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_very_large_multiplier tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_extreme_multiplier_values tests/test_integration.py::TestErrorHandling::test_self_referencing_connection tests/test_integration_simple.py::TestConnectionBasicWorkflow::test_connection_validation tests/test_phase3_inverse_connections.py::TestInverseConnectionCreation::test_automatic_inverse_creation tests/test_services_comprehensive.py::TestFindShortestPathEdgeCases::test_find_path_with_very_large_multiplier -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_compare_comprehensive.py::TestCompareRouteDirectConnections::test_compare_direct_connection_reverse_direction tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_auto_inverse tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_same_entity tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_duplicate tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_very_large_multiplier tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_extreme_multiplier_values tests/test_integration.py::TestErrorHandling::test_self_referencing_connection tests/test_integration_simple.py::TestConnectionBasicWorkflow::test_connection_validation tests/test_phase3_inverse_connections.py::TestInverseConnectionCreation::test_automatic_inverse_creation tests/test_services_comprehensive.py::TestFindShortestPathEdgeCases::test_find_path_with_very_large_multiplier)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_compare_comprehensive.py::TestCompareRouteDirectConnections::test_compare_direct_connection_reverse_direction tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_auto_inverse tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_same_entity tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_duplicate tests/test_comprehensive_connections.py::TestConnectionCRUD::test_create_connection_very_large_multiplier tests/test_comprehensive_edge_cases.py::TestEdgeCases::test_extreme_multiplier_values tests/test_integration.py::TestErrorHandling::test_self_referencing_connection tests/test_integration_simple.py::TestConnectionBasicWorkflow::test_connection_validation tests/test_phase3_inverse_connections.py::TestInverseConnectionCreation::test_automatic_inverse_creation tests/test_services_comprehensive.py::TestFindShortestPathEdgeCases::test_find_path_with_very_large_multiplier --tb=no)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest -xvs tests/test_connections.py::test_connection_edge_case_rounding -k \"test_connection_edge_case_rounding\")", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest -xvs tests/test_comprehensive_connections.py::test_connection_edge_case_rounding)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest -xvs tests/test_comprehensive_connections.py::TestConnectionCRUD::test_connection_edge_case_rounding)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest --tb=short -x)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest -xvs tests/test_connections_advanced_scenarios.py::TestConnectionsAdvancedScenarios::test_connection_multiplier_boundary_values)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest -xvs tests/test_connections_advanced_scenarios.py::TestConnectionsAdvancedScenarios::test_connection_decimal_rounding_edge_cases)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest -xvs tests/test_connections_advanced_scenarios.py::TestConnectionsAdvancedScenarios::test_connection_error_recovery)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest -xvs tests/test_connections_comprehensive_crud.py::TestConnectionsCRUDComprehensive::test_create_connection_automatic_inverse)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest -xvs tests/test_connections_comprehensive_crud.py::TestConnectionsCRUDComprehensive::test_create_connection_validation_errors)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest -xvs tests/test_connections_comprehensive_crud.py::TestConnectionsCRUDComprehensive::test_create_connection_duplicate_updates_existing)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest -xvs tests/test_connections_comprehensive_crud.py::TestConnectionsCRUDComprehensive::test_update_connection)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest --tb=short)", "Bash(TEST_DATABASE_HOST=localhost python -c \"\nimport asyncio\nfrom src.config import get_settings\n\nasync def test_config():\n    settings = get_settings()\n    print(f''Database URL: {settings.database_url}'')\n    \nasyncio.run(test_config())\n\")", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_endpoints.py -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_compare_error_scenarios.py -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_compare_error_scenarios.py --cov=src/routes/compare --cov-report=term-missing)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_compare_comprehensive.py tests/test_compare_error_scenarios.py --cov=src.routes.compare --cov-report=term-missing)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_compare_error_scenarios.py::TestCompareRouteErrorScenarios::test_compare_nonexistent_from_entity_404 -v --cov=src/routes/compare --cov-report=line-missing)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_compare_error_scenarios.py::TestCompareRouteErrorScenarios::test_compare_nonexistent_from_entity_404 -v --cov=src/routes/compare --cov-report=term-missing)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_compare_comprehensive.py tests/test_compare_error_scenarios.py --cov=src --cov-report=html)", "Bash(./venv/bin/python -m pytest tests/test_connections_critical_coverage.py -v)", "Bash(venv/bin/python -m pytest tests/test_connections_critical_coverage.py -v)", "Bash(/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/bin/python -m pytest tests/test_connections_critical_coverage.py -v)", "Bash(TEST_DATABASE_HOST=localhost venv/bin/python -m pytest tests/test_connections_critical_coverage.py -v)", "Bash(TEST_DATABASE_HOST=localhost venv/bin/python -m pytest tests/test_connections_critical_coverage.py -v --cov=src/routes/connections --cov-report=term-missing)", "Bash(TEST_DATABASE_HOST=localhost venv/bin/python -m pytest tests/test_connections_critical_coverage.py::TestConnectionsErrorPaths::test_create_connection_from_entity_not_found tests/test_connections_critical_coverage.py::TestConnectionsErrorPaths::test_create_connection_to_entity_not_found tests/test_connections_critical_coverage.py::TestConnectionsErrorPaths::test_create_connection_unit_not_found tests/test_connections_critical_coverage.py::TestConnectionsErrorPaths::test_create_connection_duplicate_update_path tests/test_connections_critical_coverage.py::TestConnectionsPaginationAndValidation::test_get_connections_negative_skip tests/test_connections_critical_coverage.py::TestConnectionsPaginationAndValidation::test_get_connections_negative_limit tests/test_connections_critical_coverage.py::TestConnectionsPaginationAndValidation::test_get_connection_not_found tests/test_connections_critical_coverage.py::TestConnectionsUpdateAndDelete::test_update_connection_not_found tests/test_connections_critical_coverage.py::TestConnectionsUpdateAndDelete::test_update_connection_integrity_error tests/test_connections_critical_coverage.py::TestConnectionsUpdateAndDelete::test_update_connection_no_fields_to_update tests/test_connections_critical_coverage.py::TestConnectionsUpdateAndDelete::test_delete_connection_not_found tests/test_connections_critical_coverage.py::TestConnectionsConcurrentOperations::test_concurrent_connection_creation tests/test_connections_critical_coverage.py::TestConnectionsConcurrentOperations::test_concurrent_connection_update -v --cov=src.routes.connections --cov-report=term-missing)", "Bash(TEST_DATABASE_HOST=localhost venv/bin/python -m pytest tests/test_connections_critical_coverage.py::TestConnectionsIntegrationScenarios::test_cascade_entity_deletion_impact tests/test_connections_critical_coverage.py::TestConnectionsIntegrationScenarios::test_transaction_rollback_on_error tests/test_connections_critical_coverage.py::TestConnectionsIntegrationScenarios::test_connection_validation_error_paths tests/test_connections_critical_coverage.py::TestConnectionsIntegrationScenarios::test_connection_pagination_edge_cases tests/test_connections_critical_coverage.py::TestConnectionsIntegrationScenarios::test_connection_crud_complete_cycle -v --cov=src.routes.connections --cov-report=term-missing)", "Bash(TEST_DATABASE_HOST=localhost venv/bin/python -m pytest tests/test_connections_critical_coverage.py -k \"not (test_create_connection_database_failure or test_create_connection_integrity_error or test_create_connection_extreme_small_multiplier or test_create_connection_bankers_rounding or test_update_connection_no_inverse_found or test_delete_connection_no_inverse or test_complex_connection_network_creation)\" -v --cov=src.routes.connections --cov-report=term-missing)", "Bash(TEST_DATABASE_HOST=localhost venv/bin/python -m pytest tests/test_connections_high_coverage.py -v --cov=src.routes.connections --cov-report=term-missing)", "Bash(TEST_DATABASE_HOST=localhost venv/bin/python -m pytest tests/test_connections_high_coverage.py tests/test_connections_critical_coverage.py -k \"not (test_create_connection_database_failure or test_create_connection_integrity_error or test_create_connection_extreme_small_multiplier or test_create_connection_bankers_rounding or test_update_connection_no_inverse_found or test_delete_connection_no_inverse or test_complex_connection_network_creation)\" -v --cov=src.routes.connections --cov-report=term-missing)", "Bash(TEST_DATABASE_HOST=localhost venv/bin/python -m pytest tests/test_connections_high_coverage.py -v --cov=src.routes.connections --cov-report=html)", "Bash(TEST_DATABASE_HOST=localhost venv/bin/python -m pytest tests/test_connections_high_coverage.py tests/test_connections_critical_coverage.py -v --cov=src --cov-report=term-missing)", "Bash(TEST_DATABASE_HOST=localhost venv/bin/python -m pytest tests/test_connections_high_coverage.py tests/test_connections_critical_coverage.py -k \"not (test_create_connection_database_failure or test_create_connection_integrity_error or test_create_connection_extreme_small_multiplier or test_create_connection_bankers_rounding or test_update_connection_no_inverse_found or test_delete_connection_no_inverse or test_complex_connection_network_creation)\" -v --cov=src.routes.connections --cov-report=term-missing --cov-report=html)", "Bash(echo $TEST_DATABASE_HOST)", "Bash(TEST_DATABASE_HOST=localhost)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_api_contract_validation.py -v --tb=short)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_api_contract_validation.py::TestAPIResponseSchemas::test_compare_response_schema -v)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_api_error_handling.py -v --tb=short -k \"test_compare\")", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_api_error_handling.py -v --tb=short)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_api_error_handling.py::TestConnectionErrorHandling::test_connection_duplicate -v)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_api_error_handling.py::TestEdgeCaseErrors::test_numeric_overflow -v)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_api_contract_validation.py tests/test_api_error_handling.py -v --tb=no)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_api_error_handling.py::TestUnitErrorHandling::test_unit_creation -v)", "Bash(TEST_DATABASE_HOST=localhost pytest tests/test_api_contract_validation.py tests/test_api_error_handling.py -v --tb=no -q)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_api_contract_validation.py tests/test_api_error_handling.py -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest --tb=short -q)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest --tb=no -q --disable-warnings)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest --tb=no --disable-warnings -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_compare_error_scenarios.py tests/test_api_contract_validation.py tests/test_api_error_handling.py --tb=no -q)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_entities.py tests/test_connections.py tests/test_units.py tests/test_compare.py --tb=no -q)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_entities.py tests/test_comprehensive_connections.py tests/test_compare_comprehensive.py --tb=no -q)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_performance_benchmarks.py -v)", "Bash(TEST_DATABASE_HOST=localhost python -m pytest tests/test_performance_benchmarks.py::TestResourceEfficiency::test_memory_usage_patterns -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_performance_benchmarks.py::TestEntityPerformance::test_entity_creation_performance -v)", "Bash(TEST_DATABASE_HOST=localhost python -m pytest tests/test_endpoints.py::test_create_entity -v)", "Bash(TEST_DATABASE_HOST=localhost python -m pytest tests/test_endpoints.py -k \"entity\" -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_performance_benchmarks.py::TestEntityPerformance::test_entity_listing_performance -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest --collect-only)", "Bash(TEST_DATABASE_HOST=localhost python scripts/validate_test_infrastructure.py)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_performance_integration.py::TestPerformanceBenchmarkIntegration::test_performance_mixin_functionality -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_performance_integration.py -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost timeout 60 pytest --tb=no --no-header -v)", "Bash(TEST_DATABASE_HOST=localhost python scripts/reset_test_database.py clean)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest -x --tb=no -q tests/test_complex_graph_scenarios.py::TestStarTopology::test_star_hub_to_spoke_paths)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest --tb=no -q --no-header)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_performance_benchmarks.py::TestEntityPerformance -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_performance_benchmarks.py::TestEntityPerformance::test_entity_retrieval_performance -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_performance_benchmarks.py::TestConnectionPerformance::test_connection_creation_performance -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_database_transactions.py -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_database_transactions.py::TestTransactionIntegrity::test_connection_self_reference_constraint_violation -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_database_transactions.py::TestRollbackScenarios::test_connection_negative_multiplier_rollback -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_database_transactions.py::TestDatabaseConstraints::test_check_constraint_zero_multiplier -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_database_transactions.py::TestConcurrentTransactions::test_concurrent_connection_creation -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_database_transactions.py::TestAdvancedTransactionScenarios::test_transaction_with_pathfinding_consistency -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_database_transactions.py::TestAdvancedTransactionScenarios::test_transaction_with_pathfinding_consistency -v -s)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_database_transactions.py::TestConnectionCleanup -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_database_transactions.py)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_complex_graph_scenarios.py --tb=no -q)", "Bash(TEST_DATABASE_HOST=localhost python -m pytest tests/test_complex_graph_scenarios.py::TestStarTopology::test_star_hub_to_spoke_paths -xvs)", "Bash(TEST_DATABASE_HOST=localhost python -c \"\nfrom src.app_factory import create_app\napp = create_app()\nfor route in app.routes:\n    if hasattr(route, ''path'') and ''compare'' in route.path:\n        print(f''Path: {route.path}, Methods: {route.methods}'')\n\")", "Bash(TEST_DATABASE_HOST=localhost python -m pytest tests/test_complex_graph_scenarios.py -v --tb=short)", "Bash(TEST_DATABASE_HOST=localhost python -m pytest tests/test_concurrent_operations.py -v --tb=short)", "Bash(TEST_DATABASE_HOST=localhost python -m pytest tests/test_concurrent_operations.py::TestConcurrentComplexOperations::test_concurrent_pathfinding_operations -xvs)", "Bash(TEST_DATABASE_HOST=localhost python -m pytest tests/test_error_recovery_cleanup.py -v --tb=short)", "Bash(TEST_DATABASE_HOST=localhost python -m pytest tests/test_error_recovery_cleanup.py::TestDatabaseConnectionRecovery::test_transaction_rollback_on_connection_failure -xvs)", "Bash(TEST_DATABASE_HOST=localhost python -m pytest tests/test_complex_graph_scenarios.py tests/test_concurrent_operations.py -v --tb=no)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_api_contract_validation.py tests/test_api_error_handling.py tests/test_compare_error_scenarios.py --tb=no -q)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_complex_graph_scenarios.py -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_database_transactions.py tests/test_concurrent_operations.py -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_performance_benchmarks.py::TestEntityPerformance::test_entity_creation_performance -v -s)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_performance_benchmarks.py tests/test_performance_integration.py -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_performance_benchmarks.py::TestEntityPerformance::test_entity_creation_performance -v --tb=short)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_performance_integration.py -v --tb=short)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_database_transactions.py::TestConcurrentTransactions::test_concurrent_connection_creation -v --tb=short)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_database_transactions.py tests/test_concurrent_operations.py -v --tb=no -q)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_complex_graph_scenarios.py -v --tb=no -q)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_connections_critical_coverage.py tests/test_error_recovery_cleanup.py -v --tb=no -q)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_connections_critical_coverage.py::TestConnectionsDecimalPrecision::test_create_connection_extreme_small_multiplier -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_connections_critical_coverage.py::TestConnectionsDecimalPrecision::test_create_connection_extreme_small_multiplier -v -s --tb=line)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost timeout 60 pytest --tb=no -q tests/test_database_transactions.py tests/test_concurrent_operations.py tests/test_performance_benchmarks.py tests/test_performance_integration.py tests/test_complex_graph_scenarios.py)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost gtimeout 60 pytest --tb=no -q tests/test_database_transactions.py tests/test_concurrent_operations.py tests/test_performance_integration.py tests/test_complex_graph_scenarios.py)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest --tb=no -q tests/test_database_transactions.py tests/test_concurrent_operations.py tests/test_performance_integration.py)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_connections_critical_coverage.py::TestConnectionsDecimalPrecision -v --tb=no -q)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_api_contract_validation.py::TestRequestParameterValidation::test_connection_multiplier_validation -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_api_contract_validation.py::TestRequestParameterValidation::test_connection_multiplier_validation -v --tb=short)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_api_contract_validation.py::TestRequestParameterValidation -v --tb=no -q)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_api_contract_validation.py::TestRequestParameterValidation::test_entity_name_validation -v)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_api_contract_validation.py -v --tb=no -q)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_api_error_handling.py -v --tb=no -q)", "Bash(PYTHONPATH=. TEST_DATABASE_HOST=localhost pytest tests/test_comprehensive_entities.py -v --tb=no -q)", "Bash(TEST_DATABASE_HOST=localhost python -m pytest tests/test_complex_graph_scenarios.py::TestStarTopology::test_star_hub_to_spoke_paths -v)", "Bash(TEST_DATABASE_HOST=localhost python -m pytest tests/test_complex_graph_scenarios.py::TestChainTopology::test_chain_end_to_end_path -v)", "Bash(TEST_DATABASE_HOST=localhost python -m pytest tests/test_complex_graph_scenarios.py::TestGridTopology::test_grid_corner_to_corner_paths -v)", "Bash(TEST_DATABASE_HOST=localhost python -m pytest --tb=line -x)", "Bash(TEST_DATABASE_HOST=localhost python -m pytest tests/test_api_error_handling.py::TestErrorResponseFormat::test_validation_error_format -v -s)", "Bash(TEST_DATABASE_HOST=localhost python -m pytest tests/test_api_error_handling.py::TestErrorResponseFormat::test_validation_error_format -v)", "Bash(TEST_DATABASE_HOST=localhost python -m pytest tests/test_api_contract_validation.py::TestRequestParameterValidation::test_entity_name_validation -v)", "Bash(TEST_DATABASE_HOST=localhost python -c \"\nimport asyncio\nfrom httpx import AsyncClient\nfrom src.app_factory import create_app\n\nasync def test_simple():\n    app = create_app()\n    async with AsyncClient(app=app, base_url=''http://test'') as client:\n        response = await client.post(''/api/v1/entities/'', json={''name'': ''Test Name''})\n        print(''Response status:'', response.status_code)\n        print(''Response content:'', response.text)\n        if response.status_code == 201:\n            data = response.json()\n            print(''Created entity:'', data)\n\nasyncio.run(test_simple())\n\")", "Bash(TEST_DATABASE_HOST=localhost python -m pytest --tb=no --no-cov -q)", "Bash(TEST_DATABASE_HOST=localhost python -m pytest tests/test_api_contract_validation.py --tb=no --no-cov -q)"], "deny": []}}