"""
Comprehensive test suite for Connection CRUD operations with enhanced coverage.
This test suite targets 85% coverage of the connections route with robust test data management.

Focus areas:
1. Complete CRUD operations testing
2. Automatic inverse connection creation and management
3. Comprehensive validation and error scenarios
4. Edge cases and boundary conditions
5. Data cleanup and isolation verification
"""
import pytest
import pytest_asyncio
from decimal import Decimal
from httpx import AsyncClient
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from tests.conftest import create_test_entity_name, test_data_generator


class TestConnectionsCRUDComprehensive:
    """Comprehensive test suite for connections CRUD operations."""

    async def create_test_setup(self, test_client: AsyncClient):
        """Create standardized test setup with entities and unit."""
        # Create unique test entities
        entity1_name = create_test_entity_name("ConnectionTestSource")
        entity2_name = create_test_entity_name("ConnectionTestTarget")
        
        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity1_name}
        )
        assert entity1_response.status_code == 201
        
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity2_name}
        )
        assert entity2_response.status_code == 201
        
        # Get first available unit
        units_response = await test_client.get("/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        assert len(units) > 0
        
        return {
            "entity1": entity1_response.json(),
            "entity2": entity2_response.json(),
            "unit": units[0]
        }

    async def verify_connection_count(self, test_client: AsyncClient, expected_count: int):
        """Verify the total number of connections in the database."""
        response = await test_client.get("/api/v1/connections/?limit=1000")
        assert response.status_code == 200
        connections = response.json()
        assert len(connections) == expected_count, f"Expected {expected_count} connections, got {len(connections)}"

    async def find_connection(self, test_client: AsyncClient, from_entity_id: int, to_entity_id: int, unit_id: int):
        """Find a specific connection by its parameters."""
        response = await test_client.get("/api/v1/connections/?limit=1000")
        assert response.status_code == 200
        connections = response.json()
        
        for conn in connections:
            if (conn["from_entity_id"] == from_entity_id and 
                conn["to_entity_id"] == to_entity_id and 
                conn["unit_id"] == unit_id):
                return conn
        return None

    @pytest.mark.asyncio
    async def test_create_connection_basic_success(self, test_client, test_data_isolation):
        """Test basic connection creation with proper data isolation."""
        setup = await self.create_test_setup(test_client)
        
        # Track test data
        test_data_isolation.track_entity(setup["entity1"]["id"])
        test_data_isolation.track_entity(setup["entity2"]["id"])
        
        # Create connection
        connection_data = {
            "from_entity_id": setup["entity1"]["id"],
            "to_entity_id": setup["entity2"]["id"],
            "unit_id": setup["unit"]["id"],
            "multiplier": 2.5
        }
        
        response = await test_client.post("/api/v1/connections/", json=connection_data)
        
        assert response.status_code == 201
        result = response.json()
        assert result["from_entity_id"] == setup["entity1"]["id"]
        assert result["to_entity_id"] == setup["entity2"]["id"]
        assert result["unit_id"] == setup["unit"]["id"]
        assert float(result["multiplier"]) == 2.5
        assert "id" in result
        assert "created_at" in result
        assert "updated_at" in result
        
        # Track connection for cleanup
        test_data_isolation.track_connection(result["id"])
        
        # Verify total connections (should be 2: main + inverse)
        await self.verify_connection_count(test_client, 2)

    @pytest.mark.asyncio
    async def test_create_connection_automatic_inverse(self, test_client, test_data_isolation):
        """Test automatic inverse connection creation."""
        setup = await self.create_test_setup(test_client)
        
        # Track test data
        test_data_isolation.track_entity(setup["entity1"]["id"])
        test_data_isolation.track_entity(setup["entity2"]["id"])
        
        # Create connection A -> B with multiplier 4.0
        connection_data = {
            "from_entity_id": setup["entity1"]["id"],
            "to_entity_id": setup["entity2"]["id"],
            "unit_id": setup["unit"]["id"],
            "multiplier": 4.0
        }
        
        response = await test_client.post("/api/v1/connections/", json=connection_data)
        assert response.status_code == 201
        main_connection = response.json()
        
        # Track connection for cleanup
        test_data_isolation.track_connection(main_connection["id"])
        
        # Find the inverse connection B -> A
        inverse_connection = await self.find_connection(
            test_client, 
            setup["entity2"]["id"], 
            setup["entity1"]["id"], 
            setup["unit"]["id"]
        )
        
        assert inverse_connection is not None, "Inverse connection should be created automatically"
        assert float(inverse_connection["multiplier"]) == 0.2  # 1/4 = 0.25, rounded to 0.2 (banker's rounding)
        assert inverse_connection["unit_id"] == setup["unit"]["id"]
        
        # Track inverse connection for cleanup
        test_data_isolation.track_connection(inverse_connection["id"])
        
        # Verify total connections
        await self.verify_connection_count(test_client, 2)

    @pytest.mark.asyncio
    async def test_create_connection_decimal_precision(self, test_client, test_data_isolation):
        """Test connection creation with decimal precision handling."""
        setup = await self.create_test_setup(test_client)
        
        # Test various decimal scenarios
        test_cases = [
            (1.1, 1.1, 0.9),      # Simple decimal
            (1.15, 1.2, 0.8),     # Rounding up
            (1.14, 1.1, 0.9),     # Rounding down
            (2.0, 2.0, 0.5),      # Whole number
            (0.5, 0.5, 2.0),      # Fraction
            (10.0, 10.0, 0.1),    # Large number
        ]
        
        for i, (input_val, expected_main, expected_inverse) in enumerate(test_cases):
            # Create additional entities for each test case
            entity1_name = create_test_entity_name(f"DecimalTestSource{chr(65+i)}")
            entity2_name = create_test_entity_name(f"DecimalTestTarget{chr(65+i)}")
            
            entity1_response = await test_client.post(
                "/api/v1/entities/",
                json={"name": entity1_name}
            )
            entity2_response = await test_client.post(
                "/api/v1/entities/",
                json={"name": entity2_name}
            )
            
            entity1_id = entity1_response.json()["id"]
            entity2_id = entity2_response.json()["id"]
            
            # Track entities
            test_data_isolation.track_entity(entity1_id)
            test_data_isolation.track_entity(entity2_id)
            
            # Create connection
            connection_data = {
                "from_entity_id": entity1_id,
                "to_entity_id": entity2_id,
                "unit_id": setup["unit"]["id"],
                "multiplier": input_val
            }
            
            response = await test_client.post("/api/v1/connections/", json=connection_data)
            assert response.status_code == 201, f"Failed for input {input_val}: {response.text}"
            
            connection = response.json()
            test_data_isolation.track_connection(connection["id"])
            
            # Verify main connection multiplier
            assert abs(float(connection["multiplier"]) - expected_main) < 0.01, \
                f"Main connection: expected {expected_main}, got {connection['multiplier']} for input {input_val}"
            
            # Verify inverse connection
            inverse_connection = await self.find_connection(
                test_client, entity2_id, entity1_id, setup["unit"]["id"]
            )
            assert inverse_connection is not None
            test_data_isolation.track_connection(inverse_connection["id"])
            
            assert abs(float(inverse_connection["multiplier"]) - expected_inverse) < 0.01, \
                f"Inverse connection: expected {expected_inverse}, got {inverse_connection['multiplier']} for input {input_val}"

    @pytest.mark.asyncio
    async def test_create_connection_validation_errors(self, test_client, test_data_isolation):
        """Test comprehensive validation error scenarios."""
        setup = await self.create_test_setup(test_client)
        
        # Test case 1: Self-connection (entity to itself)
        response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": setup["entity1"]["id"],
                "to_entity_id": setup["entity1"]["id"],
                "unit_id": setup["unit"]["id"],
                "multiplier": 1.0
            }
        )
        assert response.status_code == 422
        # Pydantic validation errors have a different structure
        error_detail = response.json()["detail"]
        if isinstance(error_detail, list) and len(error_detail) > 0:
            error_msg = error_detail[0]["msg"].lower()
        else:
            error_msg = str(error_detail).lower()
        assert "cannot create connection from entity to itself" in error_msg
        
        # Test case 2: Negative multiplier
        response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": setup["entity1"]["id"],
                "to_entity_id": setup["entity2"]["id"],
                "unit_id": setup["unit"]["id"],
                "multiplier": -2.5
            }
        )
        assert response.status_code == 422
        
        # Test case 3: Zero multiplier
        response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": setup["entity1"]["id"],
                "to_entity_id": setup["entity2"]["id"],
                "unit_id": setup["unit"]["id"],
                "multiplier": 0.0
            }
        )
        assert response.status_code == 422
        
        # Test case 4: Non-existent from_entity_id
        response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": 99999,
                "to_entity_id": setup["entity2"]["id"],
                "unit_id": setup["unit"]["id"],
                "multiplier": 2.0
            }
        )
        assert response.status_code == 404
        assert "entity" in response.json()["detail"].lower()
        
        # Test case 5: Non-existent to_entity_id
        response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": setup["entity1"]["id"],
                "to_entity_id": 99999,
                "unit_id": setup["unit"]["id"],
                "multiplier": 2.0
            }
        )
        assert response.status_code == 404
        assert "entity" in response.json()["detail"].lower()
        
        # Test case 6: Non-existent unit_id
        response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": setup["entity1"]["id"],
                "to_entity_id": setup["entity2"]["id"],
                "unit_id": 99999,
                "multiplier": 2.0
            }
        )
        assert response.status_code == 404
        assert "unit" in response.json()["detail"].lower()

    @pytest.mark.asyncio
    async def test_create_connection_duplicate_updates_existing(self, test_client, test_data_isolation):
        """Test that creating duplicate connection updates existing instead of failing."""
        setup = await self.create_test_setup(test_client)
        
        # Track entities
        test_data_isolation.track_entity(setup["entity1"]["id"])
        test_data_isolation.track_entity(setup["entity2"]["id"])
        
        # Create first connection
        connection_data = {
            "from_entity_id": setup["entity1"]["id"],
            "to_entity_id": setup["entity2"]["id"],
            "unit_id": setup["unit"]["id"],
            "multiplier": 2.0
        }
        
        response1 = await test_client.post("/api/v1/connections/", json=connection_data)
        assert response1.status_code == 201
        connection1 = response1.json()
        test_data_isolation.track_connection(connection1["id"])
        
        # Try to create duplicate with different multiplier
        connection_data["multiplier"] = 3.0
        response2 = await test_client.post("/api/v1/connections/", json=connection_data)
        assert response2.status_code == 201  # Currently returns 201 for both create and update
        connection2 = response2.json()
        
        # Should be same connection ID but updated multiplier
        assert connection2["id"] == connection1["id"]
        assert float(connection2["multiplier"]) == 3.0
        assert connection2["updated_at"] != connection1["updated_at"]
        
        # Verify inverse connection was also updated
        inverse_connection = await self.find_connection(
            test_client, 
            setup["entity2"]["id"], 
            setup["entity1"]["id"], 
            setup["unit"]["id"]
        )
        assert inverse_connection is not None
        test_data_isolation.track_connection(inverse_connection["id"])
        
        # 1/3 = 0.333..., should round to 0.3
        assert abs(float(inverse_connection["multiplier"]) - 0.3) < 0.01
        
        # Total connections should still be 2
        await self.verify_connection_count(test_client, 2)

    @pytest.mark.asyncio
    async def test_get_connections_list(self, test_client, test_data_isolation):
        """Test getting list of connections with pagination."""
        setup = await self.create_test_setup(test_client)
        
        # Create multiple connections
        connection_pairs = []
        for i in range(3):
            entity1_name = create_test_entity_name(f"ListTestSource{chr(65+i)}")
            entity2_name = create_test_entity_name(f"ListTestTarget{chr(65+i)}")
            
            entity1_response = await test_client.post(
                "/api/v1/entities/",
                json={"name": entity1_name}
            )
            entity2_response = await test_client.post(
                "/api/v1/entities/",
                json={"name": entity2_name}
            )
            
            entity1_id = entity1_response.json()["id"]
            entity2_id = entity2_response.json()["id"]
            
            test_data_isolation.track_entity(entity1_id)
            test_data_isolation.track_entity(entity2_id)
            
            # Create connection
            connection_data = {
                "from_entity_id": entity1_id,
                "to_entity_id": entity2_id,
                "unit_id": setup["unit"]["id"],
                "multiplier": float(i + 1)
            }
            
            response = await test_client.post("/api/v1/connections/", json=connection_data)
            assert response.status_code == 201
            connection = response.json()
            test_data_isolation.track_connection(connection["id"])
            
            connection_pairs.append((entity1_id, entity2_id))
        
        # Get all connections
        response = await test_client.get("/api/v1/connections/")
        assert response.status_code == 200
        connections = response.json()
        
        # Should have 6 connections (3 main + 3 inverse)
        assert len(connections) == 6
        
        # Verify all connections are present
        for entity1_id, entity2_id in connection_pairs:
            main_conn = await self.find_connection(test_client, entity1_id, entity2_id, setup["unit"]["id"])
            inverse_conn = await self.find_connection(test_client, entity2_id, entity1_id, setup["unit"]["id"])
            
            assert main_conn is not None
            assert inverse_conn is not None
            test_data_isolation.track_connection(inverse_conn["id"])
        
        # Test pagination
        response = await test_client.get("/api/v1/connections/?limit=2")
        assert response.status_code == 200
        limited_connections = response.json()
        assert len(limited_connections) == 2
        
        # Test skip parameter
        response = await test_client.get("/api/v1/connections/?skip=2&limit=2")
        assert response.status_code == 200
        skipped_connections = response.json()
        assert len(skipped_connections) == 2

    @pytest.mark.asyncio
    async def test_get_connection_by_id(self, test_client, test_data_isolation):
        """Test getting a specific connection by ID."""
        setup = await self.create_test_setup(test_client)
        
        # Track entities
        test_data_isolation.track_entity(setup["entity1"]["id"])
        test_data_isolation.track_entity(setup["entity2"]["id"])
        
        # Create connection
        connection_data = {
            "from_entity_id": setup["entity1"]["id"],
            "to_entity_id": setup["entity2"]["id"],
            "unit_id": setup["unit"]["id"],
            "multiplier": 3.5
        }
        
        create_response = await test_client.post("/api/v1/connections/", json=connection_data)
        assert create_response.status_code == 201
        created_connection = create_response.json()
        test_data_isolation.track_connection(created_connection["id"])
        
        # Get connection by ID
        response = await test_client.get(f"/api/v1/connections/{created_connection['id']}")
        assert response.status_code == 200
        
        connection = response.json()
        assert connection["id"] == created_connection["id"]
        assert connection["from_entity_id"] == setup["entity1"]["id"]
        assert connection["to_entity_id"] == setup["entity2"]["id"]
        assert connection["unit_id"] == setup["unit"]["id"]
        assert float(connection["multiplier"]) == 3.5
        
        # Test non-existent connection
        response = await test_client.get("/api/v1/connections/99999")
        assert response.status_code == 404
        assert "connection not found" in response.json()["detail"].lower()

    @pytest.mark.asyncio
    async def test_update_connection(self, test_client, test_data_isolation):
        """Test updating a connection and its inverse."""
        setup = await self.create_test_setup(test_client)
        
        # Track entities
        test_data_isolation.track_entity(setup["entity1"]["id"])
        test_data_isolation.track_entity(setup["entity2"]["id"])
        
        # Create connection
        connection_data = {
            "from_entity_id": setup["entity1"]["id"],
            "to_entity_id": setup["entity2"]["id"],
            "unit_id": setup["unit"]["id"],
            "multiplier": 2.0
        }
        
        create_response = await test_client.post("/api/v1/connections/", json=connection_data)
        assert create_response.status_code == 201
        created_connection = create_response.json()
        test_data_isolation.track_connection(created_connection["id"])
        
        # Update connection
        update_data = {"multiplier": 4.0}
        response = await test_client.put(
            f"/api/v1/connections/{created_connection['id']}", 
            json=update_data
        )
        assert response.status_code == 200
        
        updated_connection = response.json()
        assert updated_connection["id"] == created_connection["id"]
        assert float(updated_connection["multiplier"]) == 4.0
        assert updated_connection["updated_at"] != created_connection["updated_at"]
        
        # Verify inverse connection was updated
        inverse_connection = await self.find_connection(
            test_client, 
            setup["entity2"]["id"], 
            setup["entity1"]["id"], 
            setup["unit"]["id"]
        )
        assert inverse_connection is not None
        test_data_isolation.track_connection(inverse_connection["id"])
        
        # 1/4 = 0.25, should round to 0.2 (banker's rounding)
        assert abs(float(inverse_connection["multiplier"]) - 0.2) < 0.01
        
        # Test updating non-existent connection
        response = await test_client.put(
            "/api/v1/connections/99999", 
            json={"multiplier": 5.0}
        )
        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_connection(self, test_client, test_data_isolation):
        """Test deleting a connection and its inverse."""
        setup = await self.create_test_setup(test_client)
        
        # Track entities
        test_data_isolation.track_entity(setup["entity1"]["id"])
        test_data_isolation.track_entity(setup["entity2"]["id"])
        
        # Create connection
        connection_data = {
            "from_entity_id": setup["entity1"]["id"],
            "to_entity_id": setup["entity2"]["id"],
            "unit_id": setup["unit"]["id"],
            "multiplier": 2.0
        }
        
        create_response = await test_client.post("/api/v1/connections/", json=connection_data)
        assert create_response.status_code == 201
        created_connection = create_response.json()
        
        # Verify both main and inverse connections exist
        await self.verify_connection_count(test_client, 2)
        
        # Delete connection
        response = await test_client.delete(f"/api/v1/connections/{created_connection['id']}")
        assert response.status_code == 200
        
        result = response.json()
        assert "message" in result
        assert "deleted successfully" in result["message"].lower()
        
        # Verify both connections are deleted
        await self.verify_connection_count(test_client, 0)
        
        # Verify main connection is gone
        response = await test_client.get(f"/api/v1/connections/{created_connection['id']}")
        assert response.status_code == 404
        
        # Verify inverse connection is also gone
        inverse_connection = await self.find_connection(
            test_client, 
            setup["entity2"]["id"], 
            setup["entity1"]["id"], 
            setup["unit"]["id"]
        )
        assert inverse_connection is None
        
        # Test deleting non-existent connection
        response = await test_client.delete("/api/v1/connections/99999")
        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_connection_with_different_units(self, test_client, test_data_isolation):
        """Test creating multiple connections between same entities with different units."""
        setup = await self.create_test_setup(test_client)
        
        # Track entities
        test_data_isolation.track_entity(setup["entity1"]["id"])
        test_data_isolation.track_entity(setup["entity2"]["id"])
        
        # Get multiple units
        units_response = await test_client.get("/api/v1/units/")
        units = units_response.json()
        assert len(units) >= 2  # Need at least 2 units for this test
        
        # Create connections with different units
        for i, unit in enumerate(units[:2]):  # Use first 2 units
            connection_data = {
                "from_entity_id": setup["entity1"]["id"],
                "to_entity_id": setup["entity2"]["id"],
                "unit_id": unit["id"],
                "multiplier": float(i + 1) * 1.5
            }
            
            response = await test_client.post("/api/v1/connections/", json=connection_data)
            assert response.status_code == 201
            connection = response.json()
            test_data_isolation.track_connection(connection["id"])
        
        # Verify connections exist for each unit
        for unit in units[:2]:
            main_conn = await self.find_connection(
                test_client, 
                setup["entity1"]["id"], 
                setup["entity2"]["id"], 
                unit["id"]
            )
            assert main_conn is not None
            
            inverse_conn = await self.find_connection(
                test_client, 
                setup["entity2"]["id"], 
                setup["entity1"]["id"], 
                unit["id"]
            )
            assert inverse_conn is not None
            test_data_isolation.track_connection(inverse_conn["id"])
        
        # Should have 4 connections total (2 main + 2 inverse)
        await self.verify_connection_count(test_client, 4)

    @pytest.mark.asyncio
    async def test_connection_edge_cases(self, test_client, test_data_isolation):
        """Test edge cases for connection operations."""
        setup = await self.create_test_setup(test_client)
        
        # Edge case 1: Very small multiplier (should be handled gracefully)
        entity1_name = create_test_entity_name("EdgeCaseSmall")
        entity2_name = create_test_entity_name("EdgeCaseSmallTarget")
        
        entity1_response = await test_client.post("/api/v1/entities/", json={"name": entity1_name})
        entity2_response = await test_client.post("/api/v1/entities/", json={"name": entity2_name})
        
        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]
        
        test_data_isolation.track_entity(entity1_id)
        test_data_isolation.track_entity(entity2_id)
        
        # Test very small multiplier
        connection_data = {
            "from_entity_id": entity1_id,
            "to_entity_id": entity2_id,
            "unit_id": setup["unit"]["id"],
            "multiplier": 0.1
        }
        
        response = await test_client.post("/api/v1/connections/", json=connection_data)
        assert response.status_code == 201
        connection = response.json()
        test_data_isolation.track_connection(connection["id"])
        
        # Verify inverse connection (1/0.1 = 10.0)
        inverse_connection = await self.find_connection(
            test_client, entity2_id, entity1_id, setup["unit"]["id"]
        )
        assert inverse_connection is not None
        test_data_isolation.track_connection(inverse_connection["id"])
        assert float(inverse_connection["multiplier"]) == 10.0
        
        # Edge case 2: Large multiplier
        entity3_name = create_test_entity_name("EdgeCaseLarge")
        entity4_name = create_test_entity_name("EdgeCaseLargeTarget")
        
        entity3_response = await test_client.post("/api/v1/entities/", json={"name": entity3_name})
        entity4_response = await test_client.post("/api/v1/entities/", json={"name": entity4_name})
        
        entity3_id = entity3_response.json()["id"]
        entity4_id = entity4_response.json()["id"]
        
        test_data_isolation.track_entity(entity3_id)
        test_data_isolation.track_entity(entity4_id)
        
        # Test large multiplier
        connection_data = {
            "from_entity_id": entity3_id,
            "to_entity_id": entity4_id,
            "unit_id": setup["unit"]["id"],
            "multiplier": 100.0
        }
        
        response = await test_client.post("/api/v1/connections/", json=connection_data)
        assert response.status_code == 201
        connection = response.json()
        test_data_isolation.track_connection(connection["id"])
        
        # Verify inverse connection (1/100 = 0.01, rounds to 0.0, enforced minimum 0.1)
        inverse_connection = await self.find_connection(
            test_client, entity4_id, entity3_id, setup["unit"]["id"]
        )
        assert inverse_connection is not None
        test_data_isolation.track_connection(inverse_connection["id"])
        assert float(inverse_connection["multiplier"]) == 0.1  # Minimum enforced

    @pytest.mark.asyncio
    async def test_cleanup_verification(self, test_client, cleanup_verification, test_data_isolation):
        """Test that cleanup verification works correctly."""
        setup = await self.create_test_setup(test_client)
        
        # Track entities
        test_data_isolation.track_entity(setup["entity1"]["id"])
        test_data_isolation.track_entity(setup["entity2"]["id"])
        
        # Create connection
        connection_data = {
            "from_entity_id": setup["entity1"]["id"],
            "to_entity_id": setup["entity2"]["id"],
            "unit_id": setup["unit"]["id"],
            "multiplier": 2.0
        }
        
        response = await test_client.post("/api/v1/connections/", json=connection_data)
        assert response.status_code == 201
        connection = response.json()
        test_data_isolation.track_connection(connection["id"])
        
        # Verify data exists
        cleanup_state = await cleanup_verification()
        assert cleanup_state["entities"] >= 2  # At least our test entities
        assert cleanup_state["connections"] >= 2  # At least our test connections
        assert not cleanup_state["clean"]  # Should not be clean
        
        # Note: Cleanup will happen automatically via fixtures
        # The next test will verify the database is clean