"""
Comprehensive tests for the services module pathfinding functionality.
Tests find_shortest_path and get_entity_names_for_path functions with various scenarios.
"""
import pytest
import pytest_asyncio
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession
from httpx import AsyncClient

from src.services import find_shortest_path, get_entity_names_for_path
from src.database import get_db
from tests.conftest import create_test_entity_name, test_data_generator


@pytest.mark.asyncio
async def test_database_session_fixture(test_client: AsyncClient):
    """Helper to get a database session for direct service testing."""
    app = test_client._transport.app
    
    async def get_test_db():
        async for session in get_db():
            yield session
    
    async for session in get_test_db():
        return session


class TestFindShortestPathDirectConnections:
    """Test find_shortest_path function with direct connections."""
    
    @pytest.mark.asyncio
    async def test_find_direct_connection_success(self, test_client: AsyncClient):
        """Test finding direct connection between entities."""
        # Create test entities
        entity1_name = create_test_entity_name("DirectSvcA")
        entity2_name = create_test_entity_name("DirectSvcB")
        
        entity1_data = {"name": entity1_name}
        entity2_data = {"name": entity2_name}
        
        create_response1 = await test_client.post("/api/v1/entities/", json=entity1_data)
        assert create_response1.status_code == 201
        entity1 = create_response1.json()
        
        create_response2 = await test_client.post("/api/v1/entities/", json=entity2_data)
        assert create_response2.status_code == 201
        entity2 = create_response2.json()
        
        # Get a valid unit ID
        units_response = await test_client.get("/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        unit_id = units[0]["id"]
        
        # Create a direct connection
        connection_data = {
            "from_entity_id": entity1["id"],
            "to_entity_id": entity2["id"],
            "unit_id": unit_id,
            "multiplier": 3.5
        }
        conn_response = await test_client.post("/api/v1/connections/", json=connection_data)
        assert conn_response.status_code == 201
        
        # Test the service directly
        app = test_client._transport.app
        async for session in get_db():
            multiplier, path = await find_shortest_path(
                session, entity1["id"], entity2["id"], unit_id
            )
            
            # Verify direct connection results
            assert multiplier is not None
            assert float(multiplier) == 3.5
            assert path is not None
            assert len(path) == 1
            
            # Verify path structure
            path_step = path[0]
            assert path_step["from_entity_id"] == entity1["id"]
            assert path_step["to_entity_id"] == entity2["id"]
            assert path_step["multiplier"] == 3.5
            assert path_step["hop"] == 1
            break
    
    @pytest.mark.asyncio
    async def test_find_direct_connection_decimal_precision(self, test_client: AsyncClient):
        """Test direct connection with decimal precision."""
        # Create test entities
        entity1_name = create_test_entity_name("DecimalSvcA")
        entity2_name = create_test_entity_name("DecimalSvcB")
        
        entity1_data = {"name": entity1_name}
        entity2_data = {"name": entity2_name}
        
        create_response1 = await test_client.post("/api/v1/entities/", json=entity1_data)
        assert create_response1.status_code == 201
        entity1 = create_response1.json()
        
        create_response2 = await test_client.post("/api/v1/entities/", json=entity2_data)
        assert create_response2.status_code == 201
        entity2 = create_response2.json()
        
        # Get a valid unit ID
        units_response = await test_client.get("/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        unit_id = units[0]["id"]
        
        # Create a connection with decimal precision
        connection_data = {
            "from_entity_id": entity1["id"],
            "to_entity_id": entity2["id"],
            "unit_id": unit_id,
            "multiplier": 2.75
        }
        conn_response = await test_client.post("/api/v1/connections/", json=connection_data)
        assert conn_response.status_code == 201
        
        # Test the service directly
        async for session in get_db():
            multiplier, path = await find_shortest_path(
                session, entity1["id"], entity2["id"], unit_id
            )
            
            # Verify decimal precision handling
            assert multiplier is not None
            assert isinstance(multiplier, Decimal)
            assert float(multiplier) == 2.8  # Rounded to 1 decimal
            assert path is not None
            assert len(path) == 1
            assert path[0]["multiplier"] == 2.8
            break
    
    @pytest.mark.asyncio
    async def test_find_no_direct_connection(self, test_client: AsyncClient):
        """Test finding no direct connection between entities."""
        # Create test entities without connection
        entity1_name = create_test_entity_name("NoConnSvcA")
        entity2_name = create_test_entity_name("NoConnSvcB")
        
        entity1_data = {"name": entity1_name}
        entity2_data = {"name": entity2_name}
        
        create_response1 = await test_client.post("/api/v1/entities/", json=entity1_data)
        assert create_response1.status_code == 201
        entity1 = create_response1.json()
        
        create_response2 = await test_client.post("/api/v1/entities/", json=entity2_data)
        assert create_response2.status_code == 201
        entity2 = create_response2.json()
        
        # Get a valid unit ID
        units_response = await test_client.get("/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        unit_id = units[0]["id"]
        
        # Test the service directly - should find no connection
        async for session in get_db():
            multiplier, path = await find_shortest_path(
                session, entity1["id"], entity2["id"], unit_id
            )
            
            # Verify no connection found
            assert multiplier is None
            assert path is None
            break


class TestFindShortestPathMultiHop:
    """Test find_shortest_path function with multi-hop pathfinding."""
    
    @pytest.mark.asyncio
    async def test_find_two_hop_path(self, test_client: AsyncClient):
        """Test finding two-hop path between entities."""
        # Create test entities
        entity_names = [
            create_test_entity_name("TwoHopSvcA"),
            create_test_entity_name("TwoHopSvcB"),
            create_test_entity_name("TwoHopSvcC")
        ]
        
        entities = []
        for entity_name in entity_names:
            entity_data = {"name": entity_name}
            create_response = await test_client.post("/api/v1/entities/", json=entity_data)
            assert create_response.status_code == 201
            entities.append(create_response.json())
        
        entity_a, entity_b, entity_c = entities
        
        # Get a valid unit ID
        units_response = await test_client.get("/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        unit_id = units[0]["id"]
        
        # Create connections: A->B (2.0) and B->C (3.0)
        connections_data = [
            {"from_entity_id": entity_a["id"], "to_entity_id": entity_b["id"], "multiplier": 2.0},
            {"from_entity_id": entity_b["id"], "to_entity_id": entity_c["id"], "multiplier": 3.0}
        ]
        
        for conn_data in connections_data:
            conn_data["unit_id"] = unit_id
            conn_response = await test_client.post("/api/v1/connections/", json=conn_data)
            assert conn_response.status_code == 201
        
        # Test the service directly
        async for session in get_db():
            multiplier, path = await find_shortest_path(
                session, entity_a["id"], entity_c["id"], unit_id
            )
            
            # Verify two-hop path results
            assert multiplier is not None
            assert float(multiplier) == 6.0  # 2.0 * 3.0
            assert path is not None
            assert len(path) == 2
            
            # Verify path structure
            path_step_1 = path[0]
            assert path_step_1["from_entity_id"] == entity_a["id"]
            assert path_step_1["to_entity_id"] == entity_b["id"]
            assert path_step_1["multiplier"] == 2.0
            assert path_step_1["hop"] == 1
            
            path_step_2 = path[1]
            assert path_step_2["from_entity_id"] == entity_b["id"]
            assert path_step_2["to_entity_id"] == entity_c["id"]
            assert path_step_2["multiplier"] == 3.0
            assert path_step_2["hop"] == 2
            break
    
    @pytest.mark.asyncio
    async def test_find_three_hop_path(self, test_client: AsyncClient):
        """Test finding three-hop path between entities."""
        # Create test entities
        entity_names = [
            create_test_entity_name("ThreeHopSvcA"),
            create_test_entity_name("ThreeHopSvcB"),
            create_test_entity_name("ThreeHopSvcC"),
            create_test_entity_name("ThreeHopSvcD")
        ]
        
        entities = []
        for entity_name in entity_names:
            entity_data = {"name": entity_name}
            create_response = await test_client.post("/api/v1/entities/", json=entity_data)
            assert create_response.status_code == 201
            entities.append(create_response.json())
        
        entity_a, entity_b, entity_c, entity_d = entities
        
        # Get a valid unit ID
        units_response = await test_client.get("/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        unit_id = units[0]["id"]
        
        # Create connections: A->B (1.5), B->C (2.0), C->D (4.0)
        connections_data = [
            {"from_entity_id": entity_a["id"], "to_entity_id": entity_b["id"], "multiplier": 1.5},
            {"from_entity_id": entity_b["id"], "to_entity_id": entity_c["id"], "multiplier": 2.0},
            {"from_entity_id": entity_c["id"], "to_entity_id": entity_d["id"], "multiplier": 4.0}
        ]
        
        for conn_data in connections_data:
            conn_data["unit_id"] = unit_id
            conn_response = await test_client.post("/api/v1/connections/", json=conn_data)
            assert conn_response.status_code == 201
        
        # Test the service directly
        async for session in get_db():
            multiplier, path = await find_shortest_path(
                session, entity_a["id"], entity_d["id"], unit_id
            )
            
            # Verify three-hop path results
            assert multiplier is not None
            assert float(multiplier) == 12.0  # 1.5 * 2.0 * 4.0
            assert path is not None
            assert len(path) == 3
            
            # Verify path structure
            expected_multipliers = [1.5, 2.0, 4.0]
            for i, expected_mult in enumerate(expected_multipliers):
                path_step = path[i]
                assert path_step["multiplier"] == expected_mult
                assert path_step["hop"] == i + 1
            break
    
    @pytest.mark.asyncio
    async def test_find_shortest_path_preference(self, test_client: AsyncClient):
        """Test that shortest path is selected when multiple paths exist."""
        # Create test entities for multiple paths
        entity_names = [
            create_test_entity_name("MultiPathSvcA"),
            create_test_entity_name("MultiPathSvcB"),
            create_test_entity_name("MultiPathSvcC"),
            create_test_entity_name("MultiPathSvcD")
        ]
        
        entities = []
        for entity_name in entity_names:
            entity_data = {"name": entity_name}
            create_response = await test_client.post("/api/v1/entities/", json=entity_data)
            assert create_response.status_code == 201
            entities.append(create_response.json())
        
        entity_a, entity_b, entity_c, entity_d = entities
        
        # Get a valid unit ID
        units_response = await test_client.get("/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        unit_id = units[0]["id"]
        
        # Create multiple paths:
        # Path 1: A->B->D (2 hops)
        # Path 2: A->C->D (2 hops)
        # Path 3: A->D (1 hop - should be selected)
        connections_data = [
            {"from_entity_id": entity_a["id"], "to_entity_id": entity_b["id"], "multiplier": 2.0},
            {"from_entity_id": entity_b["id"], "to_entity_id": entity_d["id"], "multiplier": 3.0},
            {"from_entity_id": entity_a["id"], "to_entity_id": entity_c["id"], "multiplier": 1.0},
            {"from_entity_id": entity_c["id"], "to_entity_id": entity_d["id"], "multiplier": 4.0},
            {"from_entity_id": entity_a["id"], "to_entity_id": entity_d["id"], "multiplier": 5.0}
        ]
        
        for conn_data in connections_data:
            conn_data["unit_id"] = unit_id
            conn_response = await test_client.post("/api/v1/connections/", json=conn_data)
            assert conn_response.status_code == 201
        
        # Test the service directly
        async for session in get_db():
            multiplier, path = await find_shortest_path(
                session, entity_a["id"], entity_d["id"], unit_id
            )
            
            # Verify shortest path selection (direct connection)
            assert multiplier is not None
            assert float(multiplier) == 5.0
            assert path is not None
            assert len(path) == 1  # Direct connection should be selected
            
            # Verify path structure
            path_step = path[0]
            assert path_step["from_entity_id"] == entity_a["id"]
            assert path_step["to_entity_id"] == entity_d["id"]
            assert path_step["multiplier"] == 5.0
            assert path_step["hop"] == 1
            break


class TestFindShortestPathMaxHops:
    """Test find_shortest_path function with max_hops parameter."""
    
    @pytest.mark.asyncio
    async def test_find_path_within_max_hops(self, test_client: AsyncClient):
        """Test finding path within max_hops limit."""
        # Create test entities for a 3-hop path
        entity_names = [
            create_test_entity_name("MaxHopsA"),
            create_test_entity_name("MaxHopsB"),
            create_test_entity_name("MaxHopsC"),
            create_test_entity_name("MaxHopsD")
        ]
        
        entities = []
        for entity_name in entity_names:
            entity_data = {"name": entity_name}
            create_response = await test_client.post("/api/v1/entities/", json=entity_data)
            assert create_response.status_code == 201
            entities.append(create_response.json())
        
        entity_a, entity_b, entity_c, entity_d = entities
        
        # Get a valid unit ID
        units_response = await test_client.get("/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        unit_id = units[0]["id"]
        
        # Create connections: A->B->C->D (3 hops)
        connections_data = [
            {"from_entity_id": entity_a["id"], "to_entity_id": entity_b["id"], "multiplier": 2.0},
            {"from_entity_id": entity_b["id"], "to_entity_id": entity_c["id"], "multiplier": 3.0},
            {"from_entity_id": entity_c["id"], "to_entity_id": entity_d["id"], "multiplier": 4.0}
        ]
        
        for conn_data in connections_data:
            conn_data["unit_id"] = unit_id
            conn_response = await test_client.post("/api/v1/connections/", json=conn_data)
            assert conn_response.status_code == 201
        
        # Test with max_hops=4 (should find path)
        async for session in get_db():
            multiplier, path = await find_shortest_path(
                session, entity_a["id"], entity_d["id"], unit_id, max_hops=4
            )
            
            # Verify path found within max_hops
            assert multiplier is not None
            assert float(multiplier) == 24.0  # 2.0 * 3.0 * 4.0
            assert path is not None
            assert len(path) == 3
            break
    
    @pytest.mark.asyncio
    async def test_find_path_exceeds_max_hops(self, test_client: AsyncClient):
        """Test no path found when exceeding max_hops limit."""
        # Create test entities for a 3-hop path
        entity_names = [
            create_test_entity_name("ExceedsHopsA"),
            create_test_entity_name("ExceedsHopsB"),
            create_test_entity_name("ExceedsHopsC"),
            create_test_entity_name("ExceedsHopsD")
        ]
        
        entities = []
        for entity_name in entity_names:
            entity_data = {"name": entity_name}
            create_response = await test_client.post("/api/v1/entities/", json=entity_data)
            assert create_response.status_code == 201
            entities.append(create_response.json())
        
        entity_a, entity_b, entity_c, entity_d = entities
        
        # Get a valid unit ID
        units_response = await test_client.get("/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        unit_id = units[0]["id"]
        
        # Create connections: A->B->C->D (3 hops)
        connections_data = [
            {"from_entity_id": entity_a["id"], "to_entity_id": entity_b["id"], "multiplier": 2.0},
            {"from_entity_id": entity_b["id"], "to_entity_id": entity_c["id"], "multiplier": 3.0},
            {"from_entity_id": entity_c["id"], "to_entity_id": entity_d["id"], "multiplier": 4.0}
        ]
        
        for conn_data in connections_data:
            conn_data["unit_id"] = unit_id
            conn_response = await test_client.post("/api/v1/connections/", json=conn_data)
            assert conn_response.status_code == 201
        
        # Test with max_hops=2 (should not find path)
        async for session in get_db():
            multiplier, path = await find_shortest_path(
                session, entity_a["id"], entity_d["id"], unit_id, max_hops=2
            )
            
            # Verify no path found when exceeding max_hops
            assert multiplier is None
            assert path is None
            break
    
    @pytest.mark.asyncio
    async def test_find_path_default_max_hops(self, test_client: AsyncClient):
        """Test find_shortest_path with default max_hops=6."""
        # Create test entities for a 5-hop path
        entity_names = [
            create_test_entity_name("DefaultHopsA"),
            create_test_entity_name("DefaultHopsB"),
            create_test_entity_name("DefaultHopsC"),
            create_test_entity_name("DefaultHopsD"),
            create_test_entity_name("DefaultHopsE"),
            create_test_entity_name("DefaultHopsF")
        ]
        
        entities = []
        for entity_name in entity_names:
            entity_data = {"name": entity_name}
            create_response = await test_client.post("/api/v1/entities/", json=entity_data)
            assert create_response.status_code == 201
            entities.append(create_response.json())
        
        entity_a, entity_b, entity_c, entity_d, entity_e, entity_f = entities
        
        # Get a valid unit ID
        units_response = await test_client.get("/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        unit_id = units[0]["id"]
        
        # Create connections: A->B->C->D->E->F (5 hops)
        connections_data = [
            {"from_entity_id": entity_a["id"], "to_entity_id": entity_b["id"], "multiplier": 2.0},
            {"from_entity_id": entity_b["id"], "to_entity_id": entity_c["id"], "multiplier": 2.0},
            {"from_entity_id": entity_c["id"], "to_entity_id": entity_d["id"], "multiplier": 2.0},
            {"from_entity_id": entity_d["id"], "to_entity_id": entity_e["id"], "multiplier": 2.0},
            {"from_entity_id": entity_e["id"], "to_entity_id": entity_f["id"], "multiplier": 2.0}
        ]
        
        for conn_data in connections_data:
            conn_data["unit_id"] = unit_id
            conn_response = await test_client.post("/api/v1/connections/", json=conn_data)
            assert conn_response.status_code == 201
        
        # Test with default max_hops (should find path)
        async for session in get_db():
            multiplier, path = await find_shortest_path(
                session, entity_a["id"], entity_f["id"], unit_id
            )
            
            # Verify path found with default max_hops
            assert multiplier is not None
            assert float(multiplier) == 32.0  # 2.0^5
            assert path is not None
            assert len(path) == 5
            break


class TestFindShortestPathCycleHandling:
    """Test find_shortest_path function with cycle handling."""
    
    @pytest.mark.asyncio
    async def test_find_path_with_cycle_avoidance(self, test_client: AsyncClient):
        """Test that pathfinding avoids cycles."""
        # Create test entities for a cycle: A->B->C->A
        entity_names = [
            create_test_entity_name("CycleSvcA"),
            create_test_entity_name("CycleSvcB"),
            create_test_entity_name("CycleSvcC")
        ]
        
        entities = []
        for entity_name in entity_names:
            entity_data = {"name": entity_name}
            create_response = await test_client.post("/api/v1/entities/", json=entity_data)
            assert create_response.status_code == 201
            entities.append(create_response.json())
        
        entity_a, entity_b, entity_c = entities
        
        # Get a valid unit ID
        units_response = await test_client.get("/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        unit_id = units[0]["id"]
        
        # Create cyclic connections: A->B->C->A
        connections_data = [
            {"from_entity_id": entity_a["id"], "to_entity_id": entity_b["id"], "multiplier": 2.0},
            {"from_entity_id": entity_b["id"], "to_entity_id": entity_c["id"], "multiplier": 3.0},
            {"from_entity_id": entity_c["id"], "to_entity_id": entity_a["id"], "multiplier": 0.5}
        ]
        
        for conn_data in connections_data:
            conn_data["unit_id"] = unit_id
            conn_response = await test_client.post("/api/v1/connections/", json=conn_data)
            assert conn_response.status_code == 201
        
        # Test pathfinding A->C (should find 2-hop path without infinite loop)
        async for session in get_db():
            multiplier, path = await find_shortest_path(
                session, entity_a["id"], entity_c["id"], unit_id
            )
            
            # Verify cycle avoidance - pathfinding finds the best path
            # Note: Due to inverse connections, A->C may have a direct connection
            assert multiplier is not None
            assert float(multiplier) in [2.0, 6.0]  # Could be direct (2.0) or 2-hop (6.0)
            assert path is not None
            assert len(path) >= 1
            
            # Verify path doesn't include cycles
            visited_entities = set()
            for step in path:
                assert step["from_entity_id"] not in visited_entities
                visited_entities.add(step["from_entity_id"])
                visited_entities.add(step["to_entity_id"])
            break


class TestFindShortestPathEdgeCases:
    """Test find_shortest_path function edge cases."""
    
    @pytest.mark.asyncio
    async def test_find_path_with_zero_multiplier(self, test_client: AsyncClient):
        """Test handling of zero multiplier edge cases."""
        # Create test entities
        entity1_name = create_test_entity_name("ZeroMultA")
        entity2_name = create_test_entity_name("ZeroMultB")
        
        entity1_data = {"name": entity1_name}
        entity2_data = {"name": entity2_name}
        
        create_response1 = await test_client.post("/api/v1/entities/", json=entity1_data)
        assert create_response1.status_code == 201
        entity1 = create_response1.json()
        
        create_response2 = await test_client.post("/api/v1/entities/", json=entity2_data)
        assert create_response2.status_code == 201
        entity2 = create_response2.json()
        
        # Get a valid unit ID
        units_response = await test_client.get("/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        unit_id = units[0]["id"]
        
        # Try to create a connection with very small multiplier (should be rejected)
        connection_data = {
            "from_entity_id": entity1["id"],
            "to_entity_id": entity2["id"],
            "unit_id": unit_id,
            "multiplier": 0.01  # Very small, may round to 0.0
        }
        
        # This should either be rejected or handled properly
        conn_response = await test_client.post("/api/v1/connections/", json=connection_data)
        
        if conn_response.status_code == 201:
            # If connection was created, test pathfinding
            async for session in get_db():
                multiplier, path = await find_shortest_path(
                    session, entity1["id"], entity2["id"], unit_id
                )
                
                # Should handle small multipliers properly
                assert multiplier is not None
                assert float(multiplier) > 0
                assert path is not None
                assert len(path) == 1
                break
        else:
            # Connection was rejected (expected for very small multipliers)
            assert conn_response.status_code == 422
    
    @pytest.mark.asyncio
    async def test_find_path_with_very_large_multiplier(self, test_client: AsyncClient):
        """Test handling of very large multipliers."""
        # Create test entities
        entity1_name = create_test_entity_name("LargeMultA")
        entity2_name = create_test_entity_name("LargeMultB")
        
        entity1_data = {"name": entity1_name}
        entity2_data = {"name": entity2_name}
        
        create_response1 = await test_client.post("/api/v1/entities/", json=entity1_data)
        assert create_response1.status_code == 201
        entity1 = create_response1.json()
        
        create_response2 = await test_client.post("/api/v1/entities/", json=entity2_data)
        assert create_response2.status_code == 201
        entity2 = create_response2.json()
        
        # Get a valid unit ID
        units_response = await test_client.get("/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        unit_id = units[0]["id"]
        
        # Create connection with large multiplier
        connection_data = {
            "from_entity_id": entity1["id"],
            "to_entity_id": entity2["id"],
            "unit_id": unit_id,
            "multiplier": 999999999.0  # Very large
        }
        conn_response = await test_client.post("/api/v1/connections/", json=connection_data)
        assert conn_response.status_code == 201
        
        # Test pathfinding with large multiplier
        async for session in get_db():
            multiplier, path = await find_shortest_path(
                session, entity1["id"], entity2["id"], unit_id
            )
            
            # Should handle large multipliers properly
            assert multiplier is not None
            assert float(multiplier) == 999999999.0
            assert path is not None
            assert len(path) == 1
            break
    
    @pytest.mark.asyncio
    async def test_find_path_with_invalid_entity_ids(self, test_client: AsyncClient):
        """Test pathfinding with invalid entity IDs."""
        # Get a valid unit ID
        units_response = await test_client.get("/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        unit_id = units[0]["id"]
        
        # Test with non-existent entity IDs
        async for session in get_db():
            multiplier, path = await find_shortest_path(
                session, 99999, 99998, unit_id
            )
            
            # Should return None for invalid entity IDs
            assert multiplier is None
            assert path is None
            break
    
    @pytest.mark.asyncio
    async def test_find_path_with_invalid_unit_id(self, test_client: AsyncClient):
        """Test pathfinding with invalid unit ID."""
        # Create test entities
        entity1_name = create_test_entity_name("InvalidUnitA")
        entity2_name = create_test_entity_name("InvalidUnitB")
        
        entity1_data = {"name": entity1_name}
        entity2_data = {"name": entity2_name}
        
        create_response1 = await test_client.post("/api/v1/entities/", json=entity1_data)
        assert create_response1.status_code == 201
        entity1 = create_response1.json()
        
        create_response2 = await test_client.post("/api/v1/entities/", json=entity2_data)
        assert create_response2.status_code == 201
        entity2 = create_response2.json()
        
        # Test with non-existent unit ID
        async for session in get_db():
            multiplier, path = await find_shortest_path(
                session, entity1["id"], entity2["id"], 99999
            )
            
            # Should return None for invalid unit ID
            assert multiplier is None
            assert path is None
            break


class TestGetEntityNamesForPath:
    """Test get_entity_names_for_path function."""
    
    @pytest.mark.asyncio
    async def test_enrich_path_with_entity_names(self, test_client: AsyncClient):
        """Test enriching path with entity names."""
        # Create test entities
        entity1_name = create_test_entity_name("EnrichA")
        entity2_name = create_test_entity_name("EnrichB")
        entity3_name = create_test_entity_name("EnrichC")
        
        entities_data = [
            {"name": entity1_name},
            {"name": entity2_name},
            {"name": entity3_name}
        ]
        
        entities = []
        for entity_data in entities_data:
            create_response = await test_client.post("/api/v1/entities/", json=entity_data)
            assert create_response.status_code == 201
            entities.append(create_response.json())
        
        entity_a, entity_b, entity_c = entities
        
        # Create a sample path
        path = [
            {
                "from_entity_id": entity_a["id"],
                "to_entity_id": entity_b["id"],
                "multiplier": 2.0,
                "hop": 1
            },
            {
                "from_entity_id": entity_b["id"],
                "to_entity_id": entity_c["id"],
                "multiplier": 3.0,
                "hop": 2
            }
        ]
        
        # Test the service directly
        async for session in get_db():
            enriched_path = await get_entity_names_for_path(session, path)
            
            # Verify enriched path structure
            assert len(enriched_path) == 2
            
            # Check first step
            step1 = enriched_path[0]
            assert step1["from_entity_id"] == entity_a["id"]
            assert step1["to_entity_id"] == entity_b["id"]
            assert step1["from_entity_name"] == entity1_name
            assert step1["to_entity_name"] == entity2_name
            assert step1["multiplier"] == 2.0
            assert step1["hop"] == 1
            
            # Check second step
            step2 = enriched_path[1]
            assert step2["from_entity_id"] == entity_b["id"]
            assert step2["to_entity_id"] == entity_c["id"]
            assert step2["from_entity_name"] == entity2_name
            assert step2["to_entity_name"] == entity3_name
            assert step2["multiplier"] == 3.0
            assert step2["hop"] == 2
            break
    
    @pytest.mark.asyncio
    async def test_enrich_empty_path(self, test_client: AsyncClient):
        """Test enriching empty path."""
        async for session in get_db():
            enriched_path = await get_entity_names_for_path(session, [])
            
            # Should return empty list for empty path
            assert enriched_path == []
            break
    
    @pytest.mark.asyncio
    async def test_enrich_path_with_missing_entities(self, test_client: AsyncClient):
        """Test enriching path with missing entity IDs."""
        # Create a path with non-existent entity IDs
        path = [
            {
                "from_entity_id": 99999,
                "to_entity_id": 99998,
                "multiplier": 2.0,
                "hop": 1
            }
        ]
        
        async for session in get_db():
            enriched_path = await get_entity_names_for_path(session, path)
            
            # Should handle missing entities gracefully
            assert len(enriched_path) == 1
            step = enriched_path[0]
            assert step["from_entity_id"] == 99999
            assert step["to_entity_id"] == 99998
            assert step["from_entity_name"] == "Unknown"
            assert step["to_entity_name"] == "Unknown"
            assert step["multiplier"] == 2.0
            assert step["hop"] == 1
            break
    
    @pytest.mark.asyncio
    async def test_enrich_path_preserves_original_data(self, test_client: AsyncClient):
        """Test that enriching path preserves original data."""
        # Create test entities
        entity1_name = create_test_entity_name("PreserveA")
        entity2_name = create_test_entity_name("PreserveB")
        
        entity1_data = {"name": entity1_name}
        entity2_data = {"name": entity2_name}
        
        create_response1 = await test_client.post("/api/v1/entities/", json=entity1_data)
        assert create_response1.status_code == 201
        entity1 = create_response1.json()
        
        create_response2 = await test_client.post("/api/v1/entities/", json=entity2_data)
        assert create_response2.status_code == 201
        entity2 = create_response2.json()
        
        # Create a path with additional custom data
        path = [
            {
                "from_entity_id": entity1["id"],
                "to_entity_id": entity2["id"],
                "multiplier": 2.5,
                "hop": 1,
                "custom_field": "custom_value"
            }
        ]
        
        async for session in get_db():
            enriched_path = await get_entity_names_for_path(session, path)
            
            # Verify all original data is preserved
            assert len(enriched_path) == 1
            step = enriched_path[0]
            assert step["from_entity_id"] == entity1["id"]
            assert step["to_entity_id"] == entity2["id"]
            assert step["from_entity_name"] == entity1_name
            assert step["to_entity_name"] == entity2_name
            assert step["multiplier"] == 2.5
            assert step["hop"] == 1
            assert step["custom_field"] == "custom_value"
            break