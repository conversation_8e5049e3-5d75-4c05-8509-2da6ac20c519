"""
Comprehensive test suite for edge cases and error scenarios.
Tests boundary conditions, race conditions, and unusual scenarios.
"""
import pytest
import asyncio
from sqlalchemy import select

from src.models import Entity, Connection, Unit


class TestEdgeCases:
    """Test edge cases and error scenarios comprehensively."""
    
    @pytest.mark.asyncio
    async def test_concurrent_entity_creation_same_name(self, test_client):
        """Test race condition when creating entities with same name concurrently."""
        entity_name = "Concurrent Test Entity"
        
        async def create_entity():
            return await test_client.post(
                "/api/v1/entities/",
                json={"name": entity_name}
            )
        
        # Launch multiple concurrent requests
        tasks = [create_entity() for _ in range(5)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Count successes and failures
        successes = [r for r in results if not isinstance(r, Exception) and r.status_code == 201]
        failures = [r for r in results if not isinstance(r, Exception) and r.status_code == 400]
        
        # Exactly one should succeed
        assert len(successes) == 1
        assert len(failures) == 4
        
        # Verify only one entity exists
        all_entities = await test_client.get("/api/v1/entities/")
        matching = [e for e in all_entities.json() if e["name"] == entity_name]
        assert len(matching) == 1
    
    @pytest.mark.asyncio
    async def test_concurrent_connection_creation(self, test_client):
        """Test race condition when creating duplicate connections concurrently."""
        # Create entities
        entity1 = await test_client.post(
            "/api/v1/entities/",
            json={"name": "Concurrent Ent A"}
        )
        entity2 = await test_client.post(
            "/api/v1/entities/",
            json={"name": "Concurrent Ent B"}
        )
        
        units = await test_client.get("/api/v1/units/")
        unit_id = units.json()[0]["id"]
        
        assert entity1.status_code == 201, f"Entity 1 creation failed: {entity1.text}"
        assert entity2.status_code == 201, f"Entity 2 creation failed: {entity2.text}"
        
        entity1_id = entity1.json()["id"]
        entity2_id = entity2.json()["id"]
        
        async def create_connection():
            return await test_client.post(
                "/api/v1/connections/",
                json={
                    "from_entity_id": entity1_id,
                    "to_entity_id": entity2_id,
                    "unit_id": unit_id,
                    "multiplier": 2.5
                }
            )
        
        # Launch multiple concurrent requests
        tasks = [create_connection() for _ in range(5)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Count successes, failures, and transaction errors
        successes = [r for r in results if not isinstance(r, Exception) and r.status_code == 201]
        failures = [r for r in results if not isinstance(r, Exception) and r.status_code == 400]
        transaction_errors = [r for r in results if isinstance(r, Exception)]
        
        # Debug: Print status codes
        status_codes = [r.status_code if not isinstance(r, Exception) else type(r).__name__ for r in results]
        
        # With the new update behavior, all concurrent requests can succeed (they update the same connection)
        # We should see all successes (201) as they all update the same connection
        assert len(successes) >= 1, f"Expected at least 1 success, got {len(successes)}. Status codes: {status_codes}"
        # No more requirement for failures since duplicates now update instead of failing
    
    @pytest.mark.asyncio
    async def test_delete_entity_in_use(self, test_client):
        """Test deleting entity that's part of active connections."""
        # Create entities
        entity1 = await test_client.post(
            "/api/v1/entities/",
            json={"name": "Delete Test Source"}
        )
        entity2 = await test_client.post(
            "/api/v1/entities/",
            json={"name": "Delete Test Target"}
        )
        entity3 = await test_client.post(
            "/api/v1/entities/",
            json={"name": "Delete Test Third"}
        )
        
        units = await test_client.get("/api/v1/units/")
        unit_id = units.json()[0]["id"]
        
        # Create connections: 1->2, 2->3
        await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entity1.json()["id"],
                "to_entity_id": entity2.json()["id"],
                "unit_id": unit_id,
                "multiplier": 2.0
            }
        )
        
        await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entity2.json()["id"],
                "to_entity_id": entity3.json()["id"],
                "unit_id": unit_id,
                "multiplier": 3.0
            }
        )
        
        # Try to delete middle entity
        delete_response = await test_client.delete(f"/api/v1/entities/{entity2.json()['id']}")
        
        # Behavior depends on cascade settings
        # Either deletion succeeds (cascade) or fails (protect)
        assert delete_response.status_code in [200, 400]
        
        if delete_response.status_code == 200:
            # If cascaded, connections should be gone
            # Check with pagination to ensure we don't miss connections beyond the first 100
            entity2_id = entity2.json()["id"]
            remaining = []
            
            for skip_offset in range(0, 500, 100):  # Check up to 500 connections
                connections = await test_client.get(
                    "/api/v1/connections/",
                    params={"skip": skip_offset, "limit": 100}
                )
                conn_batch = connections.json()
                
                # Find any connections involving the deleted entity
                batch_remaining = [c for c in conn_batch 
                                 if entity2_id in [c["from_entity_id"], c["to_entity_id"]]]
                remaining.extend(batch_remaining)
                
                # If we got less than 100 connections, we've reached the end
                if len(conn_batch) < 100:
                    break
            
            assert len(remaining) == 0
    
    @pytest.mark.asyncio
    async def test_extreme_multiplier_values(self, test_client):
        """Test connections with extreme multiplier values."""
        from .conftest import generate_valid_entity_suffix
        
        suffix = generate_valid_entity_suffix()
        
        # Create entities
        entity1 = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Extreme Val Ent A {suffix}"}
        )
        entity2 = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Extreme Val Ent B {suffix}"}
        )
        
        units = await test_client.get("/api/v1/units/")
        unit_id = units.json()[0]["id"]
        
        entity1_id = entity1.json()["id"]
        entity2_id = entity2.json()["id"]
        
        # Test very large multiplier - should fail validation
        response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entity1_id,
                "to_entity_id": entity2_id,
                "unit_id": unit_id,
                "multiplier": 1e15  # 1 quadrillion - exceeds database constraint
            }
        )
        assert response.status_code == 422
        error_details = response.json()["detail"]
        assert any("less than or equal" in str(error).lower() for error in error_details)
        
        # Test very small positive multiplier
        entity3 = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Extreme Val Ent C {suffix}"}
        )
        entity4 = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Extreme Val Ent D {suffix}"}
        )
        
        response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entity3.json()["id"],
                "to_entity_id": entity4.json()["id"],
                "unit_id": unit_id,
                "multiplier": 1e-10  # Very small
            }
        )
        # Should round to 0.0 and possibly fail
        assert response.status_code in [201, 422]
    
    @pytest.mark.asyncio
    async def test_special_characters_in_entity_names(self, test_client):
        """Test entity names with various special characters and edge cases."""
        import time
        # Create a letters-only base suffix to avoid validation issues  
        timestamp = str(int(time.time()))[-6:]  # Get last 6 digits of timestamp
        base_suffix = ''.join([chr(ord('a') + int(c)) for c in timestamp])
        
        test_cases = [
            ("Entity123", 422),  # Numbers not allowed
            ("Entity 123", 422),  # Numbers with spaces not allowed
            (f"Valid Entity {base_suffix}a", 201),  # Letters with spaces allowed
            (f"ENTITY {base_suffix}b", 201),  # All caps
            (f"entity {base_suffix}c", 201),  # All lowercase
            (f"EnTiTy {base_suffix}d", 201),  # Mixed case
            (f"A {base_suffix}e", 201),  # Single character
            ("1", 422),  # Single digit not allowed
            ("Entity_123", 422),  # Underscore not allowed
            ("Entity-123", 422),  # Hyphen not allowed
            ("Entity.123", 422),  # Period not allowed
            ("Entity@123", 422),  # @ symbol not allowed
            ("Entity#123", 422),  # # symbol not allowed
            ("Entity$123", 422),  # $ symbol not allowed
            ("Entity%123", 422),  # % symbol not allowed
            ("Entity&123", 422),  # & symbol not allowed
            ("Entity*123", 422),  # * symbol not allowed
            ("Entity+123", 422),  # + symbol not allowed
            ("Entity=123", 422),  # = symbol not allowed
            ("Entity!123", 422),  # ! symbol not allowed
            ("Entity?123", 422),  # ? symbol not allowed
            ("Entity/123", 422),  # / symbol not allowed
            ("Entity\\123", 422),  # \ symbol not allowed
            ("Entity'123", 422),  # ' symbol not allowed
            ('Entity"123', 422),  # " symbol not allowed
            ("Entity(123)", 422),  # Parentheses not allowed
            ("Entity[123]", 422),  # Brackets not allowed
            ("Entity{123}", 422),  # Braces not allowed
            ("Entity<123>", 422),  # Angle brackets not allowed
            ("Entity|123", 422),  # Pipe not allowed
            ("Entity~123", 422),  # Tilde not allowed
            ("Entity`123", 422),  # Backtick not allowed
            ("Entity^123", 422),  # Caret not allowed
            ("Entity;123", 422),  # Semicolon not allowed
            ("Entity:123", 422),  # Colon not allowed
            ("Entity,123", 422),  # Comma not allowed
            (f"   Leading Spaces {base_suffix}h", 201),  # Leading spaces (stripped and allowed)
            (f"Trailing Spaces   {base_suffix}i", 201),  # Trailing spaces (stripped and allowed)
            (f"Multiple   Spaces {base_suffix}f", 201),  # Multiple spaces in middle (might be allowed)
            ("", 422),  # Empty string
            ("   ", 422),  # Only spaces
            ("\t\n", 422),  # Only whitespace
            ("A" * 91 + base_suffix + "g", 201),  # Max length (91 + 6 + 1 + 1 = 99)
            ("A" * 101, 422),  # Over max length
        ]
        
        for name, expected_status in test_cases:
            response = await test_client.post(
                "/api/v1/entities/",
                json={"name": name}
            )
            assert response.status_code == expected_status, f"Failed for name '{name}': expected {expected_status}, got {response.status_code}"
    
    @pytest.mark.asyncio
    async def test_update_entity_concurrent_requests(self, test_client):
        """Test concurrent updates to the same entity."""
        # Create entity
        entity = await test_client.post(
            "/api/v1/entities/",
            json={"name": "Concurrent Update"}
        )
        entity_id = entity.json()["id"]
        
        async def update_entity(suffix: int):
            # Convert numeric suffix to letter-based name to comply with validation
            letter_names = ["Alpha", "Beta", "Gamma", "Delta", "Epsilon"]
            return await test_client.put(
                f"/api/v1/entities/{entity_id}",
                json={"name": f"Updated Entity {letter_names[suffix]}"}
            )
        
        # Launch concurrent updates
        tasks = [update_entity(i) for i in range(5)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # All should succeed (last write wins)
        for result in results:
            if not isinstance(result, Exception):
                assert result.status_code == 200
        
        # Verify final state
        final_entity = await test_client.get(f"/api/v1/entities/{entity_id}")
        assert "Updated Entity" in final_entity.json()["name"]
    
    @pytest.mark.asyncio
    async def test_path_finding_with_very_small_multipliers(self, test_client):
        """Test path-finding with very small multipliers to verify precision."""
        # Create entities
        from .conftest import generate_valid_entity_suffix
        suffix = generate_valid_entity_suffix()
        small_multi_names = ["Alpha", "Beta", "Gamma"]
        entities = []
        for name in small_multi_names:
            entity = await test_client.post(
                "/api/v1/entities/",
                json={"name": f"Small Multi Ent {name} {suffix}"}
            )
            entities.append(entity.json()["id"])
        
        units = await test_client.get("/api/v1/units/")
        unit_id = units.json()[0]["id"]
        
        # Create connections with small multipliers
        await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entities[0],
                "to_entity_id": entities[1],
                "unit_id": unit_id,
                "multiplier": 0.1  # Small but valid
            }
        )
        
        await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entities[1],
                "to_entity_id": entities[2],
                "unit_id": unit_id,
                "multiplier": 0.1  # Second connection: 0.1 * 0.1 = 0.01
            }
        )
        
        # Try to find path
        response = await test_client.get(
            "/api/v1/compare/",
            params={
                "from": entities[0],
                "to": entities[2],
                "unit": unit_id
            }
        )
        
        assert response.status_code == 200
        # 0.1 * 0.1 = 0.01 mathematically
        # The application correctly returns 0.01, not 0.0
        assert float(response.json()["multiplier"]) == 0.01
    
    @pytest.mark.asyncio
    async def test_database_transaction_rollback(self, test_client):
        """Test that failed operations properly rollback."""
        # Create entity
        entity = await test_client.post(
            "/api/v1/entities/",
            json={"name": "Transaction Test"}
        )
        entity_id = entity.json()["id"]
        
        # Try to update with invalid data (should fail and rollback)
        response = await test_client.put(
            f"/api/v1/entities/{entity_id}",
            json={"name": "@Invalid Name!"}
        )
        assert response.status_code == 422
        
        # Verify entity is unchanged
        check_response = await test_client.get(f"/api/v1/entities/{entity_id}")
        assert check_response.json()["name"] == "Transaction Test"
    
    @pytest.mark.asyncio
    async def test_unit_operations(self, test_client):
        """Test unit-related edge cases."""
        # Get all units
        units_response = await test_client.get("/api/v1/units/")
        units = units_response.json()
        assert len(units) >= 5  # Should have at least 5 default units
        
        # Test getting specific unit
        unit_id = units[0]["id"]
        unit_response = await test_client.get(f"/api/v1/units/{unit_id}")
        assert unit_response.status_code == 200
        assert unit_response.json()["id"] == unit_id
        
        # Test getting non-existent unit
        response = await test_client.get("/api/v1/units/99999")
        assert response.status_code == 404
        
        # Test creating unit - endpoint is implemented and should work
        from .conftest import generate_valid_entity_suffix
        suffix = generate_valid_entity_suffix()
        unit_name = f"TestUnit {suffix}"
        unit_symbol = f"TU{suffix[:2].upper()}"
        
        response = await test_client.post(
            "/api/v1/units/",
            json={"name": unit_name, "symbol": unit_symbol}
        )
        # Endpoint should successfully create the unit
        # However, if units are read-only in this implementation, accept that
        if response.status_code == 405:
            # Method not allowed - units might be read-only
            # This is a valid implementation choice
            pass
        elif response.status_code in [200, 201]:
            # Unit was created successfully (201) or already exists (200)
            created_unit = response.json()
            assert created_unit["name"] == unit_name
            assert created_unit["symbol"] == unit_symbol
            assert "id" in created_unit
        else:
            # Unexpected status code
            assert False, f"Unexpected status code {response.status_code}: {response.text}"
    
    @pytest.mark.asyncio
    async def test_empty_database_operations(self, test_client):
        """Test operations when no matching entities exist."""
        # Note: We cannot truly test empty database without breaking other tests
        # Instead, test operations with non-existent entity IDs
        
        # Use very high IDs that are unlikely to exist
        non_existent_id_1 = 999999
        non_existent_id_2 = 999998
        non_existent_unit_id = 999997
        
        # Test getting specific non-existent entity
        entity_response = await test_client.get(f"/api/v1/entities/{non_existent_id_1}")
        assert entity_response.status_code == 404
        
        # Test getting specific non-existent connection
        connection_response = await test_client.get(f"/api/v1/connections/{non_existent_id_1}")
        assert connection_response.status_code == 404
        
        # Test comparison with non-existent entities
        response = await test_client.get(
            "/api/v1/compare/",
            params={
                "from": non_existent_id_1,
                "to": non_existent_id_2,
                "unit": non_existent_unit_id
            }
        )
        assert response.status_code == 404
        
        # Test creating connection with non-existent entities
        create_response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": non_existent_id_1,
                "to_entity_id": non_existent_id_2,
                "unit_id": 1,  # Assuming unit 1 exists
                "multiplier": 2.0
            }
        )
        assert create_response.status_code == 404
        
        # Test updating non-existent entity
        update_response = await test_client.put(
            f"/api/v1/entities/{non_existent_id_1}",
            json={"name": "Updated Name"}
        )
        assert update_response.status_code == 404
        
        # Test deleting non-existent entity
        delete_response = await test_client.delete(f"/api/v1/entities/{non_existent_id_1}")
        assert delete_response.status_code == 404
    
    @pytest.mark.asyncio
    async def test_sql_injection_attempts(self, test_client):
        """Test that SQL injection attempts are properly handled."""
        # Try SQL injection in entity name
        injection_attempts = [
            "'; DROP TABLE entities; --",
            "\" OR 1=1 --",
            "'; DELETE FROM entities WHERE 1=1; --",
            "Robert'); DROP TABLE entities;--",
            "1' UNION SELECT * FROM entities--",
        ]
        
        for attempt in injection_attempts:
            response = await test_client.post(
                "/api/v1/entities/",
                json={"name": attempt}
            )
            # Should fail validation (special characters not allowed)
            assert response.status_code == 422
    
    @pytest.mark.asyncio
    async def test_unicode_entity_names(self, test_client):
        """Test entity names with unicode characters."""
        unicode_names = [
            "Café",  # Accented characters
            "北京",  # Chinese characters
            "Москва",  # Cyrillic
            "🚀",  # Emoji
            "münchen",  # German umlaut
        ]
        
        for name in unicode_names:
            response = await test_client.post(
                "/api/v1/entities/",
                json={"name": name}
            )
            # Unicode might not be allowed (alphanumeric only)
            assert response.status_code == 422
    
    @pytest.mark.asyncio
    async def test_api_versioning(self, test_client):
        """Test API versioning behavior."""
        # Test v1 endpoints work
        response = await test_client.get("/api/v1/health")
        assert response.status_code == 200
        
        # Test non-versioned endpoints
        response = await test_client.get("/api/health")
        assert response.status_code == 404
        
        # Test non-existent version
        response = await test_client.get("/api/v2/entities/")
        assert response.status_code == 404
    
    @pytest.mark.asyncio
    async def test_connection_with_decimal_inverse(self, test_client):
        """Test that inverse connections maintain decimal precision."""
        from .conftest import generate_valid_entity_suffix
        suffix = generate_valid_entity_suffix()
        
        # Create entities with unique names (max 20 chars)
        entity1 = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Dec A {suffix}"}
        )
        print(f"Entity1 response status: {entity1.status_code}")
        print(f"Entity1 response text: {entity1.text}")
        
        # Add assertion to ensure entity creation succeeded
        assert entity1.status_code == 201, f"Entity1 creation failed: {entity1.text}"
        
        entity2 = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"Dec B {suffix}"}
        )
        print(f"Entity2 response status: {entity2.status_code}")
        print(f"Entity2 response text: {entity2.text}")
        
        # Add assertion to ensure entity creation succeeded
        assert entity2.status_code == 201, f"Entity2 creation failed: {entity2.text}"
        
        units = await test_client.get("/api/v1/units/")
        unit_id = units.json()[0]["id"]
        
        # Create connection with multiplier that has complex inverse
        # 3.0 -> inverse is 0.333..., should round to 0.3
        conn_response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entity1.json()["id"],
                "to_entity_id": entity2.json()["id"],
                "unit_id": unit_id,
                "multiplier": 3.0
            }
        )
        print(f"Connection response status: {conn_response.status_code}")
        print(f"Connection response text: {conn_response.text}")
        
        # Ensure connection was created successfully
        assert conn_response.status_code == 201, f"Connection creation failed: {conn_response.text}"
        
        entity1_id = entity1.json()["id"]
        entity2_id = entity2.json()["id"]
        created_connection = conn_response.json()
        created_conn_id = created_connection["id"]
        
        print(f"Created connection ID: {created_conn_id}")
        print(f"Looking for connections between entity1_id={entity1_id} and entity2_id={entity2_id}")
        
        # Use the specific connection ID to retrieve it
        specific_conn_response = await test_client.get(f"/api/v1/connections/{created_conn_id}")
        assert specific_conn_response.status_code == 200
        specific_conn = specific_conn_response.json()
        print(f"Retrieved created connection: {specific_conn}")
        
        # Now find the inverse connection by querying connections with pagination
        # The inverse should have from_entity_id=entity2_id and to_entity_id=entity1_id
        inverse = None
        max_attempts = 10  # Limit attempts to avoid infinite loop
        
        for skip_offset in range(0, max_attempts * 100, 100):
            connections_response = await test_client.get(
                "/api/v1/connections/", 
                params={"skip": skip_offset, "limit": 100}
            )
            connections = connections_response.json()
            
            print(f"Checking connections batch with skip={skip_offset}, found {len(connections)} connections")
            
            # Look for the inverse connection in this batch
            for conn in connections:
                if (conn["from_entity_id"] == entity2_id and 
                    conn["to_entity_id"] == entity1_id and
                    conn["unit_id"] == unit_id):
                    inverse = conn
                    print(f"Found inverse connection: {inverse}")
                    break
            
            if inverse or len(connections) < 100:  # Found it or reached the end
                break
        
        if not inverse:
            raise AssertionError(
                f"No inverse connection found from entity2_id={entity2_id} to entity1_id={entity1_id}. "
                f"Searched through {skip_offset + len(connections)} connections."
            )
        
        # 1/3 = 0.333..., should round to 0.3
        assert float(inverse["multiplier"]) == 0.3