"""
Comprehensive API error handling tests for the SIMILE API.
Tests error response consistency, HTTP status codes, error message formats,
and exception handling coverage.
"""
import pytest
import json
from decimal import Decimal
from typing import Dict, Any, List


class TestErrorResponseFormat:
    """Test error response format consistency across all endpoints."""

    @pytest.mark.asyncio
    async def test_validation_error_format(self, test_client):
        """Test that validation errors have consistent format."""
        # Invalid entity name
        response = await test_client.post("/api/v1/entities/", json={"name": "123"})
        assert response.status_code == 422
        
        error = response.json()
        assert "detail" in error
        assert isinstance(error["detail"], list)
        
        # Check error detail structure
        for error_item in error["detail"]:
            assert "loc" in error_item  # Location of error
            assert "msg" in error_item  # Error message
            assert "type" in error_item  # Error type
            assert isinstance(error_item["loc"], list)

    @pytest.mark.asyncio
    async def test_not_found_error_format(self, test_client):
        """Test that 404 errors have consistent format."""
        endpoints = [
            "/api/v1/entities/99999",
            "/api/v1/connections/99999",
            "/api/v1/units/99999"
        ]
        
        for endpoint in endpoints:
            response = await test_client.get(endpoint)
            assert response.status_code == 404
            
            error = response.json()
            assert "detail" in error
            assert isinstance(error["detail"], str)
            assert "not found" in error["detail"].lower()

    @pytest.mark.asyncio
    async def test_business_logic_error_format(self, test_client):
        """Test business logic errors have consistent format."""
        # Create entity
        entity = await test_client.post("/api/v1/entities/", json={"name": "Test Entity"})
        entity_id = entity.json()["id"]
        
        # Try to create self-connection
        response = await test_client.post("/api/v1/connections/", json={
            "from_entity_id": entity_id,
            "to_entity_id": entity_id,
            "unit_id": 1,
            "multiplier": 1.0
        })
        assert response.status_code == 422
        
        error = response.json()
        assert "detail" in error
        # Could be a list (validation) or string (business logic)
        if isinstance(error["detail"], list):
            assert any("Cannot create connection from entity to itself" in str(e) for e in error["detail"])
        else:
            assert "Cannot create connection from entity to itself" in error["detail"]


class TestEntityErrorHandling:
    """Test error handling for entity endpoints."""

    @pytest.mark.asyncio
    async def test_entity_not_found(self, test_client):
        """Test entity not found errors."""
        # GET non-existent entity
        response = await test_client.get("/api/v1/entities/99999")
        assert response.status_code == 404
        assert "Entity not found" in response.json()["detail"]
        
        # PUT non-existent entity
        response = await test_client.put("/api/v1/entities/99999", json={"name": "Update"})
        assert response.status_code == 404
        assert "Entity not found" in response.json()["detail"]
        
        # DELETE non-existent entity
        response = await test_client.delete("/api/v1/entities/99999")
        assert response.status_code == 404
        assert "Entity not found" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_entity_duplicate_name(self, test_client):
        """Test duplicate entity name handling."""
        # Create first entity
        response = await test_client.post("/api/v1/entities/", json={"name": "Unique Name"})
        assert response.status_code == 201
        
        # Try to create duplicate
        response = await test_client.post("/api/v1/entities/", json={"name": "Unique Name"})
        assert response.status_code == 400  # Returns 400, not 409
        assert "already exists" in response.json()["detail"].lower()

    @pytest.mark.asyncio
    async def test_entity_validation_errors(self, test_client):
        """Test comprehensive entity validation errors."""
        invalid_entities = [
            {"name": ""},  # Empty
            {"name": "   "},  # Whitespace only
            {"name": "Name123"},  # Numbers
            {"name": "Name@Special"},  # Special chars
            {"name": "A" * 101},  # Too long
            {},  # Missing name
            {"name": None},  # Null name
        ]
        
        for entity_data in invalid_entities:
            response = await test_client.post("/api/v1/entities/", json=entity_data)
            assert response.status_code == 422
            error = response.json()
            assert "detail" in error

    @pytest.mark.asyncio
    async def test_entity_in_use_deletion(self, test_client):
        """Test deletion of entity that has connections."""
        # Create entities
        entity1 = await test_client.post("/api/v1/entities/", json={"name": "Source"})
        entity2 = await test_client.post("/api/v1/entities/", json={"name": "Target"})
        e1_id = entity1.json()["id"]
        e2_id = entity2.json()["id"]
        
        # Create connection
        await test_client.post("/api/v1/connections/", json={
            "from_entity_id": e1_id,
            "to_entity_id": e2_id,
            "unit_id": 1,
            "multiplier": 2.0
        })
        
        # Try to delete entity with connections
        # Note: The current implementation doesn't check for connections before deleting
        # It will cascade delete or fail with a foreign key constraint
        response = await test_client.delete(f"/api/v1/entities/{e1_id}")
        # The actual behavior depends on database constraints
        # For now, we'll check that it either succeeds or fails appropriately
        assert response.status_code in [200, 400, 409]


class TestConnectionErrorHandling:
    """Test error handling for connection endpoints."""

    @pytest.mark.asyncio
    async def test_connection_not_found(self, test_client):
        """Test connection not found errors."""
        # GET non-existent connection
        response = await test_client.get("/api/v1/connections/99999")
        assert response.status_code == 404
        assert "Connection not found" in response.json()["detail"]
        
        # DELETE non-existent connection
        response = await test_client.delete("/api/v1/connections/99999")
        assert response.status_code == 404
        assert "Connection not found" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_connection_invalid_entities(self, test_client):
        """Test connection creation with invalid entity IDs."""
        # Non-existent entities
        response = await test_client.post("/api/v1/connections/", json={
            "from_entity_id": 99999,
            "to_entity_id": 99998,
            "unit_id": 1,
            "multiplier": 1.0
        })
        assert response.status_code == 404
        # The error message will be either "From entity not found" or "To entity not found"
        assert "entity not found" in response.json()["detail"].lower()

    @pytest.mark.asyncio
    async def test_connection_invalid_unit(self, test_client):
        """Test connection creation with invalid unit ID."""
        # Create entities
        entity1 = await test_client.post("/api/v1/entities/", json={"name": "Entity One"})
        entity2 = await test_client.post("/api/v1/entities/", json={"name": "Entity Two"})
        
        # Non-existent unit
        response = await test_client.post("/api/v1/connections/", json={
            "from_entity_id": entity1.json()["id"],
            "to_entity_id": entity2.json()["id"],
            "unit_id": 99999,
            "multiplier": 1.0
        })
        assert response.status_code == 404
        assert "Unit not found" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_connection_duplicate(self, test_client):
        """Test duplicate connection handling."""
        # Create entities
        entity1 = await test_client.post("/api/v1/entities/", json={"name": "Entity One"})
        entity2 = await test_client.post("/api/v1/entities/", json={"name": "Entity Two"})
        e1_id = entity1.json()["id"]
        e2_id = entity2.json()["id"]
        
        # Create first connection
        response = await test_client.post("/api/v1/connections/", json={
            "from_entity_id": e1_id,
            "to_entity_id": e2_id,
            "unit_id": 1,
            "multiplier": 2.0
        })
        assert response.status_code == 201
        first_conn = response.json()
        
        # Try to create duplicate - it actually updates the existing connection
        response = await test_client.post("/api/v1/connections/", json={
            "from_entity_id": e1_id,
            "to_entity_id": e2_id,
            "unit_id": 1,
            "multiplier": 3.0
        })
        # The API updates existing connections instead of returning error
        assert response.status_code == 201
        updated_conn = response.json()
        assert updated_conn["id"] == first_conn["id"]  # Same connection ID
        assert float(updated_conn["multiplier"]) == 3.0  # Updated multiplier

    @pytest.mark.asyncio
    async def test_connection_validation_errors(self, test_client):
        """Test comprehensive connection validation errors."""
        # Create entities for testing
        entity1 = await test_client.post("/api/v1/entities/", json={"name": "Entity One"})
        entity2 = await test_client.post("/api/v1/entities/", json={"name": "Entity Two"})
        e1_id = entity1.json()["id"]
        e2_id = entity2.json()["id"]
        
        invalid_connections = [
            # Invalid multipliers
            {"from_entity_id": e1_id, "to_entity_id": e2_id, "unit_id": 1, "multiplier": 0},
            {"from_entity_id": e1_id, "to_entity_id": e2_id, "unit_id": 1, "multiplier": -1},
            {"from_entity_id": e1_id, "to_entity_id": e2_id, "unit_id": 1, "multiplier": 1000000000},
            {"from_entity_id": e1_id, "to_entity_id": e2_id, "unit_id": 1, "multiplier": 0.01},
            # Invalid types
            {"from_entity_id": "not_int", "to_entity_id": e2_id, "unit_id": 1, "multiplier": 1.0},
            {"from_entity_id": e1_id, "to_entity_id": e2_id, "unit_id": "not_int", "multiplier": 1.0},
            {"from_entity_id": e1_id, "to_entity_id": e2_id, "unit_id": 1, "multiplier": "not_number"},
            # Missing fields
            {"to_entity_id": e2_id, "unit_id": 1, "multiplier": 1.0},
            {"from_entity_id": e1_id, "unit_id": 1, "multiplier": 1.0},
            {"from_entity_id": e1_id, "to_entity_id": e2_id, "multiplier": 1.0},
            {"from_entity_id": e1_id, "to_entity_id": e2_id, "unit_id": 1},
            # Self-connection
            {"from_entity_id": e1_id, "to_entity_id": e1_id, "unit_id": 1, "multiplier": 1.0},
        ]
        
        for conn_data in invalid_connections:
            response = await test_client.post("/api/v1/connections/", json=conn_data)
            assert response.status_code == 422, f"Should fail for: {conn_data}"
            error = response.json()
            assert "detail" in error


class TestCompareEndpointErrors:
    """Test error handling for compare endpoint."""

    @pytest.mark.asyncio
    async def test_compare_invalid_entities(self, test_client):
        """Test compare with non-existent entities."""
        response = await test_client.get(
            "/api/v1/compare/?from=99999&to=99998&unit=1"
        )
        assert response.status_code == 404
        error = response.json()
        assert "entity" in error["detail"].lower()
        assert "not found" in error["detail"].lower()

    @pytest.mark.asyncio
    async def test_compare_invalid_unit(self, test_client):
        """Test compare with non-existent unit."""
        # Create entities
        entity1 = await test_client.post("/api/v1/entities/", json={"name": "Entity One"})
        entity2 = await test_client.post("/api/v1/entities/", json={"name": "Entity Two"})
        
        response = await test_client.get(
            f"/api/v1/compare/?from={entity1.json()['id']}&to={entity2.json()['id']}&unit=99999"
        )
        assert response.status_code == 404
        assert "unit" in response.json()["detail"].lower()
        assert "not found" in response.json()["detail"].lower()

    @pytest.mark.asyncio
    async def test_compare_no_path(self, test_client):
        """Test compare when no path exists."""
        # Create disconnected entities
        entity1 = await test_client.post("/api/v1/entities/", json={"name": "Island One"})
        entity2 = await test_client.post("/api/v1/entities/", json={"name": "Island Two"})
        
        response = await test_client.get(
            f"/api/v1/compare/?from={entity1.json()['id']}&to={entity2.json()['id']}&unit=1"
        )
        # When no path found, it returns 404
        assert response.status_code == 404
        
        error = response.json()
        assert "no path found" in error["detail"].lower()

    @pytest.mark.asyncio
    async def test_compare_different_units(self, test_client):
        """Test compare with path that has different units."""
        # Create entities
        e1 = await test_client.post("/api/v1/entities/", json={"name": "Entity A"})
        e2 = await test_client.post("/api/v1/entities/", json={"name": "Entity B"})
        e3 = await test_client.post("/api/v1/entities/", json={"name": "Entity C"})
        
        # Create connections with different units
        await test_client.post("/api/v1/connections/", json={
            "from_entity_id": e1.json()["id"],
            "to_entity_id": e2.json()["id"],
            "unit_id": 1,  # Length
            "multiplier": 2.0
        })
        
        await test_client.post("/api/v1/connections/", json={
            "from_entity_id": e2.json()["id"],
            "to_entity_id": e3.json()["id"],
            "unit_id": 2,  # Mass (different unit)
            "multiplier": 3.0
        })
        
        # Try to compare A to C with Length unit
        response = await test_client.get(
            f"/api/v1/compare/?from={e1.json()['id']}&to={e3.json()['id']}&unit=1"
        )
        # When no path found due to different units, it returns 404
        assert response.status_code == 404
        
        error = response.json()
        assert "no path found" in error["detail"].lower()

    @pytest.mark.asyncio
    async def test_compare_validation_errors(self, test_client):
        """Test validation errors for compare endpoint."""
        invalid_requests = [
            "from=not_int&to=2&unit=1",
            "from=1&to=not_int&unit=1",
            "from=1&to=2&unit=not_int",
            "to=2&unit=1",  # Missing from
            "from=1&unit=1",  # Missing to
            "from=1&to=2",  # Missing unit
            "",  # Empty query
        ]
        
        for query_string in invalid_requests:
            response = await test_client.get(f"/api/v1/compare/?{query_string}")
            assert response.status_code == 422
            error = response.json()
            assert "detail" in error


class TestUnitErrorHandling:
    """Test error handling for unit endpoints."""

    @pytest.mark.asyncio
    async def test_unit_not_found(self, test_client):
        """Test unit not found error."""
        response = await test_client.get("/api/v1/units/99999")
        assert response.status_code == 404
        assert "Unit not found" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_unit_creation(self, test_client):
        """Test unit creation endpoint."""
        # Try to create a new unit
        response = await test_client.post("/api/v1/units/", json={"name": "TestUnit", "symbol": "TU"})
        assert response.status_code in [200, 201]  # May return 200 or 201
        
        # Try to create duplicate
        response = await test_client.post("/api/v1/units/", json={"name": "TestUnit", "symbol": "TU"})
        assert response.status_code == 400
        assert "already exists" in response.json()["detail"].lower()


class TestConcurrentRequestErrors:
    """Test error handling for concurrent requests."""

    @pytest.mark.asyncio
    async def test_concurrent_duplicate_entity_creation(self, test_client):
        """Test handling of concurrent duplicate entity creation."""
        import asyncio
        
        async def create_entity(name):
            return await test_client.post("/api/v1/entities/", json={"name": name})
        
        # Try to create same entity concurrently
        tasks = [create_entity("ConcurrentEntity") for _ in range(5)]
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Count successful vs failed
        success_count = sum(1 for r in responses if not isinstance(r, Exception) and r.status_code == 201)
        conflict_count = sum(1 for r in responses if not isinstance(r, Exception) and r.status_code == 400)  # Returns 400, not 409
        
        # Exactly one should succeed, others should get 400
        assert success_count == 1
        assert conflict_count == 4

    @pytest.mark.asyncio
    async def test_concurrent_connection_creation(self, test_client):
        """Test handling of concurrent duplicate connection creation."""
        import asyncio
        
        # Create entities
        entity1 = await test_client.post("/api/v1/entities/", json={"name": "ConcurrentSource"})
        entity2 = await test_client.post("/api/v1/entities/", json={"name": "ConcurrentTarget"})
        e1_id = entity1.json()["id"]
        e2_id = entity2.json()["id"]
        
        async def create_connection():
            return await test_client.post("/api/v1/connections/", json={
                "from_entity_id": e1_id,
                "to_entity_id": e2_id,
                "unit_id": 1,
                "multiplier": 2.0
            })
        
        # Try to create same connection concurrently
        tasks = [create_connection() for _ in range(5)]
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Count successful vs failed
        success_count = sum(1 for r in responses if not isinstance(r, Exception) and r.status_code == 201)
        conflict_count = sum(1 for r in responses if not isinstance(r, Exception) and r.status_code == 400)  # Returns 400, not 409
        
        # Exactly one should succeed, others should get 400
        assert success_count == 1
        assert conflict_count == 4


class TestMalformedRequestHandling:
    """Test handling of malformed requests."""

    @pytest.mark.asyncio
    async def test_invalid_json(self, test_client):
        """Test handling of invalid JSON in request body."""
        response = await test_client.post(
            "/api/v1/entities/",
            content='{"name": "Test", invalid json}',
            headers={"Content-Type": "application/json"}
        )
        assert response.status_code == 422
        error = response.json()
        assert "detail" in error

    @pytest.mark.asyncio
    async def test_empty_body(self, test_client):
        """Test handling of empty request body."""
        response = await test_client.post(
            "/api/v1/entities/",
            content="{}",  # Empty JSON object instead of empty string
            headers={"Content-Type": "application/json"}
        )
        assert response.status_code == 422
        error = response.json()
        assert "detail" in error

    @pytest.mark.asyncio
    async def test_wrong_content_type(self, test_client):
        """Test handling of wrong content type."""
        response = await test_client.post(
            "/api/v1/entities/",
            data="name=Test",
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_oversized_request(self, test_client):
        """Test handling of oversized request data."""
        # Create entity with extremely long name
        response = await test_client.post("/api/v1/entities/", json={
            "name": "A" * 1000  # Way over limit
        })
        assert response.status_code == 422
        error = response.json()
        assert "detail" in error


class TestEdgeCaseErrors:
    """Test error handling for edge cases."""

    @pytest.mark.asyncio
    async def test_numeric_overflow(self, test_client):
        """Test handling of numeric overflow in multipliers."""
        entity1 = await test_client.post("/api/v1/entities/", json={"name": "Entity One"})
        entity2 = await test_client.post("/api/v1/entities/", json={"name": "Entity Two"})
        
        # Test near upper bound (999999999.9 is actually at the limit and gets rejected)
        response = await test_client.post("/api/v1/connections/", json={
            "from_entity_id": entity1.json()["id"],
            "to_entity_id": entity2.json()["id"],
            "unit_id": 1,
            "multiplier": 999999999.8  # Just under limit
        })
        assert response.status_code == 201
        
        # Test at exact limit (should be rejected)
        response = await test_client.post("/api/v1/connections/", json={
            "from_entity_id": entity2.json()["id"],
            "to_entity_id": entity1.json()["id"],
            "unit_id": 1,
            "multiplier": 999999999.9  # At limit - gets rejected
        })
        assert response.status_code == 422
        
        # Test over limit
        response = await test_client.post("/api/v1/connections/", json={
            "from_entity_id": entity1.json()["id"],
            "to_entity_id": entity2.json()["id"],
            "unit_id": 2,
            "multiplier": 1000000000.0  # Over limit
        })
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_special_characters_in_paths(self, test_client):
        """Test handling of special characters in URL paths."""
        # Test with special characters in entity ID (should be rejected as invalid int)
        response = await test_client.get("/api/v1/entities/abc")
        assert response.status_code == 422
        
        response = await test_client.get("/api/v1/entities/1%20OR%201=1")
        assert response.status_code == 422
        
        # Path traversal attempt - will result in 404 as it's an invalid route
        response = await test_client.get("/api/v1/entities/../../../etc/passwd")
        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_null_values(self, test_client):
        """Test handling of null values in requests."""
        # Null in required fields
        response = await test_client.post("/api/v1/entities/", json={"name": None})
        assert response.status_code == 422
        
        # Null in numeric fields
        entity1 = await test_client.post("/api/v1/entities/", json={"name": "Entity"})
        response = await test_client.post("/api/v1/connections/", json={
            "from_entity_id": entity1.json()["id"],
            "to_entity_id": None,
            "unit_id": 1,
            "multiplier": 1.0
        })
        assert response.status_code == 422


class TestErrorMessageLocalization:
    """Test that error messages are clear and consistent."""

    @pytest.mark.asyncio
    async def test_error_messages_are_descriptive(self, test_client):
        """Test that error messages provide useful information."""
        # Entity name validation
        response = await test_client.post("/api/v1/entities/", json={"name": "123"})
        assert response.status_code == 422
        error = response.json()
        error_str = str(error)
        # Should mention pattern or validation
        assert any(keyword in error_str.lower() for keyword in ["pattern", "letters", "validation", "string"])
        
        # Multiplier validation
        entity = await test_client.post("/api/v1/entities/", json={"name": "Test"})
        response = await test_client.post("/api/v1/connections/", json={
            "from_entity_id": entity.json()["id"],
            "to_entity_id": entity.json()["id"] + 1,
            "unit_id": 1,
            "multiplier": -1
        })
        assert response.status_code == 422
        error = response.json()
        error_str = str(error)
        # Should mention positive or greater than
        assert any(keyword in error_str.lower() for keyword in ["positive", "greater", "must be"])

    @pytest.mark.asyncio
    async def test_field_location_in_errors(self, test_client):
        """Test that validation errors indicate which field has the problem."""
        # Multiple field errors
        response = await test_client.post("/api/v1/connections/", json={
            "from_entity_id": "not_int",
            "to_entity_id": "not_int",
            "unit_id": "not_int",
            "multiplier": "not_number"
        })
        assert response.status_code == 422
        
        error = response.json()
        assert isinstance(error["detail"], list)
        
        # Check that each field is mentioned in errors
        field_errors = {tuple(e["loc"]): e for e in error["detail"]}
        expected_fields = ["from_entity_id", "to_entity_id", "unit_id", "multiplier"]
        
        for field in expected_fields:
            assert any(field in loc for loc in field_errors.keys())