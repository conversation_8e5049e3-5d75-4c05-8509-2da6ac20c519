"""
Comprehensive tests for the compare route functionality.
Tests entity comparison logic, pathfinding scenarios, error handling, and edge cases.
"""
import pytest
import pytest_asyncio
from decimal import Decimal
from httpx import AsyncClient

from tests.conftest import create_test_entity_name, test_data_generator


class TestCompareRouteValidation:
    """Test compare route parameter validation and error handling."""
    
    @pytest.mark.asyncio
    async def test_compare_nonexistent_from_entity(self, test_client: AsyncClient):
        """Test compare with non-existent from entity."""
        # Get a valid unit ID
        units_response = await test_client.get("/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        unit_id = units[0]["id"]
        
        # Try to compare with non-existent from entity
        response = await test_client.get(f"/api/v1/compare/?from=9999&to=9998&unit={unit_id}")
        assert response.status_code == 404
        error_detail = response.json()
        assert "From entity not found" in error_detail["detail"]
    
    @pytest.mark.asyncio
    async def test_compare_nonexistent_to_entity(self, test_client: AsyncClient):
        """Test compare with non-existent to entity."""
        # Create a test entity
        entity_name = create_test_entity_name("CompareTest")
        entity_data = {"name": entity_name}
        create_response = await test_client.post("/api/v1/entities/", json=entity_data)
        assert create_response.status_code == 201
        entity = create_response.json()
        from_entity_id = entity["id"]
        
        # Get a valid unit ID
        units_response = await test_client.get("/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        unit_id = units[0]["id"]
        
        # Try to compare with non-existent to entity
        response = await test_client.get(f"/api/v1/compare/?from={from_entity_id}&to=9999&unit={unit_id}")
        assert response.status_code == 404
        error_detail = response.json()
        assert "To entity not found" in error_detail["detail"]
    
    @pytest.mark.asyncio
    async def test_compare_nonexistent_unit(self, test_client: AsyncClient):
        """Test compare with non-existent unit."""
        # Create test entities
        entity1_name = create_test_entity_name("CompareTestA")
        entity2_name = create_test_entity_name("CompareTestB")
        
        entity1_data = {"name": entity1_name}
        entity2_data = {"name": entity2_name}
        
        create_response1 = await test_client.post("/api/v1/entities/", json=entity1_data)
        assert create_response1.status_code == 201
        entity1 = create_response1.json()
        
        create_response2 = await test_client.post("/api/v1/entities/", json=entity2_data)
        assert create_response2.status_code == 201
        entity2 = create_response2.json()
        
        # Try to compare with non-existent unit
        response = await test_client.get(f"/api/v1/compare/?from={entity1['id']}&to={entity2['id']}&unit=9999")
        assert response.status_code == 404
        error_detail = response.json()
        assert "Unit not found" in error_detail["detail"]
    
    @pytest.mark.asyncio
    async def test_compare_missing_query_parameters(self, test_client: AsyncClient):
        """Test compare with missing query parameters."""
        # Missing 'from' parameter
        response = await test_client.get("/api/v1/compare/?to=1&unit=1")
        assert response.status_code == 422
        
        # Missing 'to' parameter
        response = await test_client.get("/api/v1/compare/?from=1&unit=1")
        assert response.status_code == 422
        
        # Missing 'unit' parameter
        response = await test_client.get("/api/v1/compare/?from=1&to=2")
        assert response.status_code == 422
        
        # Missing all parameters
        response = await test_client.get("/api/v1/compare/")
        assert response.status_code == 422
    
    @pytest.mark.asyncio
    async def test_compare_invalid_parameter_types(self, test_client: AsyncClient):
        """Test compare with invalid parameter types."""
        # Non-numeric entity IDs
        response = await test_client.get("/api/v1/compare/?from=abc&to=def&unit=xyz")
        assert response.status_code == 422
        
        # Negative entity IDs (may return 404 if they reach the endpoint)
        response = await test_client.get("/api/v1/compare/?from=-1&to=-2&unit=-3")
        assert response.status_code in [404, 422]
        
        # Zero entity IDs (may return 404 if they reach the endpoint)
        response = await test_client.get("/api/v1/compare/?from=0&to=0&unit=0")
        assert response.status_code in [404, 422]


class TestCompareRouteSameEntity:
    """Test compare route with same entity comparisons."""
    
    @pytest.mark.asyncio
    async def test_compare_same_entity_returns_multiplier_one(self, test_client: AsyncClient):
        """Test that comparing an entity to itself returns multiplier 1.0."""
        # Create a test entity
        entity_name = create_test_entity_name("SameEntityTest")
        entity_data = {"name": entity_name}
        create_response = await test_client.post("/api/v1/entities/", json=entity_data)
        assert create_response.status_code == 201
        entity = create_response.json()
        entity_id = entity["id"]
        
        # Get a valid unit ID
        units_response = await test_client.get("/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        unit_id = units[0]["id"]
        
        # Compare entity to itself
        response = await test_client.get(f"/api/v1/compare/?from={entity_id}&to={entity_id}&unit={unit_id}")
        assert response.status_code == 200
        result = response.json()
        
        # Verify response structure
        assert "from_entity" in result
        assert "to_entity" in result
        assert "unit" in result
        assert "multiplier" in result
        assert "path" in result
        assert "error" in result
        
        # Verify same entity comparison results
        assert result["from_entity"]["id"] == entity_id
        assert result["to_entity"]["id"] == entity_id
        assert result["unit"]["id"] == unit_id
        assert float(result["multiplier"]) == 1.0
        assert result["path"] == []
        assert result["error"] is None
    
    @pytest.mark.asyncio
    async def test_compare_same_entity_all_units(self, test_client: AsyncClient):
        """Test same entity comparison works for all available units."""
        # Create a test entity
        entity_name = create_test_entity_name("AllUnitsTest")
        entity_data = {"name": entity_name}
        create_response = await test_client.post("/api/v1/entities/", json=entity_data)
        assert create_response.status_code == 201
        entity = create_response.json()
        entity_id = entity["id"]
        
        # Get all available units
        units_response = await test_client.get("/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        
        # Test same entity comparison for each unit
        for unit in units:
            unit_id = unit["id"]
            response = await test_client.get(f"/api/v1/compare/?from={entity_id}&to={entity_id}&unit={unit_id}")
            assert response.status_code == 200
            result = response.json()
            assert float(result["multiplier"]) == 1.0
            assert result["path"] == []
            assert result["error"] is None


class TestCompareRouteDirectConnections:
    """Test compare route with direct connections between entities."""
    
    @pytest.mark.asyncio
    async def test_compare_direct_connection_success(self, test_client: AsyncClient):
        """Test successful comparison with direct connection."""
        # Create test entities
        entity1_name = create_test_entity_name("DirectTestA")
        entity2_name = create_test_entity_name("DirectTestB")
        
        entity1_data = {"name": entity1_name}
        entity2_data = {"name": entity2_name}
        
        create_response1 = await test_client.post("/api/v1/entities/", json=entity1_data)
        assert create_response1.status_code == 201
        entity1 = create_response1.json()
        
        create_response2 = await test_client.post("/api/v1/entities/", json=entity2_data)
        assert create_response2.status_code == 201
        entity2 = create_response2.json()
        
        # Get a valid unit ID
        units_response = await test_client.get("/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        unit_id = units[0]["id"]
        
        # Create a direct connection
        connection_data = {
            "from_entity_id": entity1["id"],
            "to_entity_id": entity2["id"],
            "unit_id": unit_id,
            "multiplier": 2.5
        }
        conn_response = await test_client.post("/api/v1/connections/", json=connection_data)
        assert conn_response.status_code == 201
        
        # Test comparison
        response = await test_client.get(f"/api/v1/compare/?from={entity1['id']}&to={entity2['id']}&unit={unit_id}")
        assert response.status_code == 200
        result = response.json()
        
        # Verify direct connection results
        assert result["from_entity"]["id"] == entity1["id"]
        assert result["to_entity"]["id"] == entity2["id"]
        assert result["unit"]["id"] == unit_id
        assert float(result["multiplier"]) == 2.5
        assert len(result["path"]) == 1
        assert result["error"] is None
        
        # Verify path structure
        path_step = result["path"][0]
        assert path_step["from_entity_id"] == entity1["id"]
        assert path_step["to_entity_id"] == entity2["id"]
        assert path_step["from_entity_name"] == entity1_name
        assert path_step["to_entity_name"] == entity2_name
        assert path_step["multiplier"] == 2.5
        assert path_step["hop"] == 1
    
    @pytest.mark.asyncio
    async def test_compare_direct_connection_reverse_direction(self, test_client: AsyncClient):
        """Test comparison in reverse direction using inverse connection."""
        # Create test entities
        entity1_name = create_test_entity_name("ReverseTestA")
        entity2_name = create_test_entity_name("ReverseTestB")
        
        entity1_data = {"name": entity1_name}
        entity2_data = {"name": entity2_name}
        
        create_response1 = await test_client.post("/api/v1/entities/", json=entity1_data)
        assert create_response1.status_code == 201
        entity1 = create_response1.json()
        
        create_response2 = await test_client.post("/api/v1/entities/", json=entity2_data)
        assert create_response2.status_code == 201
        entity2 = create_response2.json()
        
        # Get a valid unit ID
        units_response = await test_client.get("/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        unit_id = units[0]["id"]
        
        # Create a direct connection from entity1 to entity2
        connection_data = {
            "from_entity_id": entity1["id"],
            "to_entity_id": entity2["id"],
            "unit_id": unit_id,
            "multiplier": 4.0
        }
        conn_response = await test_client.post("/api/v1/connections/", json=connection_data)
        assert conn_response.status_code == 201
        
        # Test comparison in reverse direction (entity2 to entity1)
        response = await test_client.get(f"/api/v1/compare/?from={entity2['id']}&to={entity1['id']}&unit={unit_id}")
        assert response.status_code == 200
        result = response.json()
        
        # Verify inverse connection results (multiplier should be 1/4.0 = 0.25, rounded to 0.2)
        assert result["from_entity"]["id"] == entity2["id"]
        assert result["to_entity"]["id"] == entity1["id"]
        assert result["unit"]["id"] == unit_id
        assert float(result["multiplier"]) == 0.2  # 1/4.0 = 0.25 rounded to 1 decimal = 0.2
        assert len(result["path"]) == 1
        assert result["error"] is None
    
    @pytest.mark.asyncio
    async def test_compare_direct_connection_decimal_precision(self, test_client: AsyncClient):
        """Test comparison with decimal precision handling."""
        # Create test entities
        entity1_name = create_test_entity_name("DecimalTestA")
        entity2_name = create_test_entity_name("DecimalTestB")
        
        entity1_data = {"name": entity1_name}
        entity2_data = {"name": entity2_name}
        
        create_response1 = await test_client.post("/api/v1/entities/", json=entity1_data)
        assert create_response1.status_code == 201
        entity1 = create_response1.json()
        
        create_response2 = await test_client.post("/api/v1/entities/", json=entity2_data)
        assert create_response2.status_code == 201
        entity2 = create_response2.json()
        
        # Get a valid unit ID
        units_response = await test_client.get("/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        unit_id = units[0]["id"]
        
        # Create a connection with decimal precision
        connection_data = {
            "from_entity_id": entity1["id"],
            "to_entity_id": entity2["id"],
            "unit_id": unit_id,
            "multiplier": 3.14159  # Will be rounded to 3.1
        }
        conn_response = await test_client.post("/api/v1/connections/", json=connection_data)
        assert conn_response.status_code == 201
        
        # Test comparison
        response = await test_client.get(f"/api/v1/compare/?from={entity1['id']}&to={entity2['id']}&unit={unit_id}")
        assert response.status_code == 200
        result = response.json()
        
        # Verify decimal precision is maintained
        assert float(result["multiplier"]) == 3.1
        assert result["path"][0]["multiplier"] == 3.1


class TestCompareRouteMultiHopPaths:
    """Test compare route with multi-hop pathfinding."""
    
    @pytest.mark.asyncio
    async def test_compare_two_hop_path(self, test_client: AsyncClient):
        """Test comparison with two-hop path (A->B->C)."""
        # Create test entities
        entity_a_name = create_test_entity_name("TwoHopA")
        entity_b_name = create_test_entity_name("TwoHopB")
        entity_c_name = create_test_entity_name("TwoHopC")
        
        entities_data = [
            {"name": entity_a_name},
            {"name": entity_b_name},
            {"name": entity_c_name}
        ]
        
        entities = []
        for entity_data in entities_data:
            create_response = await test_client.post("/api/v1/entities/", json=entity_data)
            assert create_response.status_code == 201
            entities.append(create_response.json())
        
        entity_a, entity_b, entity_c = entities
        
        # Get a valid unit ID
        units_response = await test_client.get("/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        unit_id = units[0]["id"]
        
        # Create connections: A->B (2.0) and B->C (3.0)
        connection_ab_data = {
            "from_entity_id": entity_a["id"],
            "to_entity_id": entity_b["id"],
            "unit_id": unit_id,
            "multiplier": 2.0
        }
        connection_bc_data = {
            "from_entity_id": entity_b["id"],
            "to_entity_id": entity_c["id"],
            "unit_id": unit_id,
            "multiplier": 3.0
        }
        
        conn_ab_response = await test_client.post("/api/v1/connections/", json=connection_ab_data)
        assert conn_ab_response.status_code == 201
        
        conn_bc_response = await test_client.post("/api/v1/connections/", json=connection_bc_data)
        assert conn_bc_response.status_code == 201
        
        # Test comparison A->C (should be 2.0 * 3.0 = 6.0)
        response = await test_client.get(f"/api/v1/compare/?from={entity_a['id']}&to={entity_c['id']}&unit={unit_id}")
        assert response.status_code == 200
        result = response.json()
        
        # Verify two-hop path results
        assert result["from_entity"]["id"] == entity_a["id"]
        assert result["to_entity"]["id"] == entity_c["id"]
        assert result["unit"]["id"] == unit_id
        assert float(result["multiplier"]) == 6.0
        assert len(result["path"]) == 2
        assert result["error"] is None
        
        # Verify path structure
        path_step_1 = result["path"][0]
        assert path_step_1["from_entity_id"] == entity_a["id"]
        assert path_step_1["to_entity_id"] == entity_b["id"]
        assert path_step_1["from_entity_name"] == entity_a_name
        assert path_step_1["to_entity_name"] == entity_b_name
        assert path_step_1["multiplier"] == 2.0
        assert path_step_1["hop"] == 1
        
        path_step_2 = result["path"][1]
        assert path_step_2["from_entity_id"] == entity_b["id"]
        assert path_step_2["to_entity_id"] == entity_c["id"]
        assert path_step_2["from_entity_name"] == entity_b_name
        assert path_step_2["to_entity_name"] == entity_c_name
        assert path_step_2["multiplier"] == 3.0
        assert path_step_2["hop"] == 2
    
    @pytest.mark.asyncio
    async def test_compare_three_hop_path(self, test_client: AsyncClient):
        """Test comparison with three-hop path (A->B->C->D)."""
        # Create test entities
        entity_names = [
            create_test_entity_name("ThreeHopA"),
            create_test_entity_name("ThreeHopB"),
            create_test_entity_name("ThreeHopC"),
            create_test_entity_name("ThreeHopD")
        ]
        
        entities = []
        for entity_name in entity_names:
            entity_data = {"name": entity_name}
            create_response = await test_client.post("/api/v1/entities/", json=entity_data)
            assert create_response.status_code == 201
            entities.append(create_response.json())
        
        entity_a, entity_b, entity_c, entity_d = entities
        
        # Get a valid unit ID
        units_response = await test_client.get("/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        unit_id = units[0]["id"]
        
        # Create connections: A->B (2.0), B->C (5.0), C->D (0.5)
        connections_data = [
            {"from_entity_id": entity_a["id"], "to_entity_id": entity_b["id"], "multiplier": 2.0},
            {"from_entity_id": entity_b["id"], "to_entity_id": entity_c["id"], "multiplier": 5.0},
            {"from_entity_id": entity_c["id"], "to_entity_id": entity_d["id"], "multiplier": 0.5}
        ]
        
        for conn_data in connections_data:
            conn_data["unit_id"] = unit_id
            conn_response = await test_client.post("/api/v1/connections/", json=conn_data)
            assert conn_response.status_code == 201
        
        # Test comparison A->D (should be 2.0 * 5.0 * 0.5 = 5.0)
        response = await test_client.get(f"/api/v1/compare/?from={entity_a['id']}&to={entity_d['id']}&unit={unit_id}")
        assert response.status_code == 200
        result = response.json()
        
        # Verify three-hop path results
        assert result["from_entity"]["id"] == entity_a["id"]
        assert result["to_entity"]["id"] == entity_d["id"]
        assert result["unit"]["id"] == unit_id
        assert float(result["multiplier"]) == 5.0
        assert len(result["path"]) == 3
        assert result["error"] is None
        
        # Verify path structure
        expected_multipliers = [2.0, 5.0, 0.5]
        for i, expected_mult in enumerate(expected_multipliers):
            path_step = result["path"][i]
            assert path_step["multiplier"] == expected_mult
            assert path_step["hop"] == i + 1
    
    @pytest.mark.asyncio
    async def test_compare_shortest_path_selection(self, test_client: AsyncClient):
        """Test that shortest path is selected when multiple paths exist."""
        # Create test entities for a diamond pattern
        entity_names = [
            create_test_entity_name("DiamondA"),
            create_test_entity_name("DiamondB"),
            create_test_entity_name("DiamondC"),
            create_test_entity_name("DiamondD")
        ]
        
        entities = []
        for entity_name in entity_names:
            entity_data = {"name": entity_name}
            create_response = await test_client.post("/api/v1/entities/", json=entity_data)
            assert create_response.status_code == 201
            entities.append(create_response.json())
        
        entity_a, entity_b, entity_c, entity_d = entities
        
        # Get a valid unit ID
        units_response = await test_client.get("/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        unit_id = units[0]["id"]
        
        # Create diamond pattern connections:
        # A->B (2.0), B->D (3.0) - Path 1: A->B->D = 6.0 (2 hops)
        # A->C (1.0), C->D (4.0) - Path 2: A->C->D = 4.0 (2 hops)
        # A->D (10.0) - Path 3: A->D = 10.0 (1 hop, should be selected as shortest)
        connections_data = [
            {"from_entity_id": entity_a["id"], "to_entity_id": entity_b["id"], "multiplier": 2.0},
            {"from_entity_id": entity_b["id"], "to_entity_id": entity_d["id"], "multiplier": 3.0},
            {"from_entity_id": entity_a["id"], "to_entity_id": entity_c["id"], "multiplier": 1.0},
            {"from_entity_id": entity_c["id"], "to_entity_id": entity_d["id"], "multiplier": 4.0},
            {"from_entity_id": entity_a["id"], "to_entity_id": entity_d["id"], "multiplier": 10.0}
        ]
        
        for conn_data in connections_data:
            conn_data["unit_id"] = unit_id
            conn_response = await test_client.post("/api/v1/connections/", json=conn_data)
            assert conn_response.status_code == 201
        
        # Test comparison A->D (should select direct path with 1 hop)
        response = await test_client.get(f"/api/v1/compare/?from={entity_a['id']}&to={entity_d['id']}&unit={unit_id}")
        assert response.status_code == 200
        result = response.json()
        
        # Verify shortest path selection (direct connection)
        assert result["from_entity"]["id"] == entity_a["id"]
        assert result["to_entity"]["id"] == entity_d["id"]
        assert result["unit"]["id"] == unit_id
        assert float(result["multiplier"]) == 10.0
        assert len(result["path"]) == 1  # Direct connection should be selected
        assert result["error"] is None


class TestCompareRouteNoPath:
    """Test compare route when no path exists between entities."""
    
    @pytest.mark.asyncio
    async def test_compare_no_connection_returns_404(self, test_client: AsyncClient):
        """Test that no connection between entities returns 404."""
        # Create two isolated entities
        entity1_name = create_test_entity_name("IsolatedA")
        entity2_name = create_test_entity_name("IsolatedB")
        
        entity1_data = {"name": entity1_name}
        entity2_data = {"name": entity2_name}
        
        create_response1 = await test_client.post("/api/v1/entities/", json=entity1_data)
        assert create_response1.status_code == 201
        entity1 = create_response1.json()
        
        create_response2 = await test_client.post("/api/v1/entities/", json=entity2_data)
        assert create_response2.status_code == 201
        entity2 = create_response2.json()
        
        # Get a valid unit ID
        units_response = await test_client.get("/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        unit_id = units[0]["id"]
        
        # Test comparison with no connection
        response = await test_client.get(f"/api/v1/compare/?from={entity1['id']}&to={entity2['id']}&unit={unit_id}")
        assert response.status_code == 404
        error_detail = response.json()
        
        # Verify error message includes helpful information
        assert "No path found between" in error_detail["detail"]
        assert entity1_name in error_detail["detail"]
        assert entity2_name in error_detail["detail"]
        assert "Consider creating a direct connection" in error_detail["detail"]
    
    @pytest.mark.asyncio
    async def test_compare_wrong_unit_no_path(self, test_client: AsyncClient):
        """Test that connections in wrong unit return 404."""
        # Create test entities
        entity1_name = create_test_entity_name("WrongUnitA")
        entity2_name = create_test_entity_name("WrongUnitB")
        
        entity1_data = {"name": entity1_name}
        entity2_data = {"name": entity2_name}
        
        create_response1 = await test_client.post("/api/v1/entities/", json=entity1_data)
        assert create_response1.status_code == 201
        entity1 = create_response1.json()
        
        create_response2 = await test_client.post("/api/v1/entities/", json=entity2_data)
        assert create_response2.status_code == 201
        entity2 = create_response2.json()
        
        # Get valid unit IDs
        units_response = await test_client.get("/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        unit1_id = units[0]["id"]
        unit2_id = units[1]["id"]
        
        # Create connection in unit1
        connection_data = {
            "from_entity_id": entity1["id"],
            "to_entity_id": entity2["id"],
            "unit_id": unit1_id,
            "multiplier": 2.0
        }
        conn_response = await test_client.post("/api/v1/connections/", json=connection_data)
        assert conn_response.status_code == 201
        
        # Test comparison in unit2 (should return 404)
        response = await test_client.get(f"/api/v1/compare/?from={entity1['id']}&to={entity2['id']}&unit={unit2_id}")
        assert response.status_code == 404
        error_detail = response.json()
        assert "No path found between" in error_detail["detail"]
    
    @pytest.mark.asyncio
    async def test_compare_partial_path_different_units(self, test_client: AsyncClient):
        """Test that partial paths in different units return 404."""
        # Create test entities A->B->C
        entity_names = [
            create_test_entity_name("PartialA"),
            create_test_entity_name("PartialB"),
            create_test_entity_name("PartialC")
        ]
        
        entities = []
        for entity_name in entity_names:
            entity_data = {"name": entity_name}
            create_response = await test_client.post("/api/v1/entities/", json=entity_data)
            assert create_response.status_code == 201
            entities.append(create_response.json())
        
        entity_a, entity_b, entity_c = entities
        
        # Get valid unit IDs
        units_response = await test_client.get("/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        unit1_id = units[0]["id"]
        unit2_id = units[1]["id"]
        
        # Create connections in different units:
        # A->B in unit1, B->C in unit2
        connection_ab_data = {
            "from_entity_id": entity_a["id"],
            "to_entity_id": entity_b["id"],
            "unit_id": unit1_id,
            "multiplier": 2.0
        }
        connection_bc_data = {
            "from_entity_id": entity_b["id"],
            "to_entity_id": entity_c["id"],
            "unit_id": unit2_id,
            "multiplier": 3.0
        }
        
        conn_ab_response = await test_client.post("/api/v1/connections/", json=connection_ab_data)
        assert conn_ab_response.status_code == 201
        
        conn_bc_response = await test_client.post("/api/v1/connections/", json=connection_bc_data)
        assert conn_bc_response.status_code == 201
        
        # Test comparison A->C in unit1 (should return 404 because path is incomplete)
        response = await test_client.get(f"/api/v1/compare/?from={entity_a['id']}&to={entity_c['id']}&unit={unit1_id}")
        assert response.status_code == 404
        error_detail = response.json()
        assert "No path found between" in error_detail["detail"]


class TestCompareRouteComplexScenarios:
    """Test compare route with complex graph scenarios."""
    
    @pytest.mark.asyncio
    async def test_compare_with_cycles_avoids_infinite_loops(self, test_client: AsyncClient):
        """Test that pathfinding avoids infinite loops in cyclic graphs."""
        # Create entities for a cycle: A->B->C->A
        entity_names = [
            create_test_entity_name("CycleA"),
            create_test_entity_name("CycleB"),
            create_test_entity_name("CycleC")
        ]
        
        entities = []
        for entity_name in entity_names:
            entity_data = {"name": entity_name}
            create_response = await test_client.post("/api/v1/entities/", json=entity_data)
            assert create_response.status_code == 201
            entities.append(create_response.json())
        
        entity_a, entity_b, entity_c = entities
        
        # Get a valid unit ID
        units_response = await test_client.get("/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        unit_id = units[0]["id"]
        
        # Create cyclic connections: A->B->C->A
        connections_data = [
            {"from_entity_id": entity_a["id"], "to_entity_id": entity_b["id"], "multiplier": 2.0},
            {"from_entity_id": entity_b["id"], "to_entity_id": entity_c["id"], "multiplier": 3.0},
            {"from_entity_id": entity_c["id"], "to_entity_id": entity_a["id"], "multiplier": 0.5}
        ]
        
        for conn_data in connections_data:
            conn_data["unit_id"] = unit_id
            conn_response = await test_client.post("/api/v1/connections/", json=conn_data)
            assert conn_response.status_code == 201
        
        # Test comparison A->C (should find 2-hop path without infinite loop)
        response = await test_client.get(f"/api/v1/compare/?from={entity_a['id']}&to={entity_c['id']}&unit={unit_id}")
        assert response.status_code == 200
        result = response.json()
        
        # Verify cycle handling - the pathfinding should find the best path
        # Note: Due to inverse connections, A->C may have a direct connection (inverse of C->A)
        assert result["from_entity"]["id"] == entity_a["id"]
        assert result["to_entity"]["id"] == entity_c["id"]
        assert result["unit"]["id"] == unit_id
        # The multiplier could be either 6.0 (A->B->C path) or 2.0 (direct A->C inverse)
        assert float(result["multiplier"]) in [2.0, 6.0]
        assert len(result["path"]) >= 1  # Should find some valid path
        assert result["error"] is None
    
    @pytest.mark.asyncio
    async def test_compare_decimal_precision_in_multi_hop(self, test_client: AsyncClient):
        """Test decimal precision handling in multi-hop calculations."""
        # Create entities for precision testing
        entity_names = [
            create_test_entity_name("PrecisionA"),
            create_test_entity_name("PrecisionB"),
            create_test_entity_name("PrecisionC")
        ]
        
        entities = []
        for entity_name in entity_names:
            entity_data = {"name": entity_name}
            create_response = await test_client.post("/api/v1/entities/", json=entity_data)
            assert create_response.status_code == 201
            entities.append(create_response.json())
        
        entity_a, entity_b, entity_c = entities
        
        # Get a valid unit ID
        units_response = await test_client.get("/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        unit_id = units[0]["id"]
        
        # Create connections with decimal precision: A->B (1.1), B->C (2.2)
        connections_data = [
            {"from_entity_id": entity_a["id"], "to_entity_id": entity_b["id"], "multiplier": 1.1},
            {"from_entity_id": entity_b["id"], "to_entity_id": entity_c["id"], "multiplier": 2.2}
        ]
        
        for conn_data in connections_data:
            conn_data["unit_id"] = unit_id
            conn_response = await test_client.post("/api/v1/connections/", json=conn_data)
            assert conn_response.status_code == 201
        
        # Test comparison A->C (should be 1.1 * 2.2 = 2.42, rounded to 2.4)
        response = await test_client.get(f"/api/v1/compare/?from={entity_a['id']}&to={entity_c['id']}&unit={unit_id}")
        assert response.status_code == 200
        result = response.json()
        
        # Verify decimal precision handling
        assert result["from_entity"]["id"] == entity_a["id"]
        assert result["to_entity"]["id"] == entity_c["id"]
        assert result["unit"]["id"] == unit_id
        # The result should be properly rounded to 1 decimal place
        expected_multiplier = 1.1 * 2.2  # 2.42
        assert abs(float(result["multiplier"]) - expected_multiplier) < 0.01
        assert len(result["path"]) == 2
        assert result["error"] is None