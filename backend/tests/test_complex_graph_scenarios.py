"""
Comprehensive test suite for complex graph scenarios and edge cases.

This module tests:
- All graph topologies (star, chain, grid, complete, sparse)
- Edge cases (circular references, disconnected components, deep paths)
- Complex pathfinding scenarios
- Graph traversal stress tests
- Performance with large graphs
- Unit isolation and multi-unit scenarios
"""

import pytest
import pytest_asyncio
from decimal import Decimal
from typing import Dict, List, Any

from .fixtures.complex_graphs import ComplexGraphFixtures
from .conftest import generate_valid_entity_suffix


class TestStarTopology:
    """Test star topology graph scenarios."""
    
    @pytest.mark.asyncio
    async def test_star_hub_to_spoke_paths(self, test_client):
        """Test direct paths from hub to all spokes."""
        data = await ComplexGraphFixtures.create_star_graph(test_client, num_spokes=5)
        
        # Test hub to each spoke (direct connections)
        for i in range(5):
            spoke_letter = chr(65 + i)  # A, B, C, ...
            response = await test_client.get(
                "/api/v1/compare/",
                params={
                    "from": data["entities"]["Hub"],
                    "to": data["entities"][f"Spoke{spoke_letter}"],
                    "unit": data["unit_id"]
                }
            )
            assert response.status_code == 200
            result = response.json()
            assert len(result["path"]) == 1  # Direct connection
            assert float(result["multiplier"]) > 0
    
    @pytest.mark.asyncio
    async def test_star_spoke_to_spoke_paths(self, test_client):
        """Test paths between spokes (must go through hub)."""
        data = await ComplexGraphFixtures.create_star_graph(test_client, num_spokes=4)
        
        # Test spoke to spoke (2-hop path through hub)
        response = await test_client.get(
            "/api/v1/compare/",
            params={
                "from": data["entities"]["SpokeA"],
                "to": data["entities"]["SpokeB"],
                "unit": data["unit_id"]
            }
        )
        assert response.status_code == 200
        result = response.json()
        assert len(result["path"]) == 2  # Spoke0 -> Hub -> Spoke1
        # Multiplier should be (1/spokeA_multiplier) * spokeB_multiplier
        assert float(result["multiplier"]) > 0
    
    @pytest.mark.asyncio
    async def test_star_isolated_spokes(self, test_client):
        """Test that spokes are not directly connected to each other."""
        data = await ComplexGraphFixtures.create_star_graph(test_client, num_spokes=3)
        
        # Check that direct spoke-to-spoke connections don't exist
        # by looking at the path structure
        response = await test_client.get(
            "/api/v1/compare/",
            params={
                "from": data["entities"]["SpokeA"],
                "to": data["entities"]["SpokeC"],
                "unit": data["unit_id"]
            }
        )
        assert response.status_code == 200
        result = response.json()
        # Path must go through hub, so it's at least 2 hops
        assert len(result["path"]) >= 2


class TestChainTopology:
    """Test linear chain topology scenarios."""
    
    @pytest.mark.asyncio
    async def test_chain_end_to_end_path(self, test_client):
        """Test path from start to end of chain."""
        data = await ComplexGraphFixtures.create_chain_graph(test_client, length=6)
        
        response = await test_client.get(
            "/api/v1/compare/",
            params={
                "from": data["entities"]["NodeA"],
                "to": data["entities"]["NodeF"],
                "unit": data["unit_id"]
            }
        )
        assert response.status_code == 200
        result = response.json()
        assert len(result["path"]) == 5  # 5 connections for 6 nodes
        assert float(result["multiplier"]) > 0
    
    @pytest.mark.asyncio
    async def test_chain_intermediate_paths(self, test_client):
        """Test paths to intermediate nodes."""
        data = await ComplexGraphFixtures.create_chain_graph(test_client, length=5)
        
        # Test path to middle node
        response = await test_client.get(
            "/api/v1/compare/",
            params={
                "from": data["entities"]["NodeA"],
                "to": data["entities"]["NodeC"],
                "unit": data["unit_id"]
            }
        )
        assert response.status_code == 200
        result = response.json()
        assert len(result["path"]) == 2  # 2 connections
        assert float(result["multiplier"]) > 0
    
    @pytest.mark.asyncio
    async def test_chain_max_length_limit(self, test_client):
        """Test chain that exceeds max path length."""
        data = await ComplexGraphFixtures.create_chain_graph(test_client, length=9)
        
        # Test path that would be 8 hops (exceeds limit of 6)
        response = await test_client.get(
            "/api/v1/compare/",
            params={
                "from": data["entities"]["NodeA"],
                "to": data["entities"]["NodeI"],
                "unit": data["unit_id"]
            }
        )
        # Should fail due to max path length
        assert response.status_code == 404
        assert "no path" in response.json()["detail"].lower()
    
    @pytest.mark.asyncio
    async def test_chain_reverse_path(self, test_client):
        """Test reverse path in chain."""
        data = await ComplexGraphFixtures.create_chain_graph(test_client, length=4)
        
        # Test reverse path
        response = await test_client.get(
            "/api/v1/compare/",
            params={
                "from": data["entities"]["NodeD"],
                "to": data["entities"]["NodeA"],
                "unit": data["unit_id"]
            }
        )
        assert response.status_code == 200
        result = response.json()
        assert len(result["path"]) == 3  # 3 connections in reverse
        assert float(result["multiplier"]) > 0


class TestGridTopology:
    """Test grid topology scenarios."""
    
    @pytest.mark.asyncio
    async def test_grid_corner_to_corner_paths(self, test_client):
        """Test paths between opposite corners of grid."""
        data = await ComplexGraphFixtures.create_grid_graph(test_client, rows=3, cols=3)
        
        # Test top-left to bottom-right
        response = await test_client.get(
            "/api/v1/compare/",
            params={
                "from": data["entities"]["GridAA"],
                "to": data["entities"]["GridCC"],
                "unit": data["unit_id"]
            }
        )
        assert response.status_code == 200
        result = response.json()
        # Manhattan distance is 4 (2 right + 2 down)
        assert len(result["path"]) == 4
        assert float(result["multiplier"]) > 0
    
    @pytest.mark.asyncio
    async def test_grid_adjacent_paths(self, test_client):
        """Test paths between adjacent grid cells."""
        data = await ComplexGraphFixtures.create_grid_graph(test_client, rows=2, cols=2)
        
        # Test horizontal adjacency
        response = await test_client.get(
            "/api/v1/compare/",
            params={
                "from": data["entities"]["GridAA"],
                "to": data["entities"]["GridAB"],
                "unit": data["unit_id"]
            }
        )
        assert response.status_code == 200
        result = response.json()
        assert len(result["path"]) == 1  # Direct connection
        assert float(result["multiplier"]) > 0
    
    @pytest.mark.asyncio
    async def test_grid_multiple_paths(self, test_client):
        """Test that grid has multiple valid paths between distant points."""
        data = await ComplexGraphFixtures.create_grid_graph(test_client, rows=3, cols=3)
        
        # From GridAA to GridBB, there are multiple paths
        response = await test_client.get(
            "/api/v1/compare/",
            params={
                "from": data["entities"]["GridAA"],
                "to": data["entities"]["GridBB"],
                "unit": data["unit_id"]
            }
        )
        assert response.status_code == 200
        result = response.json()
        # Should find optimal path (may vary based on implementation)
        assert len(result["path"]) >= 2  # At least 2 connections
        assert float(result["multiplier"]) > 0


class TestCompleteGraphTopology:
    """Test complete graph topology scenarios."""
    
    @pytest.mark.asyncio
    async def test_complete_graph_direct_connections(self, test_client):
        """Test that all nodes are directly connected."""
        data = await ComplexGraphFixtures.create_complete_graph(test_client, num_nodes=4)
        
        # Test all possible pairs have direct connections
        node_letters = ['A', 'B', 'C', 'D']
        for i in range(4):
            for j in range(i + 1, 4):
                response = await test_client.get(
                    "/api/v1/compare/",
                    params={
                        "from": data["entities"][f"Complete{node_letters[i]}"],
                        "to": data["entities"][f"Complete{node_letters[j]}"],
                        "unit": data["unit_id"]
                    }
                )
                assert response.status_code == 200
                result = response.json()
                assert len(result["path"]) == 1  # Direct connection
                assert float(result["multiplier"]) > 0
    
    @pytest.mark.asyncio
    async def test_complete_graph_optimal_paths(self, test_client):
        """Test that complete graph always finds optimal (direct) paths."""
        data = await ComplexGraphFixtures.create_complete_graph(test_client, num_nodes=5)
        
        # In complete graph, all paths should be direct (length 1)
        response = await test_client.get(
            "/api/v1/compare/",
            params={
                "from": data["entities"]["CompleteA"],
                "to": data["entities"]["CompleteE"],
                "unit": data["unit_id"]
            }
        )
        assert response.status_code == 200
        result = response.json()
        assert len(result["path"]) == 1  # Direct connection
        assert float(result["multiplier"]) > 0


class TestDisconnectedGraphTopology:
    """Test disconnected graph scenarios."""
    
    @pytest.mark.asyncio
    async def test_disconnected_components_isolated(self, test_client):
        """Test that disconnected components have no paths between them."""
        data = await ComplexGraphFixtures.create_disconnected_graph(test_client, num_components=3, nodes_per_component=2)
        
        # Test that nodes from different components are not connected
        response = await test_client.get(
            "/api/v1/compare/",
            params={
                "from": data["entities"]["CompANodeA"],
                "to": data["entities"]["CompBNodeA"],
                "unit": data["unit_id"]
            }
        )
        assert response.status_code == 404
        assert "no path" in response.json()["detail"].lower()
    
    @pytest.mark.asyncio
    async def test_disconnected_within_component_paths(self, test_client):
        """Test paths within connected components."""
        data = await ComplexGraphFixtures.create_disconnected_graph(test_client, num_components=2, nodes_per_component=3)
        
        # Test path within first component
        response = await test_client.get(
            "/api/v1/compare/",
            params={
                "from": data["entities"]["CompANodeA"],
                "to": data["entities"]["CompANodeC"],
                "unit": data["unit_id"]
            }
        )
        assert response.status_code == 200
        result = response.json()
        assert len(result["path"]) == 2  # 2 connections for 3 nodes
        assert float(result["multiplier"]) > 0
    
    @pytest.mark.asyncio
    async def test_disconnected_all_component_pairs(self, test_client):
        """Test that all cross-component connections fail."""
        data = await ComplexGraphFixtures.create_disconnected_graph(test_client, num_components=3, nodes_per_component=2)
        
        # Test all cross-component pairs
        component_letters = ['A', 'B', 'C']
        component_pairs = [(0, 1), (0, 2), (1, 2)]
        for comp1, comp2 in component_pairs:
            response = await test_client.get(
                "/api/v1/compare/",
                params={
                    "from": data["entities"][f"Comp{component_letters[comp1]}NodeA"],
                    "to": data["entities"][f"Comp{component_letters[comp2]}NodeA"],
                    "unit": data["unit_id"]
                }
            )
            assert response.status_code == 404


class TestCyclicGraphTopology:
    """Test cyclic graph scenarios."""
    
    @pytest.mark.asyncio
    async def test_cyclic_graph_cycle_detection(self, test_client):
        """Test pathfinding in cyclic graphs."""
        data = await ComplexGraphFixtures.create_cyclic_graph(test_client, cycle_length=4)
        
        # Test path that could go around the cycle
        response = await test_client.get(
            "/api/v1/compare/",
            params={
                "from": data["entities"]["CycleA"],
                "to": data["entities"]["CycleC"],
                "unit": data["unit_id"]
            }
        )
        assert response.status_code == 200
        result = response.json()
        # Should find path (either direct or through cycle)
        assert len(result["path"]) >= 2  # At least 2 connections
        assert float(result["multiplier"]) > 0
    
    @pytest.mark.asyncio
    async def test_cyclic_graph_shortest_path(self, test_client):
        """Test that shortest path is found in cyclic graph."""
        data = await ComplexGraphFixtures.create_cyclic_graph(test_client, cycle_length=6)
        
        # Test path from CycleA to CycleD (can go either direction)
        response = await test_client.get(
            "/api/v1/compare/",
            params={
                "from": data["entities"]["CycleA"],
                "to": data["entities"]["CycleD"],
                "unit": data["unit_id"]
            }
        )
        assert response.status_code == 200
        result = response.json()
        # Should find optimal path (either 3 forward or 3 backward)
        assert len(result["path"]) == 3
        assert float(result["multiplier"]) > 0
    
    @pytest.mark.asyncio
    async def test_cyclic_graph_no_infinite_loops(self, test_client):
        """Test that pathfinding doesn't loop infinitely."""
        data = await ComplexGraphFixtures.create_cyclic_graph(test_client, cycle_length=5)
        
        # Test all possible paths in cycle
        cycle_letters = ['A', 'B', 'C', 'D', 'E']
        for i in range(5):
            for j in range(5):
                if i != j:
                    response = await test_client.get(
                        "/api/v1/compare/",
                        params={
                            "from": data["entities"][f"Cycle{cycle_letters[i]}"],
                            "to": data["entities"][f"Cycle{cycle_letters[j]}"],
                            "unit": data["unit_id"]
                        }
                    )
                    assert response.status_code == 200
                    result = response.json()
                    assert len(result["path"]) <= 4  # Never more than n-1 connections
                    assert float(result["multiplier"]) > 0


class TestDeepTreeTopology:
    """Test deep tree scenarios for max path length."""
    
    @pytest.mark.asyncio
    async def test_deep_tree_within_limit(self, test_client):
        """Test paths within max depth limit."""
        data = await ComplexGraphFixtures.create_deep_tree(test_client, depth=5, branching_factor=2)
        
        # Test path from root to deepest level (5 connections = within limit)
        response = await test_client.get(
            "/api/v1/compare/",
            params={
                "from": data["entities"]["Root"],
                "to": data["entities"]["LENA"],
                "unit": data["unit_id"]
            }
        )
        assert response.status_code == 200
        result = response.json()
        assert len(result["path"]) == 5  # 5 levels deep
        assert float(result["multiplier"]) > 0
    
    @pytest.mark.asyncio
    async def test_deep_tree_at_limit(self, test_client):
        """Test paths at max depth limit."""
        data = await ComplexGraphFixtures.create_deep_tree(test_client, depth=6, branching_factor=2)
        
        # Test path from root to deepest level (6 connections = at limit)
        response = await test_client.get(
            "/api/v1/compare/",
            params={
                "from": data["entities"]["Root"],
                "to": data["entities"]["LFNA"],
                "unit": data["unit_id"]
            }
        )
        assert response.status_code == 200
        result = response.json()
        assert len(result["path"]) == 6  # 6 levels deep
        assert float(result["multiplier"]) > 0
    
    @pytest.mark.asyncio
    async def test_deep_tree_beyond_limit(self, test_client):
        """Test paths beyond max depth limit."""
        data = await ComplexGraphFixtures.create_deep_tree(test_client, depth=7, branching_factor=2)
        
        # Test path from root to deepest level (7 connections = beyond limit)
        response = await test_client.get(
            "/api/v1/compare/",
            params={
                "from": data["entities"]["Root"],
                "to": data["entities"]["LGNA"],
                "unit": data["unit_id"]
            }
        )
        # Should fail due to max path length
        assert response.status_code == 404
        assert "no path" in response.json()["detail"].lower()
    
    @pytest.mark.asyncio
    async def test_deep_tree_sibling_paths(self, test_client):
        """Test paths between siblings in deep tree."""
        data = await ComplexGraphFixtures.create_deep_tree(test_client, depth=4, branching_factor=3)
        
        # Test path between siblings (must go through parent)
        response = await test_client.get(
            "/api/v1/compare/",
            params={
                "from": data["entities"]["LDNA"],
                "to": data["entities"]["LDNB"],
                "unit": data["unit_id"]
            }
        )
        assert response.status_code == 200
        result = response.json()
        # Path: LDNA -> LCNA -> ... -> Root -> ... -> LCNA -> LDNB (8 connections)
        # This exceeds max path length, so might fail
        # Accept either result based on implementation
        if response.status_code == 200:
            assert len(result["path"]) > 0
            assert float(result["multiplier"]) > 0


class TestMultiUnitGraphTopology:
    """Test multi-unit graph scenarios."""
    
    @pytest.mark.asyncio
    async def test_multi_unit_same_unit_paths(self, test_client):
        """Test paths within same unit type."""
        data = await ComplexGraphFixtures.create_multi_unit_graph(test_client)
        
        # Test length unit path
        if "length" in data["unit_ids"]:
            response = await test_client.get(
                "/api/v1/compare/",
                params={
                    "from": data["entities"]["MultiA"],
                    "to": data["entities"]["MultiC"],
                    "unit": data["unit_ids"]["length"]
                }
            )
            assert response.status_code == 200
            result = response.json()
            assert len(result["path"]) == 2  # MultiA -> MultiB -> MultiC
            assert float(result["multiplier"]) > 0
    
    @pytest.mark.asyncio
    async def test_multi_unit_different_unit_isolation(self, test_client):
        """Test that different units are isolated."""
        data = await ComplexGraphFixtures.create_multi_unit_graph(test_client)
        
        if "length" in data["unit_ids"] and "mass" in data["unit_ids"]:
            # Test that mass unit can't traverse length connections
            response = await test_client.get(
                "/api/v1/compare/",
                params={
                    "from": data["entities"]["MultiA"],
                    "to": data["entities"]["MultiC"],
                    "unit": data["unit_ids"]["mass"]
                }
            )
            assert response.status_code == 404
            assert "no path" in response.json()["detail"].lower()
    
    @pytest.mark.asyncio
    async def test_multi_unit_cross_unit_paths(self, test_client):
        """Test paths that exist in different units."""
        data = await ComplexGraphFixtures.create_multi_unit_graph(test_client)
        
        # Test separate paths in different units
        if "mass" in data["unit_ids"]:
            response = await test_client.get(
                "/api/v1/compare/",
                params={
                    "from": data["entities"]["MultiC"],
                    "to": data["entities"]["MultiE"],
                    "unit": data["unit_ids"]["mass"]
                }
            )
            assert response.status_code == 200
            result = response.json()
            assert len(result["path"]) == 2  # MultiC -> MultiD -> MultiE
            assert float(result["multiplier"]) > 0


class TestLargeScaleGraphTopology:
    """Test large-scale graph performance scenarios."""
    
    @pytest.mark.asyncio
    async def test_large_scale_graph_creation(self, test_client):
        """Test creation of large-scale graph."""
        data = await ComplexGraphFixtures.create_large_scale_graph(test_client, num_entities=20, connection_density=0.2)
        
        # Verify graph was created
        assert len(data["entities"]) == 20
        assert data["properties"]["num_entities"] == 20
        assert data["properties"]["connection_density"] == 0.2
        assert data["properties"]["performance_test"] is True
    
    @pytest.mark.asyncio
    async def test_large_scale_random_paths(self, test_client):
        """Test random paths in large-scale graph."""
        data = await ComplexGraphFixtures.create_large_scale_graph(test_client, num_entities=15, connection_density=0.3)
        
        # Test a few random paths
        import random
        entity_names = list(data["entities"].keys())
        
        for _ in range(3):  # Test 3 random pairs
            from_entity = random.choice(entity_names)
            to_entity = random.choice(entity_names)
            
            if from_entity != to_entity:
                response = await test_client.get(
                    "/api/v1/compare/",
                    params={
                        "from": data["entities"][from_entity],
                        "to": data["entities"][to_entity],
                        "unit": data["unit_id"]
                    }
                )
                # May or may not have path depending on connectivity
                if response.status_code == 200:
                    result = response.json()
                    assert len(result["path"]) > 0
                    assert float(result["multiplier"]) > 0
                else:
                    assert response.status_code == 404
    
    @pytest.mark.asyncio
    async def test_large_scale_performance_timing(self, test_client):
        """Test performance of pathfinding in large graph."""
        import time
        
        data = await ComplexGraphFixtures.create_large_scale_graph(test_client, num_entities=25, connection_density=0.15)
        
        # Time a pathfinding operation
        start_time = time.time()
        
        # Get first and 11th entity names from the data
        entity_names = list(data["entities"].keys())
        response = await test_client.get(
            "/api/v1/compare/",
            params={
                "from": data["entities"][entity_names[0]],
                "to": data["entities"][entity_names[10]] if len(entity_names) > 10 else data["entities"][entity_names[-1]],
                "unit": data["unit_id"]
            }
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Should complete within reasonable time (adjust threshold as needed)
        assert duration < 5.0  # 5 seconds max
        
        # Result may or may not exist depending on connectivity
        if response.status_code == 200:
            result = response.json()
            assert len(result["path"]) > 0
            assert float(result["multiplier"]) > 0


class TestEdgeCaseGraphTopology:
    """Test edge case scenarios."""
    
    @pytest.mark.asyncio
    async def test_edge_case_isolated_entity(self, test_client):
        """Test behavior with isolated entities."""
        data = await ComplexGraphFixtures.create_edge_case_graph(test_client)
        
        # Test path to isolated entity
        response = await test_client.get(
            "/api/v1/compare/",
            params={
                "from": data["entities"]["EdgeDecimalA"],
                "to": data["entities"]["EdgeSingle"],
                "unit": data["unit_id"]
            }
        )
        assert response.status_code == 404
        assert "no path" in response.json()["detail"].lower()
    
    @pytest.mark.asyncio
    async def test_edge_case_decimal_precision(self, test_client):
        """Test decimal precision handling."""
        data = await ComplexGraphFixtures.create_edge_case_graph(test_client)
        
        # Test path with very small multipliers
        response = await test_client.get(
            "/api/v1/compare/",
            params={
                "from": data["entities"]["EdgeDecimalA"],
                "to": data["entities"]["EdgeDecimalC"],
                "unit": data["unit_id"]
            }
        )
        assert response.status_code == 200
        result = response.json()
        assert len(result["path"]) == 2  # EdgeDecimalA -> EdgeDecimalB -> EdgeDecimalC
        # 0.1 * 0.1 = 0.01
        assert float(result["multiplier"]) == 0.01
    
    @pytest.mark.asyncio
    async def test_edge_case_extreme_multipliers(self, test_client):
        """Test handling of extreme multiplier values."""
        data = await ComplexGraphFixtures.create_edge_case_graph(test_client)
        
        # Test minimum multiplier
        response = await test_client.get(
            "/api/v1/compare/",
            params={
                "from": data["entities"]["EdgeMinMultiplier"],
                "to": data["entities"]["EdgeMaxMultiplier"],
                "unit": data["unit_id"]
            }
        )
        assert response.status_code == 200
        result = response.json()
        assert len(result["path"]) == 1  # Direct connection
        assert float(result["multiplier"]) == 0.1
        
        # Test reverse (maximum multiplier)
        response = await test_client.get(
            "/api/v1/compare/",
            params={
                "from": data["entities"]["EdgeMaxMultiplier"],
                "to": data["entities"]["EdgeMinMultiplier"],
                "unit": data["unit_id"]
            }
        )
        assert response.status_code == 200
        result = response.json()
        assert len(result["path"]) == 1  # Direct connection
        assert float(result["multiplier"]) == 999.9
    
    @pytest.mark.asyncio
    async def test_edge_case_self_comparison(self, test_client):
        """Test self-comparison edge case."""
        data = await ComplexGraphFixtures.create_edge_case_graph(test_client)
        
        # Test entity compared to itself
        response = await test_client.get(
            "/api/v1/compare/",
            params={
                "from": data["entities"]["EdgeZeroPath"],
                "to": data["entities"]["EdgeZeroPath"],
                "unit": data["unit_id"]
            }
        )
        assert response.status_code == 200
        result = response.json()
        assert len(result["path"]) == 0  # No path needed
        assert float(result["multiplier"]) == 1.0  # Identity


class TestComplexGraphInteractions:
    """Test complex interactions between different graph properties."""
    
    @pytest.mark.asyncio
    async def test_multiple_graph_types_comparison(self, test_client):
        """Test comparing paths across different graph structures."""
        # Create different graph types and compare their behavior
        star_data = await ComplexGraphFixtures.create_star_graph(test_client, num_spokes=3)
        chain_data = await ComplexGraphFixtures.create_chain_graph(test_client, length=4)
        
        # Both should be functional independently
        # Test star graph
        response = await test_client.get(
            "/api/v1/compare/",
            params={
                "from": star_data["entities"]["Hub"],
                "to": star_data["entities"]["SpokeA"],
                "unit": star_data["unit_id"]
            }
        )
        assert response.status_code == 200
        
        # Test chain graph
        response = await test_client.get(
            "/api/v1/compare/",
            params={
                "from": chain_data["entities"]["NodeA"],
                "to": chain_data["entities"]["NodeD"],
                "unit": chain_data["unit_id"]
            }
        )
        assert response.status_code == 200
    
    @pytest.mark.asyncio
    async def test_graph_stress_multiple_queries(self, test_client):
        """Test multiple simultaneous queries on complex graph."""
        data = await ComplexGraphFixtures.create_complete_graph(test_client, num_nodes=5)
        
        # Perform multiple queries to stress test
        queries = [
            (data["entities"]["CompleteA"], data["entities"]["CompleteB"]),
            (data["entities"]["CompleteB"], data["entities"]["CompleteC"]),
            (data["entities"]["CompleteC"], data["entities"]["CompleteD"]),
            (data["entities"]["CompleteD"], data["entities"]["CompleteE"]),
            (data["entities"]["CompleteE"], data["entities"]["CompleteA"]),
        ]
        
        for from_entity, to_entity in queries:
            response = await test_client.get(
                "/api/v1/compare/",
                params={
                    "from": from_entity,
                    "to": to_entity,
                    "unit": data["unit_id"]
                }
            )
            assert response.status_code == 200
            result = response.json()
            assert len(result["path"]) > 0
            assert float(result["multiplier"]) > 0
    
    @pytest.mark.asyncio
    async def test_graph_robustness_invalid_queries(self, test_client):
        """Test graph robustness with invalid queries."""
        data = await ComplexGraphFixtures.create_star_graph(test_client, num_spokes=3)
        
        # Test with non-existent entity IDs
        response = await test_client.get(
            "/api/v1/compare/",
            params={
                "from": 99999,
                "to": data["entities"]["Hub"],
                "unit": data["unit_id"]
            }
        )
        assert response.status_code == 404
        
        # Test with invalid unit ID
        response = await test_client.get(
            "/api/v1/compare/",
            params={
                "from": data["entities"]["Hub"],
                "to": data["entities"]["SpokeA"],
                "unit": 99999
            }
        )
        assert response.status_code == 404
    
    @pytest.mark.asyncio
    async def test_mixed_precision_calculations(self, test_client):
        """Test calculations with mixed precision values."""
        # Create a custom graph with mixed precision
        suffix = generate_valid_entity_suffix()
        
        entities = {}
        for name in ["MixedA", "MixedB", "MixedC", "MixedD"]:
            response = await test_client.post(
                "/api/v1/entities/",
                json={"name": f"{name} {suffix}"}
            )
            assert response.status_code == 201
            entities[name] = response.json()["id"]
        
        # Get unit
        units = await test_client.get("/api/v1/units/")
        unit_id = units.json()[0]["id"]
        
        # Create connections with different precision levels
        connections = [
            (entities["MixedA"], entities["MixedB"], 0.1),    # 1 decimal
            (entities["MixedB"], entities["MixedC"], 1.23),   # 2 decimals (rounded to 1)
            (entities["MixedC"], entities["MixedD"], 10.0),   # Whole number
        ]
        
        for from_id, to_id, multiplier in connections:
            await test_client.post(
                "/api/v1/connections/",
                json={
                    "from_entity_id": from_id,
                    "to_entity_id": to_id,
                    "unit_id": unit_id,
                    "multiplier": multiplier
                }
            )
        
        # Test full path calculation
        response = await test_client.get(
            "/api/v1/compare/",
            params={
                "from": entities["MixedA"],
                "to": entities["MixedD"],
                "unit": unit_id
            }
        )
        assert response.status_code == 200
        result = response.json()
        assert len(result["path"]) == 3
        # Expected: 0.1 * 1.2 * 10.0 = 1.2 (after rounding)
        expected_multiplier = 0.1 * 1.2 * 10.0
        assert abs(float(result["multiplier"]) - expected_multiplier) < 0.1