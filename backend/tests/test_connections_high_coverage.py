"""
High Coverage Tests for Connections Module - Focused Coverage Improvement

This module contains highly targeted tests designed to increase connections.py coverage
from 23% to 70%+ by covering specific missing code paths.

Missing Lines to Cover:
- 35-39: from_entity debug logging 
- 41-50: to_entity debug logging
- 52-188: Main connection creation logic paths
- 210-218: Connection listing with debug logging
- 230-233: Individual connection retrieval
- 247-300: Connection update logic
- 312-334: Connection deletion logic
"""

import pytest
import pytest_asyncio
from decimal import Decimal
from httpx import AsyncClient
from unittest.mock import patch, MagicMock
from sqlalchemy.exc import IntegrityError
import asyncio
import logging

from tests.conftest import create_test_entity_name, test_data_generator


class TestConnectionsHighCoverage:
    """High coverage tests targeting specific missing lines."""
    
    @pytest.mark.asyncio
    async def test_create_connection_entity_not_found_with_debug_logging(self, test_client: AsyncClient, test_data_isolation):
        """Test entity not found scenarios to trigger debug logging paths - covers lines 35-41 and 47-52."""
        # Create one valid entity for testing
        valid_entity_name = create_test_entity_name("ValidForDebug")
        valid_response = await test_client.post("/api/v1/entities/", json={"name": valid_entity_name})
        valid_entity_id = valid_response.json()["id"]
        
        # Test from_entity not found (covers lines 35-41)
        connection_data = {
            "from_entity_id": 99999,  # Non-existent
            "to_entity_id": valid_entity_id,
            "unit_id": 1,
            "multiplier": 2.0
        }
        
        response = await test_client.post("/api/v1/connections/", json=connection_data)
        assert response.status_code == 404
        assert "From entity not found" in response.json()["detail"]
        
        # Test to_entity not found (covers lines 47-52)
        connection_data = {
            "from_entity_id": valid_entity_id,
            "to_entity_id": 99999,  # Non-existent
            "unit_id": 1,
            "multiplier": 2.0
        }
        
        response = await test_client.post("/api/v1/connections/", json=connection_data)
        assert response.status_code == 404
        assert "To entity not found" in response.json()["detail"]
    
    @pytest.mark.asyncio
    async def test_create_connection_full_success_path(self, test_client: AsyncClient, test_data_isolation):
        """Test full connection creation success path to cover lines 52-156."""
        # Create test entities
        entity1_name = create_test_entity_name("SuccessA")
        entity2_name = create_test_entity_name("SuccessB")
        
        entity1_response = await test_client.post("/api/v1/entities/", json={"name": entity1_name})
        entity2_response = await test_client.post("/api/v1/entities/", json={"name": entity2_name})
        
        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]
        
        # Create connection - this covers the full success path
        connection_data = {
            "from_entity_id": entity1_id,
            "to_entity_id": entity2_id,
            "unit_id": 1,
            "multiplier": 2.5
        }
        
        response = await test_client.post("/api/v1/connections/", json=connection_data)
        assert response.status_code == 201
        
        # Verify connection was created
        connection = response.json()
        assert connection["from_entity_id"] == entity1_id
        assert connection["to_entity_id"] == entity2_id
        assert float(connection["multiplier"]) == 2.5
        
        # Verify inverse connection was created
        connections_response = await test_client.get("/api/v1/connections/")
        connections = connections_response.json()
        
        inverse_connection = next(
            (c for c in connections if c["from_entity_id"] == entity2_id and c["to_entity_id"] == entity1_id),
            None
        )
        assert inverse_connection is not None
        assert float(inverse_connection["multiplier"]) == 0.4  # 1/2.5 = 0.4
    
    @pytest.mark.asyncio
    async def test_create_connection_duplicate_update_path(self, test_client: AsyncClient, test_data_isolation):
        """Test connection duplicate/update path to cover lines 75-114."""
        # Create test entities
        entity1_name = create_test_entity_name("DuplicateA")
        entity2_name = create_test_entity_name("DuplicateB")
        
        entity1_response = await test_client.post("/api/v1/entities/", json={"name": entity1_name})
        entity2_response = await test_client.post("/api/v1/entities/", json={"name": entity2_name})
        
        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]
        
        # Create initial connection
        connection_data = {
            "from_entity_id": entity1_id,
            "to_entity_id": entity2_id,
            "unit_id": 1,
            "multiplier": 2.0
        }
        
        response1 = await test_client.post("/api/v1/connections/", json=connection_data)
        assert response1.status_code == 201
        
        # Create duplicate with different multiplier - should trigger update path
        connection_data["multiplier"] = 3.0
        response2 = await test_client.post("/api/v1/connections/", json=connection_data)
        assert response2.status_code == 201
        
        # Verify the multiplier was updated
        updated_connection = response2.json()
        assert float(updated_connection["multiplier"]) == 3.0
        
        # Verify only one connection pair exists
        connections_response = await test_client.get("/api/v1/connections/")
        connections = connections_response.json()
        
        relevant_connections = [
            c for c in connections 
            if (c["from_entity_id"] == entity1_id and c["to_entity_id"] == entity2_id) or
               (c["from_entity_id"] == entity2_id and c["to_entity_id"] == entity1_id)
        ]
        assert len(relevant_connections) == 2  # Primary + inverse
    
    @pytest.mark.asyncio
    async def test_get_connections_with_debug_logging(self, test_client: AsyncClient, test_data_isolation):
        """Test get connections to trigger debug logging - covers lines 210-218."""
        # Create some test connections first
        entity1_name = create_test_entity_name("LogTestA")
        entity2_name = create_test_entity_name("LogTestB")
        
        entity1_response = await test_client.post("/api/v1/entities/", json={"name": entity1_name})
        entity2_response = await test_client.post("/api/v1/entities/", json={"name": entity2_name})
        
        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]
        
        # Create a connection to have something to retrieve
        connection_data = {
            "from_entity_id": entity1_id,
            "to_entity_id": entity2_id,
            "unit_id": 1,
            "multiplier": 1.5
        }
        
        await test_client.post("/api/v1/connections/", json=connection_data)
        
        # Test get all connections (covers debug logging lines)
        response = await test_client.get("/api/v1/connections/")
        assert response.status_code == 200
        connections = response.json()
        assert len(connections) >= 2  # At least primary + inverse
        
        # Test with pagination parameters
        response = await test_client.get("/api/v1/connections/?skip=0&limit=10")
        assert response.status_code == 200
    
    @pytest.mark.asyncio
    async def test_get_single_connection_success_and_not_found(self, test_client: AsyncClient, test_data_isolation):
        """Test get single connection - covers lines 230-233."""
        # Create test entities and connection
        entity1_name = create_test_entity_name("SingleTestA")
        entity2_name = create_test_entity_name("SingleTestB")
        
        entity1_response = await test_client.post("/api/v1/entities/", json={"name": entity1_name})
        entity2_response = await test_client.post("/api/v1/entities/", json={"name": entity2_name})
        
        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]
        
        # Create connection
        connection_data = {
            "from_entity_id": entity1_id,
            "to_entity_id": entity2_id,
            "unit_id": 1,
            "multiplier": 2.0
        }
        
        create_response = await test_client.post("/api/v1/connections/", json=connection_data)
        connection_id = create_response.json()["id"]
        
        # Test successful retrieval
        get_response = await test_client.get(f"/api/v1/connections/{connection_id}")
        assert get_response.status_code == 200
        connection = get_response.json()
        assert connection["id"] == connection_id
        assert float(connection["multiplier"]) == 2.0
        
        # Test not found case (covers line 232-233)
        not_found_response = await test_client.get("/api/v1/connections/99999")
        assert not_found_response.status_code == 404
        assert "Connection not found" in not_found_response.json()["detail"]
    
    @pytest.mark.asyncio
    async def test_update_connection_full_path(self, test_client: AsyncClient, test_data_isolation):
        """Test connection update - covers lines 247-300."""
        # Create test entities and connection
        entity1_name = create_test_entity_name("UpdateTestA")
        entity2_name = create_test_entity_name("UpdateTestB")
        
        entity1_response = await test_client.post("/api/v1/entities/", json={"name": entity1_name})
        entity2_response = await test_client.post("/api/v1/entities/", json={"name": entity2_name})
        
        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]
        
        # Create connection
        connection_data = {
            "from_entity_id": entity1_id,
            "to_entity_id": entity2_id,
            "unit_id": 1,
            "multiplier": 2.0
        }
        
        create_response = await test_client.post("/api/v1/connections/", json=connection_data)
        connection_id = create_response.json()["id"]
        
        # Test successful update (covers lines 252-290)
        update_data = {"multiplier": 4.0}
        update_response = await test_client.put(f"/api/v1/connections/{connection_id}", json=update_data)
        assert update_response.status_code == 200
        updated_connection = update_response.json()
        assert float(updated_connection["multiplier"]) == 4.0
        
        # Verify inverse was also updated
        connections_response = await test_client.get("/api/v1/connections/")
        connections = connections_response.json()
        
        inverse_connection = next(
            (c for c in connections if c["from_entity_id"] == entity2_id and c["to_entity_id"] == entity1_id),
            None
        )
        assert inverse_connection is not None
        assert float(inverse_connection["multiplier"]) == 0.2  # 1/4.0 = 0.25, but capped at 0.2
        
        # Test update with None multiplier (covers lines 299-300)
        update_none_data = {"multiplier": None}
        update_none_response = await test_client.put(f"/api/v1/connections/{connection_id}", json=update_none_data)
        assert update_none_response.status_code == 200
        # Should return unchanged connection
        assert float(update_none_response.json()["multiplier"]) == 4.0
        
        # Test update not found (covers lines 247-249)
        not_found_update = await test_client.put("/api/v1/connections/99999", json={"multiplier": 5.0})
        assert not_found_update.status_code == 404
        assert "Connection not found" in not_found_update.json()["detail"]
    
    @pytest.mark.asyncio
    async def test_delete_connection_full_path(self, test_client: AsyncClient, test_data_isolation):
        """Test connection deletion - covers lines 312-334."""
        # Create test entities and connection
        entity1_name = create_test_entity_name("DeleteTestA")
        entity2_name = create_test_entity_name("DeleteTestB")
        
        entity1_response = await test_client.post("/api/v1/entities/", json={"name": entity1_name})
        entity2_response = await test_client.post("/api/v1/entities/", json={"name": entity2_name})
        
        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]
        
        # Create connection
        connection_data = {
            "from_entity_id": entity1_id,
            "to_entity_id": entity2_id,
            "unit_id": 1,
            "multiplier": 3.0
        }
        
        create_response = await test_client.post("/api/v1/connections/", json=connection_data)
        connection_id = create_response.json()["id"]
        
        # Test successful deletion (covers lines 316-334)
        delete_response = await test_client.delete(f"/api/v1/connections/{connection_id}")
        assert delete_response.status_code == 200
        assert "Connection and its inverse deleted successfully" in delete_response.json()["message"]
        
        # Verify connection is deleted
        get_response = await test_client.get(f"/api/v1/connections/{connection_id}")
        assert get_response.status_code == 404
        
        # Verify inverse is also deleted
        connections_response = await test_client.get("/api/v1/connections/")
        connections = connections_response.json()
        
        remaining_connections = [
            c for c in connections 
            if (c["from_entity_id"] == entity1_id and c["to_entity_id"] == entity2_id) or
               (c["from_entity_id"] == entity2_id and c["to_entity_id"] == entity1_id)
        ]
        assert len(remaining_connections) == 0
        
        # Test delete not found (covers lines 312-314)
        not_found_delete = await test_client.delete("/api/v1/connections/99999")
        assert not_found_delete.status_code == 404
        assert "Connection not found" in not_found_delete.json()["detail"]
    
    @pytest.mark.asyncio
    async def test_connection_decimal_precision_paths(self, test_client: AsyncClient, test_data_isolation):
        """Test decimal precision handling paths in connection creation."""
        # Test various decimal values to ensure rounding logic is covered
        test_multipliers = [1.1, 2.3, 5.7, 10.0, 0.2, 0.8]
        
        for i, multiplier in enumerate(test_multipliers):
            # Create unique entities for each test
            entity_a_name = create_test_entity_name(f"DecTest{i}A")
            entity_b_name = create_test_entity_name(f"DecTest{i}B")
            
            entity_a_response = await test_client.post("/api/v1/entities/", json={"name": entity_a_name})
            entity_b_response = await test_client.post("/api/v1/entities/", json={"name": entity_b_name})
            
            # Check if entity creation was successful
            if entity_a_response.status_code != 201 or entity_b_response.status_code != 201:
                continue  # Skip this iteration if entity creation failed
            
            entity_a_id = entity_a_response.json()["id"]
            entity_b_id = entity_b_response.json()["id"]
            
            connection_data = {
                "from_entity_id": entity_a_id,
                "to_entity_id": entity_b_id,
                "unit_id": 1,
                "multiplier": multiplier
            }
            
            response = await test_client.post("/api/v1/connections/", json=connection_data)
            assert response.status_code == 201
            
            # Verify the multiplier is properly handled
            connection = response.json()
            assert float(connection["multiplier"]) == round(multiplier, 1)
    
    @pytest.mark.asyncio
    async def test_connection_edge_cases_and_boundaries(self, test_client: AsyncClient, test_data_isolation):
        """Test edge cases and boundary conditions."""
        # Create test entities
        entity1_name = create_test_entity_name("EdgeA")
        entity2_name = create_test_entity_name("EdgeB")
        
        entity1_response = await test_client.post("/api/v1/entities/", json={"name": entity1_name})
        entity2_response = await test_client.post("/api/v1/entities/", json={"name": entity2_name})
        
        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]
        
        # Test minimum valid multiplier
        connection_data = {
            "from_entity_id": entity1_id,
            "to_entity_id": entity2_id,
            "unit_id": 1,
            "multiplier": 0.1  # Minimum allowed
        }
        
        response = await test_client.post("/api/v1/connections/", json=connection_data)
        assert response.status_code == 201
        
        # Test very large multiplier to ensure inverse is capped at 0.1
        entity3_name = create_test_entity_name("EdgeC")
        entity4_name = create_test_entity_name("EdgeD")
        
        entity3_response = await test_client.post("/api/v1/entities/", json={"name": entity3_name})
        entity4_response = await test_client.post("/api/v1/entities/", json={"name": entity4_name})
        
        entity3_id = entity3_response.json()["id"]
        entity4_id = entity4_response.json()["id"]
        
        connection_data = {
            "from_entity_id": entity3_id,
            "to_entity_id": entity4_id,
            "unit_id": 1,
            "multiplier": 100.0  # Very large multiplier
        }
        
        response = await test_client.post("/api/v1/connections/", json=connection_data)
        assert response.status_code == 201
        
        # Verify inverse is capped at minimum 0.1
        connections_response = await test_client.get("/api/v1/connections/")
        connections = connections_response.json()
        
        inverse_connection = next(
            (c for c in connections if c["from_entity_id"] == entity4_id and c["to_entity_id"] == entity3_id),
            None
        )
        assert inverse_connection is not None
        assert float(inverse_connection["multiplier"]) == 0.1  # Should be capped at minimum