"""
Concurrent operations and race condition test suite for SIMILE backend.
Tests database isolation, concurrent transaction handling, and race condition
prevention.
"""
import pytest
import asyncio
import logging
import time

from sqlalchemy import select

from src.database import async_session
from src.models import Entity, Connection, Unit
from .conftest import create_test_entity_name

# Configure logging for concurrent tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestConcurrentEntityOperations:
    """Test concurrent entity operations and race conditions."""

    @pytest.mark.asyncio
    async def test_concurrent_entity_creation_race_condition(self, test_client):
        """Test race condition prevention in concurrent entity creation."""
        base_name = create_test_entity_name("RaceCondition")

        # Create many concurrent tasks trying to create entities with same name
        async def create_entity_task(task_id: int):
            """Task to create an entity."""
            try:
                response = await test_client.post(
                    "/api/v1/entities/",
                    json={"name": base_name}
                )
                return {
                    "task_id": task_id,
                    "status_code": response.status_code,
                    "response": response.json() if response.status_code == 201 else None,
                    "error": response.json() if response.status_code != 201 else None
                }
            except Exception as e:
                return {
                    "task_id": task_id,
                    "status_code": 500,
                    "error": str(e)
                }

        # Run 20 concurrent creation attempts
        tasks = [create_entity_task(i) for i in range(20)]
        results = await asyncio.gather(*tasks)

        # Count successful creations
        successful_creations = [r for r in results if r["status_code"] == 201]
        failed_creations = [r for r in results if r["status_code"] != 201]

        # Only one should succeed
        assert len(successful_creations) == 1, f"Expected 1 successful creation, got {len(successful_creations)}"
        assert len(failed_creations) == 19, f"Expected 19 failed creations, got {len(failed_creations)}"

        # Verify database state
        async with async_session() as db:
            result = await db.execute(
                select(Entity).where(Entity.name.ilike(base_name))
            )
            entities = result.scalars().all()
            assert len(entities) == 1, f"Expected 1 entity in database, got {len(entities)}"

    @pytest.mark.asyncio
    async def test_concurrent_entity_updates(self, test_client):
        """Test concurrent entity updates and last-writer-wins behavior."""
        # Create initial entity
        initial_name = create_test_entity_name("ConcurrentUpdate")
        entity_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": initial_name}
        )
        assert entity_response.status_code == 201
        entity_id = entity_response.json()["id"]

        # Create concurrent update tasks
        async def update_entity_task(task_id: int):
            """Task to update entity name."""
            # Convert task_id to letter to avoid numeric characters
            task_letter = chr(65 + task_id)  # 0->A, 1->B, etc.
            new_name = create_test_entity_name(f"Updated{task_letter}")
            try:
                response = await test_client.put(
                    f"/api/v1/entities/{entity_id}",
                    json={"name": new_name}
                )
                return {
                    "task_id": task_id,
                    "new_name": new_name,
                    "status_code": response.status_code,
                    "response": response.json() if response.status_code == 200 else None
                }
            except Exception as e:
                return {
                    "task_id": task_id,
                    "new_name": new_name,
                    "status_code": 500,
                    "error": str(e)
                }

        # Run 10 concurrent update attempts
        tasks = [update_entity_task(i) for i in range(10)]
        results = await asyncio.gather(*tasks)

        # All updates should succeed (last writer wins)
        successful_updates = [r for r in results if r["status_code"] == 200]
        assert len(successful_updates) >= 1, "At least one update should succeed"

        # Verify final entity state
        final_response = await test_client.get(f"/api/v1/entities/{entity_id}")
        assert final_response.status_code == 200
        final_entity = final_response.json()

        # Final name should be one of the update names
        update_names = [r["new_name"] for r in successful_updates]
        assert final_entity["name"] in update_names

    @pytest.mark.asyncio
    async def test_concurrent_entity_read_consistency(self, test_client):
        """Test read consistency during concurrent operations."""
        # Create test entity
        entity_name = create_test_entity_name("ReadConsistency")
        entity_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity_name}
        )
        assert entity_response.status_code == 201
        entity_id = entity_response.json()["id"]

        # Create concurrent read tasks
        async def read_entity_task(task_id: int):
            """Task to read entity."""
            try:
                response = await test_client.get(f"/api/v1/entities/{entity_id}")
                return {
                    "task_id": task_id,
                    "status_code": response.status_code,
                    "entity_name": response.json()["name"] if response.status_code == 200 else None
                }
            except Exception as e:
                return {
                    "task_id": task_id,
                    "status_code": 500,
                    "error": str(e)
                }

        # Run many concurrent read attempts
        tasks = [read_entity_task(i) for i in range(50)]
        results = await asyncio.gather(*tasks)

        # All reads should succeed
        successful_reads = [r for r in results if r["status_code"] == 200]
        assert len(successful_reads) == 50, f"Expected 50 successful reads, got {len(successful_reads)}"

        # All reads should return the same entity name
        entity_names = [r["entity_name"] for r in successful_reads]
        assert all(name == entity_name for name in entity_names), "All reads should return consistent data"


class TestConcurrentConnectionOperations:
    """Test concurrent connection operations and race conditions."""

    @pytest.mark.asyncio
    async def test_concurrent_connection_creation_same_entities(self, test_client):
        """Test concurrent connection creation between same entities."""
        # Create test entities
        entity1_name = create_test_entity_name("ConcConnA")
        entity2_name = create_test_entity_name("ConcConnB")

        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity1_name}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity2_name}
        )

        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]

        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]

        # Create concurrent connection tasks with different multipliers
        async def create_connection_task(task_id: int, multiplier: float):
            """Task to create a connection."""
            try:
                response = await test_client.post(
                    "/api/v1/connections/",
                    json={
                        "from_entity_id": entity1_id,
                        "to_entity_id": entity2_id,
                        "unit_id": unit_id,
                        "multiplier": multiplier
                    }
                )
                return {
                    "task_id": task_id,
                    "multiplier": multiplier,
                    "status_code": response.status_code,
                    "response": response.json() if response.status_code == 201 else None
                }
            except Exception as e:
                return {
                    "task_id": task_id,
                    "multiplier": multiplier,
                    "status_code": 500,
                    "error": str(e)
                }

        # Run concurrent connection creation with different multipliers
        multipliers = [2.0, 3.0, 4.0, 5.0, 6.0]
        tasks = [create_connection_task(i, mult) for i, mult in enumerate(multipliers)]
        results = await asyncio.gather(*tasks)

        # All should succeed (system updates existing connection)
        successful_creations = [r for r in results if r["status_code"] == 201]
        assert len(successful_creations) == len(multipliers), "All connection creations should succeed"

        # Verify only one connection exists between entities
        connections_response = await test_client.get("/api/v1/connections/")
        connections = connections_response.json()

        forward_connections = [c for c in connections 
                             if c["from_entity_id"] == entity1_id and c["to_entity_id"] == entity2_id]
        inverse_connections = [c for c in connections 
                             if c["from_entity_id"] == entity2_id and c["to_entity_id"] == entity1_id]

        assert len(forward_connections) == 1, "Should have exactly one forward connection"
        assert len(inverse_connections) == 1, "Should have exactly one inverse connection"

        # Final multiplier should be one of the attempted values
        final_multiplier = float(forward_connections[0]["multiplier"])
        assert final_multiplier in multipliers, f"Final multiplier {final_multiplier} should be in {multipliers}"

    @pytest.mark.asyncio
    async def test_concurrent_connection_and_entity_deletion(self, test_client):
        """Test concurrent connection creation and entity deletion."""
        # Create test entities
        entity1_name = create_test_entity_name("ConcDelA")
        entity2_name = create_test_entity_name("ConcDelB")

        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity1_name}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity2_name}
        )

        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]

        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]

        # Create multiple concurrent scenarios
        async def create_connection_task():
            """Task to create connection."""
            try:
                response = await test_client.post(
                    "/api/v1/connections/",
                    json={
                        "from_entity_id": entity1_id,
                        "to_entity_id": entity2_id,
                        "unit_id": unit_id,
                        "multiplier": 2.0
                    }
                )
                return {"operation": "create_connection", "status_code": response.status_code}
            except Exception as e:
                return {"operation": "create_connection", "status_code": 500, "error": str(e)}

        async def delete_entity_task():
            """Task to delete entity."""
            try:
                response = await test_client.delete(f"/api/v1/entities/{entity1_id}")
                return {"operation": "delete_entity", "status_code": response.status_code}
            except Exception as e:
                return {"operation": "delete_entity", "status_code": 500, "error": str(e)}

        # Run concurrent operations multiple times
        for attempt in range(5):
            # Reset entities for each attempt
            if attempt > 0:
                # Recreate entities if they were deleted
                try:
                    entity1_response = await test_client.post(
                        "/api/v1/entities/",
                        json={"name": f"{entity1_name}_{attempt}"}
                    )
                    entity2_response = await test_client.post(
                        "/api/v1/entities/",
                        json={"name": f"{entity2_name}_{attempt}"}
                    )
                    entity1_id = entity1_response.json()["id"]
                    entity2_id = entity2_response.json()["id"]
                except:
                    continue

            # Run concurrent operations
            tasks = [create_connection_task(), delete_entity_task()]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Verify database consistency
            async with async_session() as db:
                # Check entity existence
                entity_result = await db.execute(select(Entity).where(Entity.id == entity1_id))
                entity = entity_result.scalar_one_or_none()

                # Check connection existence
                connection_result = await db.execute(
                    select(Connection).where(Connection.from_entity_id == entity1_id)
                )
                connections = connection_result.scalars().all()

                # Either entity exists (with or without connections) or entity doesn't exist (no connections)
                if entity is not None:
                    # Entity exists, connections may or may not exist
                    logger.info(f"Attempt {attempt}: Entity exists, {len(connections)} connections")
                else:
                    # Entity doesn't exist, no connections should exist
                    assert len(connections) == 0, f"Entity deleted but {len(connections)} connections remain"
                    logger.info(f"Attempt {attempt}: Entity deleted, no connections")

    @pytest.mark.asyncio
    async def test_concurrent_connection_inverse_consistency(self, test_client):
        """Test inverse connection consistency during concurrent operations."""
        # Create test entities
        entity1_name = create_test_entity_name("InverseConsA")
        entity2_name = create_test_entity_name("InverseConsB")

        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity1_name}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity2_name}
        )

        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]

        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]

        # Create concurrent bidirectional connections
        async def create_forward_connection():
            """Create forward connection."""
            response = await test_client.post(
                "/api/v1/connections/",
                json={
                    "from_entity_id": entity1_id,
                    "to_entity_id": entity2_id,
                    "unit_id": unit_id,
                    "multiplier": 2.0
                }
            )
            return {"direction": "forward", "status_code": response.status_code}

        async def create_reverse_connection():
            """Create reverse connection."""
            response = await test_client.post(
                "/api/v1/connections/",
                json={
                    "from_entity_id": entity2_id,
                    "to_entity_id": entity1_id,
                    "unit_id": unit_id,
                    "multiplier": 0.5
                }
            )
            return {"direction": "reverse", "status_code": response.status_code}

        # Run concurrent bidirectional creation
        tasks = [create_forward_connection(), create_reverse_connection()]
        results = await asyncio.gather(*tasks)

        # Both should succeed
        for result in results:
            assert result["status_code"] == 201, f"Connection creation failed: {result}"

        # Verify inverse consistency
        connections_response = await test_client.get("/api/v1/connections/")
        connections = connections_response.json()

        forward_conn = next((c for c in connections 
                           if c["from_entity_id"] == entity1_id and c["to_entity_id"] == entity2_id), None)
        reverse_conn = next((c for c in connections 
                           if c["from_entity_id"] == entity2_id and c["to_entity_id"] == entity1_id), None)

        assert forward_conn is not None, "Forward connection should exist"
        assert reverse_conn is not None, "Reverse connection should exist"

        # Check inverse relationship
        forward_mult = float(forward_conn["multiplier"])
        reverse_mult = float(reverse_conn["multiplier"])

        # They should be inverses of each other (within tolerance)
        expected_reverse = 1.0 / forward_mult
        assert abs(reverse_mult - expected_reverse) < 0.1, f"Inverse relationship broken: {forward_mult} vs {reverse_mult}"


class TestConcurrentComplexOperations:
    """Test concurrent complex operations involving multiple entities and connections."""

    @pytest.mark.asyncio
    async def test_concurrent_graph_creation(self, test_client):
        """Test concurrent creation of connection graphs."""
        # Create entities for graph
        # Convert numeric indices to letters to avoid numeric characters
        entity_names = [create_test_entity_name(f"Graph{chr(65 + i)}") for i in range(5)]
        entities = []

        for name in entity_names:
                        response = await test_client.post(
                "/api/v1/entities/",
                json={"name": name}
            )
        assert response.status_code == 201
        entities.append(response.json())

        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]

        # Define connections to create (star pattern)
        connections_to_create = [
            (entities[0]["id"], entities[1]["id"], 2.0),
            (entities[0]["id"], entities[2]["id"], 3.0),
            (entities[0]["id"], entities[3]["id"], 4.0),
            (entities[0]["id"], entities[4]["id"], 5.0),
            (entities[1]["id"], entities[2]["id"], 1.5),
            (entities[2]["id"], entities[3]["id"], 1.3),
            (entities[3]["id"], entities[4]["id"], 1.2),
        ]

        # Create connections concurrently
        async def create_connection_task(from_id, to_id, multiplier):
            """Task to create a connection."""
            try:
                response = await test_client.post(
                    "/api/v1/connections/",
                    json={
                        "from_entity_id": from_id,
                        "to_entity_id": to_id,
                        "unit_id": unit_id,
                        "multiplier": multiplier
                    }
                )
                return {
                    "from_id": from_id,
                    "to_id": to_id,
                    "multiplier": multiplier,
                    "status_code": response.status_code
                }
            except Exception as e:
                return {
                    "from_id": from_id,
                    "to_id": to_id,
                    "multiplier": multiplier,
                    "status_code": 500,
                    "error": str(e)
                }

        # Run all connection creations concurrently
        tasks = [create_connection_task(from_id, to_id, mult) 
                for from_id, to_id, mult in connections_to_create]
        results = await asyncio.gather(*tasks)

        # All should succeed
        successful_creations = [r for r in results if r["status_code"] == 201]
        assert len(successful_creations) == len(connections_to_create), "All connections should be created"

        # Verify graph structure
        connections_response = await test_client.get("/api/v1/connections/")
        connections = connections_response.json()

        # Should have twice as many connections (forward + inverse)
        expected_count = len(connections_to_create) * 2
        assert len(connections) == expected_count, f"Expected {expected_count} connections, got {len(connections)}"

        # Verify each connection has its inverse
        for from_id, to_id, multiplier in connections_to_create:
            forward = next((c for c in connections 
                          if c["from_entity_id"] == from_id and c["to_entity_id"] == to_id), None)
            inverse = next((c for c in connections 
                          if c["from_entity_id"] == to_id and c["to_entity_id"] == from_id), None)

            assert forward is not None, f"Forward connection {from_id}->{to_id} missing"
            assert inverse is not None, f"Inverse connection {to_id}->{from_id} missing"

            assert abs(float(forward["multiplier"]) - multiplier) < 0.1, "Forward multiplier mismatch"
            assert abs(float(inverse["multiplier"]) - (1.0 / multiplier)) < 0.1, "Inverse multiplier mismatch"

    @pytest.mark.asyncio
    async def test_concurrent_pathfinding_operations(self, test_client):
        """Test concurrent pathfinding operations during graph modifications."""
        # Create entities for pathfinding
        # Convert numeric indices to letters to avoid numeric characters
        entity_names = [create_test_entity_name(f"Path{chr(65 + i)}") for i in range(4)]
        entities = []

        for name in entity_names:
                        response = await test_client.post(
                "/api/v1/entities/",
                json={"name": name}
            )
        assert response.status_code == 201
        entities.append(response.json())

        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]

        # Create initial path A->B->C
        connection_ab = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entities[0]["id"],
                "to_entity_id": entities[1]["id"],
                "unit_id": unit_id,
                "multiplier": 2.0
            }
        )
        connection_bc = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entities[1]["id"],
                "to_entity_id": entities[2]["id"],
                "unit_id": unit_id,
                "multiplier": 3.0
            }
        )

        assert connection_ab.status_code == 201
        assert connection_bc.status_code == 201

        # Define concurrent operations
        async def pathfind_task(from_idx, to_idx, task_id):
            """Task to perform pathfinding."""
            try:
                response = await test_client.get(
                    f"/api/v1/compare/{entities[from_idx]['id']}/{entities[to_idx]['id']}?unit_id={unit_id}"
                )
                return {
                    "task_id": task_id,
                    "from_idx": from_idx,
                    "to_idx": to_idx,
                    "status_code": response.status_code,
                    "result": response.json() if response.status_code == 200 else None
                }
            except Exception as e:
                return {
                    "task_id": task_id,
                    "from_idx": from_idx,
                    "to_idx": to_idx,
                    "status_code": 500,
                    "error": str(e)
                }

        async def create_shortcut_task():
            """Task to create direct A->C connection."""
            try:
                response = await test_client.post(
                    "/api/v1/connections/",
                    json={
                        "from_entity_id": entities[0]["id"],
                        "to_entity_id": entities[2]["id"],
                        "unit_id": unit_id,
                        "multiplier": 10.0  # Different from A->B->C path (2*3=6)
                    }
                )
                return {
                    "operation": "create_shortcut",
                    "status_code": response.status_code
                }
            except Exception as e:
                return {
                    "operation": "create_shortcut",
                    "status_code": 500,
                    "error": str(e)
                }

        # Run concurrent pathfinding and graph modification
        tasks = [
            pathfind_task(0, 2, 1),  # A->C
            pathfind_task(0, 2, 2),  # A->C
            pathfind_task(2, 0, 3),  # C->A
            create_shortcut_task(),   # Create A->C direct
            pathfind_task(0, 2, 4),  # A->C after shortcut
            pathfind_task(0, 2, 5),  # A->C after shortcut
        ]

        results = await asyncio.gather(*tasks)

        # All pathfinding operations should succeed
        pathfind_results = [r for r in results if "from_idx" in r]
        successful_pathfinds = [r for r in pathfind_results if r["status_code"] == 200]

        assert len(successful_pathfinds) >= 4, "Most pathfinding operations should succeed"

        # Shortcut creation should succeed
        shortcut_results = [r for r in results if r.get("operation") == "create_shortcut"]
        assert len(shortcut_results) == 1
        assert shortcut_results[0]["status_code"] == 201

        # Verify final pathfinding uses direct connection
        final_path = await test_client.get(
            f"/api/v1/compare/{entities[0]['id']}/{entities[2]['id']}?unit_id={unit_id}"
        )
        assert final_path.status_code == 200
        final_result = final_path.json()

        # Should use direct connection (10.0) not indirect path (6.0)
        assert abs(float(final_result["multiplier"]) - 10.0) < 0.1, "Should use direct connection"


class TestTransactionIsolationLevels:
    """Test database isolation levels and concurrent transaction behavior."""

    @pytest.mark.asyncio
    async def test_read_committed_isolation(self, test_client):
        """Test read committed isolation level behavior."""
        # Create test entity
        entity_name = create_test_entity_name("IsolationTest")
        entity_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity_name}
        )
        assert entity_response.status_code == 201
        entity_id = entity_response.json()["id"]

        # Test isolation during concurrent reads and writes
        async def read_entity_continuously(duration: float, task_id: int):
            """Continuously read entity for given duration."""
            end_time = time.time() + duration
            read_count = 0
            consistent_reads = 0
            last_name = None

            while time.time() < end_time:
                try:
                    response = await test_client.get(f"/api/v1/entities/{entity_id}")
                    if response.status_code == 200:
                        current_name = response.json()["name"]
                        if last_name is None:
                            last_name = current_name
                        elif last_name == current_name:
                            consistent_reads += 1
                        else:
                            last_name = current_name
                        read_count += 1
                except:
                    pass

                await asyncio.sleep(0.01)  # Small delay

            return {
                "task_id": task_id,
                "read_count": read_count,
                "consistent_reads": consistent_reads,
                "final_name": last_name
            }

        async def update_entity_periodically(duration: float, task_id: int):
            """Periodically update entity name."""
            end_time = time.time() + duration
            update_count = 0

            while time.time() < end_time:
                try:
                    # Convert numeric values to letters to avoid numeric characters
                    task_letter = chr(65 + task_id)
                    count_letter = chr(65 + (update_count % 26))
                    new_name = create_test_entity_name(f"Updated{task_letter}{count_letter}")
                    response = await test_client.put(
                        f"/api/v1/entities/{entity_id}",
                        json={"name": new_name}
                    )
                    if response.status_code == 200:
                        update_count += 1
                except:
                    pass

                await asyncio.sleep(0.05)  # Update every 50ms

            return {
                "task_id": task_id,
                "update_count": update_count
            }

        # Run concurrent readers and writers
        duration = 1.0  # 1 second test
        tasks = [
            read_entity_continuously(duration, 1),
            read_entity_continuously(duration, 2),
            update_entity_periodically(duration, 1),
        ]

        results = await asyncio.gather(*tasks)

        # Verify isolation behavior
        read_results = [r for r in results if "read_count" in r]
        update_results = [r for r in results if "update_count" in r]

        # Readers should see consistent data
        for read_result in read_results:
            assert read_result["read_count"] > 0, "Should have performed reads"
            # In read committed isolation, we should see committed changes
            # but not necessarily all reads are consistent

        # Updates should succeed
        for update_result in update_results:
            assert update_result["update_count"] > 0, "Should have performed updates"

    @pytest.mark.asyncio
    async def test_phantom_read_prevention(self, test_client):
        """Test prevention of phantom reads in connection queries."""
        # Create test entities
        entity1_name = create_test_entity_name("PhantomA")
        entity2_name = create_test_entity_name("PhantomB")

        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity1_name}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity2_name}
        )

        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]

        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]

        # Create initial connection
        await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entity1_id,
                "to_entity_id": entity2_id,
                "unit_id": unit_id,
                "multiplier": 2.0
            }
        )

        # Test phantom read prevention
        async def count_connections_continuously(duration: float):
            """Continuously count connections."""
            end_time = time.time() + duration
            counts = []

            while time.time() < end_time:
                try:
                    response = await test_client.get("/api/v1/connections/")
                    if response.status_code == 200:
                        count = len(response.json())
                        counts.append(count)
                except:
                    pass

                await asyncio.sleep(0.01)

            return counts

        async def create_additional_connections(duration: float):
            """Create additional connections periodically."""
            end_time = time.time() + duration
            created_count = 0

            # Create more entities for additional connections
            for i in range(3):
                try:
                    # Convert numeric index to letter to avoid numeric characters
                    letter_index = chr(65 + i)
                    entity_name = create_test_entity_name(f"AdditionalPhantom{letter_index}")
                    entity_response = await test_client.post(
                        "/api/v1/entities/",
                        json={"name": entity_name}
                    )
                    if entity_response.status_code == 201:
                        new_entity_id = entity_response.json()["id"]

                        # Create connection from new entity to existing entity
                        conn_response = await test_client.post(
                            "/api/v1/connections/",
                            json={
                                "from_entity_id": new_entity_id,
                                "to_entity_id": entity1_id,
                                "unit_id": unit_id,
                                "multiplier": float(i + 3)
                            }
                        )
                        if conn_response.status_code == 201:
                            created_count += 1
                except:
                    pass

                await asyncio.sleep(0.1)

            return created_count

        # Run concurrent operations
        duration = 0.5  # 500ms test
        tasks = [
            count_connections_continuously(duration),
            create_additional_connections(duration),
        ]

        results = await asyncio.gather(*tasks)

        connection_counts = results[0]
        created_connections = results[1]

        # Should have created some additional connections
        assert created_connections > 0, "Should have created additional connections"

        # Connection counts should increase over time (phantom reads allowed in this case)
        assert len(connection_counts) > 0, "Should have recorded connection counts"

        # Verify final state
        final_connections = await test_client.get("/api/v1/connections/")
        final_count = len(final_connections.json())

        # Should have original connection (2 with inverse) + new connections (2 each with inverse)
        expected_min_count = 2 + (created_connections * 2)
        assert final_count >= expected_min_count, f"Expected at least {expected_min_count} connections, got {final_count}"
