"""
Complex graph test fixtures for comprehensive testing of pathfinding and entity relationships.

This module provides various graph topologies and edge cases for thorough testing of:
- Path finding with 6+ hop connections
- Multiple valid paths between entities
- Disconnected graph components
- Complex unit relationships
- Large-scale graph operations
- Edge cases (circular references, disconnected components, deep paths)
"""

import asyncio
import random
import string
from typing import Dict, List, Tuple, Optional, Any
from decimal import Decimal

from ..conftest import generate_valid_entity_suffix


class GraphTopology:
    """Base class for graph topology generators."""
    
    def __init__(self, suffix: Optional[str] = None):
        self.suffix = suffix or generate_valid_entity_suffix()
        self.entities: Dict[str, int] = {}
        self.connections: List[Tuple[str, str, float]] = []
        self.unit_id: Optional[int] = None
    
    async def create_entities(self, test_client, entity_names: List[str]) -> Dict[str, int]:
        """Create entities with unique names."""
        entities = {}
        for name in entity_names:
            response = await test_client.post(
                "/api/v1/entities/",
                json={"name": f"{name} {self.suffix}"}
            )
            assert response.status_code == 201, f"Failed to create entity {name}: {response.text}"
            entities[name] = response.json()["id"]
        return entities
    
    async def get_unit_id(self, test_client, unit_name: str = "length") -> int:
        """Get unit ID by name."""
        units = await test_client.get("/api/v1/units/")
        unit = next((u for u in units.json() if u["name"].lower() == unit_name.lower()), None)
        if not unit:
            raise ValueError(f"Unit '{unit_name}' not found")
        return unit["id"]
    
    async def create_connections(self, test_client, connections: List[Tuple[str, str, float]], unit_id: int):
        """Create connections between entities."""
        for from_name, to_name, multiplier in connections:
            await test_client.post(
                "/api/v1/connections/",
                json={
                    "from_entity_id": self.entities[from_name],
                    "to_entity_id": self.entities[to_name],
                    "unit_id": unit_id,
                    "multiplier": multiplier
                }
            )
    
    async def setup(self, test_client, unit_name: str = "length"):
        """Setup the complete graph."""
        raise NotImplementedError("Subclasses must implement setup method")


class StarTopology(GraphTopology):
    """Star graph topology with central hub connected to all other nodes."""
    
    def __init__(self, num_spokes: int = 5, suffix: Optional[str] = None):
        super().__init__(suffix)
        self.num_spokes = num_spokes
        self.hub_multipliers = [2.0, 3.0, 1.5, 4.0, 0.8][:num_spokes]
    
    async def setup(self, test_client, unit_name: str = "length"):
        """Create star topology: Hub connected to all spokes."""
        # Create hub + spoke entities with letters-only names
        spoke_letters = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J']
        entity_names = ["Hub"] + [f"Spoke{spoke_letters[i]}" for i in range(self.num_spokes)]
        self.entities = await self.create_entities(test_client, entity_names)
        self.unit_id = await self.get_unit_id(test_client, unit_name)
        
        # Create connections from hub to all spokes
        connections = []
        for i in range(self.num_spokes):
            multiplier = self.hub_multipliers[i]
            connections.append(("Hub", f"Spoke{spoke_letters[i]}", multiplier))
        
        await self.create_connections(test_client, connections, self.unit_id)
        
        return {
            "entities": self.entities,
            "unit_id": self.unit_id,
            "topology": "star",
            "properties": {
                "num_spokes": self.num_spokes,
                "hub_entity": "Hub",
                "spoke_entities": [f"Spoke{spoke_letters[i]}" for i in range(self.num_spokes)]
            }
        }


class ChainTopology(GraphTopology):
    """Linear chain topology for testing long paths."""
    
    def __init__(self, length: int = 8, suffix: Optional[str] = None):
        super().__init__(suffix)
        self.length = length
        self.multipliers = [2.0, 1.5, 3.0, 0.8, 2.5, 1.2, 4.0, 0.6][:length-1]
    
    async def setup(self, test_client, unit_name: str = "length"):
        """Create linear chain: NodeA -> NodeB -> ... -> NodeN."""
        node_letters = [chr(65 + i) for i in range(self.length)]  # A, B, C, ...
        entity_names = [f"Node{letter}" for letter in node_letters]
        self.entities = await self.create_entities(test_client, entity_names)
        self.unit_id = await self.get_unit_id(test_client, unit_name)
        
        # Create sequential connections
        connections = []
        for i in range(self.length - 1):
            multiplier = self.multipliers[i]
            connections.append((f"Node{node_letters[i]}", f"Node{node_letters[i+1]}", multiplier))
        
        await self.create_connections(test_client, connections, self.unit_id)
        
        return {
            "entities": self.entities,
            "unit_id": self.unit_id,
            "topology": "chain",
            "properties": {
                "length": self.length,
                "start_entity": f"Node{node_letters[0]}",
                "end_entity": f"Node{node_letters[self.length-1]}",
                "max_path_length": self.length - 1
            }
        }


class GridTopology(GraphTopology):
    """Grid topology for testing complex path finding."""
    
    def __init__(self, rows: int = 3, cols: int = 3, suffix: Optional[str] = None):
        super().__init__(suffix)
        self.rows = rows
        self.cols = cols
    
    async def setup(self, test_client, unit_name: str = "length"):
        """Create grid topology with horizontal and vertical connections."""
        # Generate grid names like GridAA, GridAB, GridBA, GridBB, etc.
        row_letters = [chr(65 + r) for r in range(self.rows)]  # A, B, C, ...
        col_letters = [chr(65 + c) for c in range(self.cols)]  # A, B, C, ...
        entity_names = [f"Grid{row}{col}" for row in row_letters for col in col_letters]
        self.entities = await self.create_entities(test_client, entity_names)
        self.unit_id = await self.get_unit_id(test_client, unit_name)
        
        connections = []
        
        # Horizontal connections (right)
        for r in range(self.rows):
            for c in range(self.cols - 1):
                multiplier = 1.0 + (r * 0.1) + (c * 0.05)  # Varying multipliers
                connections.append((f"Grid{row_letters[r]}{col_letters[c]}", f"Grid{row_letters[r]}{col_letters[c+1]}", multiplier))
        
        # Vertical connections (down)
        for r in range(self.rows - 1):
            for c in range(self.cols):
                multiplier = 1.0 + (r * 0.05) + (c * 0.1)  # Varying multipliers
                connections.append((f"Grid{row_letters[r]}{col_letters[c]}", f"Grid{row_letters[r+1]}{col_letters[c]}", multiplier))
        
        await self.create_connections(test_client, connections, self.unit_id)
        
        return {
            "entities": self.entities,
            "unit_id": self.unit_id,
            "topology": "grid",
            "properties": {
                "rows": self.rows,
                "cols": self.cols,
                "total_entities": self.rows * self.cols,
                "corner_entities": [f"Grid{row_letters[0]}{col_letters[0]}", f"Grid{row_letters[0]}{col_letters[self.cols-1]}", f"Grid{row_letters[self.rows-1]}{col_letters[0]}", f"Grid{row_letters[self.rows-1]}{col_letters[self.cols-1]}"]
            }
        }


class CompleteGraphTopology(GraphTopology):
    """Complete graph where every node is connected to every other node."""
    
    def __init__(self, num_nodes: int = 5, suffix: Optional[str] = None):
        super().__init__(suffix)
        self.num_nodes = num_nodes
    
    async def setup(self, test_client, unit_name: str = "length"):
        """Create complete graph with all possible connections."""
        node_letters = [chr(65 + i) for i in range(self.num_nodes)]  # A, B, C, ...
        entity_names = [f"Complete{letter}" for letter in node_letters]
        self.entities = await self.create_entities(test_client, entity_names)
        self.unit_id = await self.get_unit_id(test_client, unit_name)
        
        connections = []
        
        # Create all possible connections
        for i in range(self.num_nodes):
            for j in range(i + 1, self.num_nodes):
                # Use formula to create meaningful multipliers
                multiplier = 1.0 + (i * 0.2) + (j * 0.1)
                connections.append((f"Complete{node_letters[i]}", f"Complete{node_letters[j]}", multiplier))
        
        await self.create_connections(test_client, connections, self.unit_id)
        
        return {
            "entities": self.entities,
            "unit_id": self.unit_id,
            "topology": "complete",
            "properties": {
                "num_nodes": self.num_nodes,
                "num_connections": len(connections),
                "fully_connected": True
            }
        }


class DisconnectedGraphTopology(GraphTopology):
    """Disconnected graph with multiple components."""
    
    def __init__(self, num_components: int = 3, nodes_per_component: int = 3, suffix: Optional[str] = None):
        super().__init__(suffix)
        self.num_components = num_components
        self.nodes_per_component = nodes_per_component
    
    async def setup(self, test_client, unit_name: str = "length"):
        """Create disconnected graph with separate components."""
        entity_names = []
        comp_letters = [chr(65 + comp) for comp in range(self.num_components)]  # A, B, C, ...
        for comp in range(self.num_components):
            for node in range(self.nodes_per_component):
                node_letter = chr(65 + node)  # A, B, C, ...
                entity_names.append(f"Comp{comp_letters[comp]}Node{node_letter}")
        
        self.entities = await self.create_entities(test_client, entity_names)
        self.unit_id = await self.get_unit_id(test_client, unit_name)
        
        connections = []
        
        # Create connections within each component only
        for comp in range(self.num_components):
            for node in range(self.nodes_per_component - 1):
                multiplier = 1.0 + (comp * 0.3) + (node * 0.2)
                node_from = chr(65 + node)  # A, B, C, ...
                node_to = chr(65 + node + 1)  # B, C, D, ...
                connections.append((f"Comp{comp_letters[comp]}Node{node_from}", f"Comp{comp_letters[comp]}Node{node_to}", multiplier))
        
        await self.create_connections(test_client, connections, self.unit_id)
        
        return {
            "entities": self.entities,
            "unit_id": self.unit_id,
            "topology": "disconnected",
            "properties": {
                "num_components": self.num_components,
                "nodes_per_component": self.nodes_per_component,
                "total_entities": self.num_components * self.nodes_per_component,
                "components": [
                    [f"Comp{comp_letters[comp]}Node{chr(65 + node)}" for node in range(self.nodes_per_component)]
                    for comp in range(self.num_components)
                ]
            }
        }


class CyclicGraphTopology(GraphTopology):
    """Cyclic graph with circular references."""
    
    def __init__(self, cycle_length: int = 4, suffix: Optional[str] = None):
        super().__init__(suffix)
        self.cycle_length = cycle_length
    
    async def setup(self, test_client, unit_name: str = "length"):
        """Create cyclic graph: A -> B -> C -> ... -> A."""
        cycle_letters = [chr(65 + i) for i in range(self.cycle_length)]  # A, B, C, ...
        entity_names = [f"Cycle{letter}" for letter in cycle_letters]
        self.entities = await self.create_entities(test_client, entity_names)
        self.unit_id = await self.get_unit_id(test_client, unit_name)
        
        connections = []
        
        # Create cycle connections
        for i in range(self.cycle_length):
            next_i = (i + 1) % self.cycle_length
            multiplier = 1.2 + (i * 0.1)  # Varying multipliers
            connections.append((f"Cycle{cycle_letters[i]}", f"Cycle{cycle_letters[next_i]}", multiplier))
        
        await self.create_connections(test_client, connections, self.unit_id)
        
        return {
            "entities": self.entities,
            "unit_id": self.unit_id,
            "topology": "cyclic",
            "properties": {
                "cycle_length": self.cycle_length,
                "has_cycle": True,
                "cycle_entities": [f"Cycle{cycle_letters[i]}" for i in range(self.cycle_length)]
            }
        }


class DeepTreeTopology(GraphTopology):
    
    def _number_to_letters(self, num):
        """Convert a number to letter sequence (0->A, 1->B, ..., 25->Z, 26->AA, etc.)"""
        if num < 26:
            return chr(65 + num)  # A, B, C, ..., Z
        else:
            # For numbers >= 26, use double letters: AA, AB, AC, ...
            first = chr(65 + (num // 26) - 1)
            second = chr(65 + (num % 26))
            return first + second
    """Deep tree topology for testing maximum path length limits."""
    
    def __init__(self, depth: int = 7, branching_factor: int = 2, suffix: Optional[str] = None):
        super().__init__(suffix)
        self.depth = depth
        self.branching_factor = branching_factor
    
    async def setup(self, test_client, unit_name: str = "length"):
        """Create deep tree with specified depth and branching factor."""
        entity_names = ["Root"]
        
        # Generate entity names for all levels with letters
        for level in range(1, self.depth + 1):
            nodes_at_level = self.branching_factor ** level
            for node in range(nodes_at_level):
                # Convert node number to letter sequence (A, B, C, ..., Z, AA, AB, ...)
                node_name = self._number_to_letters(node)
                entity_names.append(f"L{level}N{node_name}")
        
        self.entities = await self.create_entities(test_client, entity_names)
        self.unit_id = await self.get_unit_id(test_client, unit_name)
        
        connections = []
        
        # Connect root to level 1
        for node in range(self.branching_factor):
            multiplier = 1.0 + (node * 0.1)
            node_name = self._number_to_letters(node)
            connections.append(("Root", f"L1N{node_name}", multiplier))
        
        # Connect each level to the next
        for level in range(1, self.depth):
            nodes_current_level = self.branching_factor ** level
            for node in range(nodes_current_level):
                # Each node connects to branching_factor nodes in next level
                for child in range(self.branching_factor):
                    child_node = node * self.branching_factor + child
                    multiplier = 1.0 + (level * 0.1) + (child * 0.05)
                    node_name = self._number_to_letters(node)
                    child_name = self._number_to_letters(child_node)
                    connections.append((f"L{level}N{node_name}", f"L{level+1}N{child_name}", multiplier))
        
        await self.create_connections(test_client, connections, self.unit_id)
        
        return {
            "entities": self.entities,
            "unit_id": self.unit_id,
            "topology": "deep_tree",
            "properties": {
                "depth": self.depth,
                "branching_factor": self.branching_factor,
                "total_entities": len(entity_names),
                "deepest_entities": [f"L{self.depth}N{self._number_to_letters(i)}" for i in range(self.branching_factor ** self.depth)]
            }
        }


class MultiUnitGraphTopology(GraphTopology):
    """Graph with multiple units to test unit isolation."""
    
    def __init__(self, suffix: Optional[str] = None):
        super().__init__(suffix)
        self.unit_ids = {}
    
    async def setup(self, test_client, unit_name: str = "length"):
        """Create multi-unit graph with different unit types."""
        entity_names = ["MultiA", "MultiB", "MultiC", "MultiD", "MultiE"]
        self.entities = await self.create_entities(test_client, entity_names)
        
        # Get multiple unit IDs
        units = await test_client.get("/api/v1/units/")
        units_data = units.json()
        
        for unit in units_data:
            self.unit_ids[unit["name"].lower()] = unit["id"]
        
        # Create connections with different units
        connections_by_unit = {
            "length": [("MultiA", "MultiB", 2.0), ("MultiB", "MultiC", 1.5)],
            "mass": [("MultiC", "MultiD", 3.0), ("MultiD", "MultiE", 0.8)],
            "time": [("MultiA", "MultiE", 4.0)]  # Cross-connection
        }
        
        for unit_name, connections in connections_by_unit.items():
            if unit_name in self.unit_ids:
                await self.create_connections(test_client, connections, self.unit_ids[unit_name])
        
        return {
            "entities": self.entities,
            "unit_ids": self.unit_ids,
            "topology": "multi_unit",
            "properties": {
                "units_used": list(self.unit_ids.keys()),
                "length_path": ["MultiA", "MultiB", "MultiC"],
                "mass_path": ["MultiC", "MultiD", "MultiE"],
                "isolated_paths": True
            }
        }


class LargeScaleGraphTopology(GraphTopology):
    """Large-scale graph for performance testing."""
    
    def __init__(self, num_entities: int = 50, connection_density: float = 0.1, suffix: Optional[str] = None):
        super().__init__(suffix)
        self.num_entities = num_entities
        self.connection_density = connection_density
    
    async def setup(self, test_client, unit_name: str = "length"):
        """Create large-scale graph with specified density."""
        # Generate letter-based names for large scale
        entity_names = []
        for i in range(self.num_entities):
            # Use a combination approach for readability
            prefix_num = i // 26
            suffix_letter = chr(65 + (i % 26))
            if prefix_num == 0:
                entity_names.append(f"Large{suffix_letter}")
            else:
                prefix_letter = chr(65 + (prefix_num - 1))
                entity_names.append(f"Large{prefix_letter}{suffix_letter}")
        self.entities = await self.create_entities(test_client, entity_names)
        self.unit_id = await self.get_unit_id(test_client, unit_name)
        
        connections = []
        
        # Calculate number of connections based on density
        max_connections = self.num_entities * (self.num_entities - 1) // 2
        num_connections = int(max_connections * self.connection_density)
        
        # Generate random connections
        random.seed(42)  # For reproducible results
        all_pairs = [(i, j) for i in range(self.num_entities) for j in range(i + 1, self.num_entities)]
        selected_pairs = random.sample(all_pairs, min(num_connections, len(all_pairs)))
        
        for i, j in selected_pairs:
            multiplier = 1.0 + random.uniform(-0.5, 2.0)  # Random multipliers
            multiplier = max(0.1, round(multiplier, 1))  # Ensure positive and 1 decimal place
            connections.append((entity_names[i], entity_names[j], multiplier))
        
        await self.create_connections(test_client, connections, self.unit_id)
        
        return {
            "entities": self.entities,
            "unit_id": self.unit_id,
            "topology": "large_scale",
            "properties": {
                "num_entities": self.num_entities,
                "num_connections": len(connections),
                "connection_density": self.connection_density,
                "performance_test": True
            }
        }


class EdgeCaseGraphTopology(GraphTopology):
    """Special edge cases for comprehensive testing."""
    
    def __init__(self, suffix: Optional[str] = None):
        super().__init__(suffix)
    
    async def setup(self, test_client, unit_name: str = "length"):
        """Create various edge case scenarios."""
        entity_names = [
            "EdgeSingle",        # Isolated entity
            "EdgeDecimalA",      # Decimal precision tests
            "EdgeDecimalB",
            "EdgeDecimalC",
            "EdgeMinMultiplier", # Minimum multiplier (0.1)
            "EdgeMaxMultiplier", # Maximum multiplier (999.9)
            "EdgeZeroPath",      # For zero-length path tests
        ]
        
        self.entities = await self.create_entities(test_client, entity_names)
        self.unit_id = await self.get_unit_id(test_client, unit_name)
        
        connections = [
            # Decimal precision chain
            ("EdgeDecimalA", "EdgeDecimalB", 0.1),
            ("EdgeDecimalB", "EdgeDecimalC", 0.1),
            
            # Extreme multipliers
            ("EdgeMinMultiplier", "EdgeMaxMultiplier", 0.1),
            ("EdgeMaxMultiplier", "EdgeMinMultiplier", 999.9),
            
            # Self-loops are prevented by constraints, but test near-loops
            ("EdgeZeroPath", "EdgeDecimalA", 1.0),
        ]
        
        await self.create_connections(test_client, connections, self.unit_id)
        
        return {
            "entities": self.entities,
            "unit_id": self.unit_id,
            "topology": "edge_cases",
            "properties": {
                "isolated_entity": "EdgeSingle",
                "decimal_precision_chain": ["EdgeDecimalA", "EdgeDecimalB", "EdgeDecimalC"],
                "extreme_multipliers": ["EdgeMinMultiplier", "EdgeMaxMultiplier"],
                "edge_test_scenarios": True
            }
        }


class ComplexGraphFixtures:
    """Main class for managing complex graph fixtures."""
    
    @staticmethod
    async def create_star_graph(test_client, num_spokes: int = 5, unit_name: str = "length", suffix: Optional[str] = None):
        """Create star topology."""
        topology = StarTopology(num_spokes, suffix)
        return await topology.setup(test_client, unit_name)
    
    @staticmethod
    async def create_chain_graph(test_client, length: int = 8, unit_name: str = "length", suffix: Optional[str] = None):
        """Create chain topology."""
        topology = ChainTopology(length, suffix)
        return await topology.setup(test_client, unit_name)
    
    @staticmethod
    async def create_grid_graph(test_client, rows: int = 3, cols: int = 3, unit_name: str = "length", suffix: Optional[str] = None):
        """Create grid topology."""
        topology = GridTopology(rows, cols, suffix)
        return await topology.setup(test_client, unit_name)
    
    @staticmethod
    async def create_complete_graph(test_client, num_nodes: int = 5, unit_name: str = "length", suffix: Optional[str] = None):
        """Create complete graph."""
        topology = CompleteGraphTopology(num_nodes, suffix)
        return await topology.setup(test_client, unit_name)
    
    @staticmethod
    async def create_disconnected_graph(test_client, num_components: int = 3, nodes_per_component: int = 3, unit_name: str = "length", suffix: Optional[str] = None):
        """Create disconnected graph."""
        topology = DisconnectedGraphTopology(num_components, nodes_per_component, suffix)
        return await topology.setup(test_client, unit_name)
    
    @staticmethod
    async def create_cyclic_graph(test_client, cycle_length: int = 4, unit_name: str = "length", suffix: Optional[str] = None):
        """Create cyclic graph."""
        topology = CyclicGraphTopology(cycle_length, suffix)
        return await topology.setup(test_client, unit_name)
    
    @staticmethod
    async def create_deep_tree(test_client, depth: int = 7, branching_factor: int = 2, unit_name: str = "length", suffix: Optional[str] = None):
        """Create deep tree topology."""
        topology = DeepTreeTopology(depth, branching_factor, suffix)
        return await topology.setup(test_client, unit_name)
    
    @staticmethod
    async def create_multi_unit_graph(test_client, suffix: Optional[str] = None):
        """Create multi-unit graph."""
        topology = MultiUnitGraphTopology(suffix)
        return await topology.setup(test_client)
    
    @staticmethod
    async def create_large_scale_graph(test_client, num_entities: int = 50, connection_density: float = 0.1, unit_name: str = "length", suffix: Optional[str] = None):
        """Create large-scale graph."""
        topology = LargeScaleGraphTopology(num_entities, connection_density, suffix)
        return await topology.setup(test_client, unit_name)
    
    @staticmethod
    async def create_edge_case_graph(test_client, unit_name: str = "length", suffix: Optional[str] = None):
        """Create edge case graph."""
        topology = EdgeCaseGraphTopology(suffix)
        return await topology.setup(test_client, unit_name)


# Export for easy imports
__all__ = [
    'ComplexGraphFixtures',
    'StarTopology',
    'ChainTopology',
    'GridTopology',
    'CompleteGraphTopology',
    'DisconnectedGraphTopology',
    'CyclicGraphTopology',
    'DeepTreeTopology',
    'MultiUnitGraphTopology',
    'LargeScaleGraphTopology',
    'EdgeCaseGraphTopology'
]