"""
Phase 2 verification test: Database connectivity and core CRUD operations working.
"""
import pytest
from .conftest import generate_valid_entity_suffix


class TestPhase2Verification:
    """Verify Phase 2 fixes: database connectivity and entity CRUD operations."""
    
    @pytest.mark.asyncio
    async def test_units_endpoint_working(self, test_client):
        """Test that units endpoint works (read-only operation)."""
        response = await test_client.get("/api/v1/units/")
        assert response.status_code == 200
        units = response.json()
        assert len(units) > 0
        unit_names = [u["name"] for u in units]
        assert "Length" in unit_names
        assert "Mass" in unit_names
    
    @pytest.mark.asyncio
    async def test_entity_creation_success(self, test_client):
        """Test successful entity creation with unique name."""
        # Use letters-only suffix to ensure uniqueness
        suffix = generate_valid_entity_suffix()
        unique_name = f"PhaseTest{suffix}"
        
        response = await test_client.post(
            "/api/v1/entities/",
            json={"name": unique_name}
        )
        
        if response.status_code != 201:
            print(f"Error: {response.status_code} - {response.text}")
        
        assert response.status_code == 201
        entity = response.json()
        assert entity["name"] == unique_name
        assert "id" in entity
        assert "created_at" in entity
    
    @pytest.mark.asyncio
    async def test_entity_duplicate_detection(self, test_client):
        """Test that duplicate entity detection works."""
        suffix = generate_valid_entity_suffix()
        unique_name = f"DuplicateTest{suffix}"
        
        # Create first entity
        response1 = await test_client.post(
            "/api/v1/entities/",
            json={"name": unique_name}
        )
        assert response1.status_code == 201
        
        # Try to create duplicate
        response2 = await test_client.post(
            "/api/v1/entities/",
            json={"name": unique_name}
        )
        assert response2.status_code == 400
        assert "already exists" in response2.text.lower()
    
    @pytest.mark.asyncio
    async def test_entity_list_retrieval(self, test_client):
        """Test entity list retrieval."""
        response = await test_client.get("/api/v1/entities/")
        assert response.status_code == 200
        entities = response.json()
        assert isinstance(entities, list)
    
    @pytest.mark.asyncio
    async def test_entity_validation_errors(self, test_client):
        """Test entity validation works correctly."""
        # Test empty name
        response1 = await test_client.post(
            "/api/v1/entities/",
            json={"name": ""}
        )
        assert response1.status_code == 422
        
        # Test name too long (over 100 chars)
        long_name = "a" * 101
        response2 = await test_client.post(
            "/api/v1/entities/",
            json={"name": long_name}
        )
        assert response2.status_code == 422
        
        # Test invalid characters
        response3 = await test_client.post(
            "/api/v1/entities/",
            json={"name": "Invalid@Entity!"}
        )
        assert response3.status_code == 422