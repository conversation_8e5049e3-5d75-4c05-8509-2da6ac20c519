"""
Comprehensive performance benchmarking test suite for SIMILE backend.
Tests API response times, database performance, pathfinding efficiency, and resource usage.
"""
import pytest
import pytest_asyncio
import asyncio
import time
import psutil
import logging
import random
import string
from decimal import Decimal
from typing import List, Dict, Any, Optional, Tuple
from concurrent.futures import Thread<PERSON>oolExecutor
from contextlib import contextmanager
from dataclasses import dataclass
from statistics import mean, median, stdev

from sqlalchemy import text, select, and_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import IntegrityError

from src.database import async_session
from src.models import Entity, Connection, Unit
from src.services import find_shortest_path
from .conftest import create_test_entity_name

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetrics:
    """Container for performance measurement results."""
    response_times: List[float]
    memory_usage: List[float]
    cpu_usage: List[float]
    database_queries: int
    success_rate: float
    
    @property
    def avg_response_time(self) -> float:
        if not self.response_times:
            return 0.0
        # Filter out infinite values
        valid_times = [t for t in self.response_times if t != float('inf')]
        return mean(valid_times) if valid_times else 0.0
    
    @property
    def median_response_time(self) -> float:
        if not self.response_times:
            return 0.0
        # Filter out infinite values
        valid_times = [t for t in self.response_times if t != float('inf')]
        return median(valid_times) if valid_times else 0.0
    
    @property
    def p95_response_time(self) -> float:
        if not self.response_times:
            return 0.0
        # Filter out infinite values
        valid_times = [t for t in self.response_times if t != float('inf')]
        if not valid_times:
            return 0.0
        sorted_times = sorted(valid_times)
        index = int(0.95 * len(sorted_times))
        return sorted_times[index]
    
    @property
    def avg_memory_usage(self) -> float:
        return mean(self.memory_usage) if self.memory_usage else 0.0
    
    @property
    def avg_cpu_usage(self) -> float:
        return mean(self.cpu_usage) if self.cpu_usage else 0.0


class PerformanceTestMixin:
    """Mixin class providing performance testing utilities."""
    
    @contextmanager
    def performance_monitor(self):
        """Context manager for monitoring performance metrics."""
        process = psutil.Process()
        
        # Initial measurements
        start_time = time.time()
        start_memory = process.memory_info().rss / 1024 / 1024  # MB
        start_cpu = process.cpu_percent()
        
        yield
        
        # Final measurements
        end_time = time.time()
        end_memory = process.memory_info().rss / 1024 / 1024  # MB
        end_cpu = process.cpu_percent()
        
        # Store metrics for later analysis
        self.last_response_time = end_time - start_time
        self.last_memory_usage = end_memory - start_memory
        self.last_cpu_usage = end_cpu - start_cpu
    
    async def measure_endpoint_performance(
        self, 
        test_client, 
        method: str, 
        endpoint: str, 
        iterations: int = 10,
        payload: Optional[Dict] = None,
        params: Optional[Dict] = None,
        payload_generator: Optional[callable] = None
    ) -> PerformanceMetrics:
        """Measure performance of an API endpoint."""
        response_times = []
        memory_usage = []
        cpu_usage = []
        successful_requests = 0
        
        process = psutil.Process()
        
        for i in range(iterations):
            # Measure resource usage before request
            start_memory = process.memory_info().rss / 1024 / 1024
            start_cpu = process.cpu_percent()
            start_time = time.time()
            
            try:
                # Generate unique payload if generator is provided
                current_payload = payload
                if payload_generator:
                    current_payload = payload_generator()
                
                # Make request
                if method.upper() == "GET":
                    response = await test_client.get(endpoint, params=params)
                elif method.upper() == "POST":
                    response = await test_client.post(endpoint, json=current_payload)
                elif method.upper() == "PUT":
                    response = await test_client.put(endpoint, json=current_payload)
                elif method.upper() == "DELETE":
                    response = await test_client.delete(endpoint)
                else:
                    raise ValueError(f"Unsupported method: {method}")
                
                # Measure response time
                end_time = time.time()
                response_times.append((end_time - start_time) * 1000)  # Convert to ms
                
                # Measure resource usage after request
                end_memory = process.memory_info().rss / 1024 / 1024
                end_cpu = process.cpu_percent()
                
                memory_usage.append(end_memory - start_memory)
                cpu_usage.append(end_cpu - start_cpu)
                
                # Check if request was successful
                if 200 <= response.status_code < 300:
                    successful_requests += 1
                    
            except Exception as e:
                logger.error(f"Request failed on iteration {i}: {e}")
                response_times.append(float('inf'))
                memory_usage.append(0)
                cpu_usage.append(0)
        
        success_rate = successful_requests / iterations
        
        return PerformanceMetrics(
            response_times=response_times,
            memory_usage=memory_usage,
            cpu_usage=cpu_usage,
            database_queries=0,  # Would need query counting implementation
            success_rate=success_rate
        )
    
    async def create_performance_test_data(
        self, 
        test_client, 
        entity_count: int = 50,
        connection_density: float = 0.3
    ) -> Dict[str, Any]:
        """Create test data for performance testing."""
        # Get units
        units_response = await test_client.get("/api/v1/units/")
        units = units_response.json()
        
        # Create entities with unique names
        entities = []
        for i in range(entity_count):
            name = create_test_entity_name("PerfEntity")
            response = await test_client.post(
                "/api/v1/entities/",
                json={"name": name}
            )
            if response.status_code == 201:
                entities.append(response.json())
            else:
                # Log error but continue
                logger.warning(f"Failed to create entity {i}: {response.status_code}")
        
        # Create connections based on density
        connections = []
        if len(entities) > 1:  # Only create connections if we have multiple entities
            connection_count = int(len(entities) * (len(entities) - 1) * connection_density / 2)
            
            for _ in range(connection_count):
                from_entity = random.choice(entities)
                to_entity = random.choice([e for e in entities if e["id"] != from_entity["id"]])
                unit = random.choice(units)
                multiplier = round(random.uniform(0.1, 10.0), 1)
                
                response = await test_client.post(
                    "/api/v1/connections/",
                    json={
                        "from_entity_id": from_entity["id"],
                        "to_entity_id": to_entity["id"],
                        "unit_id": unit["id"],
                        "multiplier": multiplier
                    }
                )
                
                if response.status_code == 201:
                    connections.append(response.json())
                else:
                    # Log error but continue
                    logger.warning(f"Failed to create connection: {response.status_code}")
        
        return {
            "entities": entities,
            "connections": connections,
            "units": units
        }


class TestEntityPerformance(PerformanceTestMixin):
    """Performance tests for entity operations."""
    
    @pytest.mark.asyncio
    async def test_entity_creation_performance(self, test_client):
        """Test entity creation performance under load."""
        # Define a payload generator to create unique entity names
        def entity_payload_generator():
            return {"name": create_test_entity_name("PerfTest")}
        
        # Benchmark entity creation (reduced iterations to avoid database stress)
        metrics = await self.measure_endpoint_performance(
            test_client,
            "POST",
            "/api/v1/entities/",
            iterations=20,
            payload_generator=entity_payload_generator
        )
        
        # Performance assertions (adjusted for test environment)
        assert metrics.avg_response_time < 500.0, f"Average response time too high: {metrics.avg_response_time}ms"
        assert metrics.p95_response_time < 1000.0, f"95th percentile response time too high: {metrics.p95_response_time}ms"
        assert metrics.success_rate > 0.80, f"Success rate too low: {metrics.success_rate}"
        
        logger.info(f"Entity creation performance: "
                   f"avg={metrics.avg_response_time:.2f}ms, "
                   f"p95={metrics.p95_response_time:.2f}ms, "
                   f"success_rate={metrics.success_rate:.2%}")
    
    @pytest.mark.asyncio
    async def test_entity_listing_performance(self, test_client):
        """Test entity listing performance with various page sizes."""
        # Create test data (minimal count to avoid database stress)
        await self.create_performance_test_data(test_client, entity_count=5)
        
        # Test different page sizes (appropriate for our test data)
        page_sizes = [5, 10]
        
        for page_size in page_sizes:
            metrics = await self.measure_endpoint_performance(
                test_client,
                "GET",
                "/api/v1/entities/",
                iterations=10,
                params={"limit": page_size, "offset": 0}
            )
            
            # Performance assertions scale with page size (adjusted for test environment)
            max_response_time = 500.0 + (page_size * 10.0)  # 500ms base + 10ms per entity
            assert metrics.avg_response_time < max_response_time, \
                f"Page size {page_size}: avg response time {metrics.avg_response_time:.2f}ms > {max_response_time}ms"
            
            logger.info(f"Entity listing (page_size={page_size}): "
                       f"avg={metrics.avg_response_time:.2f}ms")
    
    @pytest.mark.asyncio
    async def test_entity_retrieval_performance(self, test_client):
        """Test individual entity retrieval performance."""
        # Create test data (minimal count to avoid database stress)
        test_data = await self.create_performance_test_data(test_client, entity_count=5)
        entities = test_data["entities"]
        
        # Test retrieving entities (only if we have entities)
        if entities:
            for _ in range(min(3, len(entities))):
                entity = random.choice(entities)
                metrics = await self.measure_endpoint_performance(
                    test_client,
                    "GET",
                    f"/api/v1/entities/{entity['id']}",
                    iterations=5
                )
                
                assert metrics.avg_response_time < 500.0, \
                    f"Entity retrieval too slow: {metrics.avg_response_time:.2f}ms"
                assert metrics.success_rate == 1.0, \
                    f"Entity retrieval failed: success_rate={metrics.success_rate}"
        else:
            logger.warning("No entities created for retrieval test - test skipped")


class TestConnectionPerformance(PerformanceTestMixin):
    """Performance tests for connection operations."""
    
    @pytest.mark.asyncio
    async def test_connection_creation_performance(self, test_client):
        """Test connection creation performance."""
        # Create test entities (reduced for performance)
        test_data = await self.create_performance_test_data(test_client, entity_count=10)
        entities = test_data["entities"]
        units = test_data["units"]
        
        # Benchmark connection creation (reduced count)
        payloads = []
        for _ in range(10):
            from_entity = random.choice(entities)
            to_entity = random.choice([e for e in entities if e["id"] != from_entity["id"]])
            unit = random.choice(units)
            
            payloads.append({
                "from_entity_id": from_entity["id"],
                "to_entity_id": to_entity["id"],
                "unit_id": unit["id"],
                "multiplier": round(random.uniform(0.1, 10.0), 1)
            })
        
        # Test with different payloads
        response_times = []
        for payload in payloads:
            start_time = time.time()
            response = await test_client.post("/api/v1/connections/", json=payload)
            end_time = time.time()
            
            response_times.append((end_time - start_time) * 1000)
        
        avg_response_time = mean(response_times)
        p95_response_time = sorted(response_times)[int(0.95 * len(response_times))]
        
        assert avg_response_time < 1000.0, f"Connection creation too slow: {avg_response_time:.2f}ms"
        assert p95_response_time < 2000.0, f"95th percentile too slow: {p95_response_time:.2f}ms"
        
        logger.info(f"Connection creation performance: "
                   f"avg={avg_response_time:.2f}ms, p95={p95_response_time:.2f}ms")
    
    @pytest.mark.asyncio
    async def test_connection_listing_performance(self, test_client):
        """Test connection listing performance with large datasets."""
        # Create test data with many connections
        await self.create_performance_test_data(test_client, entity_count=50, connection_density=0.5)
        
        # Test different page sizes
        page_sizes = [10, 50, 100]
        
        for page_size in page_sizes:
            metrics = await self.measure_endpoint_performance(
                test_client,
                "GET",
                "/api/v1/connections/",
                iterations=10,
                params={"limit": page_size, "offset": 0}
            )
            
            # Performance assertions
            max_response_time = 500.0 + (page_size * 10.0)  # 500ms base + 10ms per connection
            assert metrics.avg_response_time < max_response_time, \
                f"Connection listing too slow for page_size={page_size}: {metrics.avg_response_time:.2f}ms"
            
            logger.info(f"Connection listing (page_size={page_size}): "
                       f"avg={metrics.avg_response_time:.2f}ms")


class TestPathfindingPerformance(PerformanceTestMixin):
    """Performance tests for pathfinding algorithms."""
    
    @pytest.mark.asyncio
    async def test_pathfinding_performance_various_graph_sizes(self, test_client):
        """Test pathfinding performance with different graph sizes."""
        graph_sizes = [10, 25, 50, 100]
        
        for size in graph_sizes:
            logger.info(f"Testing pathfinding performance with {size} entities")
            
            # Create test data
            test_data = await self.create_performance_test_data(
                test_client, 
                entity_count=size, 
                connection_density=0.3
            )
            entities = test_data["entities"]
            
            # Test pathfinding performance
            pathfinding_times = []
            successful_paths = 0
            total_tests = 20
            
            for _ in range(total_tests):
                from_entity = random.choice(entities)
                to_entity = random.choice([e for e in entities if e["id"] != from_entity["id"]])
                
                start_time = time.time()
                response = await test_client.get(
                    f"/api/v1/compare/{from_entity['id']}/{to_entity['id']}",
                    params={"max_path_length": 6}
                )
                end_time = time.time()
                
                pathfinding_times.append((end_time - start_time) * 1000)
                
                if response.status_code == 200:
                    successful_paths += 1
            
            avg_time = mean(pathfinding_times)
            p95_time = sorted(pathfinding_times)[int(0.95 * len(pathfinding_times))]
            
            # Performance assertions scale with graph size
            max_avg_time = 100.0 + (size * 2.0)  # 100ms base + 2ms per entity
            max_p95_time = 200.0 + (size * 4.0)  # 200ms base + 4ms per entity
            
            assert avg_time < max_avg_time, \
                f"Pathfinding too slow for {size} entities: {avg_time:.2f}ms > {max_avg_time}ms"
            assert p95_time < max_p95_time, \
                f"95th percentile too slow for {size} entities: {p95_time:.2f}ms > {max_p95_time}ms"
            
            logger.info(f"Pathfinding performance ({size} entities): "
                       f"avg={avg_time:.2f}ms, p95={p95_time:.2f}ms, "
                       f"success_rate={successful_paths/total_tests:.2%}")
    
    @pytest.mark.asyncio
    async def test_pathfinding_depth_performance(self, test_client):
        """Test pathfinding performance with different maximum path lengths."""
        # Create a linear chain for testing depth
        entities = []
        for i in range(10):
            name = create_test_entity_name("ChainEntity")
            response = await test_client.post("/api/v1/entities/", json={"name": name})
            entities.append(response.json())
        
        # Get length unit
        units_response = await test_client.get("/api/v1/units/")
        units = units_response.json()
        length_unit = next(u for u in units if u["name"].lower() == "length")
        
        # Create linear chain connections
        for i in range(len(entities) - 1):
            await test_client.post(
                "/api/v1/connections/",
                json={
                    "from_entity_id": entities[i]["id"],
                    "to_entity_id": entities[i + 1]["id"],
                    "unit_id": length_unit["id"],
                    "multiplier": 2.0
                }
            )
        
        # Test different path lengths
        max_path_lengths = [3, 6, 10, 15]
        
        for max_length in max_path_lengths:
            start_time = time.time()
            response = await test_client.get(
                f"/api/v1/compare/{entities[0]['id']}/{entities[-1]['id']}",
                params={"max_path_length": max_length}
            )
            end_time = time.time()
            
            response_time = (end_time - start_time) * 1000
            
            # Performance should not degrade significantly with depth
            max_response_time = 1000.0 + (max_length * 100.0)  # 1000ms base + 100ms per hop
            assert response_time < max_response_time, \
                f"Pathfinding too slow for max_length={max_length}: {response_time:.2f}ms"
            
            logger.info(f"Pathfinding depth performance (max_length={max_length}): "
                       f"{response_time:.2f}ms")


class TestDatabasePerformance(PerformanceTestMixin):
    """Performance tests for database operations."""
    
    @pytest.mark.asyncio
    async def test_database_query_performance(self, test_client):
        """Test direct database query performance."""
        # Create test data
        await self.create_performance_test_data(test_client, entity_count=100, connection_density=0.4)
        
        # Test various database operations
        async with async_session() as db:
            # Test entity queries
            start_time = time.time()
            for _ in range(100):
                result = await db.execute(select(Entity).limit(10))
                entities = result.scalars().all()
            entity_query_time = (time.time() - start_time) * 1000
            
            # Test connection queries
            start_time = time.time()
            for _ in range(100):
                result = await db.execute(select(Connection).limit(10))
                connections = result.scalars().all()
            connection_query_time = (time.time() - start_time) * 1000
            
            # Test complex join queries
            start_time = time.time()
            for _ in range(50):
                result = await db.execute(
                    select(Connection, Entity)
                    .join(Entity, Connection.from_entity_id == Entity.id)
                    .limit(10)
                )
                joined_data = result.all()
            join_query_time = (time.time() - start_time) * 1000
        
        # Performance assertions
        assert entity_query_time < 1000.0, f"Entity queries too slow: {entity_query_time:.2f}ms"
        assert connection_query_time < 1000.0, f"Connection queries too slow: {connection_query_time:.2f}ms"
        assert join_query_time < 2000.0, f"Join queries too slow: {join_query_time:.2f}ms"
        
        logger.info(f"Database query performance: "
                   f"entities={entity_query_time:.2f}ms, "
                   f"connections={connection_query_time:.2f}ms, "
                   f"joins={join_query_time:.2f}ms")
    
    @pytest.mark.asyncio
    async def test_recursive_cte_performance(self, test_client):
        """Test performance of recursive CTE queries used in pathfinding."""
        # Create test data with complex graph structure
        await self.create_performance_test_data(test_client, entity_count=50, connection_density=0.5)
        
        # Test recursive CTE performance directly
        async with async_session() as db:
            # Get some entities for testing
            result = await db.execute(select(Entity).limit(10))
            entities = result.scalars().all()
            
            # Get a unit
            result = await db.execute(select(Unit).limit(1))
            unit = result.scalar()
            
            if len(entities) >= 2 and unit:
                # Test pathfinding service performance
                pathfinding_times = []
                
                for _ in range(20):
                    from_entity = random.choice(entities)
                    to_entity = random.choice([e for e in entities if e.id != from_entity.id])
                    
                    start_time = time.time()
                    result = await find_shortest_path(
                        db, from_entity.id, to_entity.id, unit.id, max_hops=6
                    )
                    end_time = time.time()
                    
                    pathfinding_times.append((end_time - start_time) * 1000)
                
                avg_time = mean(pathfinding_times)
                p95_time = sorted(pathfinding_times)[int(0.95 * len(pathfinding_times))]
                
                # Performance assertions
                assert avg_time < 100.0, f"Recursive CTE too slow: {avg_time:.2f}ms"
                assert p95_time < 200.0, f"95th percentile CTE too slow: {p95_time:.2f}ms"
                
                logger.info(f"Recursive CTE performance: "
                           f"avg={avg_time:.2f}ms, p95={p95_time:.2f}ms")


class TestConcurrentPerformance(PerformanceTestMixin):
    """Performance tests for concurrent operations."""
    
    @pytest.mark.asyncio
    async def test_concurrent_entity_operations(self, test_client):
        """Test performance under concurrent entity operations."""
        # Create base entities
        base_entities = []
        for i in range(10):
            name = create_test_entity_name("BaseEntity")
            response = await test_client.post("/api/v1/entities/", json={"name": name})
            base_entities.append(response.json())
        
        # Test concurrent entity creation
        async def create_entity_task(task_id: int) -> Tuple[int, float]:
            start_time = time.time()
            name = create_test_entity_name("ConcurrentEntity")
            response = await test_client.post("/api/v1/entities/", json={"name": name})
            end_time = time.time()
            return task_id, (end_time - start_time) * 1000
        
        # Run concurrent tasks
        tasks = [create_entity_task(i) for i in range(50)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Analyze results
        successful_tasks = [r for r in results if not isinstance(r, Exception)]
        response_times = [r[1] for r in successful_tasks]
        
        if response_times:
            avg_time = mean(response_times)
            p95_time = sorted(response_times)[int(0.95 * len(response_times))]
            success_rate = len(successful_tasks) / len(tasks)
            
            # Performance assertions
            assert avg_time < 200.0, f"Concurrent entity creation too slow: {avg_time:.2f}ms"
            assert p95_time < 500.0, f"95th percentile too slow: {p95_time:.2f}ms"
            assert success_rate > 0.9, f"Success rate too low: {success_rate:.2%}"
            
            logger.info(f"Concurrent entity operations: "
                       f"avg={avg_time:.2f}ms, p95={p95_time:.2f}ms, "
                       f"success_rate={success_rate:.2%}")
    
    @pytest.mark.asyncio
    async def test_concurrent_pathfinding_operations(self, test_client):
        """Test pathfinding performance under concurrent load."""
        # Create test data
        test_data = await self.create_performance_test_data(test_client, entity_count=30, connection_density=0.4)
        entities = test_data["entities"]
        
        # Test concurrent pathfinding
        async def pathfinding_task(task_id: int) -> Tuple[int, float, int]:
            from_entity = random.choice(entities)
            to_entity = random.choice([e for e in entities if e["id"] != from_entity["id"]])
            
            start_time = time.time()
            response = await test_client.get(
                f"/api/v1/compare/{from_entity['id']}/{to_entity['id']}",
                params={"max_path_length": 6}
            )
            end_time = time.time()
            
            return task_id, (end_time - start_time) * 1000, response.status_code
        
        # Run concurrent pathfinding tasks
        tasks = [pathfinding_task(i) for i in range(20)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Analyze results
        successful_tasks = [r for r in results if not isinstance(r, Exception)]
        response_times = [r[1] for r in successful_tasks]
        status_codes = [r[2] for r in successful_tasks]
        
        if response_times:
            avg_time = mean(response_times)
            p95_time = sorted(response_times)[int(0.95 * len(response_times))]
            success_rate = len([s for s in status_codes if s == 200]) / len(status_codes)
            
            # Performance assertions
            assert avg_time < 300.0, f"Concurrent pathfinding too slow: {avg_time:.2f}ms"
            assert p95_time < 600.0, f"95th percentile too slow: {p95_time:.2f}ms"
            
            logger.info(f"Concurrent pathfinding operations: "
                       f"avg={avg_time:.2f}ms, p95={p95_time:.2f}ms, "
                       f"success_rate={success_rate:.2%}")


class TestResourceEfficiency(PerformanceTestMixin):
    """Performance tests for resource usage and efficiency."""
    
    @pytest.mark.asyncio
    async def test_memory_usage_patterns(self, test_client):
        """Test memory usage patterns under load."""
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Create test data and monitor memory usage
        memory_measurements = []
        
        for i in range(10):
            # Create entities and connections
            await self.create_performance_test_data(test_client, entity_count=20, connection_density=0.3)
            
            # Measure memory usage
            current_memory = process.memory_info().rss / 1024 / 1024
            memory_measurements.append(current_memory - initial_memory)
            
            # Perform some operations
            response = await test_client.get("/api/v1/entities/", params={"limit": 50})
            response = await test_client.get("/api/v1/connections/", params={"limit": 50})
        
        # Analyze memory usage
        max_memory_increase = max(memory_measurements)
        avg_memory_increase = mean(memory_measurements)
        
        # Memory usage should not grow excessively
        assert max_memory_increase < 100.0, f"Memory usage too high: {max_memory_increase:.2f}MB"
        assert avg_memory_increase < 50.0, f"Average memory usage too high: {avg_memory_increase:.2f}MB"
        
        logger.info(f"Memory usage patterns: "
                   f"max_increase={max_memory_increase:.2f}MB, "
                   f"avg_increase={avg_memory_increase:.2f}MB")
    
    @pytest.mark.asyncio
    async def test_response_time_consistency(self, test_client):
        """Test response time consistency over extended periods."""
        # Create test data
        test_data = await self.create_performance_test_data(test_client, entity_count=50, connection_density=0.3)
        entities = test_data["entities"]
        
        # Test response time consistency over many requests
        response_times = []
        
        for i in range(100):
            # Mix of different operations
            operation = random.choice(['list_entities', 'get_entity', 'pathfinding'])
            
            start_time = time.time()
            
            if operation == 'list_entities':
                response = await test_client.get("/api/v1/entities/", params={"limit": 20})
            elif operation == 'get_entity':
                entity = random.choice(entities)
                response = await test_client.get(f"/api/v1/entities/{entity['id']}")
            elif operation == 'pathfinding':
                from_entity = random.choice(entities)
                to_entity = random.choice([e for e in entities if e["id"] != from_entity["id"]])
                response = await test_client.get(f"/api/v1/compare/{from_entity['id']}/{to_entity['id']}")
            
            end_time = time.time()
            response_times.append((end_time - start_time) * 1000)
        
        # Analyze consistency
        avg_time = mean(response_times)
        std_dev = stdev(response_times)
        coefficient_of_variation = std_dev / avg_time
        
        # Response times should be consistent (low coefficient of variation)
        assert coefficient_of_variation < 1.0, f"Response times too inconsistent: CV={coefficient_of_variation:.2f}"
        
        logger.info(f"Response time consistency: "
                   f"avg={avg_time:.2f}ms, std_dev={std_dev:.2f}ms, "
                   f"CV={coefficient_of_variation:.2f}")


class TestPerformanceRegression(PerformanceTestMixin):
    """Performance regression tests with specific benchmarks."""
    
    @pytest.mark.asyncio
    async def test_benchmark_api_endpoints(self, test_client):
        """Benchmark all API endpoints for regression testing."""
        # Create test data
        test_data = await self.create_performance_test_data(test_client, entity_count=30, connection_density=0.3)
        entities = test_data["entities"]
        connections = test_data["connections"]
        
        benchmarks = {}
        
        # Benchmark entity operations
        start_time = time.time()
        response = await test_client.get("/api/v1/entities/", params={"limit": 50})
        benchmarks["list_entities"] = (time.time() - start_time) * 1000
        
        start_time = time.time()
        response = await test_client.get(f"/api/v1/entities/{entities[0]['id']}")
        benchmarks["get_entity"] = (time.time() - start_time) * 1000
        
        start_time = time.time()
        name = create_test_entity_name("BenchmarkEntity")
        response = await test_client.post("/api/v1/entities/", json={"name": name})
        benchmarks["create_entity"] = (time.time() - start_time) * 1000
        
        # Benchmark connection operations
        start_time = time.time()
        response = await test_client.get("/api/v1/connections/", params={"limit": 50})
        benchmarks["list_connections"] = (time.time() - start_time) * 1000
        
        if connections:
            start_time = time.time()
            response = await test_client.get(f"/api/v1/connections/{connections[0]['id']}")
            benchmarks["get_connection"] = (time.time() - start_time) * 1000
        
        # Benchmark pathfinding
        start_time = time.time()
        response = await test_client.get(f"/api/v1/compare/{entities[0]['id']}/{entities[1]['id']}")
        benchmarks["pathfinding"] = (time.time() - start_time) * 1000
        
        # Benchmark units
        start_time = time.time()
        response = await test_client.get("/api/v1/units/")
        benchmarks["list_units"] = (time.time() - start_time) * 1000
        
        # Log benchmarks for tracking
        logger.info("Performance benchmarks:")
        for operation, time_ms in benchmarks.items():
            logger.info(f"  {operation}: {time_ms:.2f}ms")
        
        # Regression test assertions (baseline expectations)
        assert benchmarks["list_entities"] < 100.0, f"Entity listing regression: {benchmarks['list_entities']:.2f}ms"
        assert benchmarks["get_entity"] < 50.0, f"Entity retrieval regression: {benchmarks['get_entity']:.2f}ms"
        assert benchmarks["create_entity"] < 100.0, f"Entity creation regression: {benchmarks['create_entity']:.2f}ms"
        assert benchmarks["list_connections"] < 100.0, f"Connection listing regression: {benchmarks['list_connections']:.2f}ms"
        assert benchmarks["pathfinding"] < 200.0, f"Pathfinding regression: {benchmarks['pathfinding']:.2f}ms"
        assert benchmarks["list_units"] < 50.0, f"Unit listing regression: {benchmarks['list_units']:.2f}ms"


# Performance test markers for different execution scenarios
pytestmark = [
    pytest.mark.asyncio,
    pytest.mark.performance,
]


# Custom fixtures for performance testing
@pytest.fixture(scope="session")
def performance_test_config():
    """Configuration for performance tests."""
    return {
        "max_response_time_ms": 1000,
        "max_memory_usage_mb": 100,
        "min_success_rate": 0.95,
        "concurrent_users": 10,
        "test_duration_seconds": 60
    }


@pytest.fixture
async def performance_test_environment(test_client, performance_test_config):
    """Set up performance test environment."""
    # Create a mixin instance to access the method
    mixin = PerformanceTestMixin()
    
    # Pre-create some data for consistent testing
    test_data = await mixin.create_performance_test_data(test_client, entity_count=20, connection_density=0.2)
    
    yield {
        "test_data": test_data,
        "config": performance_test_config
    }
    
    # Cleanup is handled by the test database cleanup fixtures


if __name__ == "__main__":
    # Allow running performance tests independently
    pytest.main([__file__, "-v", "--tb=short"])