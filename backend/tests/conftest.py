"""
Test configuration and fixtures for the SIMILE API tests.
Enhanced approach with comprehensive test data management and cleanup verification.
"""
import pytest
import pytest_asyncio
import asyncio
import os
import sys
import random
import string
import uuid
import logging
from pathlib import Path
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy import text

# Add backend to path for imports  
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.app_factory import create_app
from src.models import Base

# Configure logging for test data management
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database setup flag to prevent multiple setups
_database_setup_done = False

async def ensure_test_database_ready():
    """Ensure test database exists and has tables set up."""
    global _database_setup_done
    
    if _database_setup_done:
        return
    
    TEST_DATABASE_HOST = os.getenv("TEST_DATABASE_HOST", "localhost")
    ADMIN_DATABASE_URL = f"postgresql+asyncpg://postgres:postgres@{TEST_DATABASE_HOST}:5432/postgres"
    TEST_DATABASE_URL = f"postgresql+asyncpg://postgres:postgres@{TEST_DATABASE_HOST}:5432/simile_test"
    
    # Create database if needed
    admin_engine = create_async_engine(ADMIN_DATABASE_URL, isolation_level="AUTOCOMMIT")
    try:
        async with admin_engine.connect() as conn:
            result = await conn.execute(
                text("SELECT 1 FROM pg_database WHERE datname = 'simile_test'")
            )
            exists = result.scalar() is not None
            
            if not exists:
                await conn.execute(text("CREATE DATABASE simile_test"))
    finally:
        await admin_engine.dispose()
    
    # Set up tables
    test_engine = create_async_engine(TEST_DATABASE_URL)
    try:
        async with test_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
            
            # Insert initial units
            await conn.execute(text("""
                INSERT INTO units (name, symbol, created_at, updated_at) VALUES
                ('Length', 'm', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
                ('Mass', 'kg', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
                ('Time', 's', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
                ('Count', '#', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
                ('Volume', 'L', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                ON CONFLICT (name) DO NOTHING
            """))
    finally:
        await test_engine.dispose()
        
    _database_setup_done = True


async def clean_test_database():
    """Clean the test database by removing all data but preserving structure."""
    TEST_DATABASE_HOST = os.getenv("TEST_DATABASE_HOST", "localhost")
    TEST_DATABASE_URL = f"postgresql+asyncpg://postgres:postgres@{TEST_DATABASE_HOST}:5432/simile_test"
    
    test_engine = create_async_engine(TEST_DATABASE_URL)
    try:
        async with test_engine.begin() as conn:
            # Get counts before cleanup for logging
            entities_before = await conn.execute(text("SELECT COUNT(*) FROM entities"))
            connections_before = await conn.execute(text("SELECT COUNT(*) FROM connections"))
            
            entities_count = entities_before.scalar()
            connections_count = connections_before.scalar()
            
            # Only log if there's data to clean
            if entities_count > 0 or connections_count > 0:
                logger.info(f"Cleaning test database: {entities_count} entities, {connections_count} connections")
            
            # Delete all data in the correct order to avoid foreign key conflicts
            await conn.execute(text("DELETE FROM connections"))
            await conn.execute(text("DELETE FROM entities"))
            
            # Clean units except for the predefined ones
            await conn.execute(text("""
                DELETE FROM units 
                WHERE name NOT IN ('Length', 'Mass', 'Time', 'Count', 'Volume')
            """))
            
            # Reset sequences to start from 1
            await conn.execute(text("ALTER SEQUENCE entities_id_seq RESTART WITH 1"))
            await conn.execute(text("ALTER SEQUENCE connections_id_seq RESTART WITH 1"))
            await conn.execute(text("ALTER SEQUENCE units_id_seq RESTART WITH 10"))
            
            # Verify cleanup was successful
            entities_after = await conn.execute(text("SELECT COUNT(*) FROM entities"))
            connections_after = await conn.execute(text("SELECT COUNT(*) FROM connections"))
            
            entities_remaining = entities_after.scalar()
            connections_remaining = connections_after.scalar()
            
            if entities_remaining != 0 or connections_remaining != 0:
                logger.error(f"Cleanup verification failed: {entities_remaining} entities, {connections_remaining} connections remaining")
                raise Exception(f"Database cleanup failed: {entities_remaining} entities, {connections_remaining} connections remaining")
            
            # Keep the units data as it's needed for tests
            # The units should already exist from ensure_test_database_ready()
            
    finally:
        await test_engine.dispose()


@pytest_asyncio.fixture(scope="session", autouse=True)
async def setup_test_environment():
    """Set up test environment once per session."""
    await ensure_test_database_ready()


@pytest_asyncio.fixture(autouse=True)
async def clean_database_between_tests():
    """Clean the database before each test to ensure isolation."""
    await clean_test_database()
    yield
    # Don't clean after test - let the test finish completely
    # The next test will clean before it starts
    # This approach prevents cleanup interfering with test assertions


@pytest_asyncio.fixture
async def test_app():
    """Create a fresh FastAPI app instance for each test."""
    app = create_app()
    return app


@pytest_asyncio.fixture
async def test_client(test_app):
    """Create an AsyncClient with a fresh app instance for each test."""
    async with AsyncClient(app=test_app, base_url="http://test") as client:
        yield client


def pytest_configure(config):
    """Configure pytest with custom markers and settings."""
    config.addinivalue_line(
        "markers", "asyncio: mark test as async"
    )


def generate_valid_entity_suffix(length=8):
    """Generate letters-only suffix for test entity names.
    
    This ensures entity names comply with the validation pattern ^[a-zA-Z\\s]+$
    which allows only letters and spaces.
    
    Args:
        length: Number of characters in the suffix (default: 8)
        
    Returns:
        A string containing only lowercase letters
    """
    return ''.join(random.choices(string.ascii_lowercase, k=length))


def generate_unique_entity_name(base_name="Test Entity"):
    """Generate a unique entity name using UUID to prevent collisions.
    
    Args:
        base_name: Base name for the entity (default: "Test Entity")
        
    Returns:
        A unique entity name with UUID suffix (letters only)
    """
    # Generate multiple UUIDs to ensure we get enough letters
    letters_collected = ""
    attempts = 0
    while len(letters_collected) < 8 and attempts < 10:
        uuid_str = str(uuid.uuid4()).replace('-', '')
        # Keep only letters and convert to lowercase
        letters_in_uuid = ''.join(c for c in uuid_str if c.isalpha()).lower()
        letters_collected += letters_in_uuid
        attempts += 1
    
    # Take first 8 characters to keep name reasonable
    suffix = letters_collected[:8]
    
    # If we still don't have enough letters, use random letters
    if len(suffix) < 8:
        import random
        import string
        additional_letters = ''.join(random.choices(string.ascii_lowercase, k=8-len(suffix)))
        suffix += additional_letters
    
    return f"{base_name} {suffix}"


def create_test_entity_name(prefix="TestEntity"):
    """Create a test entity name with UUID suffix for test isolation.
    
    This is the recommended function for creating test entity names.
    It ensures uniqueness and compliance with validation rules.
    
    Args:
        prefix: Prefix for the entity name (default: "TestEntity")
        
    Returns:
        A unique entity name suitable for testing
    """
    return generate_unique_entity_name(prefix)


@pytest_asyncio.fixture
async def cleanup_verification():
    """Provide cleanup verification capability for tests."""
    
    async def verify_cleanup():
        """Verify that the database is in a clean state."""
        TEST_DATABASE_HOST = os.getenv("TEST_DATABASE_HOST", "localhost")
        TEST_DATABASE_URL = f"postgresql+asyncpg://postgres:postgres@{TEST_DATABASE_HOST}:5432/simile_test"
        
        test_engine = create_async_engine(TEST_DATABASE_URL)
        try:
            async with test_engine.begin() as conn:
                # Check entity count
                entities_result = await conn.execute(text("SELECT COUNT(*) FROM entities"))
                entities_count = entities_result.scalar()
                
                # Check connection count
                connections_result = await conn.execute(text("SELECT COUNT(*) FROM connections"))
                connections_count = connections_result.scalar()
                
                # Check units count (should be 5 for standard units)
                units_result = await conn.execute(text("SELECT COUNT(*) FROM units"))
                units_count = units_result.scalar()
                
                cleanup_state = {
                    'entities': entities_count,
                    'connections': connections_count,
                    'units': units_count,
                    'clean': entities_count == 0 and connections_count == 0
                }
                
                logger.info(f"Cleanup verification: {cleanup_state}")
                return cleanup_state
                
        finally:
            await test_engine.dispose()
    
    return verify_cleanup


@pytest_asyncio.fixture
async def test_data_isolation():
    """Provide test data isolation tracking."""
    
    class TestDataTracker:
        def __init__(self):
            self.created_entities = []
            self.created_connections = []
            self.test_start_time = None
            
        def track_entity(self, entity_id):
            """Track an entity created during test."""
            self.created_entities.append(entity_id)
            
        def track_connection(self, connection_id):
            """Track a connection created during test."""
            self.created_connections.append(connection_id)
            
        def get_created_data(self):
            """Get summary of created test data."""
            return {
                'entities': len(self.created_entities),
                'connections': len(self.created_connections),
                'entity_ids': self.created_entities,
                'connection_ids': self.created_connections
            }
    
    tracker = TestDataTracker()
    yield tracker
    
    # Log test data summary
    data_summary = tracker.get_created_data()
    if data_summary['entities'] > 0 or data_summary['connections'] > 0:
        logger.info(f"Test data created: {data_summary}")


# Enhanced test data generation utilities
class TestDataGenerator:
    """Enhanced test data generation with validation and uniqueness guarantees."""
    
    @staticmethod
    def entity_name(prefix="TestEntity"):
        """Generate a unique entity name."""
        return create_test_entity_name(prefix)
    
    @staticmethod
    def entity_names(count=3, prefix="TestEntity"):
        """Generate multiple unique entity names."""
        return [TestDataGenerator.entity_name(f"{prefix}{i}") for i in range(count)]
    
    @staticmethod
    def edge_case_entity_names():
        """Generate entity names for edge case testing."""
        return {
            'max_length': 'A' * 100,
            'single_char': 'A',
            'with_spaces': 'Entity With Spaces',
            'mixed_case': 'MixedCaseEntity',
            'letters_only': 'OnlyLetters',
        }
    
    @staticmethod
    def invalid_entity_names():
        """Generate invalid entity names for validation testing."""
        return {
            'empty': '',
            'whitespace_only': '   ',
            'too_long': 'A' * 101,
            'numbers_only': '12345',
            'with_numbers': 'Test123',
            'special_chars': ['Test@Entity', 'Test#1', 'Test$', 'Test&Co', 'Test!'],
        }


# Export the generator for easy import
test_data_generator = TestDataGenerator()