"""
Error recovery and cleanup validation test suite for SIMILE backend.
Tests system recovery from various error conditions and validates proper cleanup.
"""
import pytest
import pytest_asyncio
import asyncio
import logging
import time
from decimal import Decimal
from typing import List, Dict, Any
from unittest.mock import patch, AsyncMock, MagicMock
from contextlib import asynccontextmanager

from sqlalchemy import text, select, and_
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.exc import IntegrityError, SQLAlchemyError, DisconnectionError
from sqlalchemy.pool import NullPool

from src.database import async_session, get_db, engine
from src.models import Entity, Connection, Unit
from .conftest import create_test_entity_name

# Configure logging for error recovery tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestDatabaseConnectionRecovery:
    """Test database connection recovery scenarios."""
    
    @pytest.mark.asyncio
    async def test_connection_pool_exhaustion_recovery(self, test_client):
        """Test recovery from connection pool exhaustion."""
        # Create test entity
        entity_name = create_test_entity_name("PoolExhaustion")
        entity_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity_name}
        )
        assert entity_response.status_code == 201
        
        # Create many concurrent database operations to potentially exhaust pool
        async def database_operation(task_id: int):
            """Perform database operation that holds connection."""
            try:
                # Get all entities (simulates holding connection)
                response = await test_client.get("/api/v1/entities/")
                await asyncio.sleep(0.1)  # Hold connection briefly
                return {
                    "task_id": task_id,
                    "status_code": response.status_code,
                    "success": True
                }
            except Exception as e:
                return {
                    "task_id": task_id,
                    "status_code": 500,
                    "success": False,
                    "error": str(e)
                }
        
        # Create many concurrent operations
        tasks = [database_operation(i) for i in range(50)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Most operations should succeed
        successful_operations = [r for r in results 
                               if isinstance(r, dict) and r.get("success", False)]
        
        # At least 80% should succeed (some may fail due to pool exhaustion)
        success_rate = len(successful_operations) / len(results)
        assert success_rate >= 0.8, f"Success rate too low: {success_rate}"
        
        # Verify system recovers and continues functioning
        recovery_response = await test_client.get("/api/v1/entities/")
        assert recovery_response.status_code == 200
        
        # Should be able to create new entities after recovery
        new_entity_name = create_test_entity_name("PostRecovery")
        new_entity_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": new_entity_name}
        )
        assert new_entity_response.status_code == 201
    
    @pytest.mark.asyncio
    async def test_transaction_rollback_on_connection_failure(self, test_client):
        """Test transaction rollback when connection fails."""
        # Create test entities
        entity1_name = create_test_entity_name("ConnFailA")
        entity2_name = create_test_entity_name("ConnFailB")
        
        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity1_name}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity2_name}
        )
        
        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]
        
        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]
        
        # Mock connection failure during transaction
        with patch('src.database.async_session') as mock_session:
            mock_db = AsyncMock()
            mock_session.return_value.__aenter__.return_value = mock_db
            
            # Simulate connection failure during commit
            mock_db.commit.side_effect = DisconnectionError("Connection lost", None, None)
            mock_db.rollback.return_value = None
            
            # Attempt connection creation
            connection_response = await test_client.post(
                "/api/v1/connections/",
                json={
                    "from_entity_id": entity1_id,
                    "to_entity_id": entity2_id,
                    "unit_id": unit_id,
                    "multiplier": 2.0
                }
            )
            
            # Should handle the failure gracefully
            assert connection_response.status_code >= 400
        
        # Verify no partial data was committed
        connections_response = await test_client.get("/api/v1/connections/")
        connections = connections_response.json()
        
        # Should have no connections between these entities
        related_connections = [c for c in connections 
                             if (c["from_entity_id"] == entity1_id and c["to_entity_id"] == entity2_id) or
                                (c["from_entity_id"] == entity2_id and c["to_entity_id"] == entity1_id)]
        
        assert len(related_connections) == 0, "No connections should exist after failed transaction"
    
    @pytest.mark.asyncio
    async def test_session_cleanup_after_multiple_failures(self, test_client):
        """Test session cleanup after multiple consecutive failures."""
        # Create test entity
        entity_name = create_test_entity_name("MultiFailure")
        entity_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity_name}
        )
        assert entity_response.status_code == 201
        entity_id = entity_response.json()["id"]
        
        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]
        
        # Generate multiple consecutive failures
        failure_count = 0
        for i in range(10):
            # Attempt to create self-referencing connection (will fail)
            failure_response = await test_client.post(
                "/api/v1/connections/",
                json={
                    "from_entity_id": entity_id,
                    "to_entity_id": entity_id,
                    "unit_id": unit_id,
                    "multiplier": 2.0
                }
            )
            
            if failure_response.status_code == 400:
                failure_count += 1
        
        assert failure_count == 10, "All operations should have failed"
        
        # Verify system still functions after multiple failures
        success_entity_name = create_test_entity_name("PostMultiFailure")
        success_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": success_entity_name}
        )
        assert success_response.status_code == 201
        
        # Verify can create valid connections
        success_entity_id = success_response.json()["id"]
        connection_response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entity_id,
                "to_entity_id": success_entity_id,
                "unit_id": unit_id,
                "multiplier": 3.0
            }
        )
        assert connection_response.status_code == 201


class TestResourceLeakPrevention:
    """Test prevention of resource leaks during error conditions."""
    
    @pytest.mark.asyncio
    async def test_connection_leak_prevention(self, test_client):
        """Test prevention of database connection leaks."""
        # Record initial connection pool state
        initial_pool_size = engine.pool.size()
        
        # Create many operations that could potentially leak connections
        async def potentially_leaking_operation(task_id: int):
            """Operation that could leak connections if not properly handled."""
            try:
                # Start transaction
                async with async_session() as db:
                    # Create entity
                    entity_name = create_test_entity_name(f"LeakTest{task_id}")
                    entity = Entity(name=entity_name)
                    db.add(entity)
                    
                    # Force an error before commit
                    if task_id % 2 == 0:
                        raise Exception("Simulated error")
                    
                    await db.commit()
                    return {"task_id": task_id, "success": True}
            except Exception as e:
                return {"task_id": task_id, "success": False, "error": str(e)}
        
        # Run many operations
        tasks = [potentially_leaking_operation(i) for i in range(20)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Half should succeed, half should fail
        successful_results = [r for r in results if isinstance(r, dict) and r.get("success", False)]
        failed_results = [r for r in results if isinstance(r, dict) and not r.get("success", True)]
        
        assert len(successful_results) > 0, "Some operations should succeed"
        assert len(failed_results) > 0, "Some operations should fail"
        
        # Check connection pool state
        final_pool_size = engine.pool.size()
        
        # Pool size should be stable (no leaked connections)
        assert final_pool_size == initial_pool_size, f"Connection leak detected: {initial_pool_size} -> {final_pool_size}"
        
        # Verify system still functions
        test_response = await test_client.get("/api/v1/entities/")
        assert test_response.status_code == 200
    
    @pytest.mark.asyncio
    async def test_memory_leak_prevention_in_error_scenarios(self, test_client):
        """Test prevention of memory leaks during error scenarios."""
        # Create baseline
        entity_name = create_test_entity_name("MemoryLeakTest")
        entity_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity_name}
        )
        assert entity_response.status_code == 201
        entity_id = entity_response.json()["id"]
        
        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]
        
        # Generate many error conditions
        async def error_generating_operation(task_id: int):
            """Operation that generates errors."""
            try:
                # Attempt various invalid operations
                operations = [
                    # Invalid entity ID
                    test_client.post("/api/v1/connections/", json={
                        "from_entity_id": 99999,
                        "to_entity_id": entity_id,
                        "unit_id": unit_id,
                        "multiplier": 2.0
                    }),
                    # Negative multiplier
                    test_client.post("/api/v1/connections/", json={
                        "from_entity_id": entity_id,
                        "to_entity_id": entity_id,
                        "unit_id": unit_id,
                        "multiplier": -1.0
                    }),
                    # Self-reference
                    test_client.post("/api/v1/connections/", json={
                        "from_entity_id": entity_id,
                        "to_entity_id": entity_id,
                        "unit_id": unit_id,
                        "multiplier": 2.0
                    }),
                    # Duplicate entity
                    test_client.post("/api/v1/entities/", json={
                        "name": entity_name  # Already exists
                    })
                ]
                
                # Execute one random operation
                operation = operations[task_id % len(operations)]
                response = await operation
                
                return {"task_id": task_id, "status_code": response.status_code}
            except Exception as e:
                return {"task_id": task_id, "error": str(e)}
        
        # Run many error-generating operations
        tasks = [error_generating_operation(i) for i in range(100)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Most should generate errors (status codes 400+)
        error_results = [r for r in results 
                        if isinstance(r, dict) and r.get("status_code", 500) >= 400]
        
        assert len(error_results) > 80, "Most operations should generate errors"
        
        # Verify system still functions efficiently
        start_time = time.time()
        final_test_response = await test_client.get("/api/v1/entities/")
        end_time = time.time()
        
        assert final_test_response.status_code == 200
        
        # Response should be fast (no memory pressure)
        response_time = end_time - start_time
        assert response_time < 1.0, f"Response too slow: {response_time}s"
    
    @pytest.mark.asyncio
    async def test_transaction_cleanup_on_constraint_violations(self, test_client):
        """Test proper transaction cleanup on constraint violations."""
        # Create test entities
        entity1_name = create_test_entity_name("ConstraintA")
        entity2_name = create_test_entity_name("ConstraintB")
        
        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity1_name}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": entity2_name}
        )
        
        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]
        
        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]
        
        # Create valid connection first
        valid_connection = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entity1_id,
                "to_entity_id": entity2_id,
                "unit_id": unit_id,
                "multiplier": 2.0
            }
        )
        assert valid_connection.status_code == 201
        
        # Record initial state
        initial_connections = await test_client.get("/api/v1/connections/")
        initial_count = len(initial_connections.json())
        
        # Generate constraint violations
        constraint_violations = [
            # Self-reference
            test_client.post("/api/v1/connections/", json={
                "from_entity_id": entity1_id,
                "to_entity_id": entity1_id,
                "unit_id": unit_id,
                "multiplier": 2.0
            }),
            # Negative multiplier
            test_client.post("/api/v1/connections/", json={
                "from_entity_id": entity1_id,
                "to_entity_id": entity2_id,
                "unit_id": unit_id,
                "multiplier": -1.0
            }),
            # Zero multiplier
            test_client.post("/api/v1/connections/", json={
                "from_entity_id": entity1_id,
                "to_entity_id": entity2_id,
                "unit_id": unit_id,
                "multiplier": 0.0
            }),
            # Non-existent entity
            test_client.post("/api/v1/connections/", json={
                "from_entity_id": 99999,
                "to_entity_id": entity2_id,
                "unit_id": unit_id,
                "multiplier": 2.0
            })
        ]
        
        # Execute all constraint violations
        violation_results = await asyncio.gather(*constraint_violations, return_exceptions=True)
        
        # All should fail with 400+ status codes
        for result in violation_results:
            if hasattr(result, 'status_code'):
                assert result.status_code >= 400, "Constraint violation should fail"
        
        # Verify database state is unchanged
        final_connections = await test_client.get("/api/v1/connections/")
        final_count = len(final_connections.json())
        
        assert final_count == initial_count, "Connection count should be unchanged after constraint violations"
        
        # Verify original connection still exists and is valid
        connections = final_connections.json()
        original_connection = next(
            (c for c in connections 
             if c["from_entity_id"] == entity1_id and c["to_entity_id"] == entity2_id),
            None
        )
        assert original_connection is not None, "Original connection should still exist"
        assert float(original_connection["multiplier"]) == 2.0, "Original connection should be unchanged"


class TestSystemRecoveryScenarios:
    """Test system recovery from various failure scenarios."""
    
    @pytest.mark.asyncio
    async def test_recovery_from_partial_transaction_failures(self, test_client):
        """Test recovery from partial transaction failures."""
        # Create test entities
        entity_names = [create_test_entity_name(f"Recovery{i}") for i in range(5)]
        entities = []
        
        for name in entity_names:
            response = await test_client.post("/api/v1/entities/", json={"name": name})
            assert response.status_code == 201
            entities.append(response.json())
        
        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]
        
        # Create some valid connections
        valid_connections = [
            (entities[0]["id"], entities[1]["id"], 2.0),
            (entities[1]["id"], entities[2]["id"], 3.0),
        ]
        
        for from_id, to_id, multiplier in valid_connections:
            response = await test_client.post(
                "/api/v1/connections/",
                json={
                    "from_entity_id": from_id,
                    "to_entity_id": to_id,
                    "unit_id": unit_id,
                    "multiplier": multiplier
                }
            )
            assert response.status_code == 201
        
        # Mock transaction failure scenarios
        async def simulate_transaction_failure():
            """Simulate transaction that fails partway through."""
            async with async_session() as db:
                try:
                    # Start transaction
                    await db.begin()
                    
                    # Create entity (this should work)
                    entity_name = create_test_entity_name("PartialFailure")
                    entity = Entity(name=entity_name)
                    db.add(entity)
                    await db.flush()  # Get the ID
                    
                    # Create connection (this should work)
                    connection = Connection(
                        from_entity_id=entities[0]["id"],
                        to_entity_id=entity.id,
                        unit_id=unit_id,
                        multiplier=Decimal("4.0")
                    )
                    db.add(connection)
                    await db.flush()
                    
                    # Simulate failure before commit
                    raise Exception("Simulated failure")
                    
                except Exception:
                    await db.rollback()
                    raise
        
        # Run simulation
        with pytest.raises(Exception, match="Simulated failure"):
            await simulate_transaction_failure()
        
        # Verify system recovered properly
        recovery_test_response = await test_client.get("/api/v1/entities/")
        assert recovery_test_response.status_code == 200
        
        # Verify no partial data from failed transaction
        entities_list = recovery_test_response.json()
        partial_entities = [e for e in entities_list if "PartialFailure" in e["name"]]
        assert len(partial_entities) == 0, "No partial entities should exist"
        
        # Verify original data still exists
        connections_response = await test_client.get("/api/v1/connections/")
        connections = connections_response.json()
        
        # Should have original connections (2 forward + 2 inverse = 4 total)
        assert len(connections) == 4, f"Expected 4 connections, got {len(connections)}"
    
    @pytest.mark.asyncio
    async def test_database_consistency_after_mixed_operations(self, test_client):
        """Test database consistency after mixed successful and failed operations."""
        # Create test entities
        entity_names = [create_test_entity_name(f"Mixed{i}") for i in range(3)]
        entities = []
        
        for name in entity_names:
            response = await test_client.post("/api/v1/entities/", json={"name": name})
            assert response.status_code == 201
            entities.append(response.json())
        
        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]
        
        # Mix of operations (some will succeed, some will fail)
        mixed_operations = [
            # Valid connection
            test_client.post("/api/v1/connections/", json={
                "from_entity_id": entities[0]["id"],
                "to_entity_id": entities[1]["id"],
                "unit_id": unit_id,
                "multiplier": 2.0
            }),
            # Invalid connection (self-reference)
            test_client.post("/api/v1/connections/", json={
                "from_entity_id": entities[0]["id"],
                "to_entity_id": entities[0]["id"],
                "unit_id": unit_id,
                "multiplier": 2.0
            }),
            # Valid connection
            test_client.post("/api/v1/connections/", json={
                "from_entity_id": entities[1]["id"],
                "to_entity_id": entities[2]["id"],
                "unit_id": unit_id,
                "multiplier": 3.0
            }),
            # Invalid connection (negative multiplier)
            test_client.post("/api/v1/connections/", json={
                "from_entity_id": entities[0]["id"],
                "to_entity_id": entities[2]["id"],
                "unit_id": unit_id,
                "multiplier": -1.0
            }),
            # Valid entity
            test_client.post("/api/v1/entities/", json={
                "name": create_test_entity_name("MixedValid")
            }),
            # Invalid entity (duplicate)
            test_client.post("/api/v1/entities/", json={
                "name": entities[0]["name"]  # Already exists
            })
        ]
        
        # Execute mixed operations
        results = await asyncio.gather(*mixed_operations, return_exceptions=True)
        
        # Count successes and failures
        successes = [r for r in results if hasattr(r, 'status_code') and r.status_code in [200, 201]]
        failures = [r for r in results if hasattr(r, 'status_code') and r.status_code >= 400]
        
        assert len(successes) == 3, f"Expected 3 successes, got {len(successes)}"
        assert len(failures) == 3, f"Expected 3 failures, got {len(failures)}"
        
        # Verify database consistency
        final_entities = await test_client.get("/api/v1/entities/")
        final_connections = await test_client.get("/api/v1/connections/")
        
        entities_list = final_entities.json()
        connections_list = final_connections.json()
        
        # Should have original 3 entities + 1 new valid entity = 4 total
        assert len(entities_list) == 4, f"Expected 4 entities, got {len(entities_list)}"
        
        # Should have 2 valid connections (forward + inverse = 4 total)
        assert len(connections_list) == 4, f"Expected 4 connections, got {len(connections_list)}"
        
        # Verify all connections have valid inverses
        for conn in connections_list:
            inverse = next(
                (c for c in connections_list 
                 if c["from_entity_id"] == conn["to_entity_id"] 
                 and c["to_entity_id"] == conn["from_entity_id"]
                 and c["unit_id"] == conn["unit_id"]),
                None
            )
            assert inverse is not None, f"Connection {conn['id']} missing inverse"
            
            # Check inverse relationship
            forward_mult = float(conn["multiplier"])
            inverse_mult = float(inverse["multiplier"])
            expected_inverse = 1.0 / forward_mult
            
            assert abs(inverse_mult - expected_inverse) < 0.1, "Inverse relationship broken"
    
    @pytest.mark.asyncio
    async def test_long_running_operation_recovery(self, test_client):
        """Test recovery from long-running operations that might timeout."""
        # Create test entities
        entity_names = [create_test_entity_name(f"LongRun{i}") for i in range(10)]
        entities = []
        
        for name in entity_names:
            response = await test_client.post("/api/v1/entities/", json={"name": name})
            assert response.status_code == 201
            entities.append(response.json())
        
        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]
        
        # Create many connections rapidly (simulate batch operation)
        async def create_connection_batch(batch_id: int):
            """Create a batch of connections."""
            batch_connections = []
            for i in range(5):
                from_entity = entities[i]
                to_entity = entities[(i + 1) % len(entities)]
                
                try:
                    response = await test_client.post(
                        "/api/v1/connections/",
                        json={
                            "from_entity_id": from_entity["id"],
                            "to_entity_id": to_entity["id"],
                            "unit_id": unit_id,
                            "multiplier": float(batch_id + i + 1)
                        }
                    )
                    batch_connections.append({
                        "batch_id": batch_id,
                        "connection_id": i,
                        "status_code": response.status_code,
                        "success": response.status_code == 201
                    })
                except Exception as e:
                    batch_connections.append({
                        "batch_id": batch_id,
                        "connection_id": i,
                        "status_code": 500,
                        "success": False,
                        "error": str(e)
                    })
            
            return batch_connections
        
        # Run multiple batches concurrently
        batch_tasks = [create_connection_batch(i) for i in range(5)]
        batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
        
        # Flatten results
        all_results = []
        for batch_result in batch_results:
            if isinstance(batch_result, list):
                all_results.extend(batch_result)
        
        # Most should succeed
        successful_results = [r for r in all_results if r.get("success", False)]
        success_rate = len(successful_results) / len(all_results) if all_results else 0
        
        assert success_rate >= 0.8, f"Success rate too low: {success_rate}"
        
        # Verify system is still responsive
        health_check_start = time.time()
        health_response = await test_client.get("/api/v1/entities/")
        health_check_end = time.time()
        
        assert health_response.status_code == 200
        assert health_check_end - health_check_start < 2.0, "System should remain responsive"
        
        # Verify database consistency
        final_connections = await test_client.get("/api/v1/connections/")
        connections = final_connections.json()
        
        # All connections should have valid inverses
        forward_connections = [c for c in connections if c["from_entity_id"] < c["to_entity_id"]]
        
        for forward_conn in forward_connections:
            inverse_conn = next(
                (c for c in connections 
                 if c["from_entity_id"] == forward_conn["to_entity_id"] 
                 and c["to_entity_id"] == forward_conn["from_entity_id"]),
                None
            )
            assert inverse_conn is not None, f"Forward connection {forward_conn['id']} missing inverse"