"""
Phase 3 validation test: Connection API functionality working.
"""
import pytest
from .conftest import generate_valid_entity_suffix


class TestPhase3ConnectionValidation:
    """Verify Phase 3 fixes: connection APIs working correctly."""
    
    @pytest.mark.asyncio
    async def test_connection_creation_basic(self, test_client):
        """Test basic connection creation with inverse."""
        # Create two test entities
        suffix = generate_valid_entity_suffix()
        
        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"TestEntityOne {suffix}"}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"TestEntityTwo {suffix}"}
        )
        
        if entity1_response.status_code != 200:
            print(f"Entity 1 creation failed: {entity1_response.status_code} - {entity1_response.text}")
            
        if entity2_response.status_code != 200:
            print(f"Entity 2 creation failed: {entity2_response.status_code} - {entity2_response.text}")
        
        assert entity1_response.status_code == 201
        assert entity2_response.status_code == 201
        
        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]
        
        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        assert len(units) > 0
        unit_id = units[0]["id"]
        
        # Create connection
        connection_response = await test_client.post(
            "/api/v1/connections/",
            json={
                "from_entity_id": entity1_id,
                "to_entity_id": entity2_id,
                "unit_id": unit_id,
                "multiplier": 2.5
            }
        )
        
        if connection_response.status_code != 201:
            print(f"Connection creation failed: {connection_response.status_code} - {connection_response.text}")
        
        assert connection_response.status_code == 201
        connection = connection_response.json()
        assert connection["from_entity_id"] == entity1_id
        assert connection["to_entity_id"] == entity2_id
        assert float(connection["multiplier"]) == 2.5
    
    @pytest.mark.asyncio 
    async def test_units_and_entities_endpoints(self, test_client):
        """Test that basic endpoints work."""
        # Test units endpoint
        units_response = await test_client.get("/api/v1/units/")
        assert units_response.status_code == 200
        units = units_response.json()
        assert len(units) > 0
        
        # Test entities endpoint
        entities_response = await test_client.get("/api/v1/entities/")
        assert entities_response.status_code == 200
        entities = entities_response.json()
        assert isinstance(entities, list)
    
    @pytest.mark.asyncio
    async def test_pathfinding_api_basic(self, test_client):
        """Test basic pathfinding/compare API."""
        # Create entities and connection first
        suffix = generate_valid_entity_suffix()
        
        entity1_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"PathTestOne {suffix}"}
        )
        entity2_response = await test_client.post(
            "/api/v1/entities/",
            json={"name": f"PathTestTwo {suffix}"}
        )
        
        assert entity1_response.status_code == 201
        assert entity2_response.status_code == 201
        
        entity1_id = entity1_response.json()["id"]
        entity2_id = entity2_response.json()["id"]
        
        # Get a unit
        units_response = await test_client.get("/api/v1/units/")
        unit_id = units_response.json()[0]["id"]
        
        # Test same entity comparison
        compare_response = await test_client.get(
            f"/api/v1/compare/?from={entity1_id}&to={entity1_id}&unit={unit_id}"
        )
        
        if compare_response.status_code != 200:
            print(f"Compare failed: {compare_response.status_code} - {compare_response.text}")
        
        assert compare_response.status_code == 200
        compare_result = compare_response.json()
        assert float(compare_result["multiplier"]) == 1.0