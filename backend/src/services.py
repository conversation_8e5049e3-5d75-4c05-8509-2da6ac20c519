from decimal import Decimal
from typing import List, Optional, <PERSON><PERSON>
import logging

from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

logger = logging.getLogger(__name__)


async def find_shortest_path(
    db: AsyncSession,
    from_entity_id: int,
    to_entity_id: int,
    unit_id: int,
    max_hops: int = 6
) -> Tuple[Optional[Decimal], Optional[List[dict]]]:
    """
    Find the shortest path between two entities for a given unit using
    PostgreSQL CTEs. Returns (multiplier, path) where path is a list of
    connection details.
    """

    # Check for direct connection first
    logger.info(f"Checking for direct connection from {from_entity_id} to {to_entity_id} for unit {unit_id}")
    direct_query = text("""
        SELECT multiplier
        FROM connections
        WHERE from_entity_id = :from_id
        AND to_entity_id = :to_id
        AND unit_id = :unit_id
    """)

    result = await db.execute(
        direct_query,
        {"from_id": from_entity_id, "to_id": to_entity_id, "unit_id": unit_id}
    )
    direct_result = result.fetchone()

    if direct_result:
        # Direct connection found
        multiplier = direct_result[0]
        logger.info(f"Found direct connection with multiplier {multiplier}")
        path = [{
            "from_entity_id": from_entity_id,
            "to_entity_id": to_entity_id,
            "multiplier": float(multiplier),
            "hop": 1
        }]
        return Decimal(str(multiplier)), path
    
    logger.info(f"No direct connection found, checking for multi-hop paths")

    # No direct connection, use recursive CTE for pathfinding
    path_query = text("""
        WITH RECURSIVE path_finder AS (
            -- Base case: direct connections from source
            SELECT
                from_entity_id,
                to_entity_id,
                CAST(multiplier AS NUMERIC) as multiplier,
                1 as hop_count,
                ARRAY[from_entity_id, to_entity_id] as path_entities,
                ARRAY[CAST(multiplier AS NUMERIC)] as path_multipliers
            FROM connections
            WHERE from_entity_id = :from_id
            AND unit_id = :unit_id

            UNION ALL

            -- Recursive case: extend paths
            SELECT
                pf.from_entity_id,
                c.to_entity_id,
                CAST(pf.multiplier * c.multiplier AS NUMERIC) as multiplier,
                pf.hop_count + 1,
                pf.path_entities || c.to_entity_id,
                pf.path_multipliers || CAST(c.multiplier AS NUMERIC)
            FROM path_finder pf
            JOIN connections c ON pf.to_entity_id = c.from_entity_id
            WHERE c.unit_id = :unit_id
            AND pf.hop_count < :max_hops
            AND NOT (c.to_entity_id = ANY(pf.path_entities))
        )
        SELECT
            multiplier,
            hop_count,
            path_entities,
            path_multipliers
        FROM path_finder
        WHERE to_entity_id = :to_id
        ORDER BY hop_count ASC, multiplier DESC
        LIMIT 1
    """)

    result = await db.execute(
        path_query,
        {
            "from_id": from_entity_id,
            "to_id": to_entity_id,
            "unit_id": unit_id,
            "max_hops": max_hops
        }
    )
    path_result = result.fetchone()

    if not path_result:
        logger.warning(f"No path found via CTE query from {from_entity_id} to {to_entity_id} for unit {unit_id}")
        # Let's debug what connections exist
        debug_query = text("SELECT from_entity_id, to_entity_id, unit_id, multiplier FROM connections ORDER BY id")
        debug_result = await db.execute(debug_query)
        connections = debug_result.fetchall()
        logger.info(f"Available connections: {connections}")
        return None, None

    multiplier, hop_count, path_entities, path_multipliers = path_result

    # Build path details
    path = []
    for i in range(len(path_entities) - 1):
        path.append({
            "from_entity_id": path_entities[i],
            "to_entity_id": path_entities[i + 1],
            "multiplier": float(path_multipliers[i]),
            "hop": i + 1
        })

    return Decimal(str(multiplier)), path


async def get_entity_names_for_path(
    db: AsyncSession,
    path: List[dict]
) -> List[dict]:
    """Enrich path with entity names"""
    if not path:
        return []

    # Get all entity IDs from the path
    entity_ids = set()
    for step in path:
        entity_ids.add(step["from_entity_id"])
        entity_ids.add(step["to_entity_id"])

    # Fetch entity names
    entity_query = text("""
        SELECT id, name FROM entities WHERE id = ANY(:entity_ids)
    """)
    result = await db.execute(entity_query, {"entity_ids": list(entity_ids)})
    entity_map = {row[0]: row[1] for row in result.fetchall()}

    # Enrich path with names
    enriched_path = []
    for step in path:
        enriched_step = step.copy()
        enriched_step["from_entity_name"] = entity_map.get(
            step["from_entity_id"], "Unknown")
        enriched_step["to_entity_name"] = entity_map.get(
            step["to_entity_id"], "Unknown")
        enriched_path.append(enriched_step)

    return enriched_path
