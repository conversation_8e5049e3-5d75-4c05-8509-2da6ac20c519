from typing import List

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import IntegrityError

from ..database import get_db
from ..models import Unit
from ..schemas import UnitCreate, Unit as UnitSchema

router = APIRouter()


@router.get("/", response_model=List[UnitSchema])
async def get_units(
    db: AsyncSession = Depends(get_db)
):
    """Get all units"""
    result = await db.execute(select(Unit).order_by(Unit.name))
    units = result.scalars().all()
    return units


@router.post("/", response_model=UnitSchema)
async def create_unit(
    unit: UnitCreate,
    db: AsyncSession = Depends(get_db)
):
    """Create a new unit"""
    try:
        db_unit = Unit(name=unit.name, symbol=unit.symbol)
        db.add(db_unit)
        await db.commit()
        await db.refresh(db_unit)
        return db_unit
    except IntegrityError:
        await db.rollback()
        raise HTTPException(
            status_code=400,
            detail=f"Unit '{unit.name}' already exists"
        )


@router.get("/{unit_id}", response_model=UnitSchema)
async def get_unit(
    unit_id: int,
    db: AsyncSession = Depends(get_db)
):
    """Get unit by ID"""
    # Validate unit_id is positive (but allow 0 to return 404 instead of 422)
    if unit_id < 0:
        raise HTTPException(status_code=422, detail="Unit ID must be a positive integer")
    
    result = await db.execute(select(Unit).where(Unit.id == unit_id))
    unit = result.scalar_one_or_none()
    if not unit:
        raise HTTPException(status_code=404, detail="Unit not found")
    return unit
