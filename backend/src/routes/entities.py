from typing import List
import logging

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import IntegrityError

from ..database import get_db
from ..models import Entity
from ..schemas import EntityCreate, EntityUpdate, Entity as EntitySchema

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/", response_model=EntitySchema, status_code=201)
async def create_entity(
    entity: EntityCreate,
    db: AsyncSession = Depends(get_db)
):
    """Create a new entity"""
    try:
        # Note: Case-insensitive uniqueness is enforced by database index
        db_entity = Entity(name=entity.name)
        db.add(db_entity)
        await db.commit()
        await db.refresh(db_entity)
        
        # Enhanced logging for debugging entity visibility issues
        logger.info(f"Entity created successfully: '{db_entity.name}' with ID {db_entity.id}")
        
        # Verify entity is immediately queryable
        verification_result = await db.execute(select(Entity).where(Entity.id == db_entity.id))
        verification_entity = verification_result.scalar_one_or_none()
        if verification_entity:
            logger.info(f"Entity verification successful: ID {verification_entity.id} is immediately queryable")
        else:
            logger.error(f"Entity verification failed: ID {db_entity.id} not immediately queryable")
        
        return db_entity
    except IntegrityError:
        await db.rollback()
        raise HTTPException(
            status_code=400,
            detail=f"Entity '{entity.name}' already exists"
        )


@router.get("/", response_model=List[EntitySchema])
async def get_entities(
    skip: int = 0,
    limit: int = 1000,  # Increased limit to handle test entities
    db: AsyncSession = Depends(get_db)
):
    """Get all entities with pagination"""
    # Validate pagination parameters
    if skip < 0:
        raise HTTPException(status_code=422, detail="Skip parameter must be non-negative")
    if limit < 0:
        raise HTTPException(status_code=422, detail="Limit parameter must be non-negative")
    
    result = await db.execute(
        select(Entity).offset(skip).limit(limit).order_by(Entity.name)
    )
    entities = result.scalars().all()
    return entities


@router.get("/{entity_id}", response_model=EntitySchema)
async def get_entity(
    entity_id: int,
    db: AsyncSession = Depends(get_db)
):
    """Get entity by ID"""
    result = await db.execute(select(Entity).where(Entity.id == entity_id))
    entity = result.scalar_one_or_none()
    if not entity:
        raise HTTPException(status_code=404, detail="Entity not found")
    return entity


@router.put("/{entity_id}", response_model=EntitySchema)
async def update_entity(
    entity_id: int,
    entity_update: EntityUpdate,
    db: AsyncSession = Depends(get_db)
):
    """Update entity by ID"""
    result = await db.execute(select(Entity).where(Entity.id == entity_id))
    entity = result.scalar_one_or_none()
    if not entity:
        raise HTTPException(status_code=404, detail="Entity not found")

    if entity_update.name is not None:
        # Note: Case-insensitive uniqueness is enforced by database index
        entity.name = entity_update.name

    try:
        await db.commit()
        await db.refresh(entity)
        return entity
    except IntegrityError:
        await db.rollback()
        raise HTTPException(
            status_code=400,
            detail=f"Entity '{entity_update.name}' already exists"
        )


@router.delete("/{entity_id}")
async def delete_entity(
    entity_id: int,
    db: AsyncSession = Depends(get_db)
):
    """Delete entity by ID"""
    result = await db.execute(select(Entity).where(Entity.id == entity_id))
    entity = result.scalar_one_or_none()
    if not entity:
        raise HTTPException(status_code=404, detail="Entity not found")

    await db.delete(entity)
    await db.commit()
    return {"message": "Entity deleted successfully"}
