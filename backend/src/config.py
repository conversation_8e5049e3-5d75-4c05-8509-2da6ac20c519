import os
from pydantic import ConfigDict
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    app_name: str = "SIMILE API"
    debug: bool = False
    database_url: str = ("postgresql+asyncpg://postgres:postgres@"
                         "database:5432/simile")
    cors_origins: list[str] = ["http://localhost:3000"]

    model_config = ConfigDict(env_file=".env")
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Override database_url for tests
        if "TEST_DATABASE_HOST" in os.environ:
            test_host = os.environ["TEST_DATABASE_HOST"]
            self.database_url = f"postgresql+asyncpg://postgres:postgres@{test_host}:5432/simile_test"


def get_settings() -> Settings:
    """
    Get settings instance with proper test database configuration.
    
    This function ensures that the TEST_DATABASE_HOST environment variable
    is checked at the time the settings are requested, not at import time.
    """
    return Settings()


# Global settings instance for backward compatibility
settings = Settings()
