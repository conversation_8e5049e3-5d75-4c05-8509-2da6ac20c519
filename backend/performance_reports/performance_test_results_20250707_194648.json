{"created": 1751932012.894791, "duration": 3.0829529762268066, "exitcode": 0, "root": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend", "environment": {}, "summary": {"passed": 3, "total": 3, "collected": 3}, "collectors": [{"nodeid": "", "outcome": "passed", "result": [{"nodeid": "tests/test_performance_benchmarks.py::TestEntityPerformance", "type": "Class"}]}, {"nodeid": "tests/test_performance_benchmarks.py::TestEntityPerformance", "outcome": "passed", "result": [{"nodeid": "tests/test_performance_benchmarks.py::TestEntityPerformance::test_entity_creation_performance", "type": "Coroutine", "lineno": 239}, {"nodeid": "tests/test_performance_benchmarks.py::TestEntityPerformance::test_entity_listing_performance", "type": "Coroutine", "lineno": 265}, {"nodeid": "tests/test_performance_benchmarks.py::TestEntityPerformance::test_entity_retrieval_performance", "type": "Coroutine", "lineno": 291}]}], "tests": [{"nodeid": "tests/test_performance_benchmarks.py::TestEntityPerformance::test_entity_creation_performance", "lineno": 239, "outcome": "passed", "keywords": ["test_entity_creation_performance", "asyncio", "pytestmark", "TestEntityPerformance", "test_performance_benchmarks.py", "performance", "tests/__init__.py", "backend"], "setup": {"duration": 0.13844624999910593, "outcome": "passed", "stderr": "INFO:backend.tests.conftest:Cleaning test database: 10 entities, 44 connections\n", "log": [{"name": "backend.tests.conftest", "msg": "Cleaning test database: 10 entities, 44 connections", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/tests/conftest.py", "filename": "conftest.py", "module": "conftest", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 96, "funcName": "clean_test_database", "created": 1751932009.953621, "msecs": 953.0, "relativeCreated": 833.9047431945801, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}]}, "call": {"duration": 1.0214603340136819, "outcome": "passed", "stderr": "INFO:src.routes.entities:Entity created successfully: 'PerfTest cdffeaeb' with ID 1\nINFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfTest fdedcbff' with ID 2\nINFO:src.routes.entities:Entity verification successful: ID 2 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfTest fecbbdcf' with ID 3\nINFO:src.routes.entities:Entity verification successful: ID 3 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfTest aeafbdef' with ID 4\nINFO:src.routes.entities:Entity verification successful: ID 4 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfTest cbafadaa' with ID 5\nINFO:src.routes.entities:Entity verification successful: ID 5 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfTest accdbdcb' with ID 6\nINFO:src.routes.entities:Entity verification successful: ID 6 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfTest deafcacb' with ID 7\nINFO:src.routes.entities:Entity verification successful: ID 7 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfTest ddcfdcaa' with ID 8\nINFO:src.routes.entities:Entity verification successful: ID 8 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfTest dddaefcc' with ID 9\nINFO:src.routes.entities:Entity verification successful: ID 9 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfTest eaccecbf' with ID 10\nINFO:src.routes.entities:Entity verification successful: ID 10 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfTest bdffdfbc' with ID 11\nINFO:src.routes.entities:Entity verification successful: ID 11 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfTest bfbbfcde' with ID 12\nINFO:src.routes.entities:Entity verification successful: ID 12 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfTest aeeafeaf' with ID 13\nINFO:src.routes.entities:Entity verification successful: ID 13 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfTest ebbcafbe' with ID 14\nINFO:src.routes.entities:Entity verification successful: ID 14 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfTest abaafbae' with ID 15\nINFO:src.routes.entities:Entity verification successful: ID 15 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfTest fbeacaca' with ID 16\nINFO:src.routes.entities:Entity verification successful: ID 16 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfTest abeaeaca' with ID 17\nINFO:src.routes.entities:Entity verification successful: ID 17 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfTest cdcbdbdf' with ID 18\nINFO:src.routes.entities:Entity verification successful: ID 18 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfTest afebedcd' with ID 19\nINFO:src.routes.entities:Entity verification successful: ID 19 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfTest cccfbfaf' with ID 20\nINFO:src.routes.entities:Entity verification successful: ID 20 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:backend.tests.test_performance_benchmarks:Entity creation performance: avg=51.00ms, p95=56.36ms, success_rate=100.00%\n", "log": [{"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfTest cdffeaeb' with ID 1", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751932010.022325, "msecs": 22.0, "relativeCreated": 902.6088714599609, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 1 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751932010.0230742, "msecs": 23.0, "relativeCreated": 903.357982635498, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932010.025325, "msecs": 25.0, "relativeCreated": 905.6088924407959, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfTest fdedcbff' with ID 2", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751932010.0761821, "msecs": 76.0, "relativeCreated": 956.4659595489502, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 2 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751932010.076763, "msecs": 76.0, "relativeCreated": 957.0467472076416, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932010.079684, "msecs": 79.0, "relativeCreated": 959.967851638794, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfTest fecbbdcf' with ID 3", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751932010.1253881, "msecs": 125.0, "relativeCreated": 1005.6719779968262, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 3 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751932010.1259289, "msecs": 125.0, "relativeCreated": 1006.2127113342285, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932010.127769, "msecs": 127.0, "relativeCreated": 1008.0528259277344, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfTest aeafbdef' with ID 4", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751932010.1796951, "msecs": 179.0, "relativeCreated": 1059.97896194458, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 4 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751932010.180311, "msecs": 180.0, "relativeCreated": 1060.5947971343994, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932010.182385, "msecs": 182.0, "relativeCreated": 1062.668800354004, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfTest cbafadaa' with ID 5", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751932010.225652, "msecs": 225.0, "relativeCreated": 1105.93581199646, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 5 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751932010.226207, "msecs": 226.0, "relativeCreated": 1106.4908504486084, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932010.228042, "msecs": 228.0, "relativeCreated": 1108.325719833374, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfTest accdbdcb' with ID 6", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751932010.280283, "msecs": 280.0, "relativeCreated": 1160.566806793213, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 6 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751932010.280843, "msecs": 280.0, "relativeCreated": 1161.1268520355225, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932010.282683, "msecs": 282.0, "relativeCreated": 1162.9667282104492, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfTest deafcacb' with ID 7", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751932010.3256052, "msecs": 325.0, "relativeCreated": 1205.8889865875244, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 7 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751932010.326188, "msecs": 326.0, "relativeCreated": 1206.4719200134277, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932010.328053, "msecs": 328.0, "relativeCreated": 1208.3368301391602, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfTest ddcfdcaa' with ID 8", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751932010.379411, "msecs": 379.0, "relativeCreated": 1259.6948146820068, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 8 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751932010.379989, "msecs": 379.0, "relativeCreated": 1260.272741317749, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932010.3818939, "msecs": 381.0, "relativeCreated": 1262.1777057647705, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfTest dddaefcc' with ID 9", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751932010.4301991, "msecs": 430.0, "relativeCreated": 1310.4829788208008, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 9 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751932010.430772, "msecs": 430.0, "relativeCreated": 1311.0558986663818, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932010.432518, "msecs": 432.0, "relativeCreated": 1312.8018379211426, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfTest eaccecbf' with ID 10", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751932010.4791749, "msecs": 479.0, "relativeCreated": 1359.4586849212646, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 10 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751932010.480573, "msecs": 480.0, "relativeCreated": 1360.8567714691162, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932010.482608, "msecs": 482.0, "relativeCreated": 1362.8919124603271, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfTest bdffdfbc' with ID 11", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751932010.5326378, "msecs": 532.0, "relativeCreated": 1412.921667098999, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 11 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751932010.533261, "msecs": 533.0, "relativeCreated": 1413.5448932647705, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932010.53538, "msecs": 535.0, "relativeCreated": 1415.663719177246, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfTest bfbbfcde' with ID 12", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751932010.581586, "msecs": 581.0, "relativeCreated": 1461.869716644287, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 12 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751932010.58218, "msecs": 582.0, "relativeCreated": 1462.4638557434082, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932010.584028, "msecs": 584.0, "relativeCreated": 1464.3118381500244, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfTest aeeafeaf' with ID 13", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751932010.629216, "msecs": 629.0, "relativeCreated": 1509.4997882843018, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 13 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751932010.62977, "msecs": 629.0, "relativeCreated": 1510.0538730621338, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932010.631616, "msecs": 631.0, "relativeCreated": 1511.8999481201172, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfTest ebbcafbe' with ID 14", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751932010.6781619, "msecs": 678.0, "relativeCreated": 1558.445692062378, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 14 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751932010.678758, "msecs": 678.0, "relativeCreated": 1559.0417385101318, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932010.680751, "msecs": 680.0, "relativeCreated": 1561.034917831421, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfTest abaafbae' with ID 15", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751932010.725186, "msecs": 725.0, "relativeCreated": 1605.4699420928955, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 15 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751932010.725929, "msecs": 725.0, "relativeCreated": 1606.212854385376, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932010.728897, "msecs": 728.0, "relativeCreated": 1609.1809272766113, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfTest fbeacaca' with ID 16", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751932010.7815309, "msecs": 781.0, "relativeCreated": 1661.8146896362305, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 16 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751932010.782281, "msecs": 782.0, "relativeCreated": 1662.564754486084, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932010.78528, "msecs": 785.0, "relativeCreated": 1665.5638217926025, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfTest abeaeaca' with ID 17", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751932010.835855, "msecs": 835.0, "relativeCreated": 1716.1388397216797, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 17 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751932010.836738, "msecs": 836.0, "relativeCreated": 1717.0219421386719, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932010.8399308, "msecs": 839.0, "relativeCreated": 1720.214605331421, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfTest cdcbdbdf' with ID 18", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751932010.891952, "msecs": 891.0, "relativeCreated": 1772.2358703613281, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 18 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751932010.892555, "msecs": 892.0, "relativeCreated": 1772.838830947876, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932010.894528, "msecs": 894.0, "relativeCreated": 1774.8117446899414, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfTest afebedcd' with ID 19", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751932010.942612, "msecs": 942.0, "relativeCreated": 1822.8957653045654, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 19 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751932010.943159, "msecs": 943.0, "relativeCreated": 1823.4429359436035, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932010.945119, "msecs": 945.0, "relativeCreated": 1825.4027366638184, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfTest cccfbfaf' with ID 20", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751932010.9922738, "msecs": 992.0, "relativeCreated": 1872.5576400756836, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 20 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751932010.992845, "msecs": 992.0, "relativeCreated": 1873.128890991211, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932010.9947069, "msecs": 994.0, "relativeCreated": 1874.990701675415, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "backend.tests.test_performance_benchmarks", "msg": "Entity creation performance: avg=51.00ms, p95=56.36ms, success_rate=100.00%", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/tests/test_performance_benchmarks.py", "filename": "test_performance_benchmarks.py", "module": "test_performance_benchmarks", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 261, "funcName": "test_entity_creation_performance", "created": 1751932010.9950888, "msecs": 995.0, "relativeCreated": 1875.3726482391357, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}]}, "teardown": {"duration": 0.000494457985041663, "outcome": "passed"}}, {"nodeid": "tests/test_performance_benchmarks.py::TestEntityPerformance::test_entity_listing_performance", "lineno": 265, "outcome": "passed", "keywords": ["test_entity_listing_performance", "asyncio", "pytestmark", "TestEntityPerformance", "test_performance_benchmarks.py", "performance", "tests/__init__.py", "backend"], "setup": {"duration": 0.04529558299691416, "outcome": "passed", "stderr": "INFO:backend.tests.conftest:Cleaning test database: 20 entities, 0 connections\n", "log": [{"name": "backend.tests.conftest", "msg": "Cleaning test database: 20 entities, 0 connections", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/tests/conftest.py", "filename": "conftest.py", "module": "conftest", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 96, "funcName": "clean_test_database", "created": 1751932011.022636, "msecs": 22.0, "relativeCreated": 1902.9197692871094, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}]}, "call": {"duration": 0.9512274590088055, "outcome": "passed", "stderr": "INFO:httpx:HTTP Request: GET http://test/api/v1/units/ \"HTTP/1.1 200 OK\"\nINFO:src.routes.entities:Entity created successfully: 'PerfEntity fabbdbbd' with ID 1\nINFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfEntity ccdbaedb' with ID 2\nINFO:src.routes.entities:Entity verification successful: ID 2 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfEntity ebdfbdab' with ID 3\nINFO:src.routes.entities:Entity verification successful: ID 3 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfEntity fcedbbde' with ID 4\nINFO:src.routes.entities:Entity verification successful: ID 4 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfEntity dbbbdfba' with ID 5\nINFO:src.routes.entities:Entity verification successful: ID 5 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.connections:Validating entities for connection: from_entity_id=2, to_entity_id=5\nINFO:src.routes.connections:Entities validated successfully: from_entity=PerfEntity ccdbaedb (id=2), to_entity=PerfEntity dbbbdfba (id=5)\nINFO:src.routes.connections:Created connection ID: 1\nINFO:src.routes.connections:Primary connection: 2 -> 5, multiplier: 1.0\nINFO:src.routes.connections:Inverse connection should exist: 5 -> 2, multiplier: 1.0\nINFO:httpx:HTTP Request: POST http://test/api/v1/connections/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.connections:Validating entities for connection: from_entity_id=5, to_entity_id=1\nINFO:src.routes.connections:Entities validated successfully: from_entity=PerfEntity dbbbdfba (id=5), to_entity=PerfEntity fabbdbbd (id=1)\nINFO:src.routes.connections:Created connection ID: 3\nINFO:src.routes.connections:Primary connection: 5 -> 1, multiplier: 1.5\nINFO:src.routes.connections:Inverse connection should exist: 1 -> 5, multiplier: 0.7\nINFO:httpx:HTTP Request: POST http://test/api/v1/connections/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.connections:Validating entities for connection: from_entity_id=2, to_entity_id=1\nINFO:src.routes.connections:Entities validated successfully: from_entity=PerfEntity ccdbaedb (id=2), to_entity=PerfEntity fabbdbbd (id=1)\nINFO:src.routes.connections:Created connection ID: 5\nINFO:src.routes.connections:Primary connection: 2 -> 1, multiplier: 3.0\nINFO:src.routes.connections:Inverse connection should exist: 1 -> 2, multiplier: 0.3\nINFO:httpx:HTTP Request: POST http://test/api/v1/connections/ \"HTTP/1.1 201 Created\"\nINFO:httpx:HTTP Request: GET http://test/api/v1/entities/?limit=5&offset=0 \"HTTP/1.1 200 OK\"\nINFO:httpx:HTTP Request: GET http://test/api/v1/entities/?limit=5&offset=0 \"HTTP/1.1 200 OK\"\nINFO:httpx:HTTP Request: GET http://test/api/v1/entities/?limit=5&offset=0 \"HTTP/1.1 200 OK\"\nINFO:httpx:HTTP Request: GET http://test/api/v1/entities/?limit=5&offset=0 \"HTTP/1.1 200 OK\"\nINFO:httpx:HTTP Request: GET http://test/api/v1/entities/?limit=5&offset=0 \"HTTP/1.1 200 OK\"\nINFO:httpx:HTTP Request: GET http://test/api/v1/entities/?limit=5&offset=0 \"HTTP/1.1 200 OK\"\nINFO:httpx:HTTP Request: GET http://test/api/v1/entities/?limit=5&offset=0 \"HTTP/1.1 200 OK\"\nINFO:httpx:HTTP Request: GET http://test/api/v1/entities/?limit=5&offset=0 \"HTTP/1.1 200 OK\"\nINFO:httpx:HTTP Request: GET http://test/api/v1/entities/?limit=5&offset=0 \"HTTP/1.1 200 OK\"\nINFO:httpx:HTTP Request: GET http://test/api/v1/entities/?limit=5&offset=0 \"HTTP/1.1 200 OK\"\nINFO:backend.tests.test_performance_benchmarks:Entity listing (page_size=5): avg=24.99ms\nINFO:httpx:HTTP Request: GET http://test/api/v1/entities/?limit=10&offset=0 \"HTTP/1.1 200 OK\"\nINFO:httpx:HTTP Request: GET http://test/api/v1/entities/?limit=10&offset=0 \"HTTP/1.1 200 OK\"\nINFO:httpx:HTTP Request: GET http://test/api/v1/entities/?limit=10&offset=0 \"HTTP/1.1 200 OK\"\nINFO:httpx:HTTP Request: GET http://test/api/v1/entities/?limit=10&offset=0 \"HTTP/1.1 200 OK\"\nINFO:httpx:HTTP Request: GET http://test/api/v1/entities/?limit=10&offset=0 \"HTTP/1.1 200 OK\"\nINFO:httpx:HTTP Request: GET http://test/api/v1/entities/?limit=10&offset=0 \"HTTP/1.1 200 OK\"\nINFO:httpx:HTTP Request: GET http://test/api/v1/entities/?limit=10&offset=0 \"HTTP/1.1 200 OK\"\nINFO:httpx:HTTP Request: GET http://test/api/v1/entities/?limit=10&offset=0 \"HTTP/1.1 200 OK\"\nINFO:httpx:HTTP Request: GET http://test/api/v1/entities/?limit=10&offset=0 \"HTTP/1.1 200 OK\"\nINFO:httpx:HTTP Request: GET http://test/api/v1/entities/?limit=10&offset=0 \"HTTP/1.1 200 OK\"\nINFO:backend.tests.test_performance_benchmarks:Entity listing (page_size=10): avg=24.22ms\n", "log": [{"name": "httpx", "msg": "HTTP Request: GET http://test/api/v1/units/ \"HTTP/1.1 200 OK\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932011.0644019, "msecs": 64.0, "relativeCreated": 1944.685697555542, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfEntity fabbdbbd' with ID 1", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751932011.1098208, "msecs": 109.0, "relativeCreated": 1990.1046752929688, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 1 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751932011.1103969, "msecs": 110.0, "relativeCreated": 1990.6806945800781, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932011.112235, "msecs": 112.0, "relativeCreated": 1992.5189018249512, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfEntity ccdbaedb' with ID 2", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751932011.1612558, "msecs": 161.0, "relativeCreated": 2041.5396690368652, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 2 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751932011.161825, "msecs": 161.0, "relativeCreated": 2042.1087741851807, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932011.1639218, "msecs": 163.0, "relativeCreated": 2044.205665588379, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfEntity ebdfbdab' with ID 3", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751932011.215403, "msecs": 215.0, "relativeCreated": 2095.686912536621, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 3 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751932011.216065, "msecs": 216.0, "relativeCreated": 2096.348762512207, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932011.2192268, "msecs": 219.0, "relativeCreated": 2099.510669708252, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfEntity fcedbbde' with ID 4", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751932011.270401, "msecs": 270.0, "relativeCreated": 2150.6848335266113, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 4 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751932011.2711859, "msecs": 271.0, "relativeCreated": 2151.4697074890137, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932011.273098, "msecs": 273.0, "relativeCreated": 2153.381824493408, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfEntity dbbbdfba' with ID 5", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751932011.32087, "msecs": 320.0, "relativeCreated": 2201.1537551879883, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 5 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751932011.321559, "msecs": 321.0, "relativeCreated": 2201.842784881592, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932011.324777, "msecs": 324.0, "relativeCreated": 2205.0607204437256, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.connections", "msg": "Validating entities for connection: from_entity_id=2, to_entity_id=5", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/connections.py", "filename": "connections.py", "module": "connections", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 30, "funcName": "create_connection", "created": 1751932011.325269, "msecs": 325.0, "relativeCreated": 2205.552816390991, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.connections", "msg": "Entities validated successfully: from_entity=PerfEntity ccdbaedb (id=2), to_entity=PerfEntity dbbbdfba (id=5)", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/connections.py", "filename": "connections.py", "module": "connections", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 54, "funcName": "create_connection", "created": 1751932011.347976, "msecs": 347.0, "relativeCreated": 2228.259801864624, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.connections", "msg": "Created connection ID: 1", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/connections.py", "filename": "connections.py", "module": "connections", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 149, "funcName": "create_connection", "created": 1751932011.382116, "msecs": 382.0, "relativeCreated": 2262.399911880493, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.connections", "msg": "Primary connection: 2 -> 5, multiplier: 1.0", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/connections.py", "filename": "connections.py", "module": "connections", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 150, "funcName": "create_connection", "created": 1751932011.382211, "msecs": 382.0, "relativeCreated": 2262.4948024749756, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.connections", "msg": "Inverse connection should exist: 5 -> 2, multiplier: 1.0", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/connections.py", "filename": "connections.py", "module": "connections", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 151, "funcName": "create_connection", "created": 1751932011.382247, "msecs": 382.0, "relativeCreated": 2262.53080368042, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/connections/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932011.38545, "msecs": 385.0, "relativeCreated": 2265.7337188720703, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.connections", "msg": "Validating entities for connection: from_entity_id=5, to_entity_id=1", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/connections.py", "filename": "connections.py", "module": "connections", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 30, "funcName": "create_connection", "created": 1751932011.385935, "msecs": 385.0, "relativeCreated": 2266.218900680542, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.connections", "msg": "Entities validated successfully: from_entity=PerfEntity dbbbdfba (id=5), to_entity=PerfEntity fabbdbbd (id=1)", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/connections.py", "filename": "connections.py", "module": "connections", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 54, "funcName": "create_connection", "created": 1751932011.4102778, "msecs": 410.0, "relativeCreated": 2290.5616760253906, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.connections", "msg": "Created connection ID: 3", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/connections.py", "filename": "connections.py", "module": "connections", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 149, "funcName": "create_connection", "created": 1751932011.442019, "msecs": 442.0, "relativeCreated": 2322.30281829834, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.connections", "msg": "Primary connection: 5 -> 1, multiplier: 1.5", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/connections.py", "filename": "connections.py", "module": "connections", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 150, "funcName": "create_connection", "created": 1751932011.442112, "msecs": 442.0, "relativeCreated": 2322.3958015441895, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.connections", "msg": "Inverse connection should exist: 1 -> 5, multiplier: 0.7", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/connections.py", "filename": "connections.py", "module": "connections", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 151, "funcName": "create_connection", "created": 1751932011.442144, "msecs": 442.0, "relativeCreated": 2322.427749633789, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/connections/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932011.444176, "msecs": 444.0, "relativeCreated": 2324.4597911834717, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.connections", "msg": "Validating entities for connection: from_entity_id=2, to_entity_id=1", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/connections.py", "filename": "connections.py", "module": "connections", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 30, "funcName": "create_connection", "created": 1751932011.444624, "msecs": 444.0, "relativeCreated": 2324.9077796936035, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.connections", "msg": "Entities validated successfully: from_entity=PerfEntity ccdbaedb (id=2), to_entity=PerfEntity fabbdbbd (id=1)", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/connections.py", "filename": "connections.py", "module": "connections", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 54, "funcName": "create_connection", "created": 1751932011.468032, "msecs": 468.0, "relativeCreated": 2348.315715789795, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.connections", "msg": "Created connection ID: 5", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/connections.py", "filename": "connections.py", "module": "connections", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 149, "funcName": "create_connection", "created": 1751932011.496973, "msecs": 496.0, "relativeCreated": 2377.2568702697754, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.connections", "msg": "Primary connection: 2 -> 1, multiplier: 3.0", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/connections.py", "filename": "connections.py", "module": "connections", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 150, "funcName": "create_connection", "created": 1751932011.49707, "msecs": 497.0, "relativeCreated": 2377.3539066314697, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.connections", "msg": "Inverse connection should exist: 1 -> 2, multiplier: 0.3", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/connections.py", "filename": "connections.py", "module": "connections", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 151, "funcName": "create_connection", "created": 1751932011.497105, "msecs": 497.0, "relativeCreated": 2377.3887157440186, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/connections/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932011.4990451, "msecs": 499.0, "relativeCreated": 2379.328966140747, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: GET http://test/api/v1/entities/?limit=5&offset=0 \"HTTP/1.1 200 OK\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932011.52509, "msecs": 525.0, "relativeCreated": 2405.3738117218018, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: GET http://test/api/v1/entities/?limit=5&offset=0 \"HTTP/1.1 200 OK\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932011.5479329, "msecs": 547.0, "relativeCreated": 2428.2166957855225, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: GET http://test/api/v1/entities/?limit=5&offset=0 \"HTTP/1.1 200 OK\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932011.572169, "msecs": 572.0, "relativeCreated": 2452.4528980255127, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: GET http://test/api/v1/entities/?limit=5&offset=0 \"HTTP/1.1 200 OK\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932011.597277, "msecs": 597.0, "relativeCreated": 2477.5607585906982, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: GET http://test/api/v1/entities/?limit=5&offset=0 \"HTTP/1.1 200 OK\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932011.622725, "msecs": 622.0, "relativeCreated": 2503.0088424682617, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: GET http://test/api/v1/entities/?limit=5&offset=0 \"HTTP/1.1 200 OK\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932011.647284, "msecs": 647.0, "relativeCreated": 2527.5678634643555, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: GET http://test/api/v1/entities/?limit=5&offset=0 \"HTTP/1.1 200 OK\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932011.6731782, "msecs": 673.0, "relativeCreated": 2553.462028503418, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: GET http://test/api/v1/entities/?limit=5&offset=0 \"HTTP/1.1 200 OK\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932011.697628, "msecs": 697.0, "relativeCreated": 2577.911853790283, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: GET http://test/api/v1/entities/?limit=5&offset=0 \"HTTP/1.1 200 OK\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932011.722827, "msecs": 722.0, "relativeCreated": 2603.1107902526855, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: GET http://test/api/v1/entities/?limit=5&offset=0 \"HTTP/1.1 200 OK\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932011.749463, "msecs": 749.0, "relativeCreated": 2629.746913909912, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "backend.tests.test_performance_benchmarks", "msg": "Entity listing (page_size=5): avg=24.99ms", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/tests/test_performance_benchmarks.py", "filename": "test_performance_benchmarks.py", "module": "test_performance_benchmarks", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 289, "funcName": "test_entity_listing_performance", "created": 1751932011.749707, "msecs": 749.0, "relativeCreated": 2629.990816116333, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: GET http://test/api/v1/entities/?limit=10&offset=0 \"HTTP/1.1 200 OK\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932011.7756429, "msecs": 775.0, "relativeCreated": 2655.9267044067383, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: GET http://test/api/v1/entities/?limit=10&offset=0 \"HTTP/1.1 200 OK\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932011.801134, "msecs": 801.0, "relativeCreated": 2681.417942047119, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: GET http://test/api/v1/entities/?limit=10&offset=0 \"HTTP/1.1 200 OK\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932011.827159, "msecs": 827.0, "relativeCreated": 2707.4427604675293, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: GET http://test/api/v1/entities/?limit=10&offset=0 \"HTTP/1.1 200 OK\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932011.8523252, "msecs": 852.0, "relativeCreated": 2732.6090335845947, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: GET http://test/api/v1/entities/?limit=10&offset=0 \"HTTP/1.1 200 OK\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932011.8766441, "msecs": 876.0, "relativeCreated": 2756.927967071533, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: GET http://test/api/v1/entities/?limit=10&offset=0 \"HTTP/1.1 200 OK\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932011.9003499, "msecs": 900.0, "relativeCreated": 2780.6336879730225, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: GET http://test/api/v1/entities/?limit=10&offset=0 \"HTTP/1.1 200 OK\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932011.922961, "msecs": 922.0, "relativeCreated": 2803.2448291778564, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: GET http://test/api/v1/entities/?limit=10&offset=0 \"HTTP/1.1 200 OK\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932011.945488, "msecs": 945.0, "relativeCreated": 2825.7718086242676, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: GET http://test/api/v1/entities/?limit=10&offset=0 \"HTTP/1.1 200 OK\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932011.968469, "msecs": 968.0, "relativeCreated": 2848.752737045288, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: GET http://test/api/v1/entities/?limit=10&offset=0 \"HTTP/1.1 200 OK\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932011.992307, "msecs": 992.0, "relativeCreated": 2872.5907802581787, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "backend.tests.test_performance_benchmarks", "msg": "Entity listing (page_size=10): avg=24.22ms", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/tests/test_performance_benchmarks.py", "filename": "test_performance_benchmarks.py", "module": "test_performance_benchmarks", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 289, "funcName": "test_entity_listing_performance", "created": 1751932011.992536, "msecs": 992.0, "relativeCreated": 2872.8199005126953, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}]}, "teardown": {"duration": 0.00039158298750407994, "outcome": "passed"}}, {"nodeid": "tests/test_performance_benchmarks.py::TestEntityPerformance::test_entity_retrieval_performance", "lineno": 291, "outcome": "passed", "keywords": ["test_entity_retrieval_performance", "asyncio", "pytestmark", "TestEntityPerformance", "test_performance_benchmarks.py", "performance", "tests/__init__.py", "backend"], "setup": {"duration": 0.04151845900923945, "outcome": "passed", "stderr": "INFO:backend.tests.conftest:Cleaning test database: 5 entities, 6 connections\n", "log": [{"name": "backend.tests.conftest", "msg": "Cleaning test database: 5 entities, 6 connections", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/tests/conftest.py", "filename": "conftest.py", "module": "conftest", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 96, "funcName": "clean_test_database", "created": 1751932012.0173361, "msecs": 17.0, "relativeCreated": 2897.6199626922607, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}]}, "call": {"duration": 0.8153221670072526, "outcome": "passed", "stderr": "INFO:httpx:HTTP Request: GET http://test/api/v1/units/ \"HTTP/1.1 200 OK\"\nINFO:src.routes.entities:Entity created successfully: 'PerfEntity adfeceaa' with ID 1\nINFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfEntity bedebefa' with ID 2\nINFO:src.routes.entities:Entity verification successful: ID 2 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfEntity ecbecbad' with ID 3\nINFO:src.routes.entities:Entity verification successful: ID 3 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfEntity ecbcbfba' with ID 4\nINFO:src.routes.entities:Entity verification successful: ID 4 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfEntity adabcdff' with ID 5\nINFO:src.routes.entities:Entity verification successful: ID 5 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.connections:Validating entities for connection: from_entity_id=2, to_entity_id=5\nINFO:src.routes.connections:Entities validated successfully: from_entity=PerfEntity bedebefa (id=2), to_entity=PerfEntity adabcdff (id=5)\nINFO:src.routes.connections:Created connection ID: 1\nINFO:src.routes.connections:Primary connection: 2 -> 5, multiplier: 2.3\nINFO:src.routes.connections:Inverse connection should exist: 5 -> 2, multiplier: 0.4\nINFO:httpx:HTTP Request: POST http://test/api/v1/connections/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.connections:Validating entities for connection: from_entity_id=1, to_entity_id=2\nINFO:src.routes.connections:Entities validated successfully: from_entity=PerfEntity adfeceaa (id=1), to_entity=PerfEntity bedebefa (id=2)\nINFO:src.routes.connections:Created connection ID: 3\nINFO:src.routes.connections:Primary connection: 1 -> 2, multiplier: 0.2\nINFO:src.routes.connections:Inverse connection should exist: 2 -> 1, multiplier: 5.0\nINFO:httpx:HTTP Request: POST http://test/api/v1/connections/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.connections:Validating entities for connection: from_entity_id=1, to_entity_id=4\nINFO:src.routes.connections:Entities validated successfully: from_entity=PerfEntity adfeceaa (id=1), to_entity=PerfEntity ecbcbfba (id=4)\nINFO:src.routes.connections:Created connection ID: 5\nINFO:src.routes.connections:Primary connection: 1 -> 4, multiplier: 7.8\nINFO:src.routes.connections:Inverse connection should exist: 4 -> 1, multiplier: 0.1\nINFO:httpx:HTTP Request: POST http://test/api/v1/connections/ \"HTTP/1.1 201 Created\"\nINFO:httpx:HTTP Request: GET http://test/api/v1/entities/2 \"HTTP/1.1 200 OK\"\nINFO:httpx:HTTP Request: GET http://test/api/v1/entities/2 \"HTTP/1.1 200 OK\"\nINFO:httpx:HTTP Request: GET http://test/api/v1/entities/2 \"HTTP/1.1 200 OK\"\nINFO:httpx:HTTP Request: GET http://test/api/v1/entities/2 \"HTTP/1.1 200 OK\"\nINFO:httpx:HTTP Request: GET http://test/api/v1/entities/2 \"HTTP/1.1 200 OK\"\nINFO:httpx:HTTP Request: GET http://test/api/v1/entities/5 \"HTTP/1.1 200 OK\"\nINFO:httpx:HTTP Request: GET http://test/api/v1/entities/5 \"HTTP/1.1 200 OK\"\nINFO:httpx:HTTP Request: GET http://test/api/v1/entities/5 \"HTTP/1.1 200 OK\"\nINFO:httpx:HTTP Request: GET http://test/api/v1/entities/5 \"HTTP/1.1 200 OK\"\nINFO:httpx:HTTP Request: GET http://test/api/v1/entities/5 \"HTTP/1.1 200 OK\"\nINFO:httpx:HTTP Request: GET http://test/api/v1/entities/2 \"HTTP/1.1 200 OK\"\nINFO:httpx:HTTP Request: GET http://test/api/v1/entities/2 \"HTTP/1.1 200 OK\"\nINFO:httpx:HTTP Request: GET http://test/api/v1/entities/2 \"HTTP/1.1 200 OK\"\nINFO:httpx:HTTP Request: GET http://test/api/v1/entities/2 \"HTTP/1.1 200 OK\"\nINFO:httpx:HTTP Request: GET http://test/api/v1/entities/2 \"HTTP/1.1 200 OK\"\n", "log": [{"name": "httpx", "msg": "HTTP Request: GET http://test/api/v1/units/ \"HTTP/1.1 200 OK\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932012.0584362, "msecs": 58.0, "relativeCreated": 2938.7199878692627, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfEntity adfeceaa' with ID 1", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751932012.1010501, "msecs": 101.0, "relativeCreated": 2981.3339710235596, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 1 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751932012.101652, "msecs": 101.0, "relativeCreated": 2981.935739517212, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932012.103675, "msecs": 103.0, "relativeCreated": 2983.9587211608887, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfEntity bedebefa' with ID 2", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751932012.148247, "msecs": 148.0, "relativeCreated": 3028.5308361053467, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 2 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751932012.148812, "msecs": 148.0, "relativeCreated": 3029.0958881378174, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932012.150634, "msecs": 150.0, "relativeCreated": 3030.9178829193115, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfEntity ecbecbad' with ID 3", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751932012.197703, "msecs": 197.0, "relativeCreated": 3077.986717224121, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 3 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751932012.198316, "msecs": 198.0, "relativeCreated": 3078.5999298095703, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932012.2002828, "msecs": 200.0, "relativeCreated": 3080.566644668579, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfEntity ecbcbfba' with ID 4", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751932012.247953, "msecs": 247.0, "relativeCreated": 3128.236770629883, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 4 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751932012.248527, "msecs": 248.0, "relativeCreated": 3128.8108825683594, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932012.250415, "msecs": 250.0, "relativeCreated": 3130.6989192962646, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfEntity adabcdff' with ID 5", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751932012.298613, "msecs": 298.0, "relativeCreated": 3178.896903991699, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 5 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751932012.299241, "msecs": 299.0, "relativeCreated": 3179.5248985290527, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932012.3012831, "msecs": 301.0, "relativeCreated": 3181.5669536590576, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.connections", "msg": "Validating entities for connection: from_entity_id=2, to_entity_id=5", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/connections.py", "filename": "connections.py", "module": "connections", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 30, "funcName": "create_connection", "created": 1751932012.301813, "msecs": 301.0, "relativeCreated": 3182.0967197418213, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.connections", "msg": "Entities validated successfully: from_entity=PerfEntity bedebefa (id=2), to_entity=PerfEntity adabcdff (id=5)", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/connections.py", "filename": "connections.py", "module": "connections", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 54, "funcName": "create_connection", "created": 1751932012.324458, "msecs": 324.0, "relativeCreated": 3204.7417163848877, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.connections", "msg": "Created connection ID: 1", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/connections.py", "filename": "connections.py", "module": "connections", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 149, "funcName": "create_connection", "created": 1751932012.352264, "msecs": 352.0, "relativeCreated": 3232.5477600097656, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.connections", "msg": "Primary connection: 2 -> 5, multiplier: 2.3", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/connections.py", "filename": "connections.py", "module": "connections", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 150, "funcName": "create_connection", "created": 1751932012.3523672, "msecs": 352.0, "relativeCreated": 3232.6509952545166, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.connections", "msg": "Inverse connection should exist: 5 -> 2, multiplier: 0.4", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/connections.py", "filename": "connections.py", "module": "connections", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 151, "funcName": "create_connection", "created": 1751932012.352406, "msecs": 352.0, "relativeCreated": 3232.68985748291, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/connections/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932012.354377, "msecs": 354.0, "relativeCreated": 3234.660863876343, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.connections", "msg": "Validating entities for connection: from_entity_id=1, to_entity_id=2", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/connections.py", "filename": "connections.py", "module": "connections", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 30, "funcName": "create_connection", "created": 1751932012.35487, "msecs": 354.0, "relativeCreated": 3235.153913497925, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.connections", "msg": "Entities validated successfully: from_entity=PerfEntity adfeceaa (id=1), to_entity=PerfEntity bedebefa (id=2)", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/connections.py", "filename": "connections.py", "module": "connections", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 54, "funcName": "create_connection", "created": 1751932012.378711, "msecs": 378.0, "relativeCreated": 3258.9948177337646, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.connections", "msg": "Created connection ID: 3", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/connections.py", "filename": "connections.py", "module": "connections", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 149, "funcName": "create_connection", "created": 1751932012.4059038, "msecs": 405.0, "relativeCreated": 3286.1876487731934, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.connections", "msg": "Primary connection: 1 -> 2, multiplier: 0.2", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/connections.py", "filename": "connections.py", "module": "connections", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 150, "funcName": "create_connection", "created": 1751932012.405994, "msecs": 405.0, "relativeCreated": 3286.2777709960938, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.connections", "msg": "Inverse connection should exist: 2 -> 1, multiplier: 5.0", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/connections.py", "filename": "connections.py", "module": "connections", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 151, "funcName": "create_connection", "created": 1751932012.406025, "msecs": 406.0, "relativeCreated": 3286.308765411377, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/connections/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932012.407857, "msecs": 407.0, "relativeCreated": 3288.1407737731934, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.connections", "msg": "Validating entities for connection: from_entity_id=1, to_entity_id=4", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/connections.py", "filename": "connections.py", "module": "connections", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 30, "funcName": "create_connection", "created": 1751932012.408292, "msecs": 408.0, "relativeCreated": 3288.5758876800537, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.connections", "msg": "Entities validated successfully: from_entity=PerfEntity adfeceaa (id=1), to_entity=PerfEntity ecbcbfba (id=4)", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/connections.py", "filename": "connections.py", "module": "connections", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 54, "funcName": "create_connection", "created": 1751932012.4314601, "msecs": 431.0, "relativeCreated": 3311.743974685669, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.connections", "msg": "Created connection ID: 5", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/connections.py", "filename": "connections.py", "module": "connections", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 149, "funcName": "create_connection", "created": 1751932012.460172, "msecs": 460.0, "relativeCreated": 3340.4557704925537, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.connections", "msg": "Primary connection: 1 -> 4, multiplier: 7.8", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/connections.py", "filename": "connections.py", "module": "connections", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 150, "funcName": "create_connection", "created": 1751932012.4602659, "msecs": 460.0, "relativeCreated": 3340.5497074127197, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "src.routes.connections", "msg": "Inverse connection should exist: 4 -> 1, multiplier: 0.1", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/connections.py", "filename": "connections.py", "module": "connections", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 151, "funcName": "create_connection", "created": 1751932012.460302, "msecs": 460.0, "relativeCreated": 3340.585947036743, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/connections/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932012.462044, "msecs": 462.0, "relativeCreated": 3342.327833175659, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: GET http://test/api/v1/entities/2 \"HTTP/1.1 200 OK\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932012.486027, "msecs": 486.0, "relativeCreated": 3366.3108348846436, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: GET http://test/api/v1/entities/2 \"HTTP/1.1 200 OK\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932012.510606, "msecs": 510.0, "relativeCreated": 3390.889883041382, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: GET http://test/api/v1/entities/2 \"HTTP/1.1 200 OK\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932012.5364969, "msecs": 536.0, "relativeCreated": 3416.780710220337, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: GET http://test/api/v1/entities/2 \"HTTP/1.1 200 OK\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932012.5610712, "msecs": 561.0, "relativeCreated": 3441.354990005493, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: GET http://test/api/v1/entities/2 \"HTTP/1.1 200 OK\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932012.586114, "msecs": 586.0, "relativeCreated": 3466.397762298584, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: GET http://test/api/v1/entities/5 \"HTTP/1.1 200 OK\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932012.6107461, "msecs": 610.0, "relativeCreated": 3491.029977798462, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: GET http://test/api/v1/entities/5 \"HTTP/1.1 200 OK\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932012.637921, "msecs": 637.0, "relativeCreated": 3518.204927444458, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: GET http://test/api/v1/entities/5 \"HTTP/1.1 200 OK\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932012.66397, "msecs": 663.0, "relativeCreated": 3544.2538261413574, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: GET http://test/api/v1/entities/5 \"HTTP/1.1 200 OK\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932012.688798, "msecs": 688.0, "relativeCreated": 3569.0817832946777, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: GET http://test/api/v1/entities/5 \"HTTP/1.1 200 OK\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932012.71549, "msecs": 715.0, "relativeCreated": 3595.773935317993, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: GET http://test/api/v1/entities/2 \"HTTP/1.1 200 OK\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932012.742402, "msecs": 742.0, "relativeCreated": 3622.6859092712402, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: GET http://test/api/v1/entities/2 \"HTTP/1.1 200 OK\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932012.767633, "msecs": 767.0, "relativeCreated": 3647.916793823242, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: GET http://test/api/v1/entities/2 \"HTTP/1.1 200 OK\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932012.794194, "msecs": 794.0, "relativeCreated": 3674.4778156280518, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: GET http://test/api/v1/entities/2 \"HTTP/1.1 200 OK\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932012.822966, "msecs": 822.0, "relativeCreated": 3703.249931335449, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}, {"name": "httpx", "msg": "HTTP Request: GET http://test/api/v1/entities/2 \"HTTP/1.1 200 OK\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751932012.84999, "msecs": 849.0, "relativeCreated": 3730.273723602295, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 94477}]}, "teardown": {"duration": 0.0007489999989047647, "outcome": "passed"}}]}