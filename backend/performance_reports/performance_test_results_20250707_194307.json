{"created": 1751931789.965326, "duration": 1.057110071182251, "exitcode": 0, "root": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend", "environment": {}, "summary": {"passed": 1, "total": 1, "collected": 1}, "collectors": [{"nodeid": "", "outcome": "passed", "result": [{"nodeid": "tests/test_performance_benchmarks.py::TestEntityPerformance::test_entity_creation_performance", "type": "Coroutine", "lineno": 239}]}], "tests": [{"nodeid": "tests/test_performance_benchmarks.py::TestEntityPerformance::test_entity_creation_performance", "lineno": 239, "outcome": "passed", "keywords": ["test_entity_creation_performance", "asyncio", "pytestmark", "TestEntityPerformance", "test_performance_benchmarks.py", "performance", "tests/__init__.py", "backend"], "setup": {"duration": 0.12502916701487266, "outcome": "passed", "stderr": "INFO:backend.tests.conftest:Cleaning test database: 1 entities, 0 connections\n", "log": [{"name": "backend.tests.conftest", "msg": "Cleaning test database: 1 entities, 0 connections", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/tests/conftest.py", "filename": "conftest.py", "module": "conftest", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 96, "funcName": "clean_test_database", "created": 1751931789.041333, "msecs": 41.0, "relativeCreated": 810.2619647979736, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}]}, "call": {"duration": 0.8807025839923881, "outcome": "passed", "stderr": "INFO:src.routes.entities:Entity created successfully: 'PerfTest cbbbcceb' with ID 1\nINFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfTest abfeabce' with ID 1\nINFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfTest fbadabaa' with ID 3\nINFO:src.routes.entities:Entity verification successful: ID 3 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfTest fcafddcc' with ID 5\nINFO:src.routes.entities:Entity verification successful: ID 5 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfTest fdbdabfc' with ID 7\nINFO:src.routes.entities:Entity verification successful: ID 7 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfTest edbddcfb' with ID 9\nINFO:src.routes.entities:Entity verification successful: ID 9 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfTest dedbbfcb' with ID 10\nINFO:src.routes.entities:Entity verification successful: ID 10 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfTest decdfbfd' with ID 11\nINFO:src.routes.entities:Entity verification successful: ID 11 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfTest dcaeacff' with ID 12\nINFO:src.routes.entities:Entity verification successful: ID 12 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfTest aaeccebe' with ID 13\nINFO:src.routes.entities:Entity verification successful: ID 13 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfTest caddbfcc' with ID 1\nINFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfTest afbdfebe' with ID 3\nINFO:src.routes.entities:Entity verification successful: ID 3 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfTest dbbbfbeb' with ID 5\nINFO:src.routes.entities:Entity verification successful: ID 5 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfTest aaabbeba' with ID 7\nINFO:src.routes.entities:Entity verification successful: ID 7 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfTest ecabbbff' with ID 9\nINFO:src.routes.entities:Entity verification successful: ID 9 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfTest cedfebaf' with ID 10\nINFO:src.routes.entities:Entity verification successful: ID 10 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfTest ecadfcaf' with ID 11\nINFO:src.routes.entities:Entity verification successful: ID 11 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfTest bdcecafe' with ID 12\nINFO:src.routes.entities:Entity verification successful: ID 12 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nINFO:src.routes.entities:Entity created successfully: 'PerfTest dcbbcbab' with ID 13\nINFO:src.routes.entities:Entity verification successful: ID 13 is immediately queryable\nINFO:httpx:HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"\nERROR:backend.tests.test_performance_benchmarks:Request failed on iteration 19: Could not refresh instance '<Entity at 0x10a2aa3d0>'\nINFO:backend.tests.test_performance_benchmarks:Entity creation performance: avg=44.09ms, p95=50.11ms, success_rate=95.00%\n", "log": [{"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfTest cbbbcceb' with ID 1", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751931789.106372, "msecs": 106.0, "relativeCreated": 875.3011226654053, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 1 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751931789.107163, "msecs": 107.0, "relativeCreated": 876.0919570922852, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751931789.109278, "msecs": 109.0, "relativeCreated": 878.2069683074951, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfTest abfeabce' with ID 1", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751931789.150712, "msecs": 150.0, "relativeCreated": 919.6410179138184, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 1 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751931789.1512809, "msecs": 151.0, "relativeCreated": 920.2098846435547, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751931789.153138, "msecs": 153.0, "relativeCreated": 922.0669269561768, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfTest fbadabaa' with ID 3", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751931789.1939042, "msecs": 193.0, "relativeCreated": 962.8331661224365, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 3 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751931789.194498, "msecs": 194.0, "relativeCreated": 963.4270668029785, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751931789.196262, "msecs": 196.0, "relativeCreated": 965.1908874511719, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfTest fcafddcc' with ID 5", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751931789.237359, "msecs": 237.0, "relativeCreated": 1006.2880516052246, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 5 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751931789.2379782, "msecs": 237.0, "relativeCreated": 1006.9072246551514, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751931789.239587, "msecs": 239.0, "relativeCreated": 1008.5160732269287, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfTest fdbdabfc' with ID 7", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751931789.279962, "msecs": 279.0, "relativeCreated": 1048.8910675048828, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 7 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751931789.280559, "msecs": 280.0, "relativeCreated": 1049.4880676269531, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751931789.282198, "msecs": 282.0, "relativeCreated": 1051.1269569396973, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfTest edbddcfb' with ID 9", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751931789.323571, "msecs": 323.0, "relativeCreated": 1092.4999713897705, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 9 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751931789.324235, "msecs": 324.0, "relativeCreated": 1093.1639671325684, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751931789.326179, "msecs": 326.0, "relativeCreated": 1095.1080322265625, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfTest dedbbfcb' with ID 10", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751931789.368213, "msecs": 368.0, "relativeCreated": 1137.1419429779053, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 10 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751931789.368918, "msecs": 368.0, "relativeCreated": 1137.8469467163086, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751931789.371072, "msecs": 371.0, "relativeCreated": 1140.0010585784912, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfTest decdfbfd' with ID 11", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751931789.413601, "msecs": 413.0, "relativeCreated": 1182.5299263000488, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 11 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751931789.414517, "msecs": 414.0, "relativeCreated": 1183.445930480957, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751931789.4167721, "msecs": 416.0, "relativeCreated": 1185.7011318206787, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfTest dcaeacff' with ID 12", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751931789.457838, "msecs": 457.0, "relativeCreated": 1226.7670631408691, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 12 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751931789.458461, "msecs": 458.0, "relativeCreated": 1227.3900508880615, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751931789.46032, "msecs": 460.0, "relativeCreated": 1229.2490005493164, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfTest aaeccebe' with ID 13", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751931789.502195, "msecs": 502.0, "relativeCreated": 1271.1238861083984, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 13 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751931789.503036, "msecs": 503.0, "relativeCreated": 1271.9650268554688, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751931789.505604, "msecs": 505.0, "relativeCreated": 1274.5330333709717, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfTest caddbfcc' with ID 1", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751931789.5502121, "msecs": 550.0, "relativeCreated": 1319.141149520874, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 1 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751931789.5507698, "msecs": 550.0, "relativeCreated": 1319.6988105773926, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751931789.552726, "msecs": 552.0, "relativeCreated": 1321.655035018921, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfTest afbdfebe' with ID 3", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751931789.595649, "msecs": 595.0, "relativeCreated": 1364.5780086517334, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 3 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751931789.5962172, "msecs": 596.0, "relativeCreated": 1365.1461601257324, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751931789.5979571, "msecs": 597.0, "relativeCreated": 1366.8861389160156, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfTest dbbbfbeb' with ID 5", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751931789.638749, "msecs": 638.0, "relativeCreated": 1407.6778888702393, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 5 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751931789.639273, "msecs": 639.0, "relativeCreated": 1408.2019329071045, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751931789.640862, "msecs": 640.0, "relativeCreated": 1409.7909927368164, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfTest aaabbeba' with ID 7", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751931789.681772, "msecs": 681.0, "relativeCreated": 1450.7009983062744, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 7 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751931789.6823711, "msecs": 682.0, "relativeCreated": 1451.3001441955566, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751931789.684032, "msecs": 684.0, "relativeCreated": 1452.9609680175781, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfTest ecabbbff' with ID 9", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751931789.724766, "msecs": 724.0, "relativeCreated": 1493.6950206756592, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 9 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751931789.725461, "msecs": 725.0, "relativeCreated": 1494.3900108337402, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751931789.727205, "msecs": 727.0, "relativeCreated": 1496.1340427398682, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfTest cedfebaf' with ID 10", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751931789.7675872, "msecs": 767.0, "relativeCreated": 1536.5161895751953, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 10 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751931789.768106, "msecs": 768.0, "relativeCreated": 1537.0349884033203, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751931789.769708, "msecs": 769.0, "relativeCreated": 1538.6369228363037, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfTest ecadfcaf' with ID 11", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751931789.81091, "msecs": 810.0, "relativeCreated": 1579.8389911651611, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 11 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751931789.811459, "msecs": 811.0, "relativeCreated": 1580.388069152832, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751931789.813091, "msecs": 813.0, "relativeCreated": 1582.0200443267822, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfTest bdcecafe' with ID 12", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751931789.853292, "msecs": 853.0, "relativeCreated": 1622.2209930419922, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 12 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751931789.8538408, "msecs": 853.0, "relativeCreated": 1622.769832611084, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751931789.855498, "msecs": 855.0, "relativeCreated": 1624.427080154419, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "src.routes.entities", "msg": "Entity created successfully: 'PerfTest dcbbcbab' with ID 13", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 31, "funcName": "create_entity", "created": 1751931789.895454, "msecs": 895.0, "relativeCreated": 1664.3829345703125, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "src.routes.entities", "msg": "Entity verification successful: ID 13 is immediately queryable", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/src/routes/entities.py", "filename": "entities.py", "module": "entities", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 37, "funcName": "create_entity", "created": 1751931789.896009, "msecs": 896.0, "relativeCreated": 1664.937973022461, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "httpx", "msg": "HTTP Request: POST http://test/api/v1/entities/ \"HTTP/1.1 201 Created\"", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_client.py", "filename": "_client.py", "module": "_client", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 1758, "funcName": "_send_single_request", "created": 1751931789.897769, "msecs": 897.0, "relativeCreated": 1666.6979789733887, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "backend.tests.test_performance_benchmarks", "msg": "Request failed on iteration 19: Could not refresh instance '<Entity at 0x10a2aa3d0>'", "args": null, "levelname": "ERROR", "levelno": 40, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/tests/test_performance_benchmarks.py", "filename": "test_performance_benchmarks.py", "module": "test_performance_benchmarks", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 163, "funcName": "measure_endpoint_performance", "created": 1751931789.939242, "msecs": 939.0, "relativeCreated": 1708.1708908081055, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}, {"name": "backend.tests.test_performance_benchmarks", "msg": "Entity creation performance: avg=44.09ms, p95=50.11ms, success_rate=95.00%", "args": null, "levelname": "INFO", "levelno": 20, "pathname": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/tests/test_performance_benchmarks.py", "filename": "test_performance_benchmarks.py", "module": "test_performance_benchmarks", "exc_info": null, "exc_text": null, "stack_info": null, "lineno": 261, "funcName": "test_entity_creation_performance", "created": 1751931789.9395368, "msecs": 939.0, "relativeCreated": 1708.465814590454, "thread": 8592223360, "threadName": "MainThread", "processName": "MainProcess", "process": 93397}]}, "teardown": {"duration": 0.0006745829887222499, "outcome": "passed"}}]}