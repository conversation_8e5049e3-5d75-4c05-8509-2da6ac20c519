============================= test session starts ==============================
collected 455 items

tests/test_api_contract_validation.py ........................           [  5%]
tests/test_api_error_handling.py ..............................          [ 11%]
tests/test_compare_comprehensive.py ..................                   [ 15%]
tests/test_compare_error_scenarios.py ...............FF                  [ 19%]
tests/test_complex_graph_scenarios.py FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF.FF [ 27%]
F.                                                                       [ 27%]
tests/test_comprehensive_connections.py ..................               [ 31%]
tests/test_comprehensive_edge_cases.py ..............                    [ 34%]
tests/test_comprehensive_entities.py .........................           [ 40%]
tests/test_comprehensive_pathfinding.py .............                    [ 42%]
tests/test_concurrent_operations.py .F.F.FFFFF                           [ 45%]
tests/test_connection_regression.py ......                               [ 46%]
tests/test_connections_advanced_scenarios.py ..........                  [ 48%]
tests/test_connections_comprehensive_crud.py ............                [ 51%]
tests/test_connections_critical_coverage.py F....FFFF....F...FFF...F.... [ 57%]
                                                                         [ 57%]
tests/test_connections_high_coverage.py .........                        [ 59%]
tests/test_data_validation.py ............                               [ 61%]
tests/test_database_transactions.py ...FFF...F..FFF..F                   [ 65%]
tests/test_endpoints.py ....                                             [ 66%]
tests/test_entities_comprehensive_coverage.py .......................... [ 72%]
                                                                         [ 72%]
tests/test_error_recovery_cleanup.py .FFF..FFF                           [ 74%]
tests/test_integration.py .........                                      [ 76%]
tests/test_integration_simple.py ................                        [ 80%]
tests/test_performance_benchmarks.py FFFFFFFFF.FFFF                      [ 83%]
tests/test_performance_integration.py FFF..FF......                      [ 85%]
tests/test_phase1_demo.py ....                                           [ 86%]
tests/test_phase2_verification.py .....                                  [ 87%]
tests/test_phase3_connection_validation.py ...                           [ 88%]
tests/test_phase3_inverse_connections.py ...                             [ 89%]
tests/test_phase3_pathfinding.py .....                                   [ 90%]
tests/test_services_comprehensive.py ...................                 [ 94%]
tests/test_units_comprehensive_coverage.py .........................     [100%]

=================================== FAILURES ===================================
____ TestCompareRoutePerformanceEdgeCases.test_compare_with_max_path_length ____
tests/test_compare_error_scenarios.py:499: in test_compare_with_max_path_length
    assert conn_response.status_code == 201
E   assert 404 == 201
E    +  where 404 = <Response [404 Not Found]>.status_code
---------------------------- Captured stderr setup -----------------------------
INFO:backend.tests.conftest:Cleaning test database: 3 entities, 4 connections
------------------------------ Captured log setup ------------------------------
INFO     backend.tests.conftest:conftest.py:96 Cleaning test database: 3 entities, 4 connections
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'MaxPathA cafadadf' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'MaxPathB bffccabd' with ID 2
INFO:src.routes.entities:Entity verification successful: ID 2 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'MaxPathC beafddda' with ID 3
INFO:src.routes.entities:Entity verification successful: ID 3 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'MaxPathD baafbdac' with ID 4
INFO:src.routes.entities:Entity verification successful: ID 4 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'MaxPathE ecbbaaaa' with ID 5
INFO:src.routes.entities:Entity verification successful: ID 5 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'MaxPathF baacceae' with ID 6
INFO:src.routes.entities:Entity verification successful: ID 6 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'MaxPathG ddfcfdad' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:httpx:HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO:src.routes.connections:Validating entities for connection: from_entity_id=1, to_entity_id=2
ERROR:src.routes.connections:To entity 2 not found. Available entities: [1]
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 404 Not Found"
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'MaxPathA cafadadf' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'MaxPathB bffccabd' with ID 2
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 2 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'MaxPathC beafddda' with ID 3
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 3 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'MaxPathD baafbdac' with ID 4
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 4 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'MaxPathE ecbbaaaa' with ID 5
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 5 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'MaxPathF baacceae' with ID 6
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 6 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'MaxPathG ddfcfdad' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=1, to_entity_id=2
ERROR    src.routes.connections:connections.py:51 To entity 2 not found. Available entities: [1]
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 404 Not Found"
_ TestCompareRoutePerformanceEdgeCases.test_compare_with_path_exceeding_max_length _
tests/test_compare_error_scenarios.py:535: in test_compare_with_path_exceeding_max_length
    conn_response = await test_client.post("/api/v1/connections/", json=connection_data)
venv/lib/python3.11/site-packages/httpx/_client.py:1877: in post
    return await self.request(
venv/lib/python3.11/site-packages/httpx/_client.py:1559: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
venv/lib/python3.11/site-packages/httpx/_client.py:1646: in send
    response = await self._send_handling_auth(
venv/lib/python3.11/site-packages/httpx/_client.py:1674: in _send_handling_auth
    response = await self._send_handling_redirects(
venv/lib/python3.11/site-packages/httpx/_client.py:1711: in _send_handling_redirects
    response = await self._send_single_request(request)
venv/lib/python3.11/site-packages/httpx/_client.py:1748: in _send_single_request
    response = await transport.handle_async_request(request)
venv/lib/python3.11/site-packages/httpx/_transports/asgi.py:162: in handle_async_request
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/applications.py:123: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
venv/lib/python3.11/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:762: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:782: in app
    await route.handle(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:297: in handle
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:77: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:72: in app
    response = await func(request)
venv/lib/python3.11/site-packages/fastapi/routing.py:299: in app
    raise e
venv/lib/python3.11/site-packages/fastapi/routing.py:294: in app
    raw_response = await run_endpoint_function(
venv/lib/python3.11/site-packages/fastapi/routing.py:191: in run_endpoint_function
    return await dependant.call(**values)
src/routes/connections.py:146: in create_connection
    fresh_connection = result.scalar_one()
venv/lib/python3.11/site-packages/sqlalchemy/engine/result.py:1474: in scalar_one
    return self._only_one_row(
venv/lib/python3.11/site-packages/sqlalchemy/engine/result.py:757: in _only_one_row
    raise exc.NoResultFound(
E   sqlalchemy.exc.NoResultFound: No row was found when one was required
---------------------------- Captured stderr setup -----------------------------
INFO:backend.tests.conftest:Cleaning test database: 1 entities, 0 connections
------------------------------ Captured log setup ------------------------------
INFO     backend.tests.conftest:conftest.py:96 Cleaning test database: 1 entities, 0 connections
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'ExceedPathA debeafdf' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'ExceedPathB bccebebb' with ID 2
INFO:src.routes.entities:Entity verification successful: ID 2 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'ExceedPathC fcccdece' with ID 3
INFO:src.routes.entities:Entity verification successful: ID 3 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'ExceedPathD dcbdbbff' with ID 4
INFO:src.routes.entities:Entity verification successful: ID 4 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'ExceedPathE faaecbba' with ID 5
INFO:src.routes.entities:Entity verification successful: ID 5 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'ExceedPathF cbbebfaa' with ID 6
INFO:src.routes.entities:Entity verification successful: ID 6 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'ExceedPathG adcfbaaf' with ID 7
INFO:src.routes.entities:Entity verification successful: ID 7 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'ExceedPathH daffbabf' with ID 8
INFO:src.routes.entities:Entity verification successful: ID 8 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:httpx:HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO:src.routes.connections:Validating entities for connection: from_entity_id=1, to_entity_id=2
INFO:src.routes.connections:Entities validated successfully: from_entity=ExceedPathA debeafdf (id=1), to_entity=ExceedPathB bccebebb (id=2)
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'ExceedPathA debeafdf' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'ExceedPathB bccebebb' with ID 2
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 2 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'ExceedPathC fcccdece' with ID 3
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 3 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'ExceedPathD dcbdbbff' with ID 4
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 4 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'ExceedPathE faaecbba' with ID 5
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 5 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'ExceedPathF cbbebfaa' with ID 6
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 6 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'ExceedPathG adcfbaaf' with ID 7
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 7 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'ExceedPathH daffbabf' with ID 8
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 8 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=1, to_entity_id=2
INFO     src.routes.connections:connections.py:54 Entities validated successfully: from_entity=ExceedPathA debeafdf (id=1), to_entity=ExceedPathB bccebebb (id=2)
________________ TestStarTopology.test_star_hub_to_spoke_paths _________________
tests/test_complex_graph_scenarios.py:28: in test_star_hub_to_spoke_paths
    data = await ComplexGraphFixtures.create_star_graph(test_client, num_spokes=5)
tests/fixtures/complex_graphs.py:493: in create_star_graph
    return await topology.setup(test_client, unit_name)
tests/fixtures/complex_graphs.py:81: in setup
    self.entities = await self.create_entities(test_client, entity_names)
tests/fixtures/complex_graphs.py:39: in create_entities
    assert response.status_code == 201, f"Failed to create entity {name}: {response.text}"
E   AssertionError: Failed to create entity Spoke0: {"detail":[{"type":"string_pattern_mismatch","loc":["body","name"],"msg":"String should match pattern '^[a-zA-Z\\s]+$'","input":"Spoke0 qeojohrx","ctx":{"pattern":"^[a-zA-Z\\s]+$"},"url":"https://errors.pydantic.dev/2.5/v/string_pattern_mismatch"}]}
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'Hub qeojohrx' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'Hub qeojohrx' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
_______________ TestStarTopology.test_star_spoke_to_spoke_paths ________________
tests/test_complex_graph_scenarios.py:48: in test_star_spoke_to_spoke_paths
    data = await ComplexGraphFixtures.create_star_graph(test_client, num_spokes=4)
tests/fixtures/complex_graphs.py:493: in create_star_graph
    return await topology.setup(test_client, unit_name)
tests/fixtures/complex_graphs.py:81: in setup
    self.entities = await self.create_entities(test_client, entity_names)
tests/fixtures/complex_graphs.py:39: in create_entities
    assert response.status_code == 201, f"Failed to create entity {name}: {response.text}"
E   AssertionError: Failed to create entity Spoke0: {"detail":[{"type":"string_pattern_mismatch","loc":["body","name"],"msg":"String should match pattern '^[a-zA-Z\\s]+$'","input":"Spoke0 lvifzkat","ctx":{"pattern":"^[a-zA-Z\\s]+$"},"url":"https://errors.pydantic.dev/2.5/v/string_pattern_mismatch"}]}
---------------------------- Captured stderr setup -----------------------------
INFO:backend.tests.conftest:Cleaning test database: 1 entities, 0 connections
------------------------------ Captured log setup ------------------------------
INFO     backend.tests.conftest:conftest.py:96 Cleaning test database: 1 entities, 0 connections
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'Hub lvifzkat' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'Hub lvifzkat' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
__________________ TestStarTopology.test_star_isolated_spokes __________________
tests/test_complex_graph_scenarios.py:68: in test_star_isolated_spokes
    data = await ComplexGraphFixtures.create_star_graph(test_client, num_spokes=3)
tests/fixtures/complex_graphs.py:493: in create_star_graph
    return await topology.setup(test_client, unit_name)
tests/fixtures/complex_graphs.py:81: in setup
    self.entities = await self.create_entities(test_client, entity_names)
tests/fixtures/complex_graphs.py:39: in create_entities
    assert response.status_code == 201, f"Failed to create entity {name}: {response.text}"
E   AssertionError: Failed to create entity Spoke0: {"detail":[{"type":"string_pattern_mismatch","loc":["body","name"],"msg":"String should match pattern '^[a-zA-Z\\s]+$'","input":"Spoke0 linsgivc","ctx":{"pattern":"^[a-zA-Z\\s]+$"},"url":"https://errors.pydantic.dev/2.5/v/string_pattern_mismatch"}]}
---------------------------- Captured stderr setup -----------------------------
INFO:backend.tests.conftest:Cleaning test database: 1 entities, 0 connections
------------------------------ Captured log setup ------------------------------
INFO     backend.tests.conftest:conftest.py:96 Cleaning test database: 1 entities, 0 connections
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'Hub linsgivc' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'Hub linsgivc' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
_________________ TestChainTopology.test_chain_end_to_end_path _________________
tests/test_complex_graph_scenarios.py:92: in test_chain_end_to_end_path
    data = await ComplexGraphFixtures.create_chain_graph(test_client, length=6)
tests/fixtures/complex_graphs.py:499: in create_chain_graph
    return await topology.setup(test_client, unit_name)
tests/fixtures/complex_graphs.py:115: in setup
    self.entities = await self.create_entities(test_client, entity_names)
tests/fixtures/complex_graphs.py:39: in create_entities
    assert response.status_code == 201, f"Failed to create entity {name}: {response.text}"
E   AssertionError: Failed to create entity Node0: {"detail":[{"type":"string_pattern_mismatch","loc":["body","name"],"msg":"String should match pattern '^[a-zA-Z\\s]+$'","input":"Node0 nnouxujv","ctx":{"pattern":"^[a-zA-Z\\s]+$"},"url":"https://errors.pydantic.dev/2.5/v/string_pattern_mismatch"}]}
----------------------------- Captured stderr call -----------------------------
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
_______________ TestChainTopology.test_chain_intermediate_paths ________________
tests/test_complex_graph_scenarios.py:110: in test_chain_intermediate_paths
    data = await ComplexGraphFixtures.create_chain_graph(test_client, length=5)
tests/fixtures/complex_graphs.py:499: in create_chain_graph
    return await topology.setup(test_client, unit_name)
tests/fixtures/complex_graphs.py:115: in setup
    self.entities = await self.create_entities(test_client, entity_names)
tests/fixtures/complex_graphs.py:39: in create_entities
    assert response.status_code == 201, f"Failed to create entity {name}: {response.text}"
E   AssertionError: Failed to create entity Node0: {"detail":[{"type":"string_pattern_mismatch","loc":["body","name"],"msg":"String should match pattern '^[a-zA-Z\\s]+$'","input":"Node0 drmanang","ctx":{"pattern":"^[a-zA-Z\\s]+$"},"url":"https://errors.pydantic.dev/2.5/v/string_pattern_mismatch"}]}
----------------------------- Captured stderr call -----------------------------
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
________________ TestChainTopology.test_chain_max_length_limit _________________
tests/test_complex_graph_scenarios.py:129: in test_chain_max_length_limit
    data = await ComplexGraphFixtures.create_chain_graph(test_client, length=9)
tests/fixtures/complex_graphs.py:499: in create_chain_graph
    return await topology.setup(test_client, unit_name)
tests/fixtures/complex_graphs.py:115: in setup
    self.entities = await self.create_entities(test_client, entity_names)
tests/fixtures/complex_graphs.py:39: in create_entities
    assert response.status_code == 201, f"Failed to create entity {name}: {response.text}"
E   AssertionError: Failed to create entity Node0: {"detail":[{"type":"string_pattern_mismatch","loc":["body","name"],"msg":"String should match pattern '^[a-zA-Z\\s]+$'","input":"Node0 ipxagcar","ctx":{"pattern":"^[a-zA-Z\\s]+$"},"url":"https://errors.pydantic.dev/2.5/v/string_pattern_mismatch"}]}
----------------------------- Captured stderr call -----------------------------
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
__________________ TestChainTopology.test_chain_reverse_path ___________________
tests/test_complex_graph_scenarios.py:147: in test_chain_reverse_path
    data = await ComplexGraphFixtures.create_chain_graph(test_client, length=4)
tests/fixtures/complex_graphs.py:499: in create_chain_graph
    return await topology.setup(test_client, unit_name)
tests/fixtures/complex_graphs.py:115: in setup
    self.entities = await self.create_entities(test_client, entity_names)
tests/fixtures/complex_graphs.py:39: in create_entities
    assert response.status_code == 201, f"Failed to create entity {name}: {response.text}"
E   AssertionError: Failed to create entity Node0: {"detail":[{"type":"string_pattern_mismatch","loc":["body","name"],"msg":"String should match pattern '^[a-zA-Z\\s]+$'","input":"Node0 zwwhfbjh","ctx":{"pattern":"^[a-zA-Z\\s]+$"},"url":"https://errors.pydantic.dev/2.5/v/string_pattern_mismatch"}]}
----------------------------- Captured stderr call -----------------------------
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
______________ TestGridTopology.test_grid_corner_to_corner_paths _______________
tests/test_complex_graph_scenarios.py:170: in test_grid_corner_to_corner_paths
    data = await ComplexGraphFixtures.create_grid_graph(test_client, rows=3, cols=3)
tests/fixtures/complex_graphs.py:505: in create_grid_graph
    return await topology.setup(test_client, unit_name)
tests/fixtures/complex_graphs.py:150: in setup
    self.entities = await self.create_entities(test_client, entity_names)
tests/fixtures/complex_graphs.py:39: in create_entities
    assert response.status_code == 201, f"Failed to create entity {name}: {response.text}"
E   AssertionError: Failed to create entity Grid00: {"detail":[{"type":"string_pattern_mismatch","loc":["body","name"],"msg":"String should match pattern '^[a-zA-Z\\s]+$'","input":"Grid00 fgbztcyu","ctx":{"pattern":"^[a-zA-Z\\s]+$"},"url":"https://errors.pydantic.dev/2.5/v/string_pattern_mismatch"}]}
----------------------------- Captured stderr call -----------------------------
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
__________________ TestGridTopology.test_grid_adjacent_paths ___________________
tests/test_complex_graph_scenarios.py:190: in test_grid_adjacent_paths
    data = await ComplexGraphFixtures.create_grid_graph(test_client, rows=2, cols=2)
tests/fixtures/complex_graphs.py:505: in create_grid_graph
    return await topology.setup(test_client, unit_name)
tests/fixtures/complex_graphs.py:150: in setup
    self.entities = await self.create_entities(test_client, entity_names)
tests/fixtures/complex_graphs.py:39: in create_entities
    assert response.status_code == 201, f"Failed to create entity {name}: {response.text}"
E   AssertionError: Failed to create entity Grid00: {"detail":[{"type":"string_pattern_mismatch","loc":["body","name"],"msg":"String should match pattern '^[a-zA-Z\\s]+$'","input":"Grid00 qtutfwtg","ctx":{"pattern":"^[a-zA-Z\\s]+$"},"url":"https://errors.pydantic.dev/2.5/v/string_pattern_mismatch"}]}
----------------------------- Captured stderr call -----------------------------
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
__________________ TestGridTopology.test_grid_multiple_paths ___________________
tests/test_complex_graph_scenarios.py:209: in test_grid_multiple_paths
    data = await ComplexGraphFixtures.create_grid_graph(test_client, rows=3, cols=3)
tests/fixtures/complex_graphs.py:505: in create_grid_graph
    return await topology.setup(test_client, unit_name)
tests/fixtures/complex_graphs.py:150: in setup
    self.entities = await self.create_entities(test_client, entity_names)
tests/fixtures/complex_graphs.py:39: in create_entities
    assert response.status_code == 201, f"Failed to create entity {name}: {response.text}"
E   AssertionError: Failed to create entity Grid00: {"detail":[{"type":"string_pattern_mismatch","loc":["body","name"],"msg":"String should match pattern '^[a-zA-Z\\s]+$'","input":"Grid00 sdkzynfd","ctx":{"pattern":"^[a-zA-Z\\s]+$"},"url":"https://errors.pydantic.dev/2.5/v/string_pattern_mismatch"}]}
----------------------------- Captured stderr call -----------------------------
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
_______ TestCompleteGraphTopology.test_complete_graph_direct_connections _______
tests/test_complex_graph_scenarios.py:233: in test_complete_graph_direct_connections
    data = await ComplexGraphFixtures.create_complete_graph(test_client, num_nodes=4)
tests/fixtures/complex_graphs.py:511: in create_complete_graph
    return await topology.setup(test_client, unit_name)
tests/fixtures/complex_graphs.py:192: in setup
    self.entities = await self.create_entities(test_client, entity_names)
tests/fixtures/complex_graphs.py:39: in create_entities
    assert response.status_code == 201, f"Failed to create entity {name}: {response.text}"
E   AssertionError: Failed to create entity Complete0: {"detail":[{"type":"string_pattern_mismatch","loc":["body","name"],"msg":"String should match pattern '^[a-zA-Z\\s]+$'","input":"Complete0 hjgyfbfy","ctx":{"pattern":"^[a-zA-Z\\s]+$"},"url":"https://errors.pydantic.dev/2.5/v/string_pattern_mismatch"}]}
----------------------------- Captured stderr call -----------------------------
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
_________ TestCompleteGraphTopology.test_complete_graph_optimal_paths __________
tests/test_complex_graph_scenarios.py:254: in test_complete_graph_optimal_paths
    data = await ComplexGraphFixtures.create_complete_graph(test_client, num_nodes=5)
tests/fixtures/complex_graphs.py:511: in create_complete_graph
    return await topology.setup(test_client, unit_name)
tests/fixtures/complex_graphs.py:192: in setup
    self.entities = await self.create_entities(test_client, entity_names)
tests/fixtures/complex_graphs.py:39: in create_entities
    assert response.status_code == 201, f"Failed to create entity {name}: {response.text}"
E   AssertionError: Failed to create entity Complete0: {"detail":[{"type":"string_pattern_mismatch","loc":["body","name"],"msg":"String should match pattern '^[a-zA-Z\\s]+$'","input":"Complete0 msysimto","ctx":{"pattern":"^[a-zA-Z\\s]+$"},"url":"https://errors.pydantic.dev/2.5/v/string_pattern_mismatch"}]}
----------------------------- Captured stderr call -----------------------------
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
_____ TestDisconnectedGraphTopology.test_disconnected_components_isolated ______
tests/test_complex_graph_scenarios.py:277: in test_disconnected_components_isolated
    data = await ComplexGraphFixtures.create_disconnected_graph(test_client, num_components=3, nodes_per_component=2)
tests/fixtures/complex_graphs.py:517: in create_disconnected_graph
    return await topology.setup(test_client, unit_name)
tests/fixtures/complex_graphs.py:233: in setup
    self.entities = await self.create_entities(test_client, entity_names)
tests/fixtures/complex_graphs.py:39: in create_entities
    assert response.status_code == 201, f"Failed to create entity {name}: {response.text}"
E   AssertionError: Failed to create entity Comp0Node0: {"detail":[{"type":"string_pattern_mismatch","loc":["body","name"],"msg":"String should match pattern '^[a-zA-Z\\s]+$'","input":"Comp0Node0 nqgdtosw","ctx":{"pattern":"^[a-zA-Z\\s]+$"},"url":"https://errors.pydantic.dev/2.5/v/string_pattern_mismatch"}]}
----------------------------- Captured stderr call -----------------------------
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
____ TestDisconnectedGraphTopology.test_disconnected_within_component_paths ____
tests/test_complex_graph_scenarios.py:294: in test_disconnected_within_component_paths
    data = await ComplexGraphFixtures.create_disconnected_graph(test_client, num_components=2, nodes_per_component=3)
tests/fixtures/complex_graphs.py:517: in create_disconnected_graph
    return await topology.setup(test_client, unit_name)
tests/fixtures/complex_graphs.py:233: in setup
    self.entities = await self.create_entities(test_client, entity_names)
tests/fixtures/complex_graphs.py:39: in create_entities
    assert response.status_code == 201, f"Failed to create entity {name}: {response.text}"
E   AssertionError: Failed to create entity Comp0Node0: {"detail":[{"type":"string_pattern_mismatch","loc":["body","name"],"msg":"String should match pattern '^[a-zA-Z\\s]+$'","input":"Comp0Node0 bjjpqkcw","ctx":{"pattern":"^[a-zA-Z\\s]+$"},"url":"https://errors.pydantic.dev/2.5/v/string_pattern_mismatch"}]}
----------------------------- Captured stderr call -----------------------------
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
_____ TestDisconnectedGraphTopology.test_disconnected_all_component_pairs ______
tests/test_complex_graph_scenarios.py:313: in test_disconnected_all_component_pairs
    data = await ComplexGraphFixtures.create_disconnected_graph(test_client, num_components=3, nodes_per_component=2)
tests/fixtures/complex_graphs.py:517: in create_disconnected_graph
    return await topology.setup(test_client, unit_name)
tests/fixtures/complex_graphs.py:233: in setup
    self.entities = await self.create_entities(test_client, entity_names)
tests/fixtures/complex_graphs.py:39: in create_entities
    assert response.status_code == 201, f"Failed to create entity {name}: {response.text}"
E   AssertionError: Failed to create entity Comp0Node0: {"detail":[{"type":"string_pattern_mismatch","loc":["body","name"],"msg":"String should match pattern '^[a-zA-Z\\s]+$'","input":"Comp0Node0 jfiygjka","ctx":{"pattern":"^[a-zA-Z\\s]+$"},"url":"https://errors.pydantic.dev/2.5/v/string_pattern_mismatch"}]}
----------------------------- Captured stderr call -----------------------------
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
__________ TestCyclicGraphTopology.test_cyclic_graph_cycle_detection ___________
tests/test_complex_graph_scenarios.py:335: in test_cyclic_graph_cycle_detection
    data = await ComplexGraphFixtures.create_cyclic_graph(test_client, cycle_length=4)
tests/fixtures/complex_graphs.py:523: in create_cyclic_graph
    return await topology.setup(test_client, unit_name)
tests/fixtures/complex_graphs.py:272: in setup
    self.entities = await self.create_entities(test_client, entity_names)
tests/fixtures/complex_graphs.py:39: in create_entities
    assert response.status_code == 201, f"Failed to create entity {name}: {response.text}"
E   AssertionError: Failed to create entity Cycle0: {"detail":[{"type":"string_pattern_mismatch","loc":["body","name"],"msg":"String should match pattern '^[a-zA-Z\\s]+$'","input":"Cycle0 ljpqbjfp","ctx":{"pattern":"^[a-zA-Z\\s]+$"},"url":"https://errors.pydantic.dev/2.5/v/string_pattern_mismatch"}]}
----------------------------- Captured stderr call -----------------------------
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
___________ TestCyclicGraphTopology.test_cyclic_graph_shortest_path ____________
tests/test_complex_graph_scenarios.py:355: in test_cyclic_graph_shortest_path
    data = await ComplexGraphFixtures.create_cyclic_graph(test_client, cycle_length=6)
tests/fixtures/complex_graphs.py:523: in create_cyclic_graph
    return await topology.setup(test_client, unit_name)
tests/fixtures/complex_graphs.py:272: in setup
    self.entities = await self.create_entities(test_client, entity_names)
tests/fixtures/complex_graphs.py:39: in create_entities
    assert response.status_code == 201, f"Failed to create entity {name}: {response.text}"
E   AssertionError: Failed to create entity Cycle0: {"detail":[{"type":"string_pattern_mismatch","loc":["body","name"],"msg":"String should match pattern '^[a-zA-Z\\s]+$'","input":"Cycle0 qslqohks","ctx":{"pattern":"^[a-zA-Z\\s]+$"},"url":"https://errors.pydantic.dev/2.5/v/string_pattern_mismatch"}]}
----------------------------- Captured stderr call -----------------------------
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
_________ TestCyclicGraphTopology.test_cyclic_graph_no_infinite_loops __________
tests/test_complex_graph_scenarios.py:375: in test_cyclic_graph_no_infinite_loops
    data = await ComplexGraphFixtures.create_cyclic_graph(test_client, cycle_length=5)
tests/fixtures/complex_graphs.py:523: in create_cyclic_graph
    return await topology.setup(test_client, unit_name)
tests/fixtures/complex_graphs.py:272: in setup
    self.entities = await self.create_entities(test_client, entity_names)
tests/fixtures/complex_graphs.py:39: in create_entities
    assert response.status_code == 201, f"Failed to create entity {name}: {response.text}"
E   AssertionError: Failed to create entity Cycle0: {"detail":[{"type":"string_pattern_mismatch","loc":["body","name"],"msg":"String should match pattern '^[a-zA-Z\\s]+$'","input":"Cycle0 qvdbroqr","ctx":{"pattern":"^[a-zA-Z\\s]+$"},"url":"https://errors.pydantic.dev/2.5/v/string_pattern_mismatch"}]}
----------------------------- Captured stderr call -----------------------------
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
_______________ TestDeepTreeTopology.test_deep_tree_within_limit _______________
tests/test_complex_graph_scenarios.py:401: in test_deep_tree_within_limit
    data = await ComplexGraphFixtures.create_deep_tree(test_client, depth=5, branching_factor=2)
tests/fixtures/complex_graphs.py:529: in create_deep_tree
    return await topology.setup(test_client, unit_name)
tests/fixtures/complex_graphs.py:314: in setup
    self.entities = await self.create_entities(test_client, entity_names)
tests/fixtures/complex_graphs.py:39: in create_entities
    assert response.status_code == 201, f"Failed to create entity {name}: {response.text}"
E   AssertionError: Failed to create entity L1N0: {"detail":[{"type":"string_pattern_mismatch","loc":["body","name"],"msg":"String should match pattern '^[a-zA-Z\\s]+$'","input":"L1N0 dnofozac","ctx":{"pattern":"^[a-zA-Z\\s]+$"},"url":"https://errors.pydantic.dev/2.5/v/string_pattern_mismatch"}]}
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'Root dnofozac' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'Root dnofozac' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
_________________ TestDeepTreeTopology.test_deep_tree_at_limit _________________
tests/test_complex_graph_scenarios.py:420: in test_deep_tree_at_limit
    data = await ComplexGraphFixtures.create_deep_tree(test_client, depth=6, branching_factor=2)
tests/fixtures/complex_graphs.py:529: in create_deep_tree
    return await topology.setup(test_client, unit_name)
tests/fixtures/complex_graphs.py:314: in setup
    self.entities = await self.create_entities(test_client, entity_names)
tests/fixtures/complex_graphs.py:39: in create_entities
    assert response.status_code == 201, f"Failed to create entity {name}: {response.text}"
E   AssertionError: Failed to create entity L1N0: {"detail":[{"type":"string_pattern_mismatch","loc":["body","name"],"msg":"String should match pattern '^[a-zA-Z\\s]+$'","input":"L1N0 jkehinvc","ctx":{"pattern":"^[a-zA-Z\\s]+$"},"url":"https://errors.pydantic.dev/2.5/v/string_pattern_mismatch"}]}
---------------------------- Captured stderr setup -----------------------------
INFO:backend.tests.conftest:Cleaning test database: 1 entities, 0 connections
------------------------------ Captured log setup ------------------------------
INFO     backend.tests.conftest:conftest.py:96 Cleaning test database: 1 entities, 0 connections
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'Root jkehinvc' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'Root jkehinvc' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
_______________ TestDeepTreeTopology.test_deep_tree_beyond_limit _______________
tests/test_complex_graph_scenarios.py:439: in test_deep_tree_beyond_limit
    data = await ComplexGraphFixtures.create_deep_tree(test_client, depth=7, branching_factor=2)
tests/fixtures/complex_graphs.py:529: in create_deep_tree
    return await topology.setup(test_client, unit_name)
tests/fixtures/complex_graphs.py:314: in setup
    self.entities = await self.create_entities(test_client, entity_names)
tests/fixtures/complex_graphs.py:39: in create_entities
    assert response.status_code == 201, f"Failed to create entity {name}: {response.text}"
E   AssertionError: Failed to create entity L1N0: {"detail":[{"type":"string_pattern_mismatch","loc":["body","name"],"msg":"String should match pattern '^[a-zA-Z\\s]+$'","input":"L1N0 qufmbnhj","ctx":{"pattern":"^[a-zA-Z\\s]+$"},"url":"https://errors.pydantic.dev/2.5/v/string_pattern_mismatch"}]}
---------------------------- Captured stderr setup -----------------------------
INFO:backend.tests.conftest:Cleaning test database: 1 entities, 0 connections
------------------------------ Captured log setup ------------------------------
INFO     backend.tests.conftest:conftest.py:96 Cleaning test database: 1 entities, 0 connections
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'Root qufmbnhj' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'Root qufmbnhj' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
______________ TestDeepTreeTopology.test_deep_tree_sibling_paths _______________
tests/test_complex_graph_scenarios.py:457: in test_deep_tree_sibling_paths
    data = await ComplexGraphFixtures.create_deep_tree(test_client, depth=4, branching_factor=3)
tests/fixtures/complex_graphs.py:529: in create_deep_tree
    return await topology.setup(test_client, unit_name)
tests/fixtures/complex_graphs.py:314: in setup
    self.entities = await self.create_entities(test_client, entity_names)
tests/fixtures/complex_graphs.py:39: in create_entities
    assert response.status_code == 201, f"Failed to create entity {name}: {response.text}"
E   AssertionError: Failed to create entity L1N0: {"detail":[{"type":"string_pattern_mismatch","loc":["body","name"],"msg":"String should match pattern '^[a-zA-Z\\s]+$'","input":"L1N0 tkewptin","ctx":{"pattern":"^[a-zA-Z\\s]+$"},"url":"https://errors.pydantic.dev/2.5/v/string_pattern_mismatch"}]}
---------------------------- Captured stderr setup -----------------------------
INFO:backend.tests.conftest:Cleaning test database: 1 entities, 0 connections
------------------------------ Captured log setup ------------------------------
INFO     backend.tests.conftest:conftest.py:96 Cleaning test database: 1 entities, 0 connections
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'Root tkewptin' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'Root tkewptin' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
__________ TestMultiUnitGraphTopology.test_multi_unit_same_unit_paths __________
tests/test_complex_graph_scenarios.py:484: in test_multi_unit_same_unit_paths
    data = await ComplexGraphFixtures.create_multi_unit_graph(test_client)
tests/fixtures/complex_graphs.py:535: in create_multi_unit_graph
    return await topology.setup(test_client)
tests/fixtures/complex_graphs.py:359: in setup
    self.entities = await self.create_entities(test_client, entity_names)
tests/fixtures/complex_graphs.py:35: in create_entities
    response = await test_client.post(
venv/lib/python3.11/site-packages/httpx/_client.py:1877: in post
    return await self.request(
venv/lib/python3.11/site-packages/httpx/_client.py:1559: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
venv/lib/python3.11/site-packages/httpx/_client.py:1646: in send
    response = await self._send_handling_auth(
venv/lib/python3.11/site-packages/httpx/_client.py:1674: in _send_handling_auth
    response = await self._send_handling_redirects(
venv/lib/python3.11/site-packages/httpx/_client.py:1711: in _send_handling_redirects
    response = await self._send_single_request(request)
venv/lib/python3.11/site-packages/httpx/_client.py:1748: in _send_single_request
    response = await transport.handle_async_request(request)
venv/lib/python3.11/site-packages/httpx/_transports/asgi.py:162: in handle_async_request
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/applications.py:123: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
venv/lib/python3.11/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:762: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:782: in app
    await route.handle(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:297: in handle
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:77: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:72: in app
    response = await func(request)
venv/lib/python3.11/site-packages/fastapi/routing.py:299: in app
    raise e
venv/lib/python3.11/site-packages/fastapi/routing.py:294: in app
    raw_response = await run_endpoint_function(
venv/lib/python3.11/site-packages/fastapi/routing.py:191: in run_endpoint_function
    return await dependant.call(**values)
src/routes/entities.py:28: in create_entity
    await db.refresh(db_entity)
venv/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/session.py:327: in refresh
    await greenlet_spawn(
venv/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py:202: in greenlet_spawn
    result = context.switch(value)
venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:3140: in refresh
    raise sa_exc.InvalidRequestError(
E   sqlalchemy.exc.InvalidRequestError: Could not refresh instance '<Entity at 0x10b863350>'
---------------------------- Captured stderr setup -----------------------------
INFO:backend.tests.conftest:Cleaning test database: 1 entities, 0 connections
------------------------------ Captured log setup ------------------------------
INFO     backend.tests.conftest:conftest.py:96 Cleaning test database: 1 entities, 0 connections
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'MultiA lglttqao' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'MultiB lglttqao' with ID 2
INFO:src.routes.entities:Entity verification successful: ID 2 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'MultiC lglttqao' with ID 3
INFO:src.routes.entities:Entity verification successful: ID 3 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'MultiD lglttqao' with ID 4
INFO:src.routes.entities:Entity verification successful: ID 4 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'MultiA lglttqao' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'MultiB lglttqao' with ID 2
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 2 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'MultiC lglttqao' with ID 3
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 3 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'MultiD lglttqao' with ID 4
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 4 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
_____ TestMultiUnitGraphTopology.test_multi_unit_different_unit_isolation ______
tests/test_complex_graph_scenarios.py:517: in test_multi_unit_different_unit_isolation
    assert "no path" in response.json()["detail"].lower()
E   AssertionError: assert 'no path' in 'from entity not found'
E    +  where 'from entity not found' = <built-in method lower of str object at 0x10b55c990>()
E    +    where <built-in method lower of str object at 0x10b55c990> = 'From entity not found'.lower
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'MultiA mpusbmhn' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'MultiB mpusbmhn' with ID 2
INFO:src.routes.entities:Entity verification successful: ID 2 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'MultiC mpusbmhn' with ID 3
INFO:src.routes.entities:Entity verification successful: ID 3 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'MultiD mpusbmhn' with ID 4
INFO:src.routes.entities:Entity verification successful: ID 4 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'MultiE mpusbmhn' with ID 5
INFO:src.routes.entities:Entity verification successful: ID 5 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:httpx:HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO:src.routes.connections:Validating entities for connection: from_entity_id=1, to_entity_id=2
ERROR:src.routes.connections:From entity 1 not found. Available entities: []
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 404 Not Found"
INFO:src.routes.connections:Validating entities for connection: from_entity_id=2, to_entity_id=3
ERROR:src.routes.connections:From entity 2 not found. Available entities: []
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 404 Not Found"
INFO:src.routes.connections:Validating entities for connection: from_entity_id=3, to_entity_id=4
ERROR:src.routes.connections:From entity 3 not found. Available entities: []
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 404 Not Found"
INFO:src.routes.connections:Validating entities for connection: from_entity_id=4, to_entity_id=5
ERROR:src.routes.connections:From entity 4 not found. Available entities: []
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 404 Not Found"
INFO:src.routes.connections:Validating entities for connection: from_entity_id=1, to_entity_id=5
ERROR:src.routes.connections:From entity 1 not found. Available entities: []
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 404 Not Found"
INFO:httpx:HTTP Request: GET http://test/api/v1/compare/?from=1&to=3&unit=2 "HTTP/1.1 404 Not Found"
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'MultiA mpusbmhn' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'MultiB mpusbmhn' with ID 2
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 2 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'MultiC mpusbmhn' with ID 3
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 3 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'MultiD mpusbmhn' with ID 4
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 4 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'MultiE mpusbmhn' with ID 5
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 5 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=1, to_entity_id=2
ERROR    src.routes.connections:connections.py:40 From entity 1 not found. Available entities: []
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 404 Not Found"
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=2, to_entity_id=3
ERROR    src.routes.connections:connections.py:40 From entity 2 not found. Available entities: []
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 404 Not Found"
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=3, to_entity_id=4
ERROR    src.routes.connections:connections.py:40 From entity 3 not found. Available entities: []
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 404 Not Found"
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=4, to_entity_id=5
ERROR    src.routes.connections:connections.py:40 From entity 4 not found. Available entities: []
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 404 Not Found"
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=1, to_entity_id=5
ERROR    src.routes.connections:connections.py:40 From entity 1 not found. Available entities: []
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 404 Not Found"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/compare/?from=1&to=3&unit=2 "HTTP/1.1 404 Not Found"
_________ TestMultiUnitGraphTopology.test_multi_unit_cross_unit_paths __________
tests/test_complex_graph_scenarios.py:534: in test_multi_unit_cross_unit_paths
    assert response.status_code == 200
E   assert 404 == 200
E    +  where 404 = <Response [404 Not Found]>.status_code
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'MultiA envfrtcm' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'MultiB envfrtcm' with ID 2
INFO:src.routes.entities:Entity verification successful: ID 2 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'MultiC envfrtcm' with ID 3
INFO:src.routes.entities:Entity verification successful: ID 3 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'MultiD envfrtcm' with ID 4
INFO:src.routes.entities:Entity verification successful: ID 4 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'MultiE envfrtcm' with ID 5
INFO:src.routes.entities:Entity verification successful: ID 5 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:httpx:HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO:src.routes.connections:Validating entities for connection: from_entity_id=1, to_entity_id=2
INFO:src.routes.connections:Entities validated successfully: from_entity=MultiA envfrtcm (id=1), to_entity=MultiB envfrtcm (id=2)
INFO:src.routes.connections:Created connection ID: 1
INFO:src.routes.connections:Primary connection: 1 -> 2, multiplier: 2.0
INFO:src.routes.connections:Inverse connection should exist: 2 -> 1, multiplier: 0.5
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 201 Created"
INFO:src.routes.connections:Validating entities for connection: from_entity_id=2, to_entity_id=3
INFO:src.routes.connections:Entities validated successfully: from_entity=MultiB envfrtcm (id=2), to_entity=MultiC envfrtcm (id=3)
INFO:src.routes.connections:Created connection ID: 3
INFO:src.routes.connections:Primary connection: 2 -> 3, multiplier: 1.5
INFO:src.routes.connections:Inverse connection should exist: 3 -> 2, multiplier: 0.7
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 201 Created"
INFO:src.routes.connections:Validating entities for connection: from_entity_id=3, to_entity_id=4
INFO:src.routes.connections:Entities validated successfully: from_entity=MultiC envfrtcm (id=3), to_entity=MultiD envfrtcm (id=4)
INFO:src.routes.connections:Created connection ID: 5
INFO:src.routes.connections:Primary connection: 3 -> 4, multiplier: 3.0
INFO:src.routes.connections:Inverse connection should exist: 4 -> 3, multiplier: 0.3
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 201 Created"
INFO:src.routes.connections:Validating entities for connection: from_entity_id=4, to_entity_id=5
ERROR:src.routes.connections:From entity 4 not found. Available entities: []
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 404 Not Found"
INFO:src.routes.connections:Validating entities for connection: from_entity_id=1, to_entity_id=5
ERROR:src.routes.connections:From entity 1 not found. Available entities: []
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 404 Not Found"
INFO:httpx:HTTP Request: GET http://test/api/v1/compare/?from=3&to=5&unit=2 "HTTP/1.1 404 Not Found"
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'MultiA envfrtcm' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'MultiB envfrtcm' with ID 2
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 2 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'MultiC envfrtcm' with ID 3
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 3 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'MultiD envfrtcm' with ID 4
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 4 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'MultiE envfrtcm' with ID 5
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 5 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=1, to_entity_id=2
INFO     src.routes.connections:connections.py:54 Entities validated successfully: from_entity=MultiA envfrtcm (id=1), to_entity=MultiB envfrtcm (id=2)
INFO     src.routes.connections:connections.py:149 Created connection ID: 1
INFO     src.routes.connections:connections.py:150 Primary connection: 1 -> 2, multiplier: 2.0
INFO     src.routes.connections:connections.py:151 Inverse connection should exist: 2 -> 1, multiplier: 0.5
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 201 Created"
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=2, to_entity_id=3
INFO     src.routes.connections:connections.py:54 Entities validated successfully: from_entity=MultiB envfrtcm (id=2), to_entity=MultiC envfrtcm (id=3)
INFO     src.routes.connections:connections.py:149 Created connection ID: 3
INFO     src.routes.connections:connections.py:150 Primary connection: 2 -> 3, multiplier: 1.5
INFO     src.routes.connections:connections.py:151 Inverse connection should exist: 3 -> 2, multiplier: 0.7
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 201 Created"
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=3, to_entity_id=4
INFO     src.routes.connections:connections.py:54 Entities validated successfully: from_entity=MultiC envfrtcm (id=3), to_entity=MultiD envfrtcm (id=4)
INFO     src.routes.connections:connections.py:149 Created connection ID: 5
INFO     src.routes.connections:connections.py:150 Primary connection: 3 -> 4, multiplier: 3.0
INFO     src.routes.connections:connections.py:151 Inverse connection should exist: 4 -> 3, multiplier: 0.3
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 201 Created"
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=4, to_entity_id=5
ERROR    src.routes.connections:connections.py:40 From entity 4 not found. Available entities: []
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 404 Not Found"
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=1, to_entity_id=5
ERROR    src.routes.connections:connections.py:40 From entity 1 not found. Available entities: []
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 404 Not Found"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/compare/?from=3&to=5&unit=2 "HTTP/1.1 404 Not Found"
_________ TestLargeScaleGraphTopology.test_large_scale_graph_creation __________
tests/test_complex_graph_scenarios.py:546: in test_large_scale_graph_creation
    data = await ComplexGraphFixtures.create_large_scale_graph(test_client, num_entities=20, connection_density=0.2)
tests/fixtures/complex_graphs.py:541: in create_large_scale_graph
    return await topology.setup(test_client, unit_name)
tests/fixtures/complex_graphs.py:403: in setup
    self.entities = await self.create_entities(test_client, entity_names)
tests/fixtures/complex_graphs.py:39: in create_entities
    assert response.status_code == 201, f"Failed to create entity {name}: {response.text}"
E   AssertionError: Failed to create entity Large000: {"detail":[{"type":"string_pattern_mismatch","loc":["body","name"],"msg":"String should match pattern '^[a-zA-Z\\s]+$'","input":"Large000 dvtvblfg","ctx":{"pattern":"^[a-zA-Z\\s]+$"},"url":"https://errors.pydantic.dev/2.5/v/string_pattern_mismatch"}]}
----------------------------- Captured stderr call -----------------------------
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
__________ TestLargeScaleGraphTopology.test_large_scale_random_paths ___________
tests/test_complex_graph_scenarios.py:557: in test_large_scale_random_paths
    data = await ComplexGraphFixtures.create_large_scale_graph(test_client, num_entities=15, connection_density=0.3)
tests/fixtures/complex_graphs.py:541: in create_large_scale_graph
    return await topology.setup(test_client, unit_name)
tests/fixtures/complex_graphs.py:403: in setup
    self.entities = await self.create_entities(test_client, entity_names)
tests/fixtures/complex_graphs.py:39: in create_entities
    assert response.status_code == 201, f"Failed to create entity {name}: {response.text}"
E   AssertionError: Failed to create entity Large000: {"detail":[{"type":"string_pattern_mismatch","loc":["body","name"],"msg":"String should match pattern '^[a-zA-Z\\s]+$'","input":"Large000 yfsuprqn","ctx":{"pattern":"^[a-zA-Z\\s]+$"},"url":"https://errors.pydantic.dev/2.5/v/string_pattern_mismatch"}]}
----------------------------- Captured stderr call -----------------------------
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
_______ TestLargeScaleGraphTopology.test_large_scale_performance_timing ________
tests/test_complex_graph_scenarios.py:589: in test_large_scale_performance_timing
    data = await ComplexGraphFixtures.create_large_scale_graph(test_client, num_entities=25, connection_density=0.15)
tests/fixtures/complex_graphs.py:541: in create_large_scale_graph
    return await topology.setup(test_client, unit_name)
tests/fixtures/complex_graphs.py:403: in setup
    self.entities = await self.create_entities(test_client, entity_names)
tests/fixtures/complex_graphs.py:39: in create_entities
    assert response.status_code == 201, f"Failed to create entity {name}: {response.text}"
E   AssertionError: Failed to create entity Large000: {"detail":[{"type":"string_pattern_mismatch","loc":["body","name"],"msg":"String should match pattern '^[a-zA-Z\\s]+$'","input":"Large000 hppkbpxr","ctx":{"pattern":"^[a-zA-Z\\s]+$"},"url":"https://errors.pydantic.dev/2.5/v/string_pattern_mismatch"}]}
----------------------------- Captured stderr call -----------------------------
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
___________ TestEdgeCaseGraphTopology.test_edge_case_isolated_entity ___________
tests/test_complex_graph_scenarios.py:634: in test_edge_case_isolated_entity
    assert "no path" in response.json()["detail"].lower()
E   AssertionError: assert 'no path' in 'from entity not found'
E    +  where 'from entity not found' = <built-in method lower of str object at 0x10bfd98e0>()
E    +    where <built-in method lower of str object at 0x10bfd98e0> = 'From entity not found'.lower
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'EdgeSingle bfjbekhd' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'EdgeDecimalA bfjbekhd' with ID 2
INFO:src.routes.entities:Entity verification successful: ID 2 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'EdgeDecimalB bfjbekhd' with ID 3
INFO:src.routes.entities:Entity verification successful: ID 3 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'EdgeDecimalC bfjbekhd' with ID 4
INFO:src.routes.entities:Entity verification successful: ID 4 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'EdgeMinMultiplier bfjbekhd' with ID 5
INFO:src.routes.entities:Entity verification successful: ID 5 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'EdgeMaxMultiplier bfjbekhd' with ID 6
INFO:src.routes.entities:Entity verification successful: ID 6 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'EdgeZeroPath bfjbekhd' with ID 7
INFO:src.routes.entities:Entity verification successful: ID 7 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:httpx:HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO:src.routes.connections:Validating entities for connection: from_entity_id=2, to_entity_id=3
INFO:src.routes.connections:Entities validated successfully: from_entity=EdgeDecimalA bfjbekhd (id=2), to_entity=EdgeDecimalB bfjbekhd (id=3)
ERROR:src.routes.connections:IntegrityError creating connection: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.ForeignKeyViolationError'>: insert or update on table "connections" violates foreign key constraint "connections_from_entity_id_fkey"
DETAIL:  Key (from_entity_id)=(2) is not present in table "entities".
[SQL: INSERT INTO connections (from_entity_id, to_entity_id, unit_id, multiplier, created_at, updated_at) SELECT p0::INTEGER, p1::INTEGER, p2::INTEGER, p3::NUMERIC(10, 1), p4::TIMESTAMP WITHOUT TIME ZONE, p5::TIMESTAMP WITHOUT TIME ZONE FROM (VALUES ($1::I ... 275 characters truncated ...  p2, p3, p4, p5, sen_counter) ORDER BY sen_counter RETURNING connections.id, connections.id AS id__1]
[parameters: (2, 3, 1, Decimal('0.1'), datetime.datetime(2025, 7, 7, 23, 34, 12, 937707), datetime.datetime(2025, 7, 7, 23, 34, 12, 937710), 3, 2, 1, Decimal('10.0'), datetime.datetime(2025, 7, 7, 23, 34, 12, 937710), datetime.datetime(2025, 7, 7, 23, 34, 12, 937710))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
ERROR:src.routes.connections:Connection data: from_entity_id=2, to_entity_id=3, unit_id=1, multiplier=0.1
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 400 Bad Request"
INFO:src.routes.connections:Validating entities for connection: from_entity_id=3, to_entity_id=4
ERROR:src.routes.connections:From entity 3 not found. Available entities: []
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 404 Not Found"
INFO:src.routes.connections:Validating entities for connection: from_entity_id=5, to_entity_id=6
ERROR:src.routes.connections:From entity 5 not found. Available entities: []
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 404 Not Found"
INFO:src.routes.connections:Validating entities for connection: from_entity_id=6, to_entity_id=5
ERROR:src.routes.connections:From entity 6 not found. Available entities: []
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 404 Not Found"
INFO:src.routes.connections:Validating entities for connection: from_entity_id=7, to_entity_id=2
ERROR:src.routes.connections:From entity 7 not found. Available entities: []
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 404 Not Found"
INFO:httpx:HTTP Request: GET http://test/api/v1/compare/?from=2&to=1&unit=1 "HTTP/1.1 404 Not Found"
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'EdgeSingle bfjbekhd' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'EdgeDecimalA bfjbekhd' with ID 2
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 2 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'EdgeDecimalB bfjbekhd' with ID 3
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 3 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'EdgeDecimalC bfjbekhd' with ID 4
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 4 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'EdgeMinMultiplier bfjbekhd' with ID 5
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 5 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'EdgeMaxMultiplier bfjbekhd' with ID 6
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 6 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'EdgeZeroPath bfjbekhd' with ID 7
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 7 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=2, to_entity_id=3
INFO     src.routes.connections:connections.py:54 Entities validated successfully: from_entity=EdgeDecimalA bfjbekhd (id=2), to_entity=EdgeDecimalB bfjbekhd (id=3)
ERROR    src.routes.connections:connections.py:160 IntegrityError creating connection: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.ForeignKeyViolationError'>: insert or update on table "connections" violates foreign key constraint "connections_from_entity_id_fkey"
DETAIL:  Key (from_entity_id)=(2) is not present in table "entities".
[SQL: INSERT INTO connections (from_entity_id, to_entity_id, unit_id, multiplier, created_at, updated_at) SELECT p0::INTEGER, p1::INTEGER, p2::INTEGER, p3::NUMERIC(10, 1), p4::TIMESTAMP WITHOUT TIME ZONE, p5::TIMESTAMP WITHOUT TIME ZONE FROM (VALUES ($1::I ... 275 characters truncated ...  p2, p3, p4, p5, sen_counter) ORDER BY sen_counter RETURNING connections.id, connections.id AS id__1]
[parameters: (2, 3, 1, Decimal('0.1'), datetime.datetime(2025, 7, 7, 23, 34, 12, 937707), datetime.datetime(2025, 7, 7, 23, 34, 12, 937710), 3, 2, 1, Decimal('10.0'), datetime.datetime(2025, 7, 7, 23, 34, 12, 937710), datetime.datetime(2025, 7, 7, 23, 34, 12, 937710))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
ERROR    src.routes.connections:connections.py:161 Connection data: from_entity_id=2, to_entity_id=3, unit_id=1, multiplier=0.1
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 400 Bad Request"
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=3, to_entity_id=4
ERROR    src.routes.connections:connections.py:40 From entity 3 not found. Available entities: []
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 404 Not Found"
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=5, to_entity_id=6
ERROR    src.routes.connections:connections.py:40 From entity 5 not found. Available entities: []
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 404 Not Found"
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=6, to_entity_id=5
ERROR    src.routes.connections:connections.py:40 From entity 6 not found. Available entities: []
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 404 Not Found"
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=7, to_entity_id=2
ERROR    src.routes.connections:connections.py:40 From entity 7 not found. Available entities: []
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 404 Not Found"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/compare/?from=2&to=1&unit=1 "HTTP/1.1 404 Not Found"
__________ TestEdgeCaseGraphTopology.test_edge_case_decimal_precision __________
tests/test_complex_graph_scenarios.py:650: in test_edge_case_decimal_precision
    assert response.status_code == 200
E   assert 404 == 200
E    +  where 404 = <Response [404 Not Found]>.status_code
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'EdgeSingle mtngtgeg' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'EdgeDecimalA mtngtgeg' with ID 2
INFO:src.routes.entities:Entity verification successful: ID 2 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'EdgeDecimalB mtngtgeg' with ID 3
INFO:src.routes.entities:Entity verification successful: ID 3 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'EdgeDecimalC mtngtgeg' with ID 4
INFO:src.routes.entities:Entity verification successful: ID 4 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'EdgeMinMultiplier mtngtgeg' with ID 5
INFO:src.routes.entities:Entity verification successful: ID 5 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'EdgeMaxMultiplier mtngtgeg' with ID 6
INFO:src.routes.entities:Entity verification successful: ID 6 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'EdgeZeroPath mtngtgeg' with ID 7
INFO:src.routes.entities:Entity verification successful: ID 7 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:httpx:HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO:src.routes.connections:Validating entities for connection: from_entity_id=2, to_entity_id=3
INFO:src.routes.connections:Entities validated successfully: from_entity=EdgeDecimalA mtngtgeg (id=2), to_entity=EdgeDecimalB mtngtgeg (id=3)
INFO:src.routes.connections:Created connection ID: 1
INFO:src.routes.connections:Primary connection: 2 -> 3, multiplier: 0.1
INFO:src.routes.connections:Inverse connection should exist: 3 -> 2, multiplier: 10.0
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 201 Created"
INFO:src.routes.connections:Validating entities for connection: from_entity_id=3, to_entity_id=4
ERROR:src.routes.connections:From entity 3 not found. Available entities: []
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 404 Not Found"
INFO:src.routes.connections:Validating entities for connection: from_entity_id=5, to_entity_id=6
ERROR:src.routes.connections:From entity 5 not found. Available entities: []
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 404 Not Found"
INFO:src.routes.connections:Validating entities for connection: from_entity_id=6, to_entity_id=5
ERROR:src.routes.connections:From entity 6 not found. Available entities: []
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 404 Not Found"
INFO:src.routes.connections:Validating entities for connection: from_entity_id=7, to_entity_id=2
ERROR:src.routes.connections:From entity 7 not found. Available entities: []
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 404 Not Found"
INFO:httpx:HTTP Request: GET http://test/api/v1/compare/?from=2&to=4&unit=1 "HTTP/1.1 404 Not Found"
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'EdgeSingle mtngtgeg' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'EdgeDecimalA mtngtgeg' with ID 2
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 2 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'EdgeDecimalB mtngtgeg' with ID 3
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 3 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'EdgeDecimalC mtngtgeg' with ID 4
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 4 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'EdgeMinMultiplier mtngtgeg' with ID 5
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 5 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'EdgeMaxMultiplier mtngtgeg' with ID 6
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 6 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'EdgeZeroPath mtngtgeg' with ID 7
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 7 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=2, to_entity_id=3
INFO     src.routes.connections:connections.py:54 Entities validated successfully: from_entity=EdgeDecimalA mtngtgeg (id=2), to_entity=EdgeDecimalB mtngtgeg (id=3)
INFO     src.routes.connections:connections.py:149 Created connection ID: 1
INFO     src.routes.connections:connections.py:150 Primary connection: 2 -> 3, multiplier: 0.1
INFO     src.routes.connections:connections.py:151 Inverse connection should exist: 3 -> 2, multiplier: 10.0
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 201 Created"
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=3, to_entity_id=4
ERROR    src.routes.connections:connections.py:40 From entity 3 not found. Available entities: []
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 404 Not Found"
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=5, to_entity_id=6
ERROR    src.routes.connections:connections.py:40 From entity 5 not found. Available entities: []
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 404 Not Found"
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=6, to_entity_id=5
ERROR    src.routes.connections:connections.py:40 From entity 6 not found. Available entities: []
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 404 Not Found"
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=7, to_entity_id=2
ERROR    src.routes.connections:connections.py:40 From entity 7 not found. Available entities: []
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 404 Not Found"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/compare/?from=2&to=4&unit=1 "HTTP/1.1 404 Not Found"
_________ TestEdgeCaseGraphTopology.test_edge_case_extreme_multipliers _________
tests/test_complex_graph_scenarios.py:659: in test_edge_case_extreme_multipliers
    data = await ComplexGraphFixtures.create_edge_case_graph(test_client)
tests/fixtures/complex_graphs.py:547: in create_edge_case_graph
    return await topology.setup(test_client, unit_name)
tests/fixtures/complex_graphs.py:471: in setup
    await self.create_connections(test_client, connections, self.unit_id)
tests/fixtures/complex_graphs.py:54: in create_connections
    await test_client.post(
venv/lib/python3.11/site-packages/httpx/_client.py:1877: in post
    return await self.request(
venv/lib/python3.11/site-packages/httpx/_client.py:1559: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
venv/lib/python3.11/site-packages/httpx/_client.py:1646: in send
    response = await self._send_handling_auth(
venv/lib/python3.11/site-packages/httpx/_client.py:1674: in _send_handling_auth
    response = await self._send_handling_redirects(
venv/lib/python3.11/site-packages/httpx/_client.py:1711: in _send_handling_redirects
    response = await self._send_single_request(request)
venv/lib/python3.11/site-packages/httpx/_client.py:1748: in _send_single_request
    response = await transport.handle_async_request(request)
venv/lib/python3.11/site-packages/httpx/_transports/asgi.py:162: in handle_async_request
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/applications.py:123: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
venv/lib/python3.11/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:762: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:782: in app
    await route.handle(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:297: in handle
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:77: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:72: in app
    response = await func(request)
venv/lib/python3.11/site-packages/fastapi/routing.py:299: in app
    raise e
venv/lib/python3.11/site-packages/fastapi/routing.py:294: in app
    raw_response = await run_endpoint_function(
venv/lib/python3.11/site-packages/fastapi/routing.py:191: in run_endpoint_function
    return await dependant.call(**values)
src/routes/connections.py:146: in create_connection
    fresh_connection = result.scalar_one()
venv/lib/python3.11/site-packages/sqlalchemy/engine/result.py:1474: in scalar_one
    return self._only_one_row(
venv/lib/python3.11/site-packages/sqlalchemy/engine/result.py:757: in _only_one_row
    raise exc.NoResultFound(
E   sqlalchemy.exc.NoResultFound: No row was found when one was required
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'EdgeSingle gmqacqks' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'EdgeDecimalA gmqacqks' with ID 2
INFO:src.routes.entities:Entity verification successful: ID 2 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'EdgeDecimalB gmqacqks' with ID 3
INFO:src.routes.entities:Entity verification successful: ID 3 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'EdgeDecimalC gmqacqks' with ID 4
INFO:src.routes.entities:Entity verification successful: ID 4 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'EdgeMinMultiplier gmqacqks' with ID 5
INFO:src.routes.entities:Entity verification successful: ID 5 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'EdgeMaxMultiplier gmqacqks' with ID 6
INFO:src.routes.entities:Entity verification successful: ID 6 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'EdgeZeroPath gmqacqks' with ID 7
INFO:src.routes.entities:Entity verification successful: ID 7 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:httpx:HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO:src.routes.connections:Validating entities for connection: from_entity_id=2, to_entity_id=3
INFO:src.routes.connections:Entities validated successfully: from_entity=EdgeDecimalA gmqacqks (id=2), to_entity=EdgeDecimalB gmqacqks (id=3)
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'EdgeSingle gmqacqks' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'EdgeDecimalA gmqacqks' with ID 2
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 2 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'EdgeDecimalB gmqacqks' with ID 3
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 3 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'EdgeDecimalC gmqacqks' with ID 4
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 4 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'EdgeMinMultiplier gmqacqks' with ID 5
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 5 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'EdgeMaxMultiplier gmqacqks' with ID 6
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 6 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'EdgeZeroPath gmqacqks' with ID 7
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 7 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=2, to_entity_id=3
INFO     src.routes.connections:connections.py:54 Entities validated successfully: from_entity=EdgeDecimalA gmqacqks (id=2), to_entity=EdgeDecimalB gmqacqks (id=3)
______ TestComplexGraphInteractions.test_multiple_graph_types_comparison _______
tests/test_complex_graph_scenarios.py:716: in test_multiple_graph_types_comparison
    star_data = await ComplexGraphFixtures.create_star_graph(test_client, num_spokes=3)
tests/fixtures/complex_graphs.py:493: in create_star_graph
    return await topology.setup(test_client, unit_name)
tests/fixtures/complex_graphs.py:81: in setup
    self.entities = await self.create_entities(test_client, entity_names)
tests/fixtures/complex_graphs.py:39: in create_entities
    assert response.status_code == 201, f"Failed to create entity {name}: {response.text}"
E   AssertionError: Failed to create entity Spoke0: {"detail":[{"type":"string_pattern_mismatch","loc":["body","name"],"msg":"String should match pattern '^[a-zA-Z\\s]+$'","input":"Spoke0 lkaudbeb","ctx":{"pattern":"^[a-zA-Z\\s]+$"},"url":"https://errors.pydantic.dev/2.5/v/string_pattern_mismatch"}]}
---------------------------- Captured stderr setup -----------------------------
INFO:backend.tests.conftest:Cleaning test database: 7 entities, 8 connections
------------------------------ Captured log setup ------------------------------
INFO     backend.tests.conftest:conftest.py:96 Cleaning test database: 7 entities, 8 connections
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'Hub lkaudbeb' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'Hub lkaudbeb' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
_______ TestComplexGraphInteractions.test_graph_stress_multiple_queries ________
tests/test_complex_graph_scenarios.py:745: in test_graph_stress_multiple_queries
    data = await ComplexGraphFixtures.create_complete_graph(test_client, num_nodes=5)
tests/fixtures/complex_graphs.py:511: in create_complete_graph
    return await topology.setup(test_client, unit_name)
tests/fixtures/complex_graphs.py:192: in setup
    self.entities = await self.create_entities(test_client, entity_names)
tests/fixtures/complex_graphs.py:39: in create_entities
    assert response.status_code == 201, f"Failed to create entity {name}: {response.text}"
E   AssertionError: Failed to create entity Complete0: {"detail":[{"type":"string_pattern_mismatch","loc":["body","name"],"msg":"String should match pattern '^[a-zA-Z\\s]+$'","input":"Complete0 snliotks","ctx":{"pattern":"^[a-zA-Z\\s]+$"},"url":"https://errors.pydantic.dev/2.5/v/string_pattern_mismatch"}]}
---------------------------- Captured stderr setup -----------------------------
INFO:backend.tests.conftest:Cleaning test database: 1 entities, 0 connections
------------------------------ Captured log setup ------------------------------
INFO     backend.tests.conftest:conftest.py:96 Cleaning test database: 1 entities, 0 connections
----------------------------- Captured stderr call -----------------------------
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
______ TestComplexGraphInteractions.test_graph_robustness_invalid_queries ______
tests/test_complex_graph_scenarios.py:773: in test_graph_robustness_invalid_queries
    data = await ComplexGraphFixtures.create_star_graph(test_client, num_spokes=3)
tests/fixtures/complex_graphs.py:493: in create_star_graph
    return await topology.setup(test_client, unit_name)
tests/fixtures/complex_graphs.py:81: in setup
    self.entities = await self.create_entities(test_client, entity_names)
tests/fixtures/complex_graphs.py:39: in create_entities
    assert response.status_code == 201, f"Failed to create entity {name}: {response.text}"
E   AssertionError: Failed to create entity Spoke0: {"detail":[{"type":"string_pattern_mismatch","loc":["body","name"],"msg":"String should match pattern '^[a-zA-Z\\s]+$'","input":"Spoke0 ssdzgeks","ctx":{"pattern":"^[a-zA-Z\\s]+$"},"url":"https://errors.pydantic.dev/2.5/v/string_pattern_mismatch"}]}
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'Hub ssdzgeks' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'Hub ssdzgeks' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
________ TestConcurrentEntityOperations.test_concurrent_entity_updates _________
tests/test_concurrent_operations.py:118: in test_concurrent_entity_updates
    assert len(successful_updates) >= 1, "At least one update should succeed"
E   AssertionError: At least one update should succeed
E   assert 0 >= 1
E    +  where 0 = len([])
---------------------------- Captured stderr setup -----------------------------
INFO:backend.tests.conftest:Cleaning test database: 1 entities, 0 connections
------------------------------ Captured log setup ------------------------------
INFO     backend.tests.conftest:conftest.py:96 Cleaning test database: 1 entities, 0 connections
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'ConcurrentUpdate ecdaceca' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:httpx:HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'ConcurrentUpdate ecdaceca' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     httpx:_client.py:1758 HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
_ TestConcurrentConnectionOperations.test_concurrent_connection_creation_same_entities _
tests/test_concurrent_operations.py:231: in test_concurrent_connection_creation_same_entities
    assert len(successful_creations) == len(multipliers), "All connection creations should succeed"
E   AssertionError: All connection creations should succeed
E   assert 1 == 5
E    +  where 1 = len([{'multiplier': 3.0, 'response': {'created_at': '2025-07-07T23:34:33.321743', 'from_entity_id': 1, 'id': 1, 'multiplier': '3.0', ...}, 'status_code': 201, 'task_id': 1}])
E    +  and   5 = len([2.0, 3.0, 4.0, 5.0, 6.0])
---------------------------- Captured stderr setup -----------------------------
INFO:backend.tests.conftest:Cleaning test database: 1 entities, 0 connections
------------------------------ Captured log setup ------------------------------
INFO     backend.tests.conftest:conftest.py:96 Cleaning test database: 1 entities, 0 connections
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'ConcConnA aecafdba' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'ConcConnB bbcacfaf' with ID 2
INFO:src.routes.entities:Entity verification successful: ID 2 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:httpx:HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO:src.routes.connections:Validating entities for connection: from_entity_id=1, to_entity_id=2
INFO:src.routes.connections:Validating entities for connection: from_entity_id=1, to_entity_id=2
INFO:src.routes.connections:Validating entities for connection: from_entity_id=1, to_entity_id=2
INFO:src.routes.connections:Validating entities for connection: from_entity_id=1, to_entity_id=2
INFO:src.routes.connections:Validating entities for connection: from_entity_id=1, to_entity_id=2
INFO:src.routes.connections:Entities validated successfully: from_entity=ConcConnA aecafdba (id=1), to_entity=ConcConnB bbcacfaf (id=2)
INFO:src.routes.connections:Entities validated successfully: from_entity=ConcConnA aecafdba (id=1), to_entity=ConcConnB bbcacfaf (id=2)
INFO:src.routes.connections:Entities validated successfully: from_entity=ConcConnA aecafdba (id=1), to_entity=ConcConnB bbcacfaf (id=2)
INFO:src.routes.connections:Entities validated successfully: from_entity=ConcConnA aecafdba (id=1), to_entity=ConcConnB bbcacfaf (id=2)
INFO:src.routes.connections:Entities validated successfully: from_entity=ConcConnA aecafdba (id=1), to_entity=ConcConnB bbcacfaf (id=2)
ERROR:src.routes.connections:IntegrityError creating connection: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "connections_unique"
DETAIL:  Key (from_entity_id, to_entity_id, unit_id)=(1, 2, 4) already exists.
[SQL: INSERT INTO connections (from_entity_id, to_entity_id, unit_id, multiplier, created_at, updated_at) SELECT p0::INTEGER, p1::INTEGER, p2::INTEGER, p3::NUMERIC(10, 1), p4::TIMESTAMP WITHOUT TIME ZONE, p5::TIMESTAMP WITHOUT TIME ZONE FROM (VALUES ($1::I ... 275 characters truncated ...  p2, p3, p4, p5, sen_counter) ORDER BY sen_counter RETURNING connections.id, connections.id AS id__1]
[parameters: (1, 2, 4, Decimal('5.0'), datetime.datetime(2025, 7, 7, 23, 34, 33, 322467), datetime.datetime(2025, 7, 7, 23, 34, 33, 322467), 2, 1, 4, Decimal('0.2'), datetime.datetime(2025, 7, 7, 23, 34, 33, 322468), datetime.datetime(2025, 7, 7, 23, 34, 33, 322468))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
ERROR:src.routes.connections:Connection data: from_entity_id=1, to_entity_id=2, unit_id=4, multiplier=5.0
ERROR:src.routes.connections:IntegrityError creating connection: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "connections_unique"
DETAIL:  Key (from_entity_id, to_entity_id, unit_id)=(1, 2, 4) already exists.
[SQL: INSERT INTO connections (from_entity_id, to_entity_id, unit_id, multiplier, created_at, updated_at) SELECT p0::INTEGER, p1::INTEGER, p2::INTEGER, p3::NUMERIC(10, 1), p4::TIMESTAMP WITHOUT TIME ZONE, p5::TIMESTAMP WITHOUT TIME ZONE FROM (VALUES ($1::I ... 275 characters truncated ...  p2, p3, p4, p5, sen_counter) ORDER BY sen_counter RETURNING connections.id, connections.id AS id__1]
[parameters: (1, 2, 4, Decimal('4.0'), datetime.datetime(2025, 7, 7, 23, 34, 33, 322235), datetime.datetime(2025, 7, 7, 23, 34, 33, 322236), 2, 1, 4, Decimal('0.2'), datetime.datetime(2025, 7, 7, 23, 34, 33, 322236), datetime.datetime(2025, 7, 7, 23, 34, 33, 322237))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
ERROR:src.routes.connections:Connection data: from_entity_id=1, to_entity_id=2, unit_id=4, multiplier=4.0
ERROR:src.routes.connections:IntegrityError creating connection: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "connections_unique"
DETAIL:  Key (from_entity_id, to_entity_id, unit_id)=(1, 2, 4) already exists.
[SQL: INSERT INTO connections (from_entity_id, to_entity_id, unit_id, multiplier, created_at, updated_at) SELECT p0::INTEGER, p1::INTEGER, p2::INTEGER, p3::NUMERIC(10, 1), p4::TIMESTAMP WITHOUT TIME ZONE, p5::TIMESTAMP WITHOUT TIME ZONE FROM (VALUES ($1::I ... 275 characters truncated ...  p2, p3, p4, p5, sen_counter) ORDER BY sen_counter RETURNING connections.id, connections.id AS id__1]
[parameters: (1, 2, 4, Decimal('2.0'), datetime.datetime(2025, 7, 7, 23, 34, 33, 323316), datetime.datetime(2025, 7, 7, 23, 34, 33, 323317), 2, 1, 4, Decimal('0.5'), datetime.datetime(2025, 7, 7, 23, 34, 33, 323318), datetime.datetime(2025, 7, 7, 23, 34, 33, 323318))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
ERROR:src.routes.connections:Connection data: from_entity_id=1, to_entity_id=2, unit_id=4, multiplier=2.0
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 400 Bad Request"
ERROR:src.routes.connections:IntegrityError creating connection: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "connections_unique"
DETAIL:  Key (from_entity_id, to_entity_id, unit_id)=(1, 2, 4) already exists.
[SQL: INSERT INTO connections (from_entity_id, to_entity_id, unit_id, multiplier, created_at, updated_at) SELECT p0::INTEGER, p1::INTEGER, p2::INTEGER, p3::NUMERIC(10, 1), p4::TIMESTAMP WITHOUT TIME ZONE, p5::TIMESTAMP WITHOUT TIME ZONE FROM (VALUES ($1::I ... 275 characters truncated ...  p2, p3, p4, p5, sen_counter) ORDER BY sen_counter RETURNING connections.id, connections.id AS id__1]
[parameters: (1, 2, 4, Decimal('6.0'), datetime.datetime(2025, 7, 7, 23, 34, 33, 323714), datetime.datetime(2025, 7, 7, 23, 34, 33, 323715), 2, 1, 4, Decimal('0.2'), datetime.datetime(2025, 7, 7, 23, 34, 33, 323715), datetime.datetime(2025, 7, 7, 23, 34, 33, 323716))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
ERROR:src.routes.connections:Connection data: from_entity_id=1, to_entity_id=2, unit_id=4, multiplier=6.0
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 400 Bad Request"
INFO:src.routes.connections:Created connection ID: 1
INFO:src.routes.connections:Primary connection: 1 -> 2, multiplier: 3.0
INFO:src.routes.connections:Inverse connection should exist: 2 -> 1, multiplier: 0.3
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 201 Created"
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'ConcConnA aecafdba' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'ConcConnB bbcacfaf' with ID 2
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 2 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=1, to_entity_id=2
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=1, to_entity_id=2
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=1, to_entity_id=2
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=1, to_entity_id=2
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=1, to_entity_id=2
INFO     src.routes.connections:connections.py:54 Entities validated successfully: from_entity=ConcConnA aecafdba (id=1), to_entity=ConcConnB bbcacfaf (id=2)
INFO     src.routes.connections:connections.py:54 Entities validated successfully: from_entity=ConcConnA aecafdba (id=1), to_entity=ConcConnB bbcacfaf (id=2)
INFO     src.routes.connections:connections.py:54 Entities validated successfully: from_entity=ConcConnA aecafdba (id=1), to_entity=ConcConnB bbcacfaf (id=2)
INFO     src.routes.connections:connections.py:54 Entities validated successfully: from_entity=ConcConnA aecafdba (id=1), to_entity=ConcConnB bbcacfaf (id=2)
INFO     src.routes.connections:connections.py:54 Entities validated successfully: from_entity=ConcConnA aecafdba (id=1), to_entity=ConcConnB bbcacfaf (id=2)
ERROR    src.routes.connections:connections.py:160 IntegrityError creating connection: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "connections_unique"
DETAIL:  Key (from_entity_id, to_entity_id, unit_id)=(1, 2, 4) already exists.
[SQL: INSERT INTO connections (from_entity_id, to_entity_id, unit_id, multiplier, created_at, updated_at) SELECT p0::INTEGER, p1::INTEGER, p2::INTEGER, p3::NUMERIC(10, 1), p4::TIMESTAMP WITHOUT TIME ZONE, p5::TIMESTAMP WITHOUT TIME ZONE FROM (VALUES ($1::I ... 275 characters truncated ...  p2, p3, p4, p5, sen_counter) ORDER BY sen_counter RETURNING connections.id, connections.id AS id__1]
[parameters: (1, 2, 4, Decimal('5.0'), datetime.datetime(2025, 7, 7, 23, 34, 33, 322467), datetime.datetime(2025, 7, 7, 23, 34, 33, 322467), 2, 1, 4, Decimal('0.2'), datetime.datetime(2025, 7, 7, 23, 34, 33, 322468), datetime.datetime(2025, 7, 7, 23, 34, 33, 322468))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
ERROR    src.routes.connections:connections.py:161 Connection data: from_entity_id=1, to_entity_id=2, unit_id=4, multiplier=5.0
ERROR    src.routes.connections:connections.py:160 IntegrityError creating connection: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "connections_unique"
DETAIL:  Key (from_entity_id, to_entity_id, unit_id)=(1, 2, 4) already exists.
[SQL: INSERT INTO connections (from_entity_id, to_entity_id, unit_id, multiplier, created_at, updated_at) SELECT p0::INTEGER, p1::INTEGER, p2::INTEGER, p3::NUMERIC(10, 1), p4::TIMESTAMP WITHOUT TIME ZONE, p5::TIMESTAMP WITHOUT TIME ZONE FROM (VALUES ($1::I ... 275 characters truncated ...  p2, p3, p4, p5, sen_counter) ORDER BY sen_counter RETURNING connections.id, connections.id AS id__1]
[parameters: (1, 2, 4, Decimal('4.0'), datetime.datetime(2025, 7, 7, 23, 34, 33, 322235), datetime.datetime(2025, 7, 7, 23, 34, 33, 322236), 2, 1, 4, Decimal('0.2'), datetime.datetime(2025, 7, 7, 23, 34, 33, 322236), datetime.datetime(2025, 7, 7, 23, 34, 33, 322237))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
ERROR    src.routes.connections:connections.py:161 Connection data: from_entity_id=1, to_entity_id=2, unit_id=4, multiplier=4.0
ERROR    src.routes.connections:connections.py:160 IntegrityError creating connection: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "connections_unique"
DETAIL:  Key (from_entity_id, to_entity_id, unit_id)=(1, 2, 4) already exists.
[SQL: INSERT INTO connections (from_entity_id, to_entity_id, unit_id, multiplier, created_at, updated_at) SELECT p0::INTEGER, p1::INTEGER, p2::INTEGER, p3::NUMERIC(10, 1), p4::TIMESTAMP WITHOUT TIME ZONE, p5::TIMESTAMP WITHOUT TIME ZONE FROM (VALUES ($1::I ... 275 characters truncated ...  p2, p3, p4, p5, sen_counter) ORDER BY sen_counter RETURNING connections.id, connections.id AS id__1]
[parameters: (1, 2, 4, Decimal('2.0'), datetime.datetime(2025, 7, 7, 23, 34, 33, 323316), datetime.datetime(2025, 7, 7, 23, 34, 33, 323317), 2, 1, 4, Decimal('0.5'), datetime.datetime(2025, 7, 7, 23, 34, 33, 323318), datetime.datetime(2025, 7, 7, 23, 34, 33, 323318))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
ERROR    src.routes.connections:connections.py:161 Connection data: from_entity_id=1, to_entity_id=2, unit_id=4, multiplier=2.0
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 400 Bad Request"
ERROR    src.routes.connections:connections.py:160 IntegrityError creating connection: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "connections_unique"
DETAIL:  Key (from_entity_id, to_entity_id, unit_id)=(1, 2, 4) already exists.
[SQL: INSERT INTO connections (from_entity_id, to_entity_id, unit_id, multiplier, created_at, updated_at) SELECT p0::INTEGER, p1::INTEGER, p2::INTEGER, p3::NUMERIC(10, 1), p4::TIMESTAMP WITHOUT TIME ZONE, p5::TIMESTAMP WITHOUT TIME ZONE FROM (VALUES ($1::I ... 275 characters truncated ...  p2, p3, p4, p5, sen_counter) ORDER BY sen_counter RETURNING connections.id, connections.id AS id__1]
[parameters: (1, 2, 4, Decimal('6.0'), datetime.datetime(2025, 7, 7, 23, 34, 33, 323714), datetime.datetime(2025, 7, 7, 23, 34, 33, 323715), 2, 1, 4, Decimal('0.2'), datetime.datetime(2025, 7, 7, 23, 34, 33, 323715), datetime.datetime(2025, 7, 7, 23, 34, 33, 323716))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
ERROR    src.routes.connections:connections.py:161 Connection data: from_entity_id=1, to_entity_id=2, unit_id=4, multiplier=6.0
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 400 Bad Request"
INFO     src.routes.connections:connections.py:149 Created connection ID: 1
INFO     src.routes.connections:connections.py:150 Primary connection: 1 -> 2, multiplier: 3.0
INFO     src.routes.connections:connections.py:151 Inverse connection should exist: 2 -> 1, multiplier: 0.3
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 201 Created"
_ TestConcurrentConnectionOperations.test_concurrent_connection_inverse_consistency _
tests/test_concurrent_operations.py:397: in test_concurrent_connection_inverse_consistency
    assert result["status_code"] == 201, f"Connection creation failed: {result}"
E   AssertionError: Connection creation failed: {'direction': 'forward', 'status_code': 400}
E   assert 400 == 201
---------------------------- Captured stderr setup -----------------------------
INFO:backend.tests.conftest:Cleaning test database: 1 entities, 0 connections
------------------------------ Captured log setup ------------------------------
INFO     backend.tests.conftest:conftest.py:96 Cleaning test database: 1 entities, 0 connections
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'InverseConsA fceaefab' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'InverseConsB dbecabbf' with ID 2
INFO:src.routes.entities:Entity verification successful: ID 2 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:httpx:HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO:src.routes.connections:Validating entities for connection: from_entity_id=1, to_entity_id=2
INFO:src.routes.connections:Validating entities for connection: from_entity_id=2, to_entity_id=1
INFO:src.routes.connections:Entities validated successfully: from_entity=InverseConsB dbecabbf (id=2), to_entity=InverseConsA fceaefab (id=1)
INFO:src.routes.connections:Entities validated successfully: from_entity=InverseConsA fceaefab (id=1), to_entity=InverseConsB dbecabbf (id=2)
ERROR:src.routes.connections:IntegrityError creating connection: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "connections_unique"
DETAIL:  Key (from_entity_id, to_entity_id, unit_id)=(1, 2, 4) already exists.
[SQL: INSERT INTO connections (from_entity_id, to_entity_id, unit_id, multiplier, created_at, updated_at) SELECT p0::INTEGER, p1::INTEGER, p2::INTEGER, p3::NUMERIC(10, 1), p4::TIMESTAMP WITHOUT TIME ZONE, p5::TIMESTAMP WITHOUT TIME ZONE FROM (VALUES ($1::I ... 275 characters truncated ...  p2, p3, p4, p5, sen_counter) ORDER BY sen_counter RETURNING connections.id, connections.id AS id__1]
[parameters: (1, 2, 4, Decimal('2.0'), datetime.datetime(2025, 7, 7, 23, 34, 33, 784512), datetime.datetime(2025, 7, 7, 23, 34, 33, 784513), 2, 1, 4, Decimal('0.5'), datetime.datetime(2025, 7, 7, 23, 34, 33, 784513), datetime.datetime(2025, 7, 7, 23, 34, 33, 784513))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
ERROR:src.routes.connections:Connection data: from_entity_id=1, to_entity_id=2, unit_id=4, multiplier=2.0
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 400 Bad Request"
INFO:src.routes.connections:Created connection ID: 1
INFO:src.routes.connections:Primary connection: 2 -> 1, multiplier: 0.5
INFO:src.routes.connections:Inverse connection should exist: 1 -> 2, multiplier: 2.0
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 201 Created"
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'InverseConsA fceaefab' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'InverseConsB dbecabbf' with ID 2
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 2 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=1, to_entity_id=2
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=2, to_entity_id=1
INFO     src.routes.connections:connections.py:54 Entities validated successfully: from_entity=InverseConsB dbecabbf (id=2), to_entity=InverseConsA fceaefab (id=1)
INFO     src.routes.connections:connections.py:54 Entities validated successfully: from_entity=InverseConsA fceaefab (id=1), to_entity=InverseConsB dbecabbf (id=2)
ERROR    src.routes.connections:connections.py:160 IntegrityError creating connection: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "connections_unique"
DETAIL:  Key (from_entity_id, to_entity_id, unit_id)=(1, 2, 4) already exists.
[SQL: INSERT INTO connections (from_entity_id, to_entity_id, unit_id, multiplier, created_at, updated_at) SELECT p0::INTEGER, p1::INTEGER, p2::INTEGER, p3::NUMERIC(10, 1), p4::TIMESTAMP WITHOUT TIME ZONE, p5::TIMESTAMP WITHOUT TIME ZONE FROM (VALUES ($1::I ... 275 characters truncated ...  p2, p3, p4, p5, sen_counter) ORDER BY sen_counter RETURNING connections.id, connections.id AS id__1]
[parameters: (1, 2, 4, Decimal('2.0'), datetime.datetime(2025, 7, 7, 23, 34, 33, 784512), datetime.datetime(2025, 7, 7, 23, 34, 33, 784513), 2, 1, 4, Decimal('0.5'), datetime.datetime(2025, 7, 7, 23, 34, 33, 784513), datetime.datetime(2025, 7, 7, 23, 34, 33, 784513))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
ERROR    src.routes.connections:connections.py:161 Connection data: from_entity_id=1, to_entity_id=2, unit_id=4, multiplier=2.0
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 400 Bad Request"
INFO     src.routes.connections:connections.py:149 Created connection ID: 1
INFO     src.routes.connections:connections.py:150 Primary connection: 2 -> 1, multiplier: 0.5
INFO     src.routes.connections:connections.py:151 Inverse connection should exist: 1 -> 2, multiplier: 2.0
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 201 Created"
________ TestConcurrentComplexOperations.test_concurrent_graph_creation ________
tests/test_concurrent_operations.py:432: in test_concurrent_graph_creation
    assert response.status_code == 201
E   assert 422 == 201
E    +  where 422 = <Response [422 Unprocessable Entity]>.status_code
---------------------------- Captured stderr setup -----------------------------
INFO:backend.tests.conftest:Cleaning test database: 2 entities, 2 connections
------------------------------ Captured log setup ------------------------------
INFO     backend.tests.conftest:conftest.py:96 Cleaning test database: 2 entities, 2 connections
----------------------------- Captured stderr call -----------------------------
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
____ TestConcurrentComplexOperations.test_concurrent_pathfinding_operations ____
tests/test_concurrent_operations.py:517: in test_concurrent_pathfinding_operations
    assert response.status_code == 201
E   assert 422 == 201
E    +  where 422 = <Response [422 Unprocessable Entity]>.status_code
----------------------------- Captured stderr call -----------------------------
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
_________ TestTransactionIsolationLevels.test_read_committed_isolation _________
tests/test_concurrent_operations.py:720: in test_read_committed_isolation
    assert update_result["update_count"] > 0, "Should have performed updates"
E   AssertionError: Should have performed updates
E   assert 0 > 0
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'IsolationTest aeecbfab' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:httpx:HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'IsolationTest aeecbfab' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     httpx:_client.py:1758 HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: PUT http://test/api/v1/entities/1 "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/entities/1 "HTTP/1.1 200 OK"
_________ TestTransactionIsolationLevels.test_phantom_read_prevention __________
tests/test_concurrent_operations.py:823: in test_phantom_read_prevention
    assert created_connections > 0, "Should have created additional connections"
E   AssertionError: Should have created additional connections
E   assert 0 > 0
---------------------------- Captured stderr setup -----------------------------
INFO:backend.tests.conftest:Cleaning test database: 1 entities, 0 connections
------------------------------ Captured log setup ------------------------------
INFO     backend.tests.conftest:conftest.py:96 Cleaning test database: 1 entities, 0 connections
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'PhantomA eaacecbb' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'PhantomB ffccdccb' with ID 2
INFO:src.routes.entities:Entity verification successful: ID 2 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:httpx:HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO:src.routes.connections:Validating entities for connection: from_entity_id=1, to_entity_id=2
INFO:src.routes.connections:Entities validated successfully: from_entity=PhantomA eaacecbb (id=1), to_entity=PhantomB ffccdccb (id=2)
INFO:src.routes.connections:Created connection ID: 1
INFO:src.routes.connections:Primary connection: 1 -> 2, multiplier: 2.0
INFO:src.routes.connections:Inverse connection should exist: 2 -> 1, multiplier: 0.5
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 201 Created"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:src.routes.connections:GET connections called with skip=0, limit=100
INFO:src.routes.connections:Found 2 connections
INFO:src.routes.connections:  Connection 1: 1 -> 2, multiplier: 2.0
INFO:src.routes.connections:  Connection 2: 2 -> 1, multiplier: 0.5
INFO:httpx:HTTP Request: GET http://test/api/v1/connections/ "HTTP/1.1 200 OK"
INFO:src.routes.connections:GET connections called with skip=0, limit=100
INFO:src.routes.connections:Found 2 connections
INFO:src.routes.connections:  Connection 1: 1 -> 2, multiplier: 2.0
INFO:src.routes.connections:  Connection 2: 2 -> 1, multiplier: 0.5
INFO:httpx:HTTP Request: GET http://test/api/v1/connections/ "HTTP/1.1 200 OK"
INFO:src.routes.connections:GET connections called with skip=0, limit=100
INFO:src.routes.connections:Found 2 connections
INFO:src.routes.connections:  Connection 1: 1 -> 2, multiplier: 2.0
INFO:src.routes.connections:  Connection 2: 2 -> 1, multiplier: 0.5
INFO:httpx:HTTP Request: GET http://test/api/v1/connections/ "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:src.routes.connections:GET connections called with skip=0, limit=100
INFO:src.routes.connections:Found 2 connections
INFO:src.routes.connections:  Connection 1: 1 -> 2, multiplier: 2.0
INFO:src.routes.connections:  Connection 2: 2 -> 1, multiplier: 0.5
INFO:httpx:HTTP Request: GET http://test/api/v1/connections/ "HTTP/1.1 200 OK"
INFO:src.routes.connections:GET connections called with skip=0, limit=100
INFO:src.routes.connections:Found 2 connections
INFO:src.routes.connections:  Connection 1: 1 -> 2, multiplier: 2.0
INFO:src.routes.connections:  Connection 2: 2 -> 1, multiplier: 0.5
INFO:httpx:HTTP Request: GET http://test/api/v1/connections/ "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:src.routes.connections:GET connections called with skip=0, limit=100
INFO:src.routes.connections:Found 2 connections
INFO:src.routes.connections:  Connection 1: 1 -> 2, multiplier: 2.0
INFO:src.routes.connections:  Connection 2: 2 -> 1, multiplier: 0.5
INFO:httpx:HTTP Request: GET http://test/api/v1/connections/ "HTTP/1.1 200 OK"
INFO:src.routes.connections:GET connections called with skip=0, limit=100
INFO:src.routes.connections:Found 2 connections
INFO:src.routes.connections:  Connection 1: 1 -> 2, multiplier: 2.0
INFO:src.routes.connections:  Connection 2: 2 -> 1, multiplier: 0.5
INFO:httpx:HTTP Request: GET http://test/api/v1/connections/ "HTTP/1.1 200 OK"
INFO:src.routes.connections:GET connections called with skip=0, limit=100
INFO:src.routes.connections:Found 2 connections
INFO:src.routes.connections:  Connection 1: 1 -> 2, multiplier: 2.0
INFO:src.routes.connections:  Connection 2: 2 -> 1, multiplier: 0.5
INFO:httpx:HTTP Request: GET http://test/api/v1/connections/ "HTTP/1.1 200 OK"
INFO:src.routes.connections:GET connections called with skip=0, limit=100
INFO:src.routes.connections:Found 2 connections
INFO:src.routes.connections:  Connection 1: 1 -> 2, multiplier: 2.0
INFO:src.routes.connections:  Connection 2: 2 -> 1, multiplier: 0.5
INFO:httpx:HTTP Request: GET http://test/api/v1/connections/ "HTTP/1.1 200 OK"
INFO:src.routes.connections:GET connections called with skip=0, limit=100
INFO:src.routes.connections:Found 2 connections
INFO:src.routes.connections:  Connection 1: 1 -> 2, multiplier: 2.0
INFO:src.routes.connections:  Connection 2: 2 -> 1, multiplier: 0.5
INFO:httpx:HTTP Request: GET http://test/api/v1/connections/ "HTTP/1.1 200 OK"
INFO:src.routes.connections:GET connections called with skip=0, limit=100
INFO:src.routes.connections:Found 2 connections
INFO:src.routes.connections:  Connection 1: 1 -> 2, multiplier: 2.0
INFO:src.routes.connections:  Connection 2: 2 -> 1, multiplier: 0.5
INFO:httpx:HTTP Request: GET http://test/api/v1/connections/ "HTTP/1.1 200 OK"
INFO:src.routes.connections:GET connections called with skip=0, limit=100
INFO:src.routes.connections:Found 2 connections
INFO:src.routes.connections:  Connection 1: 1 -> 2, multiplier: 2.0
INFO:src.routes.connections:  Connection 2: 2 -> 1, multiplier: 0.5
INFO:httpx:HTTP Request: GET http://test/api/v1/connections/ "HTTP/1.1 200 OK"
INFO:src.routes.connections:GET connections called with skip=0, limit=100
INFO:src.routes.connections:Found 2 connections
INFO:src.routes.connections:  Connection 1: 1 -> 2, multiplier: 2.0
INFO:src.routes.connections:  Connection 2: 2 -> 1, multiplier: 0.5
INFO:httpx:HTTP Request: GET http://test/api/v1/connections/ "HTTP/1.1 200 OK"
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'PhantomA eaacecbb' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'PhantomB ffccdccb' with ID 2
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 2 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=1, to_entity_id=2
INFO     src.routes.connections:connections.py:54 Entities validated successfully: from_entity=PhantomA eaacecbb (id=1), to_entity=PhantomB ffccdccb (id=2)
INFO     src.routes.connections:connections.py:149 Created connection ID: 1
INFO     src.routes.connections:connections.py:150 Primary connection: 1 -> 2, multiplier: 2.0
INFO     src.routes.connections:connections.py:151 Inverse connection should exist: 2 -> 1, multiplier: 0.5
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 201 Created"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     src.routes.connections:connections.py:213 GET connections called with skip=0, limit=100
INFO     src.routes.connections:connections.py:214 Found 2 connections
INFO     src.routes.connections:connections.py:216   Connection 1: 1 -> 2, multiplier: 2.0
INFO     src.routes.connections:connections.py:216   Connection 2: 2 -> 1, multiplier: 0.5
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/connections/ "HTTP/1.1 200 OK"
INFO     src.routes.connections:connections.py:213 GET connections called with skip=0, limit=100
INFO     src.routes.connections:connections.py:214 Found 2 connections
INFO     src.routes.connections:connections.py:216   Connection 1: 1 -> 2, multiplier: 2.0
INFO     src.routes.connections:connections.py:216   Connection 2: 2 -> 1, multiplier: 0.5
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/connections/ "HTTP/1.1 200 OK"
INFO     src.routes.connections:connections.py:213 GET connections called with skip=0, limit=100
INFO     src.routes.connections:connections.py:214 Found 2 connections
INFO     src.routes.connections:connections.py:216   Connection 1: 1 -> 2, multiplier: 2.0
INFO     src.routes.connections:connections.py:216   Connection 2: 2 -> 1, multiplier: 0.5
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/connections/ "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     src.routes.connections:connections.py:213 GET connections called with skip=0, limit=100
INFO     src.routes.connections:connections.py:214 Found 2 connections
INFO     src.routes.connections:connections.py:216   Connection 1: 1 -> 2, multiplier: 2.0
INFO     src.routes.connections:connections.py:216   Connection 2: 2 -> 1, multiplier: 0.5
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/connections/ "HTTP/1.1 200 OK"
INFO     src.routes.connections:connections.py:213 GET connections called with skip=0, limit=100
INFO     src.routes.connections:connections.py:214 Found 2 connections
INFO     src.routes.connections:connections.py:216   Connection 1: 1 -> 2, multiplier: 2.0
INFO     src.routes.connections:connections.py:216   Connection 2: 2 -> 1, multiplier: 0.5
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/connections/ "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     src.routes.connections:connections.py:213 GET connections called with skip=0, limit=100
INFO     src.routes.connections:connections.py:214 Found 2 connections
INFO     src.routes.connections:connections.py:216   Connection 1: 1 -> 2, multiplier: 2.0
INFO     src.routes.connections:connections.py:216   Connection 2: 2 -> 1, multiplier: 0.5
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/connections/ "HTTP/1.1 200 OK"
INFO     src.routes.connections:connections.py:213 GET connections called with skip=0, limit=100
INFO     src.routes.connections:connections.py:214 Found 2 connections
INFO     src.routes.connections:connections.py:216   Connection 1: 1 -> 2, multiplier: 2.0
INFO     src.routes.connections:connections.py:216   Connection 2: 2 -> 1, multiplier: 0.5
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/connections/ "HTTP/1.1 200 OK"
INFO     src.routes.connections:connections.py:213 GET connections called with skip=0, limit=100
INFO     src.routes.connections:connections.py:214 Found 2 connections
INFO     src.routes.connections:connections.py:216   Connection 1: 1 -> 2, multiplier: 2.0
INFO     src.routes.connections:connections.py:216   Connection 2: 2 -> 1, multiplier: 0.5
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/connections/ "HTTP/1.1 200 OK"
INFO     src.routes.connections:connections.py:213 GET connections called with skip=0, limit=100
INFO     src.routes.connections:connections.py:214 Found 2 connections
INFO     src.routes.connections:connections.py:216   Connection 1: 1 -> 2, multiplier: 2.0
INFO     src.routes.connections:connections.py:216   Connection 2: 2 -> 1, multiplier: 0.5
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/connections/ "HTTP/1.1 200 OK"
INFO     src.routes.connections:connections.py:213 GET connections called with skip=0, limit=100
INFO     src.routes.connections:connections.py:214 Found 2 connections
INFO     src.routes.connections:connections.py:216   Connection 1: 1 -> 2, multiplier: 2.0
INFO     src.routes.connections:connections.py:216   Connection 2: 2 -> 1, multiplier: 0.5
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/connections/ "HTTP/1.1 200 OK"
INFO     src.routes.connections:connections.py:213 GET connections called with skip=0, limit=100
INFO     src.routes.connections:connections.py:214 Found 2 connections
INFO     src.routes.connections:connections.py:216   Connection 1: 1 -> 2, multiplier: 2.0
INFO     src.routes.connections:connections.py:216   Connection 2: 2 -> 1, multiplier: 0.5
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/connections/ "HTTP/1.1 200 OK"
INFO     src.routes.connections:connections.py:213 GET connections called with skip=0, limit=100
INFO     src.routes.connections:connections.py:214 Found 2 connections
INFO     src.routes.connections:connections.py:216   Connection 1: 1 -> 2, multiplier: 2.0
INFO     src.routes.connections:connections.py:216   Connection 2: 2 -> 1, multiplier: 0.5
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/connections/ "HTTP/1.1 200 OK"
INFO     src.routes.connections:connections.py:213 GET connections called with skip=0, limit=100
INFO     src.routes.connections:connections.py:214 Found 2 connections
INFO     src.routes.connections:connections.py:216   Connection 1: 1 -> 2, multiplier: 2.0
INFO     src.routes.connections:connections.py:216   Connection 2: 2 -> 1, multiplier: 0.5
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/connections/ "HTTP/1.1 200 OK"
______ TestConnectionsErrorPaths.test_create_connection_database_failure _______
tests/test_connections_critical_coverage.py:59: in test_create_connection_database_failure
    assert response.status_code == 500
E   assert 201 == 500
E    +  where 201 = <Response [201 Created]>.status_code
---------------------------- Captured stderr setup -----------------------------
INFO:backend.tests.conftest:Cleaning test database: 2 entities, 2 connections
------------------------------ Captured log setup ------------------------------
INFO     backend.tests.conftest:conftest.py:96 Cleaning test database: 2 entities, 2 connections
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'DbFailTest cbeabfca' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'DbFailTest dabfeabf' with ID 2
INFO:src.routes.entities:Entity verification successful: ID 2 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.connections:Validating entities for connection: from_entity_id=1, to_entity_id=2
INFO:src.routes.connections:Entities validated successfully: from_entity=DbFailTest cbeabfca (id=1), to_entity=DbFailTest dabfeabf (id=2)
INFO:src.routes.connections:Created connection ID: 1
INFO:src.routes.connections:Primary connection: 1 -> 2, multiplier: 2.5
INFO:src.routes.connections:Inverse connection should exist: 2 -> 1, multiplier: 0.4
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 201 Created"
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'DbFailTest cbeabfca' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'DbFailTest dabfeabf' with ID 2
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 2 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=1, to_entity_id=2
INFO     src.routes.connections:connections.py:54 Entities validated successfully: from_entity=DbFailTest cbeabfca (id=1), to_entity=DbFailTest dabfeabf (id=2)
INFO     src.routes.connections:connections.py:149 Created connection ID: 1
INFO     src.routes.connections:connections.py:150 Primary connection: 1 -> 2, multiplier: 2.5
INFO     src.routes.connections:connections.py:151 Inverse connection should exist: 2 -> 1, multiplier: 0.4
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 201 Created"
_ TestConnectionsErrorPaths.test_create_connection_integrity_error_foreign_key _
tests/test_connections_critical_coverage.py:190: in test_create_connection_integrity_error_foreign_key
    assert response.status_code == 400
E   assert 201 == 400
E    +  where 201 = <Response [201 Created]>.status_code
---------------------------- Captured stderr setup -----------------------------
INFO:backend.tests.conftest:Cleaning test database: 2 entities, 2 connections
------------------------------ Captured log setup ------------------------------
INFO     backend.tests.conftest:conftest.py:96 Cleaning test database: 2 entities, 2 connections
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'FKFailTest affecbee' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'FKFailTest acbebdaa' with ID 2
INFO:src.routes.entities:Entity verification successful: ID 2 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.connections:Validating entities for connection: from_entity_id=1, to_entity_id=2
INFO:src.routes.connections:Entities validated successfully: from_entity=FKFailTest affecbee (id=1), to_entity=FKFailTest acbebdaa (id=2)
INFO:src.routes.connections:Created connection ID: 1
INFO:src.routes.connections:Primary connection: 1 -> 2, multiplier: 2.0
INFO:src.routes.connections:Inverse connection should exist: 2 -> 1, multiplier: 0.5
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 201 Created"
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'FKFailTest affecbee' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'FKFailTest acbebdaa' with ID 2
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 2 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=1, to_entity_id=2
INFO     src.routes.connections:connections.py:54 Entities validated successfully: from_entity=FKFailTest affecbee (id=1), to_entity=FKFailTest acbebdaa (id=2)
INFO     src.routes.connections:connections.py:149 Created connection ID: 1
INFO     src.routes.connections:connections.py:150 Primary connection: 1 -> 2, multiplier: 2.0
INFO     src.routes.connections:connections.py:151 Inverse connection should exist: 2 -> 1, multiplier: 0.5
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 201 Created"
_ TestConnectionsErrorPaths.test_create_connection_integrity_error_self_reference _
tests/test_connections_critical_coverage.py:222: in test_create_connection_integrity_error_self_reference
    assert response.status_code == 400
E   assert 422 == 400
E    +  where 422 = <Response [422 Unprocessable Entity]>.status_code
---------------------------- Captured stderr setup -----------------------------
INFO:backend.tests.conftest:Cleaning test database: 2 entities, 2 connections
------------------------------ Captured log setup ------------------------------
INFO     backend.tests.conftest:conftest.py:96 Cleaning test database: 2 entities, 2 connections
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'SelfRefTest defcabcc' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'SelfRefTest defcabcc' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
_ TestConnectionsErrorPaths.test_create_connection_integrity_error_positive_multiplier _
tests/test_connections_critical_coverage.py:259: in test_create_connection_integrity_error_positive_multiplier
    assert response.status_code == 400
E   assert 422 == 400
E    +  where 422 = <Response [422 Unprocessable Entity]>.status_code
---------------------------- Captured stderr setup -----------------------------
INFO:backend.tests.conftest:Cleaning test database: 1 entities, 0 connections
------------------------------ Captured log setup ------------------------------
INFO     backend.tests.conftest:conftest.py:96 Cleaning test database: 1 entities, 0 connections
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'PosMultTest cbaeaade' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'PosMultTest efaeceba' with ID 2
INFO:src.routes.entities:Entity verification successful: ID 2 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'PosMultTest cbaeaade' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'PosMultTest efaeceba' with ID 2
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 2 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
___ TestConnectionsErrorPaths.test_create_connection_integrity_error_generic ___
tests/test_connections_critical_coverage.py:296: in test_create_connection_integrity_error_generic
    assert response.status_code == 400
E   assert 201 == 400
E    +  where 201 = <Response [201 Created]>.status_code
---------------------------- Captured stderr setup -----------------------------
INFO:backend.tests.conftest:Cleaning test database: 2 entities, 0 connections
------------------------------ Captured log setup ------------------------------
INFO     backend.tests.conftest:conftest.py:96 Cleaning test database: 2 entities, 0 connections
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'GenericError ecbcbbca' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'GenericError fafadeea' with ID 2
INFO:src.routes.entities:Entity verification successful: ID 2 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.connections:Validating entities for connection: from_entity_id=1, to_entity_id=2
INFO:src.routes.connections:Entities validated successfully: from_entity=GenericError ecbcbbca (id=1), to_entity=GenericError fafadeea (id=2)
INFO:src.routes.connections:Created connection ID: 1
INFO:src.routes.connections:Primary connection: 1 -> 2, multiplier: 2.0
INFO:src.routes.connections:Inverse connection should exist: 2 -> 1, multiplier: 0.5
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 201 Created"
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'GenericError ecbcbbca' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'GenericError fafadeea' with ID 2
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 2 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=1, to_entity_id=2
INFO     src.routes.connections:connections.py:54 Entities validated successfully: from_entity=GenericError ecbcbbca (id=1), to_entity=GenericError fafadeea (id=2)
INFO     src.routes.connections:connections.py:149 Created connection ID: 1
INFO     src.routes.connections:connections.py:150 Primary connection: 1 -> 2, multiplier: 2.0
INFO     src.routes.connections:connections.py:151 Inverse connection should exist: 2 -> 1, multiplier: 0.5
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 201 Created"
____ TestConnectionsUpdateAndDelete.test_update_connection_no_inverse_found ____
tests/test_connections_critical_coverage.py:380: in test_update_connection_no_inverse_found
    assert response.status_code == 200
E   assert 404 == 200
E    +  where 404 = <Response [404 Not Found]>.status_code
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'UpdateTest adbdbcdb' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'UpdateTest fcdedada' with ID 2
INFO:src.routes.entities:Entity verification successful: ID 2 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.connections:Validating entities for connection: from_entity_id=1, to_entity_id=2
INFO:src.routes.connections:Entities validated successfully: from_entity=UpdateTest adbdbcdb (id=1), to_entity=UpdateTest fcdedada (id=2)
INFO:src.routes.connections:Created connection ID: 1
INFO:src.routes.connections:Primary connection: 1 -> 2, multiplier: 2.0
INFO:src.routes.connections:Inverse connection should exist: 2 -> 1, multiplier: 0.5
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 201 Created"
INFO:src.routes.connections:GET connections called with skip=0, limit=100
INFO:src.routes.connections:Found 2 connections
INFO:src.routes.connections:  Connection 1: 1 -> 2, multiplier: 2.0
INFO:src.routes.connections:  Connection 2: 2 -> 1, multiplier: 0.5
INFO:httpx:HTTP Request: GET http://test/api/v1/connections/ "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: PUT http://test/api/v1/connections/1 "HTTP/1.1 404 Not Found"
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'UpdateTest adbdbcdb' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'UpdateTest fcdedada' with ID 2
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 2 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=1, to_entity_id=2
INFO     src.routes.connections:connections.py:54 Entities validated successfully: from_entity=UpdateTest adbdbcdb (id=1), to_entity=UpdateTest fcdedada (id=2)
INFO     src.routes.connections:connections.py:149 Created connection ID: 1
INFO     src.routes.connections:connections.py:150 Primary connection: 1 -> 2, multiplier: 2.0
INFO     src.routes.connections:connections.py:151 Inverse connection should exist: 2 -> 1, multiplier: 0.5
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 201 Created"
INFO     src.routes.connections:connections.py:213 GET connections called with skip=0, limit=100
INFO     src.routes.connections:connections.py:214 Found 2 connections
INFO     src.routes.connections:connections.py:216   Connection 1: 1 -> 2, multiplier: 2.0
INFO     src.routes.connections:connections.py:216   Connection 2: 2 -> 1, multiplier: 0.5
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/connections/ "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: PUT http://test/api/v1/connections/1 "HTTP/1.1 404 Not Found"
_______ TestConnectionsUpdateAndDelete.test_delete_connection_no_inverse _______
tests/test_connections_critical_coverage.py:491: in test_delete_connection_no_inverse
    response = await test_client.delete(f"/api/v1/connections/{connection_id}")
venv/lib/python3.11/site-packages/httpx/_client.py:1984: in delete
    return await self.request(
venv/lib/python3.11/site-packages/httpx/_client.py:1559: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
venv/lib/python3.11/site-packages/httpx/_client.py:1646: in send
    response = await self._send_handling_auth(
venv/lib/python3.11/site-packages/httpx/_client.py:1674: in _send_handling_auth
    response = await self._send_handling_redirects(
venv/lib/python3.11/site-packages/httpx/_client.py:1711: in _send_handling_redirects
    response = await self._send_single_request(request)
venv/lib/python3.11/site-packages/httpx/_client.py:1748: in _send_single_request
    response = await transport.handle_async_request(request)
venv/lib/python3.11/site-packages/httpx/_transports/asgi.py:162: in handle_async_request
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/applications.py:123: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
venv/lib/python3.11/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:762: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:782: in app
    await route.handle(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:297: in handle
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:77: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:72: in app
    response = await func(request)
venv/lib/python3.11/site-packages/fastapi/routing.py:221: in app
    async with AsyncExitStack() as async_exit_stack:
/opt/homebrew/Cellar/python@3.11/3.11.13/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py:745: in __aexit__
    raise exc_details[1]
/opt/homebrew/Cellar/python@3.11/3.11.13/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py:728: in __aexit__
    cb_suppress = await cb(*exc_details)
/opt/homebrew/Cellar/python@3.11/3.11.13/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py:231: in __aexit__
    await self.gen.athrow(typ, value, traceback)
src/database.py:67: in get_db
    await session.close()
venv/lib/python3.11/site-packages/sqlalchemy/ext/asyncio/session.py:1027: in close
    await greenlet_spawn(self.sync_session.close)
venv/lib/python3.11/site-packages/sqlalchemy/util/_concurrency_py3k.py:189: in greenlet_spawn
    result = context.switch(*args, **kwargs)
venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:2468: in close
    self._close_impl(invalidate=False)
venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:2534: in _close_impl
    self.expunge_all()
venv/lib/python3.11/site-packages/sqlalchemy/orm/session.py:2553: in expunge_all
    statelib.InstanceState._detach_states(all_states, self)
venv/lib/python3.11/site-packages/sqlalchemy/orm/state.py:478: in _detach_states
    deleted = state._deleted
/opt/homebrew/Cellar/python@3.11/3.11.13/Frameworks/Python.framework/Versions/3.11/lib/python3.11/unittest/mock.py:665: in __getattr__
    raise AttributeError(name)
E   AttributeError: _deleted
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'DeleteTest defbacea' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'DeleteTest acddadaf' with ID 2
INFO:src.routes.entities:Entity verification successful: ID 2 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.connections:Validating entities for connection: from_entity_id=1, to_entity_id=2
INFO:src.routes.connections:Entities validated successfully: from_entity=DeleteTest defbacea (id=1), to_entity=DeleteTest acddadaf (id=2)
INFO:src.routes.connections:Created connection ID: 1
INFO:src.routes.connections:Primary connection: 1 -> 2, multiplier: 2.0
INFO:src.routes.connections:Inverse connection should exist: 2 -> 1, multiplier: 0.5
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 201 Created"
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'DeleteTest defbacea' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'DeleteTest acddadaf' with ID 2
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 2 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=1, to_entity_id=2
INFO     src.routes.connections:connections.py:54 Entities validated successfully: from_entity=DeleteTest defbacea (id=1), to_entity=DeleteTest acddadaf (id=2)
INFO     src.routes.connections:connections.py:149 Created connection ID: 1
INFO     src.routes.connections:connections.py:150 Primary connection: 1 -> 2, multiplier: 2.0
INFO     src.routes.connections:connections.py:151 Inverse connection should exist: 2 -> 1, multiplier: 0.5
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 201 Created"
_ TestConnectionsDecimalPrecision.test_create_connection_extreme_small_multiplier _
tests/test_connections_critical_coverage.py:521: in test_create_connection_extreme_small_multiplier
    assert response.status_code == 201
E   assert 422 == 201
E    +  where 422 = <Response [422 Unprocessable Entity]>.status_code
---------------------------- Captured stderr setup -----------------------------
INFO:backend.tests.conftest:Cleaning test database: 2 entities, 2 connections
------------------------------ Captured log setup ------------------------------
INFO     backend.tests.conftest:conftest.py:96 Cleaning test database: 2 entities, 2 connections
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'DecimalTest eefabade' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'DecimalTest baebfdce' with ID 2
INFO:src.routes.entities:Entity verification successful: ID 2 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'DecimalTest eefabade' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'DecimalTest baebfdce' with ID 2
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 2 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
___ TestConnectionsDecimalPrecision.test_create_connection_bankers_rounding ____
tests/test_connections_critical_coverage.py:566: in test_create_connection_bankers_rounding
    entity1_test_id = entity1_test_response.json()["id"]
E   KeyError: 'id'
---------------------------- Captured stderr setup -----------------------------
INFO:backend.tests.conftest:Cleaning test database: 2 entities, 0 connections
------------------------------ Captured log setup ------------------------------
INFO     backend.tests.conftest:conftest.py:96 Cleaning test database: 2 entities, 0 connections
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'BankersTest aefcacde' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'BankersTest eacadbcb' with ID 2
INFO:src.routes.entities:Entity verification successful: ID 2 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'BankersTest aefcacde' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'BankersTest eacadbcb' with ID 2
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 2 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
_ TestConnectionsIntegrationScenarios.test_complex_connection_network_creation _
tests/test_connections_critical_coverage.py:740: in test_complex_connection_network_creation
    entity_ids.append(response.json()["id"])
E   KeyError: 'id'
---------------------------- Captured stderr setup -----------------------------
INFO:backend.tests.conftest:Cleaning test database: 2 entities, 0 connections
------------------------------ Captured log setup ------------------------------
INFO     backend.tests.conftest:conftest.py:96 Cleaning test database: 2 entities, 0 connections
----------------------------- Captured stderr call -----------------------------
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
_ TestTransactionIntegrity.test_connection_self_reference_constraint_violation _
tests/test_database_transactions.py:161: in test_connection_self_reference_constraint_violation
    assert connection_response.status_code == 400
E   assert 422 == 400
E    +  where 422 = <Response [422 Unprocessable Entity]>.status_code
---------------------------- Captured stderr setup -----------------------------
INFO:backend.tests.conftest:Cleaning test database: 2 entities, 2 connections
------------------------------ Captured log setup ------------------------------
INFO     backend.tests.conftest:conftest.py:96 Cleaning test database: 2 entities, 2 connections
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'SelfRefTest afecfaca' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:httpx:HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'SelfRefTest afecfaca' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
________ TestTransactionIntegrity.test_multiple_entity_creation_atomic _________
tests/test_database_transactions.py:186: in test_multiple_entity_creation_atomic
    assert response.status_code == 201
E   assert 422 == 201
E    +  where 422 = <Response [422 Unprocessable Entity]>.status_code
---------------------------- Captured stderr setup -----------------------------
INFO:backend.tests.conftest:Cleaning test database: 1 entities, 0 connections
------------------------------ Captured log setup ------------------------------
INFO     backend.tests.conftest:conftest.py:96 Cleaning test database: 1 entities, 0 connections
----------------------------- Captured stderr call -----------------------------
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
______ TestRollbackScenarios.test_connection_negative_multiplier_rollback ______
tests/test_database_transactions.py:233: in test_connection_negative_multiplier_rollback
    assert connection_response.status_code == 400
E   assert 422 == 400
E    +  where 422 = <Response [422 Unprocessable Entity]>.status_code
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'RollbackTestA fefcdebe' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'RollbackTestB eeefdeff' with ID 2
INFO:src.routes.entities:Entity verification successful: ID 2 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:httpx:HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'RollbackTestA fefcdebe' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'RollbackTestB eeefdeff' with ID 2
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 2 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
________ TestConcurrentTransactions.test_concurrent_connection_creation ________
tests/test_database_transactions.py:392: in test_concurrent_connection_creation
    assert response.status_code == 201
E   assert 400 == 201
E    +  where 400 = <Response [400 Bad Request]>.status_code
---------------------------- Captured stderr setup -----------------------------
INFO:backend.tests.conftest:Cleaning test database: 1 entities, 0 connections
------------------------------ Captured log setup ------------------------------
INFO     backend.tests.conftest:conftest.py:96 Cleaning test database: 1 entities, 0 connections
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'ConcConnA bbcddabf' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'ConcConnB abdddada' with ID 2
INFO:src.routes.entities:Entity verification successful: ID 2 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:httpx:HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO:src.routes.connections:Validating entities for connection: from_entity_id=1, to_entity_id=2
INFO:src.routes.connections:Validating entities for connection: from_entity_id=1, to_entity_id=2
INFO:src.routes.connections:Validating entities for connection: from_entity_id=1, to_entity_id=2
INFO:src.routes.connections:Entities validated successfully: from_entity=ConcConnA bbcddabf (id=1), to_entity=ConcConnB abdddada (id=2)
INFO:src.routes.connections:Entities validated successfully: from_entity=ConcConnA bbcddabf (id=1), to_entity=ConcConnB abdddada (id=2)
INFO:src.routes.connections:Entities validated successfully: from_entity=ConcConnA bbcddabf (id=1), to_entity=ConcConnB abdddada (id=2)
ERROR:src.routes.connections:IntegrityError creating connection: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "connections_unique"
DETAIL:  Key (from_entity_id, to_entity_id, unit_id)=(1, 2, 4) already exists.
[SQL: INSERT INTO connections (from_entity_id, to_entity_id, unit_id, multiplier, created_at, updated_at) SELECT p0::INTEGER, p1::INTEGER, p2::INTEGER, p3::NUMERIC(10, 1), p4::TIMESTAMP WITHOUT TIME ZONE, p5::TIMESTAMP WITHOUT TIME ZONE FROM (VALUES ($1::I ... 275 characters truncated ...  p2, p3, p4, p5, sen_counter) ORDER BY sen_counter RETURNING connections.id, connections.id AS id__1]
[parameters: (1, 2, 4, Decimal('3.0'), datetime.datetime(2025, 7, 7, 23, 34, 56, 524467), datetime.datetime(2025, 7, 7, 23, 34, 56, 524468), 2, 1, 4, Decimal('0.3'), datetime.datetime(2025, 7, 7, 23, 34, 56, 524468), datetime.datetime(2025, 7, 7, 23, 34, 56, 524468))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
ERROR:src.routes.connections:Connection data: from_entity_id=1, to_entity_id=2, unit_id=4, multiplier=3.0
ERROR:src.routes.connections:IntegrityError creating connection: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "connections_unique"
DETAIL:  Key (from_entity_id, to_entity_id, unit_id)=(1, 2, 4) already exists.
[SQL: INSERT INTO connections (from_entity_id, to_entity_id, unit_id, multiplier, created_at, updated_at) SELECT p0::INTEGER, p1::INTEGER, p2::INTEGER, p3::NUMERIC(10, 1), p4::TIMESTAMP WITHOUT TIME ZONE, p5::TIMESTAMP WITHOUT TIME ZONE FROM (VALUES ($1::I ... 275 characters truncated ...  p2, p3, p4, p5, sen_counter) ORDER BY sen_counter RETURNING connections.id, connections.id AS id__1]
[parameters: (1, 2, 4, Decimal('4.0'), datetime.datetime(2025, 7, 7, 23, 34, 56, 525204), datetime.datetime(2025, 7, 7, 23, 34, 56, 525205), 2, 1, 4, Decimal('0.2'), datetime.datetime(2025, 7, 7, 23, 34, 56, 525206), datetime.datetime(2025, 7, 7, 23, 34, 56, 525206))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
ERROR:src.routes.connections:Connection data: from_entity_id=1, to_entity_id=2, unit_id=4, multiplier=4.0
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 400 Bad Request"
INFO:src.routes.connections:Created connection ID: 1
INFO:src.routes.connections:Primary connection: 1 -> 2, multiplier: 2.0
INFO:src.routes.connections:Inverse connection should exist: 2 -> 1, multiplier: 0.5
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 201 Created"
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'ConcConnA bbcddabf' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'ConcConnB abdddada' with ID 2
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 2 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=1, to_entity_id=2
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=1, to_entity_id=2
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=1, to_entity_id=2
INFO     src.routes.connections:connections.py:54 Entities validated successfully: from_entity=ConcConnA bbcddabf (id=1), to_entity=ConcConnB abdddada (id=2)
INFO     src.routes.connections:connections.py:54 Entities validated successfully: from_entity=ConcConnA bbcddabf (id=1), to_entity=ConcConnB abdddada (id=2)
INFO     src.routes.connections:connections.py:54 Entities validated successfully: from_entity=ConcConnA bbcddabf (id=1), to_entity=ConcConnB abdddada (id=2)
ERROR    src.routes.connections:connections.py:160 IntegrityError creating connection: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "connections_unique"
DETAIL:  Key (from_entity_id, to_entity_id, unit_id)=(1, 2, 4) already exists.
[SQL: INSERT INTO connections (from_entity_id, to_entity_id, unit_id, multiplier, created_at, updated_at) SELECT p0::INTEGER, p1::INTEGER, p2::INTEGER, p3::NUMERIC(10, 1), p4::TIMESTAMP WITHOUT TIME ZONE, p5::TIMESTAMP WITHOUT TIME ZONE FROM (VALUES ($1::I ... 275 characters truncated ...  p2, p3, p4, p5, sen_counter) ORDER BY sen_counter RETURNING connections.id, connections.id AS id__1]
[parameters: (1, 2, 4, Decimal('3.0'), datetime.datetime(2025, 7, 7, 23, 34, 56, 524467), datetime.datetime(2025, 7, 7, 23, 34, 56, 524468), 2, 1, 4, Decimal('0.3'), datetime.datetime(2025, 7, 7, 23, 34, 56, 524468), datetime.datetime(2025, 7, 7, 23, 34, 56, 524468))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
ERROR    src.routes.connections:connections.py:161 Connection data: from_entity_id=1, to_entity_id=2, unit_id=4, multiplier=3.0
ERROR    src.routes.connections:connections.py:160 IntegrityError creating connection: (sqlalchemy.dialects.postgresql.asyncpg.IntegrityError) <class 'asyncpg.exceptions.UniqueViolationError'>: duplicate key value violates unique constraint "connections_unique"
DETAIL:  Key (from_entity_id, to_entity_id, unit_id)=(1, 2, 4) already exists.
[SQL: INSERT INTO connections (from_entity_id, to_entity_id, unit_id, multiplier, created_at, updated_at) SELECT p0::INTEGER, p1::INTEGER, p2::INTEGER, p3::NUMERIC(10, 1), p4::TIMESTAMP WITHOUT TIME ZONE, p5::TIMESTAMP WITHOUT TIME ZONE FROM (VALUES ($1::I ... 275 characters truncated ...  p2, p3, p4, p5, sen_counter) ORDER BY sen_counter RETURNING connections.id, connections.id AS id__1]
[parameters: (1, 2, 4, Decimal('4.0'), datetime.datetime(2025, 7, 7, 23, 34, 56, 525204), datetime.datetime(2025, 7, 7, 23, 34, 56, 525205), 2, 1, 4, Decimal('0.2'), datetime.datetime(2025, 7, 7, 23, 34, 56, 525206), datetime.datetime(2025, 7, 7, 23, 34, 56, 525206))]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
ERROR    src.routes.connections:connections.py:161 Connection data: from_entity_id=1, to_entity_id=2, unit_id=4, multiplier=4.0
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 400 Bad Request"
INFO     src.routes.connections:connections.py:149 Created connection ID: 1
INFO     src.routes.connections:connections.py:150 Primary connection: 1 -> 2, multiplier: 2.0
INFO     src.routes.connections:connections.py:151 Inverse connection should exist: 2 -> 1, multiplier: 0.5
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 201 Created"
________ TestDatabaseConstraints.test_check_constraint_zero_multiplier _________
tests/test_database_transactions.py:548: in test_check_constraint_zero_multiplier
    assert connection_response.status_code == 400
E   assert 422 == 400
E    +  where 422 = <Response [422 Unprocessable Entity]>.status_code
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'ZeroMultA bffadada' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'ZeroMultB cdcaadae' with ID 2
INFO:src.routes.entities:Entity verification successful: ID 2 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:httpx:HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'ZeroMultA bffadada' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'ZeroMultB cdcaadae' with ID 2
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 2 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
___________ TestConnectionCleanup.test_session_cleanup_after_failure ___________
tests/test_database_transactions.py:593: in test_session_cleanup_after_failure
    assert failed_operations == 5
E   assert 0 == 5
---------------------------- Captured stderr setup -----------------------------
INFO:backend.tests.conftest:Cleaning test database: 2 entities, 0 connections
------------------------------ Captured log setup ------------------------------
INFO     backend.tests.conftest:conftest.py:96 Cleaning test database: 2 entities, 0 connections
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'CleanupTest ccedfafd' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:httpx:HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'CleanupTest ccedfafd' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
_____________ TestConnectionCleanup.test_connection_pool_recovery ______________
tests/test_database_transactions.py:650: in test_connection_pool_recovery
    assert failed_count == 10
E   assert 0 == 10
---------------------------- Captured stderr setup -----------------------------
INFO:backend.tests.conftest:Cleaning test database: 1 entities, 0 connections
------------------------------ Captured log setup ------------------------------
INFO     backend.tests.conftest:conftest.py:96 Cleaning test database: 1 entities, 0 connections
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'PoolTestA fafbaebd' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'PoolTestB ccecafbf' with ID 2
INFO:src.routes.entities:Entity verification successful: ID 2 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:httpx:HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'PoolTestA fafbaebd' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'PoolTestB ccecafbf' with ID 2
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 2 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
_ TestAdvancedTransactionScenarios.test_transaction_with_pathfinding_consistency _
tests/test_database_transactions.py:887: in test_transaction_with_pathfinding_consistency
    assert path_response.status_code == 200
E   assert 404 == 200
E    +  where 404 = <Response [404 Not Found]>.status_code
---------------------------- Captured stderr setup -----------------------------
INFO:backend.tests.conftest:Cleaning test database: 4 entities, 8 connections
------------------------------ Captured log setup ------------------------------
INFO     backend.tests.conftest:conftest.py:96 Cleaning test database: 4 entities, 8 connections
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'PathA bfefdadc' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'PathB baefefed' with ID 2
INFO:src.routes.entities:Entity verification successful: ID 2 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'PathC cfbefaad' with ID 3
INFO:src.routes.entities:Entity verification successful: ID 3 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:httpx:HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO:src.routes.connections:Validating entities for connection: from_entity_id=1, to_entity_id=2
INFO:src.routes.connections:Entities validated successfully: from_entity=PathA bfefdadc (id=1), to_entity=PathB baefefed (id=2)
INFO:src.routes.connections:Created connection ID: 1
INFO:src.routes.connections:Primary connection: 1 -> 2, multiplier: 2.0
INFO:src.routes.connections:Inverse connection should exist: 2 -> 1, multiplier: 0.5
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 201 Created"
INFO:src.routes.connections:Validating entities for connection: from_entity_id=2, to_entity_id=3
INFO:src.routes.connections:Entities validated successfully: from_entity=PathB baefefed (id=2), to_entity=PathC cfbefaad (id=3)
INFO:src.routes.connections:Created connection ID: 3
INFO:src.routes.connections:Primary connection: 2 -> 3, multiplier: 3.0
INFO:src.routes.connections:Inverse connection should exist: 3 -> 2, multiplier: 0.3
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 201 Created"
INFO:httpx:HTTP Request: GET http://test/api/v1/compare/1/3?unit_id=4 "HTTP/1.1 404 Not Found"
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'PathA bfefdadc' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'PathB baefefed' with ID 2
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 2 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'PathC cfbefaad' with ID 3
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 3 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=1, to_entity_id=2
INFO     src.routes.connections:connections.py:54 Entities validated successfully: from_entity=PathA bfefdadc (id=1), to_entity=PathB baefefed (id=2)
INFO     src.routes.connections:connections.py:149 Created connection ID: 1
INFO     src.routes.connections:connections.py:150 Primary connection: 1 -> 2, multiplier: 2.0
INFO     src.routes.connections:connections.py:151 Inverse connection should exist: 2 -> 1, multiplier: 0.5
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 201 Created"
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=2, to_entity_id=3
INFO     src.routes.connections:connections.py:54 Entities validated successfully: from_entity=PathB baefefed (id=2), to_entity=PathC cfbefaad (id=3)
INFO     src.routes.connections:connections.py:149 Created connection ID: 3
INFO     src.routes.connections:connections.py:150 Primary connection: 2 -> 3, multiplier: 3.0
INFO     src.routes.connections:connections.py:151 Inverse connection should exist: 3 -> 2, multiplier: 0.3
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 201 Created"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/compare/1/3?unit_id=4 "HTTP/1.1 404 Not Found"
_ TestDatabaseConnectionRecovery.test_transaction_rollback_on_connection_failure _
tests/test_error_recovery_cleanup.py:120: in test_transaction_rollback_on_connection_failure
    connection_response = await test_client.post(
venv/lib/python3.11/site-packages/httpx/_client.py:1877: in post
    return await self.request(
venv/lib/python3.11/site-packages/httpx/_client.py:1559: in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
venv/lib/python3.11/site-packages/httpx/_client.py:1646: in send
    response = await self._send_handling_auth(
venv/lib/python3.11/site-packages/httpx/_client.py:1674: in _send_handling_auth
    response = await self._send_handling_redirects(
venv/lib/python3.11/site-packages/httpx/_client.py:1711: in _send_handling_redirects
    response = await self._send_single_request(request)
venv/lib/python3.11/site-packages/httpx/_client.py:1748: in _send_single_request
    response = await transport.handle_async_request(request)
venv/lib/python3.11/site-packages/httpx/_transports/asgi.py:162: in handle_async_request
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/fastapi/applications.py:1054: in __call__
    await super().__call__(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/applications.py:123: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:186: in __call__
    raise exc
venv/lib/python3.11/site-packages/starlette/middleware/errors.py:164: in __call__
    await self.app(scope, receive, _send)
venv/lib/python3.11/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py:62: in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:762: in __call__
    await self.middleware_stack(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:782: in app
    await route.handle(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:297: in handle
    await self.app(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/routing.py:77: in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:64: in wrapped_app
    raise exc
venv/lib/python3.11/site-packages/starlette/_exception_handler.py:53: in wrapped_app
    await app(scope, receive, sender)
venv/lib/python3.11/site-packages/starlette/routing.py:72: in app
    response = await func(request)
venv/lib/python3.11/site-packages/fastapi/routing.py:299: in app
    raise e
venv/lib/python3.11/site-packages/fastapi/routing.py:294: in app
    raw_response = await run_endpoint_function(
venv/lib/python3.11/site-packages/fastapi/routing.py:191: in run_endpoint_function
    return await dependant.call(**values)
src/routes/connections.py:54: in create_connection
    logger.info(f"Entities validated successfully: from_entity={from_entity.name} (id={from_entity.id}), to_entity={to_entity.name} (id={to_entity.id})")
E   AttributeError: 'coroutine' object has no attribute 'name'
---------------------------- Captured stderr setup -----------------------------
INFO:backend.tests.conftest:Cleaning test database: 2 entities, 0 connections
------------------------------ Captured log setup ------------------------------
INFO     backend.tests.conftest:conftest.py:96 Cleaning test database: 2 entities, 0 connections
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'ConnFailA bafdadbe' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:src.routes.entities:Entity created successfully: 'ConnFailB fbcfbedd' with ID 2
INFO:src.routes.entities:Entity verification successful: ID 2 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:httpx:HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO:src.routes.connections:Validating entities for connection: from_entity_id=1, to_entity_id=2
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'ConnFailA bafdadbe' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'ConnFailB fbcfbedd' with ID 2
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 2 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO     src.routes.connections:connections.py:30 Validating entities for connection: from_entity_id=1, to_entity_id=2
_ TestDatabaseConnectionRecovery.test_session_cleanup_after_multiple_failures __
tests/test_error_recovery_cleanup.py:177: in test_session_cleanup_after_multiple_failures
    assert failure_count == 10, "All operations should have failed"
E   AssertionError: All operations should have failed
E   assert 0 == 10
---------------------------- Captured stderr setup -----------------------------
INFO:backend.tests.conftest:Cleaning test database: 2 entities, 0 connections
------------------------------ Captured log setup ------------------------------
INFO     backend.tests.conftest:conftest.py:96 Cleaning test database: 2 entities, 0 connections
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'MultiFailure ddacbafa' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:httpx:HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'MultiFailure ddacbafa' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/connections/ "HTTP/1.1 422 Unprocessable Entity"
__________ TestResourceLeakPrevention.test_connection_leak_prevention __________
tests/test_error_recovery_cleanup.py:208: in test_connection_leak_prevention
    initial_pool_size = engine.pool.size()
E   AttributeError: 'NullPool' object has no attribute 'size'
---------------------------- Captured stderr setup -----------------------------
INFO:backend.tests.conftest:Cleaning test database: 1 entities, 0 connections
------------------------------ Captured log setup ------------------------------
INFO     backend.tests.conftest:conftest.py:96 Cleaning test database: 1 entities, 0 connections
_ TestSystemRecoveryScenarios.test_recovery_from_partial_transaction_failures __
tests/test_error_recovery_cleanup.py:437: in test_recovery_from_partial_transaction_failures
    assert response.status_code == 201
E   assert 422 == 201
E    +  where 422 = <Response [422 Unprocessable Entity]>.status_code
---------------------------- Captured stderr setup -----------------------------
INFO:backend.tests.conftest:Cleaning test database: 2 entities, 2 connections
------------------------------ Captured log setup ------------------------------
INFO     backend.tests.conftest:conftest.py:96 Cleaning test database: 2 entities, 2 connections
----------------------------- Captured stderr call -----------------------------
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
_ TestSystemRecoveryScenarios.test_database_consistency_after_mixed_operations _
tests/test_error_recovery_cleanup.py:522: in test_database_consistency_after_mixed_operations
    assert response.status_code == 201
E   assert 422 == 201
E    +  where 422 = <Response [422 Unprocessable Entity]>.status_code
----------------------------- Captured stderr call -----------------------------
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
_______ TestSystemRecoveryScenarios.test_long_running_operation_recovery _______
tests/test_error_recovery_cleanup.py:619: in test_long_running_operation_recovery
    assert response.status_code == 201
E   assert 422 == 201
E    +  where 422 = <Response [422 Unprocessable Entity]>.status_code
----------------------------- Captured stderr call -----------------------------
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
____________ TestEntityPerformance.test_entity_creation_performance ____________
tests/test_performance_benchmarks.py:230: in test_entity_creation_performance
    assert metrics.success_rate > 0.95, f"Success rate too low: {metrics.success_rate}"
E   AssertionError: Success rate too low: 0.01
E   assert 0.01 > 0.95
E    +  where 0.01 = PerformanceMetrics(response_times=[41.14890098571777, 20.906925201416016, 20.025253295898438, 20.162105560302734, 20.5...99999999994, -48.60000000000001, -8.099999999999994, -9.299999999999997, -27.5], database_queries=0, success_rate=0.01).success_rate
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'PerfTest fbbdafca' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'PerfTest fbbdafca' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
____________ TestEntityPerformance.test_entity_listing_performance _____________
tests/test_performance_benchmarks.py:241: in test_entity_listing_performance
    await self.create_performance_test_data(test_client, entity_count=100)
tests/test_performance_benchmarks.py:187: in create_performance_test_data
    from_entity = random.choice(entities)
/opt/homebrew/Cellar/python@3.11/3.11.13/Frameworks/Python.framework/Versions/3.11/lib/python3.11/random.py:373: in choice
    raise IndexError('Cannot choose from an empty sequence')
E   IndexError: Cannot choose from an empty sequence
---------------------------- Captured stderr setup -----------------------------
INFO:backend.tests.conftest:Cleaning test database: 1 entities, 0 connections
------------------------------ Captured log setup ------------------------------
INFO     backend.tests.conftest:conftest.py:96 Cleaning test database: 1 entities, 0 connections
----------------------------- Captured stderr call -----------------------------
INFO:httpx:HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
___________ TestEntityPerformance.test_entity_retrieval_performance ____________
tests/test_performance_benchmarks.py:267: in test_entity_retrieval_performance
    test_data = await self.create_performance_test_data(test_client, entity_count=50)
tests/test_performance_benchmarks.py:187: in create_performance_test_data
    from_entity = random.choice(entities)
/opt/homebrew/Cellar/python@3.11/3.11.13/Frameworks/Python.framework/Versions/3.11/lib/python3.11/random.py:373: in choice
    raise IndexError('Cannot choose from an empty sequence')
E   IndexError: Cannot choose from an empty sequence
----------------------------- Captured stderr call -----------------------------
INFO:httpx:HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
________ TestConnectionPerformance.test_connection_creation_performance ________
tests/test_performance_benchmarks.py:293: in test_connection_creation_performance
    test_data = await self.create_performance_test_data(test_client, entity_count=20)
tests/test_performance_benchmarks.py:187: in create_performance_test_data
    from_entity = random.choice(entities)
/opt/homebrew/Cellar/python@3.11/3.11.13/Frameworks/Python.framework/Versions/3.11/lib/python3.11/random.py:373: in choice
    raise IndexError('Cannot choose from an empty sequence')
E   IndexError: Cannot choose from an empty sequence
----------------------------- Captured stderr call -----------------------------
INFO:httpx:HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
________ TestConnectionPerformance.test_connection_listing_performance _________
tests/test_performance_benchmarks.py:333: in test_connection_listing_performance
    await self.create_performance_test_data(test_client, entity_count=50, connection_density=0.5)
tests/test_performance_benchmarks.py:187: in create_performance_test_data
    from_entity = random.choice(entities)
/opt/homebrew/Cellar/python@3.11/3.11.13/Frameworks/Python.framework/Versions/3.11/lib/python3.11/random.py:373: in choice
    raise IndexError('Cannot choose from an empty sequence')
E   IndexError: Cannot choose from an empty sequence
----------------------------- Captured stderr call -----------------------------
INFO:httpx:HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
_ TestPathfindingPerformance.test_pathfinding_performance_various_graph_sizes __
tests/test_performance_benchmarks.py:368: in test_pathfinding_performance_various_graph_sizes
    test_data = await self.create_performance_test_data(
tests/test_performance_benchmarks.py:187: in create_performance_test_data
    from_entity = random.choice(entities)
/opt/homebrew/Cellar/python@3.11/3.11.13/Frameworks/Python.framework/Versions/3.11/lib/python3.11/random.py:373: in choice
    raise IndexError('Cannot choose from an empty sequence')
E   IndexError: Cannot choose from an empty sequence
----------------------------- Captured stderr call -----------------------------
INFO:backend.tests.test_performance_benchmarks:Testing pathfinding performance with 10 entities
INFO:httpx:HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     backend.tests.test_performance_benchmarks:test_performance_benchmarks.py:365 Testing pathfinding performance with 10 entities
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
________ TestPathfindingPerformance.test_pathfinding_depth_performance _________
tests/test_performance_benchmarks.py:432: in test_pathfinding_depth_performance
    "from_entity_id": entities[i]["id"],
E   KeyError: 'id'
----------------------------- Captured stderr call -----------------------------
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
------------------------------ Captured log call -------------------------------
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
___________ TestDatabasePerformance.test_database_query_performance ____________
tests/test_performance_benchmarks.py:468: in test_database_query_performance
    await self.create_performance_test_data(test_client, entity_count=100, connection_density=0.4)
tests/test_performance_benchmarks.py:187: in create_performance_test_data
    from_entity = random.choice(entities)
/opt/homebrew/Cellar/python@3.11/3.11.13/Frameworks/Python.framework/Versions/3.11/lib/python3.11/random.py:373: in choice
    raise IndexError('Cannot choose from an empty sequence')
E   IndexError: Cannot choose from an empty sequence
----------------------------- Captured stderr call -----------------------------
INFO:httpx:HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
____________ TestDatabasePerformance.test_recursive_cte_performance ____________
tests/test_performance_benchmarks.py:511: in test_recursive_cte_performance
    await self.create_performance_test_data(test_client, entity_count=50, connection_density=0.5)
tests/test_performance_benchmarks.py:187: in create_performance_test_data
    from_entity = random.choice(entities)
/opt/homebrew/Cellar/python@3.11/3.11.13/Frameworks/Python.framework/Versions/3.11/lib/python3.11/random.py:373: in choice
    raise IndexError('Cannot choose from an empty sequence')
E   IndexError: Cannot choose from an empty sequence
----------------------------- Captured stderr call -----------------------------
INFO:httpx:HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
_______ TestConcurrentPerformance.test_concurrent_pathfinding_operations _______
tests/test_performance_benchmarks.py:597: in test_concurrent_pathfinding_operations
    test_data = await self.create_performance_test_data(test_client, entity_count=30, connection_density=0.4)
tests/test_performance_benchmarks.py:187: in create_performance_test_data
    from_entity = random.choice(entities)
/opt/homebrew/Cellar/python@3.11/3.11.13/Frameworks/Python.framework/Versions/3.11/lib/python3.11/random.py:373: in choice
    raise IndexError('Cannot choose from an empty sequence')
E   IndexError: Cannot choose from an empty sequence
----------------------------- Captured stderr call -----------------------------
INFO:httpx:HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
______________ TestResourceEfficiency.test_memory_usage_patterns _______________
tests/test_performance_benchmarks.py:651: in test_memory_usage_patterns
    await self.create_performance_test_data(test_client, entity_count=20, connection_density=0.3)
tests/test_performance_benchmarks.py:187: in create_performance_test_data
    from_entity = random.choice(entities)
/opt/homebrew/Cellar/python@3.11/3.11.13/Frameworks/Python.framework/Versions/3.11/lib/python3.11/random.py:373: in choice
    raise IndexError('Cannot choose from an empty sequence')
E   IndexError: Cannot choose from an empty sequence
----------------------------- Captured stderr call -----------------------------
INFO:httpx:HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
____________ TestResourceEfficiency.test_response_time_consistency _____________
tests/test_performance_benchmarks.py:677: in test_response_time_consistency
    test_data = await self.create_performance_test_data(test_client, entity_count=50, connection_density=0.3)
tests/test_performance_benchmarks.py:187: in create_performance_test_data
    from_entity = random.choice(entities)
/opt/homebrew/Cellar/python@3.11/3.11.13/Frameworks/Python.framework/Versions/3.11/lib/python3.11/random.py:373: in choice
    raise IndexError('Cannot choose from an empty sequence')
E   IndexError: Cannot choose from an empty sequence
----------------------------- Captured stderr call -----------------------------
INFO:httpx:HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
____________ TestPerformanceRegression.test_benchmark_api_endpoints ____________
tests/test_performance_benchmarks.py:722: in test_benchmark_api_endpoints
    test_data = await self.create_performance_test_data(test_client, entity_count=30, connection_density=0.3)
E   AttributeError: 'TestPerformanceRegression' object has no attribute 'create_performance_test_data'
____________ TestEntityPerformance.test_entity_creation_performance ____________
tests/test_performance_benchmarks.py:230: in test_entity_creation_performance
    assert metrics.success_rate > 0.95, f"Success rate too low: {metrics.success_rate}"
E   AssertionError: Success rate too low: 0.01
E   assert 0.01 > 0.95
E    +  where 0.01 = PerformanceMetrics(response_times=[48.24686050415039, 23.97894859313965, 22.67599105834961, 23.151159286499023, 21.451...999999994, -12.900000000000006, -9.0, -38.099999999999994, -13.400000000000006], database_queries=0, success_rate=0.01).success_rate
----------------------------- Captured stderr call -----------------------------
INFO:src.routes.entities:Entity created successfully: 'PerfTest aeabccfb' with ID 1
INFO:src.routes.entities:Entity verification successful: ID 1 is immediately queryable
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
------------------------------ Captured log call -------------------------------
INFO     src.routes.entities:entities.py:31 Entity created successfully: 'PerfTest aeabccfb' with ID 1
INFO     src.routes.entities:entities.py:37 Entity verification successful: ID 1 is immediately queryable
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 201 Created"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 400 Bad Request"
____________ TestEntityPerformance.test_entity_listing_performance _____________
tests/test_performance_benchmarks.py:241: in test_entity_listing_performance
    await self.create_performance_test_data(test_client, entity_count=100)
tests/test_performance_benchmarks.py:187: in create_performance_test_data
    from_entity = random.choice(entities)
/opt/homebrew/Cellar/python@3.11/3.11.13/Frameworks/Python.framework/Versions/3.11/lib/python3.11/random.py:373: in choice
    raise IndexError('Cannot choose from an empty sequence')
E   IndexError: Cannot choose from an empty sequence
---------------------------- Captured stderr setup -----------------------------
INFO:backend.tests.conftest:Cleaning test database: 1 entities, 0 connections
------------------------------ Captured log setup ------------------------------
INFO     backend.tests.conftest:conftest.py:96 Cleaning test database: 1 entities, 0 connections
----------------------------- Captured stderr call -----------------------------
INFO:httpx:HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
___________ TestEntityPerformance.test_entity_retrieval_performance ____________
tests/test_performance_benchmarks.py:267: in test_entity_retrieval_performance
    test_data = await self.create_performance_test_data(test_client, entity_count=50)
tests/test_performance_benchmarks.py:187: in create_performance_test_data
    from_entity = random.choice(entities)
/opt/homebrew/Cellar/python@3.11/3.11.13/Frameworks/Python.framework/Versions/3.11/lib/python3.11/random.py:373: in choice
    raise IndexError('Cannot choose from an empty sequence')
E   IndexError: Cannot choose from an empty sequence
----------------------------- Captured stderr call -----------------------------
INFO:httpx:HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
____ TestPerformanceBenchmarkIntegration.test_create_performance_test_data _____
tests/test_performance_integration.py:62: in test_create_performance_test_data
    test_data = await self.create_performance_test_data(
tests/test_performance_benchmarks.py:187: in create_performance_test_data
    from_entity = random.choice(entities)
/opt/homebrew/Cellar/python@3.11/3.11.13/Frameworks/Python.framework/Versions/3.11/lib/python3.11/random.py:373: in choice
    raise IndexError('Cannot choose from an empty sequence')
E   IndexError: Cannot choose from an empty sequence
----------------------------- Captured stderr call -----------------------------
INFO:httpx:HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
_ TestPerformanceBenchmarkIntegration.test_performance_test_class_instantiation _
tests/test_performance_integration.py:108: in test_performance_test_class_instantiation
    assert len(test_data["entities"]) == 3
E   assert 0 == 3
E    +  where 0 = len([])
----------------------------- Captured stderr call -----------------------------
INFO:httpx:HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO:httpx:HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
------------------------------ Captured log call -------------------------------
INFO     httpx:_client.py:1758 HTTP Request: GET http://test/api/v1/units/ "HTTP/1.1 200 OK"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
INFO     httpx:_client.py:1758 HTTP Request: POST http://test/api/v1/entities/ "HTTP/1.1 422 Unprocessable Entity"
=============================== warnings summary ===============================
tests/test_api_contract_validation.py::TestContentTypeAndHeaders::test_request_content_type_validation
tests/test_api_error_handling.py::TestMalformedRequestHandling::test_wrong_content_type
tests/test_entities_comprehensive_coverage.py::TestEntitiesComprehensiveCoverage::test_create_entity_malformed_request
tests/test_units_comprehensive_coverage.py::TestUnitsComprehensiveCoverage::test_create_unit_malformed_request
  /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/httpx/_content.py:204: DeprecationWarning: Use 'content=<...>' to upload raw bytes/text content.
    warnings.warn(message, DeprecationWarning)

tests/test_connections_critical_coverage.py::TestConnectionsUpdateAndDelete::test_delete_connection_no_inverse
  sys:1: SAWarning: Session's state has been changed on a non-active transaction - this state will be discarded.

tests/test_error_recovery_cleanup.py::TestResourceLeakPrevention::test_memory_leak_prevention_in_error_scenarios
  /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/backend/venv/lib/python3.11/site-packages/sqlalchemy/util/topological.py:84: RuntimeWarning: coroutine 'AsyncMockMixin._execute_mock_call' was never awaited
    for parent, child in tuples:
  Enable tracemalloc to get traceback where the object was allocated.
  See https://docs.pytest.org/en/stable/how-to/capture-warnings.html#resource-warnings for more info.

tests/test_error_recovery_cleanup.py::TestResourceLeakPrevention::test_memory_leak_prevention_in_error_scenarios
  /opt/homebrew/Cellar/python@3.11/3.11.13/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/events.py:84: RuntimeWarning: coroutine 'AsyncClient.post' was never awaited
    self._context.run(self._callback, *self._args)
  Enable tracemalloc to get traceback where the object was allocated.
  See https://docs.pytest.org/en/stable/how-to/capture-warnings.html#resource-warnings for more info.

tests/test_performance_integration.py::TestPerformanceBenchmarkConfiguration::test_performance_test_markers
  tests/test_performance_integration.py:178: PytestWarning: The test <Function test_performance_test_markers> is marked with '@pytest.mark.asyncio' but it is not an async function. Please remove the asyncio mark. If the test is not marked explicitly, check for global marks applied via 'pytestmark'.
    def test_performance_test_markers(self):

tests/test_performance_integration.py::TestPerformanceBenchmarkConfiguration::test_performance_dependencies
  tests/test_performance_integration.py:184: PytestWarning: The test <Function test_performance_dependencies> is marked with '@pytest.mark.asyncio' but it is not an async function. Please remove the asyncio mark. If the test is not marked explicitly, check for global marks applied via 'pytestmark'.
    def test_performance_dependencies(self):

tests/test_performance_integration.py::TestPerformanceBenchmarkConfiguration::test_performance_test_file_structure
  tests/test_performance_integration.py:207: PytestWarning: The test <Function test_performance_test_file_structure> is marked with '@pytest.mark.asyncio' but it is not an async function. Please remove the asyncio mark. If the test is not marked explicitly, check for global marks applied via 'pytestmark'.
    def test_performance_test_file_structure(self):

-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html

--------- coverage: platform darwin, python 3.11.13-final-0 ----------
Name                        Stmts   Miss  Cover   Missing
---------------------------------------------------------
src/__init__.py                 0      0   100%
src/app_factory.py             14      0   100%
src/config.py                  17      1    94%   30
src/database.py                21      0   100%
src/main.py                    13      1    92%   37
src/models.py                  27      0   100%
src/routes/__init__.py          0      0   100%
src/routes/compare.py          30     18    40%   29-74
src/routes/connections.py     147     97    34%   38-39, 41, 49-50, 52, 57-188, 210-218, 230-233, 252-300, 314, 331, 334
src/routes/entities.py         68     35    49%   28-41, 43-44, 59, 66-67, 77-80, 91-105, 118-124
src/routes/units.py            34     11    68%   21-22, 35-39, 56-59
src/schemas.py                107      4    96%   92, 130, 133, 138
src/services.py                39      0   100%
---------------------------------------------------------
TOTAL                         517    167    68%

=========================== short test summary info ============================
FAILED tests/test_compare_error_scenarios.py::TestCompareRoutePerformanceEdgeCases::test_compare_with_max_path_length
FAILED tests/test_compare_error_scenarios.py::TestCompareRoutePerformanceEdgeCases::test_compare_with_path_exceeding_max_length
FAILED tests/test_complex_graph_scenarios.py::TestStarTopology::test_star_hub_to_spoke_paths
FAILED tests/test_complex_graph_scenarios.py::TestStarTopology::test_star_spoke_to_spoke_paths
FAILED tests/test_complex_graph_scenarios.py::TestStarTopology::test_star_isolated_spokes
FAILED tests/test_complex_graph_scenarios.py::TestChainTopology::test_chain_end_to_end_path
FAILED tests/test_complex_graph_scenarios.py::TestChainTopology::test_chain_intermediate_paths
FAILED tests/test_complex_graph_scenarios.py::TestChainTopology::test_chain_max_length_limit
FAILED tests/test_complex_graph_scenarios.py::TestChainTopology::test_chain_reverse_path
FAILED tests/test_complex_graph_scenarios.py::TestGridTopology::test_grid_corner_to_corner_paths
FAILED tests/test_complex_graph_scenarios.py::TestGridTopology::test_grid_adjacent_paths
FAILED tests/test_complex_graph_scenarios.py::TestGridTopology::test_grid_multiple_paths
FAILED tests/test_complex_graph_scenarios.py::TestCompleteGraphTopology::test_complete_graph_direct_connections
FAILED tests/test_complex_graph_scenarios.py::TestCompleteGraphTopology::test_complete_graph_optimal_paths
FAILED tests/test_complex_graph_scenarios.py::TestDisconnectedGraphTopology::test_disconnected_components_isolated
FAILED tests/test_complex_graph_scenarios.py::TestDisconnectedGraphTopology::test_disconnected_within_component_paths
FAILED tests/test_complex_graph_scenarios.py::TestDisconnectedGraphTopology::test_disconnected_all_component_pairs
FAILED tests/test_complex_graph_scenarios.py::TestCyclicGraphTopology::test_cyclic_graph_cycle_detection
FAILED tests/test_complex_graph_scenarios.py::TestCyclicGraphTopology::test_cyclic_graph_shortest_path
FAILED tests/test_complex_graph_scenarios.py::TestCyclicGraphTopology::test_cyclic_graph_no_infinite_loops
FAILED tests/test_complex_graph_scenarios.py::TestDeepTreeTopology::test_deep_tree_within_limit
FAILED tests/test_complex_graph_scenarios.py::TestDeepTreeTopology::test_deep_tree_at_limit
FAILED tests/test_complex_graph_scenarios.py::TestDeepTreeTopology::test_deep_tree_beyond_limit
FAILED tests/test_complex_graph_scenarios.py::TestDeepTreeTopology::test_deep_tree_sibling_paths
FAILED tests/test_complex_graph_scenarios.py::TestMultiUnitGraphTopology::test_multi_unit_same_unit_paths
FAILED tests/test_complex_graph_scenarios.py::TestMultiUnitGraphTopology::test_multi_unit_different_unit_isolation
FAILED tests/test_complex_graph_scenarios.py::TestMultiUnitGraphTopology::test_multi_unit_cross_unit_paths
FAILED tests/test_complex_graph_scenarios.py::TestLargeScaleGraphTopology::test_large_scale_graph_creation
FAILED tests/test_complex_graph_scenarios.py::TestLargeScaleGraphTopology::test_large_scale_random_paths
FAILED tests/test_complex_graph_scenarios.py::TestLargeScaleGraphTopology::test_large_scale_performance_timing
FAILED tests/test_complex_graph_scenarios.py::TestEdgeCaseGraphTopology::test_edge_case_isolated_entity
FAILED tests/test_complex_graph_scenarios.py::TestEdgeCaseGraphTopology::test_edge_case_decimal_precision
FAILED tests/test_complex_graph_scenarios.py::TestEdgeCaseGraphTopology::test_edge_case_extreme_multipliers
FAILED tests/test_complex_graph_scenarios.py::TestComplexGraphInteractions::test_multiple_graph_types_comparison
FAILED tests/test_complex_graph_scenarios.py::TestComplexGraphInteractions::test_graph_stress_multiple_queries
FAILED tests/test_complex_graph_scenarios.py::TestComplexGraphInteractions::test_graph_robustness_invalid_queries
FAILED tests/test_concurrent_operations.py::TestConcurrentEntityOperations::test_concurrent_entity_updates
FAILED tests/test_concurrent_operations.py::TestConcurrentConnectionOperations::test_concurrent_connection_creation_same_entities
FAILED tests/test_concurrent_operations.py::TestConcurrentConnectionOperations::test_concurrent_connection_inverse_consistency
FAILED tests/test_concurrent_operations.py::TestConcurrentComplexOperations::test_concurrent_graph_creation
FAILED tests/test_concurrent_operations.py::TestConcurrentComplexOperations::test_concurrent_pathfinding_operations
FAILED tests/test_concurrent_operations.py::TestTransactionIsolationLevels::test_read_committed_isolation
FAILED tests/test_concurrent_operations.py::TestTransactionIsolationLevels::test_phantom_read_prevention
FAILED tests/test_connections_critical_coverage.py::TestConnectionsErrorPaths::test_create_connection_database_failure
FAILED tests/test_connections_critical_coverage.py::TestConnectionsErrorPaths::test_create_connection_integrity_error_foreign_key
FAILED tests/test_connections_critical_coverage.py::TestConnectionsErrorPaths::test_create_connection_integrity_error_self_reference
FAILED tests/test_connections_critical_coverage.py::TestConnectionsErrorPaths::test_create_connection_integrity_error_positive_multiplier
FAILED tests/test_connections_critical_coverage.py::TestConnectionsErrorPaths::test_create_connection_integrity_error_generic
FAILED tests/test_connections_critical_coverage.py::TestConnectionsUpdateAndDelete::test_update_connection_no_inverse_found
FAILED tests/test_connections_critical_coverage.py::TestConnectionsUpdateAndDelete::test_delete_connection_no_inverse
FAILED tests/test_connections_critical_coverage.py::TestConnectionsDecimalPrecision::test_create_connection_extreme_small_multiplier
FAILED tests/test_connections_critical_coverage.py::TestConnectionsDecimalPrecision::test_create_connection_bankers_rounding
FAILED tests/test_connections_critical_coverage.py::TestConnectionsIntegrationScenarios::test_complex_connection_network_creation
FAILED tests/test_database_transactions.py::TestTransactionIntegrity::test_connection_self_reference_constraint_violation
FAILED tests/test_database_transactions.py::TestTransactionIntegrity::test_multiple_entity_creation_atomic
FAILED tests/test_database_transactions.py::TestRollbackScenarios::test_connection_negative_multiplier_rollback
FAILED tests/test_database_transactions.py::TestConcurrentTransactions::test_concurrent_connection_creation
FAILED tests/test_database_transactions.py::TestDatabaseConstraints::test_check_constraint_zero_multiplier
FAILED tests/test_database_transactions.py::TestConnectionCleanup::test_session_cleanup_after_failure
FAILED tests/test_database_transactions.py::TestConnectionCleanup::test_connection_pool_recovery
FAILED tests/test_database_transactions.py::TestAdvancedTransactionScenarios::test_transaction_with_pathfinding_consistency
FAILED tests/test_error_recovery_cleanup.py::TestDatabaseConnectionRecovery::test_transaction_rollback_on_connection_failure
FAILED tests/test_error_recovery_cleanup.py::TestDatabaseConnectionRecovery::test_session_cleanup_after_multiple_failures
FAILED tests/test_error_recovery_cleanup.py::TestResourceLeakPrevention::test_connection_leak_prevention
FAILED tests/test_error_recovery_cleanup.py::TestSystemRecoveryScenarios::test_recovery_from_partial_transaction_failures
FAILED tests/test_error_recovery_cleanup.py::TestSystemRecoveryScenarios::test_database_consistency_after_mixed_operations
FAILED tests/test_error_recovery_cleanup.py::TestSystemRecoveryScenarios::test_long_running_operation_recovery
FAILED tests/test_performance_benchmarks.py::TestEntityPerformance::test_entity_creation_performance
FAILED tests/test_performance_benchmarks.py::TestEntityPerformance::test_entity_listing_performance
FAILED tests/test_performance_benchmarks.py::TestEntityPerformance::test_entity_retrieval_performance
FAILED tests/test_performance_benchmarks.py::TestConnectionPerformance::test_connection_creation_performance
FAILED tests/test_performance_benchmarks.py::TestConnectionPerformance::test_connection_listing_performance
FAILED tests/test_performance_benchmarks.py::TestPathfindingPerformance::test_pathfinding_performance_various_graph_sizes
FAILED tests/test_performance_benchmarks.py::TestPathfindingPerformance::test_pathfinding_depth_performance
FAILED tests/test_performance_benchmarks.py::TestDatabasePerformance::test_database_query_performance
FAILED tests/test_performance_benchmarks.py::TestDatabasePerformance::test_recursive_cte_performance
FAILED tests/test_performance_benchmarks.py::TestConcurrentPerformance::test_concurrent_pathfinding_operations
FAILED tests/test_performance_benchmarks.py::TestResourceEfficiency::test_memory_usage_patterns
FAILED tests/test_performance_benchmarks.py::TestResourceEfficiency::test_response_time_consistency
FAILED tests/test_performance_benchmarks.py::TestPerformanceRegression::test_benchmark_api_endpoints
FAILED tests/test_performance_integration.py::TestEntityPerformance::test_entity_creation_performance
FAILED tests/test_performance_integration.py::TestEntityPerformance::test_entity_listing_performance
FAILED tests/test_performance_integration.py::TestEntityPerformance::test_entity_retrieval_performance
FAILED tests/test_performance_integration.py::TestPerformanceBenchmarkIntegration::test_create_performance_test_data
FAILED tests/test_performance_integration.py::TestPerformanceBenchmarkIntegration::test_performance_test_class_instantiation
============ 85 failed, 370 passed, 10 warnings in 91.44s (0:01:31) ============
