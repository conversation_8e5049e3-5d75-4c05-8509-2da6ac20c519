export const testEntities = [
  { name: 'Human', unit: 'length' },
  { name: 'Basketball', unit: 'length' },
  { name: 'Building', unit: 'length' },
  { name: 'Car', unit: 'mass' },
  { name: 'Elephant', unit: 'mass' },
];

export const testConnections = [
  { fromEntity: 'Human', toEntity: 'Basketball', multiplier: '10.0' },
  { fromEntity: 'Basketball', toEntity: 'Building', multiplier: '50.0' },
  { fromEntity: 'Car', toEntity: 'Elephant', multiplier: '0.3' },
];

export const invalidEntityNames = [
  '', // Empty string
  'A'.repeat(21), // Too long (max 20 chars)
  'Test123!', // Invalid characters (only letters and spaces allowed)
  '   ', // Only whitespace
];

export const validEntityNames = [
  'Test Entity',
  'Human Being',
  'Small Object',
  'Large Building',
];

export const invalidConnections = [
  { fromEntity: 'Human', toEntity: 'Car', multiplier: '2.0' }, // Different units
  { fromEntity: 'Human', toEntity: 'Basketball', multiplier: '-5.0' }, // Negative
  { fromEntity: 'Human', toEntity: 'Basketball', multiplier: '2.55' }, // Too many decimals
  { fromEntity: 'Human', toEntity: 'Basketball', multiplier: '0' }, // Zero value
];

export const validConnections = [
  { fromEntity: 'Human', toEntity: 'Basketball', multiplier: '2.5' },
  { fromEntity: 'Basketball', toEntity: 'Building', multiplier: '10.0' },
  { fromEntity: 'Human', toEntity: 'Building', multiplier: '0.1' },
];

// Test data for cleanup - entities that should be deleted after tests
export const temporaryEntities = [
  'Test Entity Temp',
  'Delete Me',
  'Temp Object',
  'Test Connection A',
  'Test Connection B',
];

// Units available in the system
export const availableUnits = [
  'length',
  'mass', 
  'volume',
  'time',
  'count',
];