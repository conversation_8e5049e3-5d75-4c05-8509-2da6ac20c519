
> simile-frontend@0.1.0 test:e2e
> playwright test --reporter=json

{
  "config": {
    "configFile": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/playwright.config.ts",
    "rootDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests",
    "forbidOnly": false,
    "fullyParallel": false,
    "globalSetup": null,
    "globalTeardown": null,
    "globalTimeout": 7200000,
    "grep": {},
    "grepInvert": null,
    "maxFailures": 5,
    "metadata": {
      "actualWorkers": 1
    },
    "preserveOutput": "always",
    "reporter": [
      [
        "json"
      ]
    ],
    "reportSlowTests": {
      "max": 5,
      "threshold": 300000
    },
    "quiet": false,
    "projects": [
      {
        "outputDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results",
        "repeatEach": 1,
        "retries": 0,
        "metadata": {
          "actualWorkers": 1
        },
        "id": "chromium",
        "name": "chromium",
        "testDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests",
        "testIgnore": [],
        "testMatch": [
          "**/*.@(spec|test).?(c|m)[jt]s?(x)"
        ],
        "timeout": 120000
      },
      {
        "outputDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results",
        "repeatEach": 1,
        "retries": 0,
        "metadata": {
          "actualWorkers": 1
        },
        "id": "firefox",
        "name": "firefox",
        "testDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests",
        "testIgnore": [],
        "testMatch": [
          "**/*.@(spec|test).?(c|m)[jt]s?(x)"
        ],
        "timeout": 120000
      },
      {
        "outputDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results",
        "repeatEach": 1,
        "retries": 0,
        "metadata": {
          "actualWorkers": 1
        },
        "id": "webkit",
        "name": "webkit",
        "testDir": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests",
        "testIgnore": [],
        "testMatch": [
          "**/*.@(spec|test).?(c|m)[jt]s?(x)"
        ],
        "timeout": 120000
      }
    ],
    "shard": null,
    "updateSnapshots": "missing",
    "updateSourceMethod": "patch",
    "version": "1.53.1",
    "workers": 1,
    "webServer": {
      "command": "npm start",
      "url": "http://localhost:3000",
      "reuseExistingServer": true,
      "timeout": 120000
    }
  },
  "suites": [
    {
      "title": "comparisons.spec.ts",
      "file": "comparisons.spec.ts",
      "column": 0,
      "line": 0,
      "specs": [],
      "suites": [
        {
          "title": "Entity Comparisons and Pathfinding",
          "file": "comparisons.spec.ts",
          "line": 5,
          "column": 6,
          "specs": [
            {
              "title": "should display comparison page correctly",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 0,
                      "parallelIndex": 0,
                      "status": "failed",
                      "duration": 29959,
                      "error": {
                        "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Build GBNZC\")').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Build GBNZC\")').first()\u001b[22m\n",
                        "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Build GBNZC\")').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Build GBNZC\")').first()\u001b[22m\n\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:98:67",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts",
                          "column": 67,
                          "line": 98
                        },
                        "snippet": "   96 |         // Verify connection was created - check for both entity names in the connection list\n   97 |         await expect(page.locator(`:text(\"${conn.from}\")`).first()).toBeVisible({ timeout: 10000 });\n>  98 |         await expect(page.locator(`:text(\"${conn.to}\")`).first()).toBeVisible({ timeout: 10000 });\n      |                                                                   ^\n   99 |       } catch (error: any) {\n  100 |         // If connection creation fails, check if it already exists\n  101 |         console.log(`Connection creation failed for ${conn.from} → ${conn.to}, checking if it exists...`);"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts",
                            "column": 67,
                            "line": 98
                          },
                          "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Build GBNZC\")').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Build GBNZC\")').first()\u001b[22m\n\n\n   96 |         // Verify connection was created - check for both entity names in the connection list\n   97 |         await expect(page.locator(`:text(\"${conn.from}\")`).first()).toBeVisible({ timeout: 10000 });\n>  98 |         await expect(page.locator(`:text(\"${conn.to}\")`).first()).toBeVisible({ timeout: 10000 });\n      |                                                                   ^\n   99 |       } catch (error: any) {\n  100 |         // If connection creation fails, check if it already exists\n  101 |         console.log(`Connection creation failed for ${conn.from} → ${conn.to}, checking if it exists...`);\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:98:67"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 133 total entities in database\n"
                        },
                        {
                          "text": "  Identified 3 test entities to delete\n"
                        },
                        {
                          "text": "  ✓ Deleted test entity: Rapid A CCBHFBAEJCC (ID: 3057)\n"
                        },
                        {
                          "text": "  ✓ Deleted test entity: Rapid B CCBHFBAEJCC (ID: 3058)\n"
                        },
                        {
                          "text": "  ✓ Deleted test entity: Rapid C CCBHFBAEJCC (ID: 3059)\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 37ms:\n"
                        },
                        {
                          "text": "  • Initial entities: 133\n"
                        },
                        {
                          "text": "  • Entities deleted: 3\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 130\n"
                        },
                        {
                          "text": "  • Net reduction: 3\n"
                        },
                        {
                          "text": "Creating test entities for comparison tests...\n"
                        },
                        {
                          "text": "Creating entity: Human GBNZA\n"
                        },
                        {
                          "text": "Setting up API monitoring for entity creation...\n"
                        },
                        {
                          "text": "Submitting entity creation form...\n"
                        },
                        {
                          "text": "Waiting for API response...\n"
                        },
                        {
                          "text": "Entity API Response: http://localhost:8000/api/v1/entities/ - Status: 201\n"
                        },
                        {
                          "text": "Request Body: {\"name\":\"Human GBNZA\"}\n"
                        },
                        {
                          "text": "Response Body: {\"name\":\"Human GBNZA\",\"id\":3060,\"created_at\":\"2025-06-27T18:48:46.631376\",\"updated_at\":\"2025-06-27T18:48:46.631379\"}\n"
                        },
                        {
                          "text": "Response Headers: {\"access-control-allow-origin\":\"http://localhost:3000\",\"content-length\":\"116\",\"date\":\"Fri, 27 Jun 2025 18:48:46 GMT\",\"content-type\":\"application/json\",\"vary\":\"Origin\",\"server\":\"uvicorn\",\"access-control-allow-credentials\":\"true\"}\n"
                        },
                        {
                          "text": "API response successful: 201\n"
                        },
                        {
                          "text": "Verifying entity exists in database...\n"
                        },
                        {
                          "text": "✅ Entity confirmed in database: ID 3060\n"
                        },
                        {
                          "text": "Form closed successfully\n"
                        },
                        {
                          "text": "Waiting for entity to appear in UI...\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human GBNZA\" in 1178ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Human GBNZA (ID: 3060)\n"
                        },
                        {
                          "text": "Creating entity: Ball GBNZB\n"
                        },
                        {
                          "text": "Setting up API monitoring for entity creation...\n"
                        },
                        {
                          "text": "Submitting entity creation form...\n"
                        },
                        {
                          "text": "Waiting for API response...\n"
                        },
                        {
                          "text": "Entity API Response: http://localhost:8000/api/v1/entities/ - Status: 201\n"
                        },
                        {
                          "text": "Request Body: {\"name\":\"Ball GBNZB\"}\n"
                        },
                        {
                          "text": "Response Body: {\"name\":\"Ball GBNZB\",\"id\":3061,\"created_at\":\"2025-06-27T18:48:48.262151\",\"updated_at\":\"2025-06-27T18:48:48.262157\"}\n"
                        },
                        {
                          "text": "Response Headers: {\"access-control-allow-origin\":\"http://localhost:3000\",\"content-length\":\"115\",\"date\":\"Fri, 27 Jun 2025 18:48:47 GMT\",\"content-type\":\"application/json\",\"vary\":\"Origin\",\"server\":\"uvicorn\",\"access-control-allow-credentials\":\"true\"}\n"
                        },
                        {
                          "text": "API response successful: 201\n"
                        },
                        {
                          "text": "Verifying entity exists in database...\n"
                        },
                        {
                          "text": "✅ Entity confirmed in database: ID 3061\n"
                        },
                        {
                          "text": "Form closed successfully\n"
                        },
                        {
                          "text": "Waiting for entity to appear in UI...\n"
                        },
                        {
                          "text": "Entity creation completed for \"Ball GBNZB\" in 1124ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Ball GBNZB (ID: 3061)\n"
                        },
                        {
                          "text": "Creating entity: Build GBNZC\n"
                        },
                        {
                          "text": "Setting up API monitoring for entity creation...\n"
                        },
                        {
                          "text": "Submitting entity creation form...\n"
                        },
                        {
                          "text": "Waiting for API response...\n"
                        },
                        {
                          "text": "Entity API Response: http://localhost:8000/api/v1/entities/ - Status: 201\n"
                        },
                        {
                          "text": "Request Body: {\"name\":\"Build GBNZC\"}\n"
                        },
                        {
                          "text": "Response Body: {\"name\":\"Build GBNZC\",\"id\":3062,\"created_at\":\"2025-06-27T18:48:49.895871\",\"updated_at\":\"2025-06-27T18:48:49.895875\"}\n"
                        },
                        {
                          "text": "Response Headers: {\"access-control-allow-origin\":\"http://localhost:3000\",\"content-length\":\"116\",\"date\":\"Fri, 27 Jun 2025 18:48:49 GMT\",\"content-type\":\"application/json\",\"vary\":\"Origin\",\"server\":\"uvicorn\",\"access-control-allow-credentials\":\"true\"}\n"
                        },
                        {
                          "text": "API response successful: 201\n"
                        },
                        {
                          "text": "Verifying entity exists in database...\n"
                        },
                        {
                          "text": "✅ Entity confirmed in database: ID 3062\n"
                        },
                        {
                          "text": "Form closed successfully\n"
                        },
                        {
                          "text": "Waiting for entity to appear in UI...\n"
                        },
                        {
                          "text": "Entity creation completed for \"Build GBNZC\" in 1118ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Build GBNZC (ID: 3062)\n"
                        },
                        {
                          "text": "Creating entity: Car GBNZD\n"
                        },
                        {
                          "text": "Setting up API monitoring for entity creation...\n"
                        },
                        {
                          "text": "Submitting entity creation form...\n"
                        },
                        {
                          "text": "Waiting for API response...\n"
                        },
                        {
                          "text": "Entity API Response: http://localhost:8000/api/v1/entities/ - Status: 201\n"
                        },
                        {
                          "text": "Request Body: {\"name\":\"Car GBNZD\"}\n"
                        },
                        {
                          "text": "Response Body: {\"name\":\"Car GBNZD\",\"id\":3063,\"created_at\":\"2025-06-27T18:48:51.529899\",\"updated_at\":\"2025-06-27T18:48:51.529904\"}\n"
                        },
                        {
                          "text": "Response Headers: {\"access-control-allow-origin\":\"http://localhost:3000\",\"content-length\":\"114\",\"date\":\"Fri, 27 Jun 2025 18:48:51 GMT\",\"content-type\":\"application/json\",\"vary\":\"Origin\",\"server\":\"uvicorn\",\"access-control-allow-credentials\":\"true\"}\n"
                        },
                        {
                          "text": "API response successful: 201\n"
                        },
                        {
                          "text": "Verifying entity exists in database...\n"
                        },
                        {
                          "text": "✅ Entity confirmed in database: ID 3063\n"
                        },
                        {
                          "text": "Form closed successfully\n"
                        },
                        {
                          "text": "Waiting for entity to appear in UI...\n"
                        },
                        {
                          "text": "Entity creation completed for \"Car GBNZD\" in 1125ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Car GBNZD (ID: 3063)\n"
                        },
                        {
                          "text": "Creating entity: Eleph GBNZE\n"
                        },
                        {
                          "text": "Setting up API monitoring for entity creation...\n"
                        },
                        {
                          "text": "Submitting entity creation form...\n"
                        },
                        {
                          "text": "Waiting for API response...\n"
                        },
                        {
                          "text": "Entity API Response: http://localhost:8000/api/v1/entities/ - Status: 201\n"
                        },
                        {
                          "text": "Request Body: {\"name\":\"Eleph GBNZE\"}\n"
                        },
                        {
                          "text": "Response Body: {\"name\":\"Eleph GBNZE\",\"id\":3064,\"created_at\":\"2025-06-27T18:48:53.162450\",\"updated_at\":\"2025-06-27T18:48:53.162454\"}\n"
                        },
                        {
                          "text": "Response Headers: {\"access-control-allow-origin\":\"http://localhost:3000\",\"content-length\":\"116\",\"date\":\"Fri, 27 Jun 2025 18:48:52 GMT\",\"content-type\":\"application/json\",\"vary\":\"Origin\",\"server\":\"uvicorn\",\"access-control-allow-credentials\":\"true\"}\n"
                        },
                        {
                          "text": "API response successful: 201\n"
                        },
                        {
                          "text": "Verifying entity exists in database...\n"
                        },
                        {
                          "text": "✅ Entity confirmed in database: ID 3064\n"
                        },
                        {
                          "text": "Form closed successfully\n"
                        },
                        {
                          "text": "Waiting for entity to appear in UI...\n"
                        },
                        {
                          "text": "Entity creation completed for \"Eleph GBNZE\" in 1119ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Eleph GBNZE (ID: 3064)\n"
                        },
                        {
                          "text": "Creating entity: Mouse GBNZF\n"
                        },
                        {
                          "text": "Setting up API monitoring for entity creation...\n"
                        },
                        {
                          "text": "Submitting entity creation form...\n"
                        },
                        {
                          "text": "Waiting for API response...\n"
                        },
                        {
                          "text": "Entity API Response: http://localhost:8000/api/v1/entities/ - Status: 201\n"
                        },
                        {
                          "text": "Request Body: {\"name\":\"Mouse GBNZF\"}\n"
                        },
                        {
                          "text": "Response Body: {\"name\":\"Mouse GBNZF\",\"id\":3065,\"created_at\":\"2025-06-27T18:48:54.895849\",\"updated_at\":\"2025-06-27T18:48:54.895855\"}\n"
                        },
                        {
                          "text": "Response Headers: {\"access-control-allow-origin\":\"http://localhost:3000\",\"content-length\":\"116\",\"date\":\"Fri, 27 Jun 2025 18:48:54 GMT\",\"content-type\":\"application/json\",\"vary\":\"Origin\",\"server\":\"uvicorn\",\"access-control-allow-credentials\":\"true\"}\n"
                        },
                        {
                          "text": "API response successful: 201\n"
                        },
                        {
                          "text": "Verifying entity exists in database...\n"
                        },
                        {
                          "text": "✅ Entity confirmed in database: ID 3065\n"
                        },
                        {
                          "text": "Form closed successfully\n"
                        },
                        {
                          "text": "Waiting for entity to appear in UI...\n"
                        },
                        {
                          "text": "Entity creation completed for \"Mouse GBNZF\" in 1222ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Mouse GBNZF (ID: 3065)\n"
                        },
                        {
                          "text": "Creating test connections...\n"
                        },
                        {
                          "text": "Creating connection: Human GBNZA → Ball GBNZB (10.0x)\n"
                        },
                        {
                          "text": "Selecting AutoComplete option: Human GBNZA\n"
                        },
                        {
                          "text": "Successfully selected: Human GBNZA using .autocomplete-dropdown/.autocomplete-option\n"
                        },
                        {
                          "text": "Selecting AutoComplete option: Ball GBNZB\n"
                        },
                        {
                          "text": "Successfully selected: Ball GBNZB using .autocomplete-dropdown/.autocomplete-option\n"
                        },
                        {
                          "text": "Selecting unit: Length\n"
                        },
                        {
                          "text": "Selected unit: Length (m)\n"
                        },
                        {
                          "text": "Filling multiplier: 10.0\n"
                        },
                        {
                          "text": "Waiting for submit button to be enabled...\n"
                        },
                        {
                          "text": "Setting up API monitoring for connection creation...\n"
                        },
                        {
                          "text": "Submitting connection creation form...\n"
                        },
                        {
                          "text": "Waiting for API response...\n"
                        },
                        {
                          "text": "Connection API response: 201\n"
                        },
                        {
                          "text": "API response successful: 201\n"
                        },
                        {
                          "text": "Connection created with ID: 729\n"
                        },
                        {
                          "text": "Verifying connection exists in database...\n"
                        },
                        {
                          "text": "✅ Connection confirmed in database: ID 729 (Human GBNZA[3060] → Ball GBNZB[3061] × 10.0)\n"
                        },
                        {
                          "text": "Form closed successfully\n"
                        },
                        {
                          "text": "Connection creation completed for \"Human GBNZA → Ball GBNZB\" in 4068ms\n"
                        },
                        {
                          "text": "  ℹ️  Connection tracking deprecated: 3060 → 3061 (auto-deleted via CASCADE)\n"
                        },
                        {
                          "text": "Creating connection: Ball GBNZB → Build GBNZC (50.0x)\n"
                        },
                        {
                          "text": "Selecting AutoComplete option: Ball GBNZB\n"
                        },
                        {
                          "text": "Successfully selected: Ball GBNZB using .autocomplete-dropdown/.autocomplete-option\n"
                        },
                        {
                          "text": "Selecting AutoComplete option: Build GBNZC\n"
                        },
                        {
                          "text": "Successfully selected: Build GBNZC using .autocomplete-dropdown/.autocomplete-option\n"
                        },
                        {
                          "text": "Selecting unit: Length\n"
                        },
                        {
                          "text": "Selected unit: Length (m)\n"
                        },
                        {
                          "text": "Filling multiplier: 50.0\n"
                        },
                        {
                          "text": "Waiting for submit button to be enabled...\n"
                        },
                        {
                          "text": "Setting up API monitoring for connection creation...\n"
                        },
                        {
                          "text": "Submitting connection creation form...\n"
                        },
                        {
                          "text": "Waiting for API response...\n"
                        },
                        {
                          "text": "Connection API response: 400\n"
                        },
                        {
                          "text": "Connection creation failed due to constraints, assuming connection exists...\n"
                        },
                        {
                          "text": "Skipping connection creation for \"Ball GBNZB → Build GBNZC\" due to constraints\n"
                        },
                        {
                          "text": "Successfully handled existing connection for \"Ball GBNZB → Build GBNZC\"\n"
                        },
                        {
                          "text": "  ℹ️  Connection tracking deprecated: 3061 → 3062 (auto-deleted via CASCADE)\n"
                        },
                        {
                          "text": "Connection creation failed for Ball GBNZB → Build GBNZC, checking if it exists...\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Cleaning 6 tracked entity IDs...\n"
                        },
                        {
                          "text": "  ℹ️  Connections will be auto-deleted via CASCADE DELETE\n"
                        },
                        {
                          "text": "  ✓ Deleted entity ID: 3060 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  ✓ Deleted entity ID: 3061 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  ✓ Deleted entity ID: 3062 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  ✓ Deleted entity ID: 3063 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  ✓ Deleted entity ID: 3064 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  ✓ Deleted entity ID: 3065 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  Fallback cleanup for 6 named entities...\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 33ms:\n"
                        },
                        {
                          "text": "  • Entities deleted: 6 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-27T18:48:45.251Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-b379b-y-comparison-page-correctly-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-b379b-y-comparison-page-correctly-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-b379b-y-comparison-page-correctly-chromium/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-b379b-y-comparison-page-correctly-chromium/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts",
                        "column": 67,
                        "line": 98
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "0f325a1a01063f2777e1-ec5b6735c4e594d4a630",
              "file": "comparisons.spec.ts",
              "line": 130,
              "column": 7
            },
            {
              "title": "should calculate direct relationships",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 1,
                      "parallelIndex": 0,
                      "status": "failed",
                      "duration": 29880,
                      "error": {
                        "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Build TVJPC\")').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Build TVJPC\")').first()\u001b[22m\n",
                        "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Build TVJPC\")').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Build TVJPC\")').first()\u001b[22m\n\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:98:67",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts",
                          "column": 67,
                          "line": 98
                        },
                        "snippet": "   96 |         // Verify connection was created - check for both entity names in the connection list\n   97 |         await expect(page.locator(`:text(\"${conn.from}\")`).first()).toBeVisible({ timeout: 10000 });\n>  98 |         await expect(page.locator(`:text(\"${conn.to}\")`).first()).toBeVisible({ timeout: 10000 });\n      |                                                                   ^\n   99 |       } catch (error: any) {\n  100 |         // If connection creation fails, check if it already exists\n  101 |         console.log(`Connection creation failed for ${conn.from} → ${conn.to}, checking if it exists...`);"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts",
                            "column": 67,
                            "line": 98
                          },
                          "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Build TVJPC\")').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Build TVJPC\")').first()\u001b[22m\n\n\n   96 |         // Verify connection was created - check for both entity names in the connection list\n   97 |         await expect(page.locator(`:text(\"${conn.from}\")`).first()).toBeVisible({ timeout: 10000 });\n>  98 |         await expect(page.locator(`:text(\"${conn.to}\")`).first()).toBeVisible({ timeout: 10000 });\n      |                                                                   ^\n   99 |       } catch (error: any) {\n  100 |         // If connection creation fails, check if it already exists\n  101 |         console.log(`Connection creation failed for ${conn.from} → ${conn.to}, checking if it exists...`);\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:98:67"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 130 total entities in database\n"
                        },
                        {
                          "text": "  Identified 0 test entities to delete\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 27ms:\n"
                        },
                        {
                          "text": "  • Initial entities: 130\n"
                        },
                        {
                          "text": "  • Entities deleted: 0\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 130\n"
                        },
                        {
                          "text": "  • Net reduction: 0\n"
                        },
                        {
                          "text": "Creating test entities for comparison tests...\n"
                        },
                        {
                          "text": "Creating entity: Human TVJPA\n"
                        },
                        {
                          "text": "Setting up API monitoring for entity creation...\n"
                        },
                        {
                          "text": "Submitting entity creation form...\n"
                        },
                        {
                          "text": "Waiting for API response...\n"
                        },
                        {
                          "text": "Entity API Response: http://localhost:8000/api/v1/entities/ - Status: 201\n"
                        },
                        {
                          "text": "Request Body: {\"name\":\"Human TVJPA\"}\n"
                        },
                        {
                          "text": "Response Body: {\"name\":\"Human TVJPA\",\"id\":3066,\"created_at\":\"2025-06-27T18:49:16.960690\",\"updated_at\":\"2025-06-27T18:49:16.960694\"}\n"
                        },
                        {
                          "text": "Response Headers: {\"access-control-allow-origin\":\"http://localhost:3000\",\"content-length\":\"116\",\"date\":\"Fri, 27 Jun 2025 18:49:16 GMT\",\"content-type\":\"application/json\",\"vary\":\"Origin\",\"server\":\"uvicorn\",\"access-control-allow-credentials\":\"true\"}\n"
                        },
                        {
                          "text": "API response successful: 201\n"
                        },
                        {
                          "text": "Verifying entity exists in database...\n"
                        },
                        {
                          "text": "✅ Entity confirmed in database: ID 3066\n"
                        },
                        {
                          "text": "Form closed successfully\n"
                        },
                        {
                          "text": "Waiting for entity to appear in UI...\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human TVJPA\" in 1162ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Human TVJPA (ID: 3066)\n"
                        },
                        {
                          "text": "Creating entity: Ball TVJPB\n"
                        },
                        {
                          "text": "Setting up API monitoring for entity creation...\n"
                        },
                        {
                          "text": "Submitting entity creation form...\n"
                        },
                        {
                          "text": "Waiting for API response...\n"
                        },
                        {
                          "text": "Entity API Response: http://localhost:8000/api/v1/entities/ - Status: 201\n"
                        },
                        {
                          "text": "Request Body: {\"name\":\"Ball TVJPB\"}\n"
                        },
                        {
                          "text": "Response Body: {\"name\":\"Ball TVJPB\",\"id\":3067,\"created_at\":\"2025-06-27T18:49:18.594576\",\"updated_at\":\"2025-06-27T18:49:18.594580\"}\n"
                        },
                        {
                          "text": "Response Headers: {\"access-control-allow-origin\":\"http://localhost:3000\",\"content-length\":\"115\",\"date\":\"Fri, 27 Jun 2025 18:49:18 GMT\",\"content-type\":\"application/json\",\"vary\":\"Origin\",\"server\":\"uvicorn\",\"access-control-allow-credentials\":\"true\"}\n"
                        },
                        {
                          "text": "API response successful: 201\n"
                        },
                        {
                          "text": "Verifying entity exists in database...\n"
                        },
                        {
                          "text": "✅ Entity confirmed in database: ID 3067\n"
                        },
                        {
                          "text": "Form closed successfully\n"
                        },
                        {
                          "text": "Waiting for entity to appear in UI...\n"
                        },
                        {
                          "text": "Entity creation completed for \"Ball TVJPB\" in 1125ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Ball TVJPB (ID: 3067)\n"
                        },
                        {
                          "text": "Creating entity: Build TVJPC\n"
                        },
                        {
                          "text": "Setting up API monitoring for entity creation...\n"
                        },
                        {
                          "text": "Submitting entity creation form...\n"
                        },
                        {
                          "text": "Waiting for API response...\n"
                        },
                        {
                          "text": "Entity API Response: http://localhost:8000/api/v1/entities/ - Status: 201\n"
                        },
                        {
                          "text": "Request Body: {\"name\":\"Build TVJPC\"}\n"
                        },
                        {
                          "text": "Response Body: {\"name\":\"Build TVJPC\",\"id\":3068,\"created_at\":\"2025-06-27T18:49:20.327589\",\"updated_at\":\"2025-06-27T18:49:20.327625\"}\n"
                        },
                        {
                          "text": "Response Headers: {\"access-control-allow-origin\":\"http://localhost:3000\",\"content-length\":\"116\",\"date\":\"Fri, 27 Jun 2025 18:49:20 GMT\",\"content-type\":\"application/json\",\"vary\":\"Origin\",\"server\":\"uvicorn\",\"access-control-allow-credentials\":\"true\"}\n"
                        },
                        {
                          "text": "API response successful: 201\n"
                        },
                        {
                          "text": "Verifying entity exists in database...\n"
                        },
                        {
                          "text": "✅ Entity confirmed in database: ID 3068\n"
                        },
                        {
                          "text": "Form closed successfully\n"
                        },
                        {
                          "text": "Waiting for entity to appear in UI...\n"
                        },
                        {
                          "text": "Entity creation completed for \"Build TVJPC\" in 1217ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Build TVJPC (ID: 3068)\n"
                        },
                        {
                          "text": "Creating entity: Car TVJPD\n"
                        },
                        {
                          "text": "Setting up API monitoring for entity creation...\n"
                        },
                        {
                          "text": "Submitting entity creation form...\n"
                        },
                        {
                          "text": "Waiting for API response...\n"
                        },
                        {
                          "text": "Entity API Response: http://localhost:8000/api/v1/entities/ - Status: 201\n"
                        },
                        {
                          "text": "Request Body: {\"name\":\"Car TVJPD\"}\n"
                        },
                        {
                          "text": "Response Body: {\"name\":\"Car TVJPD\",\"id\":3069,\"created_at\":\"2025-06-27T18:49:22.060969\",\"updated_at\":\"2025-06-27T18:49:22.060973\"}\n"
                        },
                        {
                          "text": "Response Headers: {\"access-control-allow-origin\":\"http://localhost:3000\",\"content-length\":\"114\",\"date\":\"Fri, 27 Jun 2025 18:49:21 GMT\",\"content-type\":\"application/json\",\"vary\":\"Origin\",\"server\":\"uvicorn\",\"access-control-allow-credentials\":\"true\"}\n"
                        },
                        {
                          "text": "API response successful: 201\n"
                        },
                        {
                          "text": "Verifying entity exists in database...\n"
                        },
                        {
                          "text": "✅ Entity confirmed in database: ID 3069\n"
                        },
                        {
                          "text": "Form closed successfully\n"
                        },
                        {
                          "text": "Waiting for entity to appear in UI...\n"
                        },
                        {
                          "text": "Entity creation completed for \"Car TVJPD\" in 1224ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Car TVJPD (ID: 3069)\n"
                        },
                        {
                          "text": "Creating entity: Eleph TVJPE\n"
                        },
                        {
                          "text": "Setting up API monitoring for entity creation...\n"
                        },
                        {
                          "text": "Submitting entity creation form...\n"
                        },
                        {
                          "text": "Waiting for API response...\n"
                        },
                        {
                          "text": "Entity API Response: http://localhost:8000/api/v1/entities/ - Status: 201\n"
                        },
                        {
                          "text": "Request Body: {\"name\":\"Eleph TVJPE\"}\n"
                        },
                        {
                          "text": "Response Body: {\"name\":\"Eleph TVJPE\",\"id\":3070,\"created_at\":\"2025-06-27T18:49:23.676741\",\"updated_at\":\"2025-06-27T18:49:23.676744\"}\n"
                        },
                        {
                          "text": "Response Headers: {\"access-control-allow-origin\":\"http://localhost:3000\",\"content-length\":\"116\",\"date\":\"Fri, 27 Jun 2025 18:49:23 GMT\",\"content-type\":\"application/json\",\"vary\":\"Origin\",\"server\":\"uvicorn\",\"access-control-allow-credentials\":\"true\"}\n"
                        },
                        {
                          "text": "API response successful: 201\n"
                        },
                        {
                          "text": "Verifying entity exists in database...\n"
                        },
                        {
                          "text": "✅ Entity confirmed in database: ID 3070\n"
                        },
                        {
                          "text": "Form closed successfully\n"
                        },
                        {
                          "text": "Waiting for entity to appear in UI...\n"
                        },
                        {
                          "text": "Entity creation completed for \"Eleph TVJPE\" in 1107ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Eleph TVJPE (ID: 3070)\n"
                        },
                        {
                          "text": "Creating entity: Mouse TVJPF\n"
                        },
                        {
                          "text": "Setting up API monitoring for entity creation...\n"
                        },
                        {
                          "text": "Submitting entity creation form...\n"
                        },
                        {
                          "text": "Waiting for API response...\n"
                        },
                        {
                          "text": "Entity API Response: http://localhost:8000/api/v1/entities/ - Status: 201\n"
                        },
                        {
                          "text": "Request Body: {\"name\":\"Mouse TVJPF\"}\n"
                        },
                        {
                          "text": "Response Body: {\"name\":\"Mouse TVJPF\",\"id\":3071,\"created_at\":\"2025-06-27T18:49:25.376230\",\"updated_at\":\"2025-06-27T18:49:25.376234\"}\n"
                        },
                        {
                          "text": "Response Headers: {\"access-control-allow-origin\":\"http://localhost:3000\",\"content-length\":\"116\",\"date\":\"Fri, 27 Jun 2025 18:49:25 GMT\",\"content-type\":\"application/json\",\"vary\":\"Origin\",\"server\":\"uvicorn\",\"access-control-allow-credentials\":\"true\"}\n"
                        },
                        {
                          "text": "API response successful: 201\n"
                        },
                        {
                          "text": "Verifying entity exists in database...\n"
                        },
                        {
                          "text": "✅ Entity confirmed in database: ID 3071\n"
                        },
                        {
                          "text": "Form closed successfully\n"
                        },
                        {
                          "text": "Waiting for entity to appear in UI...\n"
                        },
                        {
                          "text": "Entity creation completed for \"Mouse TVJPF\" in 1197ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Mouse TVJPF (ID: 3071)\n"
                        },
                        {
                          "text": "Creating test connections...\n"
                        },
                        {
                          "text": "Creating connection: Human TVJPA → Ball TVJPB (10.0x)\n"
                        },
                        {
                          "text": "Selecting AutoComplete option: Human TVJPA\n"
                        },
                        {
                          "text": "Successfully selected: Human TVJPA using .autocomplete-dropdown/.autocomplete-option\n"
                        },
                        {
                          "text": "Selecting AutoComplete option: Ball TVJPB\n"
                        },
                        {
                          "text": "Successfully selected: Ball TVJPB using .autocomplete-dropdown/.autocomplete-option\n"
                        },
                        {
                          "text": "Selecting unit: Length\n"
                        },
                        {
                          "text": "Selected unit: Length (m)\n"
                        },
                        {
                          "text": "Filling multiplier: 10.0\n"
                        },
                        {
                          "text": "Waiting for submit button to be enabled...\n"
                        },
                        {
                          "text": "Setting up API monitoring for connection creation...\n"
                        },
                        {
                          "text": "Submitting connection creation form...\n"
                        },
                        {
                          "text": "Waiting for API response...\n"
                        },
                        {
                          "text": "Connection API response: 201\n"
                        },
                        {
                          "text": "API response successful: 201\n"
                        },
                        {
                          "text": "Connection created with ID: 733\n"
                        },
                        {
                          "text": "Verifying connection exists in database...\n"
                        },
                        {
                          "text": "✅ Connection confirmed in database: ID 733 (Human TVJPA[3066] → Ball TVJPB[3067] × 10.0)\n"
                        },
                        {
                          "text": "Form closed successfully\n"
                        },
                        {
                          "text": "Connection creation completed for \"Human TVJPA → Ball TVJPB\" in 4084ms\n"
                        },
                        {
                          "text": "  ℹ️  Connection tracking deprecated: 3066 → 3067 (auto-deleted via CASCADE)\n"
                        },
                        {
                          "text": "Creating connection: Ball TVJPB → Build TVJPC (50.0x)\n"
                        },
                        {
                          "text": "Selecting AutoComplete option: Ball TVJPB\n"
                        },
                        {
                          "text": "Successfully selected: Ball TVJPB using .autocomplete-dropdown/.autocomplete-option\n"
                        },
                        {
                          "text": "Selecting AutoComplete option: Build TVJPC\n"
                        },
                        {
                          "text": "Successfully selected: Build TVJPC using .autocomplete-dropdown/.autocomplete-option\n"
                        },
                        {
                          "text": "Selecting unit: Length\n"
                        },
                        {
                          "text": "Selected unit: Length (m)\n"
                        },
                        {
                          "text": "Filling multiplier: 50.0\n"
                        },
                        {
                          "text": "Waiting for submit button to be enabled...\n"
                        },
                        {
                          "text": "Setting up API monitoring for connection creation...\n"
                        },
                        {
                          "text": "Submitting connection creation form...\n"
                        },
                        {
                          "text": "Waiting for API response...\n"
                        },
                        {
                          "text": "Connection API response: 400\n"
                        },
                        {
                          "text": "Connection creation failed due to constraints, assuming connection exists...\n"
                        },
                        {
                          "text": "Skipping connection creation for \"Ball TVJPB → Build TVJPC\" due to constraints\n"
                        },
                        {
                          "text": "Successfully handled existing connection for \"Ball TVJPB → Build TVJPC\"\n"
                        },
                        {
                          "text": "  ℹ️  Connection tracking deprecated: 3067 → 3068 (auto-deleted via CASCADE)\n"
                        },
                        {
                          "text": "Connection creation failed for Ball TVJPB → Build TVJPC, checking if it exists...\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Cleaning 6 tracked entity IDs...\n"
                        },
                        {
                          "text": "  ℹ️  Connections will be auto-deleted via CASCADE DELETE\n"
                        },
                        {
                          "text": "  ✓ Deleted entity ID: 3066 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  ✓ Deleted entity ID: 3067 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  ✓ Deleted entity ID: 3068 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  ✓ Deleted entity ID: 3069 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  ✓ Deleted entity ID: 3070 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  ✓ Deleted entity ID: 3071 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  Fallback cleanup for 6 named entities...\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 26ms:\n"
                        },
                        {
                          "text": "  • Entities deleted: 6 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-27T18:49:16.023Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-1f028-culate-direct-relationships-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-1f028-culate-direct-relationships-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-1f028-culate-direct-relationships-chromium/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-1f028-culate-direct-relationships-chromium/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts",
                        "column": 67,
                        "line": 98
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "0f325a1a01063f2777e1-f2bf549b20a73c7f70d0",
              "file": "comparisons.spec.ts",
              "line": 138,
              "column": 7
            },
            {
              "title": "should calculate transitive relationships",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 2,
                      "parallelIndex": 0,
                      "status": "failed",
                      "duration": 29911,
                      "error": {
                        "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Build BIHYC\")').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Build BIHYC\")').first()\u001b[22m\n",
                        "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Build BIHYC\")').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Build BIHYC\")').first()\u001b[22m\n\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:98:67",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts",
                          "column": 67,
                          "line": 98
                        },
                        "snippet": "   96 |         // Verify connection was created - check for both entity names in the connection list\n   97 |         await expect(page.locator(`:text(\"${conn.from}\")`).first()).toBeVisible({ timeout: 10000 });\n>  98 |         await expect(page.locator(`:text(\"${conn.to}\")`).first()).toBeVisible({ timeout: 10000 });\n      |                                                                   ^\n   99 |       } catch (error: any) {\n  100 |         // If connection creation fails, check if it already exists\n  101 |         console.log(`Connection creation failed for ${conn.from} → ${conn.to}, checking if it exists...`);"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts",
                            "column": 67,
                            "line": 98
                          },
                          "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Build BIHYC\")').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Build BIHYC\")').first()\u001b[22m\n\n\n   96 |         // Verify connection was created - check for both entity names in the connection list\n   97 |         await expect(page.locator(`:text(\"${conn.from}\")`).first()).toBeVisible({ timeout: 10000 });\n>  98 |         await expect(page.locator(`:text(\"${conn.to}\")`).first()).toBeVisible({ timeout: 10000 });\n      |                                                                   ^\n   99 |       } catch (error: any) {\n  100 |         // If connection creation fails, check if it already exists\n  101 |         console.log(`Connection creation failed for ${conn.from} → ${conn.to}, checking if it exists...`);\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:98:67"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 130 total entities in database\n"
                        },
                        {
                          "text": "  Identified 0 test entities to delete\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 27ms:\n"
                        },
                        {
                          "text": "  • Initial entities: 130\n"
                        },
                        {
                          "text": "  • Entities deleted: 0\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 130\n"
                        },
                        {
                          "text": "  • Net reduction: 0\n"
                        },
                        {
                          "text": "Creating test entities for comparison tests...\n"
                        },
                        {
                          "text": "Creating entity: Human BIHYA\n"
                        },
                        {
                          "text": "Setting up API monitoring for entity creation...\n"
                        },
                        {
                          "text": "Submitting entity creation form...\n"
                        },
                        {
                          "text": "Waiting for API response...\n"
                        },
                        {
                          "text": "Entity API Response: http://localhost:8000/api/v1/entities/ - Status: 201\n"
                        },
                        {
                          "text": "Request Body: {\"name\":\"Human BIHYA\"}\n"
                        },
                        {
                          "text": "Response Body: {\"name\":\"Human BIHYA\",\"id\":3072,\"created_at\":\"2025-06-27T18:49:47.327712\",\"updated_at\":\"2025-06-27T18:49:47.327716\"}\n"
                        },
                        {
                          "text": "Response Headers: {\"access-control-allow-origin\":\"http://localhost:3000\",\"content-length\":\"116\",\"date\":\"Fri, 27 Jun 2025 18:49:46 GMT\",\"content-type\":\"application/json\",\"vary\":\"Origin\",\"server\":\"uvicorn\",\"access-control-allow-credentials\":\"true\"}\n"
                        },
                        {
                          "text": "API response successful: 201\n"
                        },
                        {
                          "text": "Verifying entity exists in database...\n"
                        },
                        {
                          "text": "✅ Entity confirmed in database: ID 3072\n"
                        },
                        {
                          "text": "Form closed successfully\n"
                        },
                        {
                          "text": "Waiting for entity to appear in UI...\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human BIHYA\" in 1152ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Human BIHYA (ID: 3072)\n"
                        },
                        {
                          "text": "Creating entity: Ball BIHYB\n"
                        },
                        {
                          "text": "Setting up API monitoring for entity creation...\n"
                        },
                        {
                          "text": "Submitting entity creation form...\n"
                        },
                        {
                          "text": "Waiting for API response...\n"
                        },
                        {
                          "text": "Entity API Response: http://localhost:8000/api/v1/entities/ - Status: 201\n"
                        },
                        {
                          "text": "Request Body: {\"name\":\"Ball BIHYB\"}\n"
                        },
                        {
                          "text": "Response Body: {\"name\":\"Ball BIHYB\",\"id\":3073,\"created_at\":\"2025-06-27T18:49:49.059831\",\"updated_at\":\"2025-06-27T18:49:49.059835\"}\n"
                        },
                        {
                          "text": "Response Headers: {\"access-control-allow-origin\":\"http://localhost:3000\",\"content-length\":\"115\",\"date\":\"Fri, 27 Jun 2025 18:49:48 GMT\",\"content-type\":\"application/json\",\"vary\":\"Origin\",\"server\":\"uvicorn\",\"access-control-allow-credentials\":\"true\"}\n"
                        },
                        {
                          "text": "API response successful: 201\n"
                        },
                        {
                          "text": "Verifying entity exists in database...\n"
                        },
                        {
                          "text": "✅ Entity confirmed in database: ID 3073\n"
                        },
                        {
                          "text": "Form closed successfully\n"
                        },
                        {
                          "text": "Waiting for entity to appear in UI...\n"
                        },
                        {
                          "text": "Entity creation completed for \"Ball BIHYB\" in 1225ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Ball BIHYB (ID: 3073)\n"
                        },
                        {
                          "text": "Creating entity: Build BIHYC\n"
                        },
                        {
                          "text": "Setting up API monitoring for entity creation...\n"
                        },
                        {
                          "text": "Submitting entity creation form...\n"
                        },
                        {
                          "text": "Waiting for API response...\n"
                        },
                        {
                          "text": "Entity API Response: http://localhost:8000/api/v1/entities/ - Status: 201\n"
                        },
                        {
                          "text": "Request Body: {\"name\":\"Build BIHYC\"}\n"
                        },
                        {
                          "text": "Response Body: {\"name\":\"Build BIHYC\",\"id\":3074,\"created_at\":\"2025-06-27T18:49:50.792937\",\"updated_at\":\"2025-06-27T18:49:50.792941\"}\n"
                        },
                        {
                          "text": "Response Headers: {\"access-control-allow-origin\":\"http://localhost:3000\",\"content-length\":\"116\",\"date\":\"Fri, 27 Jun 2025 18:49:50 GMT\",\"content-type\":\"application/json\",\"vary\":\"Origin\",\"server\":\"uvicorn\",\"access-control-allow-credentials\":\"true\"}\n"
                        },
                        {
                          "text": "API response successful: 201\n"
                        },
                        {
                          "text": "Verifying entity exists in database...\n"
                        },
                        {
                          "text": "✅ Entity confirmed in database: ID 3074\n"
                        },
                        {
                          "text": "Form closed successfully\n"
                        },
                        {
                          "text": "Waiting for entity to appear in UI...\n"
                        },
                        {
                          "text": "Entity creation completed for \"Build BIHYC\" in 1235ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Build BIHYC (ID: 3074)\n"
                        },
                        {
                          "text": "Creating entity: Car BIHYD\n"
                        },
                        {
                          "text": "Setting up API monitoring for entity creation...\n"
                        },
                        {
                          "text": "Submitting entity creation form...\n"
                        },
                        {
                          "text": "Waiting for API response...\n"
                        },
                        {
                          "text": "Entity API Response: http://localhost:8000/api/v1/entities/ - Status: 201\n"
                        },
                        {
                          "text": "Request Body: {\"name\":\"Car BIHYD\"}\n"
                        },
                        {
                          "text": "Response Body: {\"name\":\"Car BIHYD\",\"id\":3075,\"created_at\":\"2025-06-27T18:49:52.426447\",\"updated_at\":\"2025-06-27T18:49:52.426451\"}\n"
                        },
                        {
                          "text": "Response Headers: {\"access-control-allow-origin\":\"http://localhost:3000\",\"content-length\":\"114\",\"date\":\"Fri, 27 Jun 2025 18:49:52 GMT\",\"content-type\":\"application/json\",\"vary\":\"Origin\",\"server\":\"uvicorn\",\"access-control-allow-credentials\":\"true\"}\n"
                        },
                        {
                          "text": "API response successful: 201\n"
                        },
                        {
                          "text": "Verifying entity exists in database...\n"
                        },
                        {
                          "text": "✅ Entity confirmed in database: ID 3075\n"
                        },
                        {
                          "text": "Form closed successfully\n"
                        },
                        {
                          "text": "Waiting for entity to appear in UI...\n"
                        },
                        {
                          "text": "Entity creation completed for \"Car BIHYD\" in 1117ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Car BIHYD (ID: 3075)\n"
                        },
                        {
                          "text": "Creating entity: Eleph BIHYE\n"
                        },
                        {
                          "text": "Setting up API monitoring for entity creation...\n"
                        },
                        {
                          "text": "Submitting entity creation form...\n"
                        },
                        {
                          "text": "Waiting for API response...\n"
                        },
                        {
                          "text": "Entity API Response: http://localhost:8000/api/v1/entities/ - Status: 201\n"
                        },
                        {
                          "text": "Request Body: {\"name\":\"Eleph BIHYE\"}\n"
                        },
                        {
                          "text": "Response Body: {\"name\":\"Eleph BIHYE\",\"id\":3076,\"created_at\":\"2025-06-27T18:49:54.060290\",\"updated_at\":\"2025-06-27T18:49:54.060297\"}\n"
                        },
                        {
                          "text": "Response Headers: {\"access-control-allow-origin\":\"http://localhost:3000\",\"content-length\":\"116\",\"date\":\"Fri, 27 Jun 2025 18:49:53 GMT\",\"content-type\":\"application/json\",\"vary\":\"Origin\",\"server\":\"uvicorn\",\"access-control-allow-credentials\":\"true\"}\n"
                        },
                        {
                          "text": "API response successful: 201\n"
                        },
                        {
                          "text": "Verifying entity exists in database...\n"
                        },
                        {
                          "text": "✅ Entity confirmed in database: ID 3076\n"
                        },
                        {
                          "text": "Form closed successfully\n"
                        },
                        {
                          "text": "Waiting for entity to appear in UI...\n"
                        },
                        {
                          "text": "Entity creation completed for \"Eleph BIHYE\" in 1127ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Eleph BIHYE (ID: 3076)\n"
                        },
                        {
                          "text": "Creating entity: Mouse BIHYF\n"
                        },
                        {
                          "text": "Setting up API monitoring for entity creation...\n"
                        },
                        {
                          "text": "Submitting entity creation form...\n"
                        },
                        {
                          "text": "Waiting for API response...\n"
                        },
                        {
                          "text": "Entity API Response: http://localhost:8000/api/v1/entities/ - Status: 201\n"
                        },
                        {
                          "text": "Request Body: {\"name\":\"Mouse BIHYF\"}\n"
                        },
                        {
                          "text": "Response Body: {\"name\":\"Mouse BIHYF\",\"id\":3077,\"created_at\":\"2025-06-27T18:49:55.792929\",\"updated_at\":\"2025-06-27T18:49:55.792933\"}\n"
                        },
                        {
                          "text": "Response Headers: {\"access-control-allow-origin\":\"http://localhost:3000\",\"content-length\":\"116\",\"date\":\"Fri, 27 Jun 2025 18:49:55 GMT\",\"content-type\":\"application/json\",\"vary\":\"Origin\",\"server\":\"uvicorn\",\"access-control-allow-credentials\":\"true\"}\n"
                        },
                        {
                          "text": "API response successful: 201\n"
                        },
                        {
                          "text": "Verifying entity exists in database...\n"
                        },
                        {
                          "text": "✅ Entity confirmed in database: ID 3077\n"
                        },
                        {
                          "text": "Form closed successfully\n"
                        },
                        {
                          "text": "Waiting for entity to appear in UI...\n"
                        },
                        {
                          "text": "Entity creation completed for \"Mouse BIHYF\" in 1223ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Mouse BIHYF (ID: 3077)\n"
                        },
                        {
                          "text": "Creating test connections...\n"
                        },
                        {
                          "text": "Creating connection: Human BIHYA → Ball BIHYB (10.0x)\n"
                        },
                        {
                          "text": "Selecting AutoComplete option: Human BIHYA\n"
                        },
                        {
                          "text": "Successfully selected: Human BIHYA using .autocomplete-dropdown/.autocomplete-option\n"
                        },
                        {
                          "text": "Selecting AutoComplete option: Ball BIHYB\n"
                        },
                        {
                          "text": "Successfully selected: Ball BIHYB using .autocomplete-dropdown/.autocomplete-option\n"
                        },
                        {
                          "text": "Selecting unit: Length\n"
                        },
                        {
                          "text": "Selected unit: Length (m)\n"
                        },
                        {
                          "text": "Filling multiplier: 10.0\n"
                        },
                        {
                          "text": "Waiting for submit button to be enabled...\n"
                        },
                        {
                          "text": "Setting up API monitoring for connection creation...\n"
                        },
                        {
                          "text": "Submitting connection creation form...\n"
                        },
                        {
                          "text": "Waiting for API response...\n"
                        },
                        {
                          "text": "Connection API response: 201\n"
                        },
                        {
                          "text": "API response successful: 201\n"
                        },
                        {
                          "text": "Connection created with ID: 737\n"
                        },
                        {
                          "text": "Verifying connection exists in database...\n"
                        },
                        {
                          "text": "✅ Connection confirmed in database: ID 737 (Human BIHYA[3072] → Ball BIHYB[3073] × 10.0)\n"
                        },
                        {
                          "text": "Form closed successfully\n"
                        },
                        {
                          "text": "Connection creation completed for \"Human BIHYA → Ball BIHYB\" in 4037ms\n"
                        },
                        {
                          "text": "  ℹ️  Connection tracking deprecated: 3072 → 3073 (auto-deleted via CASCADE)\n"
                        },
                        {
                          "text": "Creating connection: Ball BIHYB → Build BIHYC (50.0x)\n"
                        },
                        {
                          "text": "Selecting AutoComplete option: Ball BIHYB\n"
                        },
                        {
                          "text": "Successfully selected: Ball BIHYB using .autocomplete-dropdown/.autocomplete-option\n"
                        },
                        {
                          "text": "Selecting AutoComplete option: Build BIHYC\n"
                        },
                        {
                          "text": "Successfully selected: Build BIHYC using .autocomplete-dropdown/.autocomplete-option\n"
                        },
                        {
                          "text": "Selecting unit: Length\n"
                        },
                        {
                          "text": "Selected unit: Length (m)\n"
                        },
                        {
                          "text": "Filling multiplier: 50.0\n"
                        },
                        {
                          "text": "Waiting for submit button to be enabled...\n"
                        },
                        {
                          "text": "Setting up API monitoring for connection creation...\n"
                        },
                        {
                          "text": "Submitting connection creation form...\n"
                        },
                        {
                          "text": "Waiting for API response...\n"
                        },
                        {
                          "text": "Connection API response: 400\n"
                        },
                        {
                          "text": "Connection creation failed due to constraints, assuming connection exists...\n"
                        },
                        {
                          "text": "Skipping connection creation for \"Ball BIHYB → Build BIHYC\" due to constraints\n"
                        },
                        {
                          "text": "Successfully handled existing connection for \"Ball BIHYB → Build BIHYC\"\n"
                        },
                        {
                          "text": "  ℹ️  Connection tracking deprecated: 3073 → 3074 (auto-deleted via CASCADE)\n"
                        },
                        {
                          "text": "Connection creation failed for Ball BIHYB → Build BIHYC, checking if it exists...\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Cleaning 6 tracked entity IDs...\n"
                        },
                        {
                          "text": "  ℹ️  Connections will be auto-deleted via CASCADE DELETE\n"
                        },
                        {
                          "text": "  ✓ Deleted entity ID: 3072 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  ✓ Deleted entity ID: 3073 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  ✓ Deleted entity ID: 3074 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  ✓ Deleted entity ID: 3075 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  ✓ Deleted entity ID: 3076 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  ✓ Deleted entity ID: 3077 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  Fallback cleanup for 6 named entities...\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 33ms:\n"
                        },
                        {
                          "text": "  • Entities deleted: 6 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-27T18:49:46.407Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-3fc48-te-transitive-relationships-chromium/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts",
                        "column": 67,
                        "line": 98
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "0f325a1a01063f2777e1-06fb9657b4a5cf096857",
              "file": "comparisons.spec.ts",
              "line": 152,
              "column": 7
            },
            {
              "title": "should calculate complex multi-hop paths",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 3,
                      "parallelIndex": 0,
                      "status": "failed",
                      "duration": 29863,
                      "error": {
                        "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Build JSKRC\")').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Build JSKRC\")').first()\u001b[22m\n",
                        "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Build JSKRC\")').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Build JSKRC\")').first()\u001b[22m\n\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:98:67",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts",
                          "column": 67,
                          "line": 98
                        },
                        "snippet": "   96 |         // Verify connection was created - check for both entity names in the connection list\n   97 |         await expect(page.locator(`:text(\"${conn.from}\")`).first()).toBeVisible({ timeout: 10000 });\n>  98 |         await expect(page.locator(`:text(\"${conn.to}\")`).first()).toBeVisible({ timeout: 10000 });\n      |                                                                   ^\n   99 |       } catch (error: any) {\n  100 |         // If connection creation fails, check if it already exists\n  101 |         console.log(`Connection creation failed for ${conn.from} → ${conn.to}, checking if it exists...`);"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts",
                            "column": 67,
                            "line": 98
                          },
                          "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Build JSKRC\")').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Build JSKRC\")').first()\u001b[22m\n\n\n   96 |         // Verify connection was created - check for both entity names in the connection list\n   97 |         await expect(page.locator(`:text(\"${conn.from}\")`).first()).toBeVisible({ timeout: 10000 });\n>  98 |         await expect(page.locator(`:text(\"${conn.to}\")`).first()).toBeVisible({ timeout: 10000 });\n      |                                                                   ^\n   99 |       } catch (error: any) {\n  100 |         // If connection creation fails, check if it already exists\n  101 |         console.log(`Connection creation failed for ${conn.from} → ${conn.to}, checking if it exists...`);\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:98:67"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 130 total entities in database\n"
                        },
                        {
                          "text": "  Identified 0 test entities to delete\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 30ms:\n"
                        },
                        {
                          "text": "  • Initial entities: 130\n"
                        },
                        {
                          "text": "  • Entities deleted: 0\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 130\n"
                        },
                        {
                          "text": "  • Net reduction: 0\n"
                        },
                        {
                          "text": "Creating test entities for comparison tests...\n"
                        },
                        {
                          "text": "Creating entity: Human JSKRA\n"
                        },
                        {
                          "text": "Setting up API monitoring for entity creation...\n"
                        },
                        {
                          "text": "Submitting entity creation form...\n"
                        },
                        {
                          "text": "Waiting for API response...\n"
                        },
                        {
                          "text": "Entity API Response: http://localhost:8000/api/v1/entities/ - Status: 201\n"
                        },
                        {
                          "text": "Request Body: {\"name\":\"Human JSKRA\"}\n"
                        },
                        {
                          "text": "Response Body: {\"name\":\"Human JSKRA\",\"id\":3078,\"created_at\":\"2025-06-27T18:50:17.726341\",\"updated_at\":\"2025-06-27T18:50:17.726345\"}\n"
                        },
                        {
                          "text": "Response Headers: {\"access-control-allow-origin\":\"http://localhost:3000\",\"content-length\":\"116\",\"date\":\"Fri, 27 Jun 2025 18:50:16 GMT\",\"content-type\":\"application/json\",\"vary\":\"Origin\",\"server\":\"uvicorn\",\"access-control-allow-credentials\":\"true\"}\n"
                        },
                        {
                          "text": "API response successful: 201\n"
                        },
                        {
                          "text": "Verifying entity exists in database...\n"
                        },
                        {
                          "text": "✅ Entity confirmed in database: ID 3078\n"
                        },
                        {
                          "text": "Form closed successfully\n"
                        },
                        {
                          "text": "Waiting for entity to appear in UI...\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human JSKRA\" in 1160ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Human JSKRA (ID: 3078)\n"
                        },
                        {
                          "text": "Creating entity: Ball JSKRB\n"
                        },
                        {
                          "text": "Setting up API monitoring for entity creation...\n"
                        },
                        {
                          "text": "Submitting entity creation form...\n"
                        },
                        {
                          "text": "Waiting for API response...\n"
                        },
                        {
                          "text": "Entity API Response: http://localhost:8000/api/v1/entities/ - Status: 201\n"
                        },
                        {
                          "text": "Request Body: {\"name\":\"Ball JSKRB\"}\n"
                        },
                        {
                          "text": "Response Body: {\"name\":\"Ball JSKRB\",\"id\":3079,\"created_at\":\"2025-06-27T18:50:19.357248\",\"updated_at\":\"2025-06-27T18:50:19.357251\"}\n"
                        },
                        {
                          "text": "Response Headers: {\"access-control-allow-origin\":\"http://localhost:3000\",\"content-length\":\"115\",\"date\":\"Fri, 27 Jun 2025 18:50:18 GMT\",\"content-type\":\"application/json\",\"vary\":\"Origin\",\"server\":\"uvicorn\",\"access-control-allow-credentials\":\"true\"}\n"
                        },
                        {
                          "text": "API response successful: 201\n"
                        },
                        {
                          "text": "Verifying entity exists in database...\n"
                        },
                        {
                          "text": "✅ Entity confirmed in database: ID 3079\n"
                        },
                        {
                          "text": "Form closed successfully\n"
                        },
                        {
                          "text": "Waiting for entity to appear in UI...\n"
                        },
                        {
                          "text": "Entity creation completed for \"Ball JSKRB\" in 1119ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Ball JSKRB (ID: 3079)\n"
                        },
                        {
                          "text": "Creating entity: Build JSKRC\n"
                        },
                        {
                          "text": "Setting up API monitoring for entity creation...\n"
                        },
                        {
                          "text": "Submitting entity creation form...\n"
                        },
                        {
                          "text": "Waiting for API response...\n"
                        },
                        {
                          "text": "Entity API Response: http://localhost:8000/api/v1/entities/ - Status: 201\n"
                        },
                        {
                          "text": "Request Body: {\"name\":\"Build JSKRC\"}\n"
                        },
                        {
                          "text": "Response Body: {\"name\":\"Build JSKRC\",\"id\":3080,\"created_at\":\"2025-06-27T18:50:20.992328\",\"updated_at\":\"2025-06-27T18:50:20.992333\"}\n"
                        },
                        {
                          "text": "Response Headers: {\"access-control-allow-origin\":\"http://localhost:3000\",\"content-length\":\"116\",\"date\":\"Fri, 27 Jun 2025 18:50:20 GMT\",\"content-type\":\"application/json\",\"vary\":\"Origin\",\"server\":\"uvicorn\",\"access-control-allow-credentials\":\"true\"}\n"
                        },
                        {
                          "text": "API response successful: 201\n"
                        },
                        {
                          "text": "Verifying entity exists in database...\n"
                        },
                        {
                          "text": "✅ Entity confirmed in database: ID 3080\n"
                        },
                        {
                          "text": "Form closed successfully\n"
                        },
                        {
                          "text": "Waiting for entity to appear in UI...\n"
                        },
                        {
                          "text": "Entity creation completed for \"Build JSKRC\" in 1115ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Build JSKRC (ID: 3080)\n"
                        },
                        {
                          "text": "Creating entity: Car JSKRD\n"
                        },
                        {
                          "text": "Setting up API monitoring for entity creation...\n"
                        },
                        {
                          "text": "Submitting entity creation form...\n"
                        },
                        {
                          "text": "Waiting for API response...\n"
                        },
                        {
                          "text": "Entity API Response: http://localhost:8000/api/v1/entities/ - Status: 201\n"
                        },
                        {
                          "text": "Request Body: {\"name\":\"Car JSKRD\"}\n"
                        },
                        {
                          "text": "Response Body: {\"name\":\"Car JSKRD\",\"id\":3081,\"created_at\":\"2025-06-27T18:50:22.724748\",\"updated_at\":\"2025-06-27T18:50:22.724752\"}\n"
                        },
                        {
                          "text": "Response Headers: {\"access-control-allow-origin\":\"http://localhost:3000\",\"content-length\":\"114\",\"date\":\"Fri, 27 Jun 2025 18:50:21 GMT\",\"content-type\":\"application/json\",\"vary\":\"Origin\",\"server\":\"uvicorn\",\"access-control-allow-credentials\":\"true\"}\n"
                        },
                        {
                          "text": "API response successful: 201\n"
                        },
                        {
                          "text": "Verifying entity exists in database...\n"
                        },
                        {
                          "text": "✅ Entity confirmed in database: ID 3081\n"
                        },
                        {
                          "text": "Form closed successfully\n"
                        },
                        {
                          "text": "Waiting for entity to appear in UI...\n"
                        },
                        {
                          "text": "Entity creation completed for \"Car JSKRD\" in 1228ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Car JSKRD (ID: 3081)\n"
                        },
                        {
                          "text": "Creating entity: Eleph JSKRE\n"
                        },
                        {
                          "text": "Setting up API monitoring for entity creation...\n"
                        },
                        {
                          "text": "Submitting entity creation form...\n"
                        },
                        {
                          "text": "Waiting for API response...\n"
                        },
                        {
                          "text": "Entity API Response: http://localhost:8000/api/v1/entities/ - Status: 201\n"
                        },
                        {
                          "text": "Request Body: {\"name\":\"Eleph JSKRE\"}\n"
                        },
                        {
                          "text": "Response Body: {\"name\":\"Eleph JSKRE\",\"id\":3082,\"created_at\":\"2025-06-27T18:50:24.459185\",\"updated_at\":\"2025-06-27T18:50:24.459190\"}\n"
                        },
                        {
                          "text": "Response Headers: {\"access-control-allow-origin\":\"http://localhost:3000\",\"content-length\":\"116\",\"date\":\"Fri, 27 Jun 2025 18:50:23 GMT\",\"content-type\":\"application/json\",\"vary\":\"Origin\",\"server\":\"uvicorn\",\"access-control-allow-credentials\":\"true\"}\n"
                        },
                        {
                          "text": "API response successful: 201\n"
                        },
                        {
                          "text": "Verifying entity exists in database...\n"
                        },
                        {
                          "text": "✅ Entity confirmed in database: ID 3082\n"
                        },
                        {
                          "text": "Form closed successfully\n"
                        },
                        {
                          "text": "Waiting for entity to appear in UI...\n"
                        },
                        {
                          "text": "Entity creation completed for \"Eleph JSKRE\" in 1225ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Eleph JSKRE (ID: 3082)\n"
                        },
                        {
                          "text": "Creating entity: Mouse JSKRF\n"
                        },
                        {
                          "text": "Setting up API monitoring for entity creation...\n"
                        },
                        {
                          "text": "Submitting entity creation form...\n"
                        },
                        {
                          "text": "Waiting for API response...\n"
                        },
                        {
                          "text": "Entity API Response: http://localhost:8000/api/v1/entities/ - Status: 201\n"
                        },
                        {
                          "text": "Request Body: {\"name\":\"Mouse JSKRF\"}\n"
                        },
                        {
                          "text": "Response Body: {\"name\":\"Mouse JSKRF\",\"id\":3083,\"created_at\":\"2025-06-27T18:50:26.174037\",\"updated_at\":\"2025-06-27T18:50:26.174040\"}\n"
                        },
                        {
                          "text": "Response Headers: {\"access-control-allow-origin\":\"http://localhost:3000\",\"content-length\":\"116\",\"date\":\"Fri, 27 Jun 2025 18:50:26 GMT\",\"content-type\":\"application/json\",\"vary\":\"Origin\",\"server\":\"uvicorn\",\"access-control-allow-credentials\":\"true\"}\n"
                        },
                        {
                          "text": "API response successful: 201\n"
                        },
                        {
                          "text": "Verifying entity exists in database...\n"
                        },
                        {
                          "text": "✅ Entity confirmed in database: ID 3083\n"
                        },
                        {
                          "text": "Form closed successfully\n"
                        },
                        {
                          "text": "Waiting for entity to appear in UI...\n"
                        },
                        {
                          "text": "Entity creation completed for \"Mouse JSKRF\" in 1203ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Mouse JSKRF (ID: 3083)\n"
                        },
                        {
                          "text": "Creating test connections...\n"
                        },
                        {
                          "text": "Creating connection: Human JSKRA → Ball JSKRB (10.0x)\n"
                        },
                        {
                          "text": "Selecting AutoComplete option: Human JSKRA\n"
                        },
                        {
                          "text": "Successfully selected: Human JSKRA using .autocomplete-dropdown/.autocomplete-option\n"
                        },
                        {
                          "text": "Selecting AutoComplete option: Ball JSKRB\n"
                        },
                        {
                          "text": "Successfully selected: Ball JSKRB using .autocomplete-dropdown/.autocomplete-option\n"
                        },
                        {
                          "text": "Selecting unit: Length\n"
                        },
                        {
                          "text": "Selected unit: Length (m)\n"
                        },
                        {
                          "text": "Filling multiplier: 10.0\n"
                        },
                        {
                          "text": "Waiting for submit button to be enabled...\n"
                        },
                        {
                          "text": "Setting up API monitoring for connection creation...\n"
                        },
                        {
                          "text": "Submitting connection creation form...\n"
                        },
                        {
                          "text": "Waiting for API response...\n"
                        },
                        {
                          "text": "Connection API response: 201\n"
                        },
                        {
                          "text": "API response successful: 201\n"
                        },
                        {
                          "text": "Connection created with ID: 741\n"
                        },
                        {
                          "text": "Verifying connection exists in database...\n"
                        },
                        {
                          "text": "✅ Connection confirmed in database: ID 741 (Human JSKRA[3078] → Ball JSKRB[3079] × 10.0)\n"
                        },
                        {
                          "text": "Form closed successfully\n"
                        },
                        {
                          "text": "Connection creation completed for \"Human JSKRA → Ball JSKRB\" in 4055ms\n"
                        },
                        {
                          "text": "  ℹ️  Connection tracking deprecated: 3078 → 3079 (auto-deleted via CASCADE)\n"
                        },
                        {
                          "text": "Creating connection: Ball JSKRB → Build JSKRC (50.0x)\n"
                        },
                        {
                          "text": "Selecting AutoComplete option: Ball JSKRB\n"
                        },
                        {
                          "text": "Successfully selected: Ball JSKRB using .autocomplete-dropdown/.autocomplete-option\n"
                        },
                        {
                          "text": "Selecting AutoComplete option: Build JSKRC\n"
                        },
                        {
                          "text": "Successfully selected: Build JSKRC using .autocomplete-dropdown/.autocomplete-option\n"
                        },
                        {
                          "text": "Selecting unit: Length\n"
                        },
                        {
                          "text": "Selected unit: Length (m)\n"
                        },
                        {
                          "text": "Filling multiplier: 50.0\n"
                        },
                        {
                          "text": "Waiting for submit button to be enabled...\n"
                        },
                        {
                          "text": "Setting up API monitoring for connection creation...\n"
                        },
                        {
                          "text": "Submitting connection creation form...\n"
                        },
                        {
                          "text": "Waiting for API response...\n"
                        },
                        {
                          "text": "Connection API response: 400\n"
                        },
                        {
                          "text": "Connection creation failed due to constraints, assuming connection exists...\n"
                        },
                        {
                          "text": "Skipping connection creation for \"Ball JSKRB → Build JSKRC\" due to constraints\n"
                        },
                        {
                          "text": "Successfully handled existing connection for \"Ball JSKRB → Build JSKRC\"\n"
                        },
                        {
                          "text": "  ℹ️  Connection tracking deprecated: 3079 → 3080 (auto-deleted via CASCADE)\n"
                        },
                        {
                          "text": "Connection creation failed for Ball JSKRB → Build JSKRC, checking if it exists...\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Cleaning 6 tracked entity IDs...\n"
                        },
                        {
                          "text": "  ℹ️  Connections will be auto-deleted via CASCADE DELETE\n"
                        },
                        {
                          "text": "  ✓ Deleted entity ID: 3078 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  ✓ Deleted entity ID: 3079 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  ✓ Deleted entity ID: 3080 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  ✓ Deleted entity ID: 3081 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  ✓ Deleted entity ID: 3082 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  ✓ Deleted entity ID: 3083 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  Fallback cleanup for 6 named entities...\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 25ms:\n"
                        },
                        {
                          "text": "  • Entities deleted: 6 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-27T18:50:16.803Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-041ad-ate-complex-multi-hop-paths-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-041ad-ate-complex-multi-hop-paths-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-041ad-ate-complex-multi-hop-paths-chromium/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-041ad-ate-complex-multi-hop-paths-chromium/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts",
                        "column": 67,
                        "line": 98
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "0f325a1a01063f2777e1-75303118dff4d7b236bf",
              "file": "comparisons.spec.ts",
              "line": 166,
              "column": 7
            },
            {
              "title": "should handle reverse path calculations",
              "ok": false,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [
                    {
                      "workerIndex": 4,
                      "parallelIndex": 0,
                      "status": "failed",
                      "duration": 29789,
                      "error": {
                        "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Build DNXWC\")').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Build DNXWC\")').first()\u001b[22m\n",
                        "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Build DNXWC\")').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Build DNXWC\")').first()\u001b[22m\n\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:98:67",
                        "location": {
                          "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts",
                          "column": 67,
                          "line": 98
                        },
                        "snippet": "   96 |         // Verify connection was created - check for both entity names in the connection list\n   97 |         await expect(page.locator(`:text(\"${conn.from}\")`).first()).toBeVisible({ timeout: 10000 });\n>  98 |         await expect(page.locator(`:text(\"${conn.to}\")`).first()).toBeVisible({ timeout: 10000 });\n      |                                                                   ^\n   99 |       } catch (error: any) {\n  100 |         // If connection creation fails, check if it already exists\n  101 |         console.log(`Connection creation failed for ${conn.from} → ${conn.to}, checking if it exists...`);"
                      },
                      "errors": [
                        {
                          "location": {
                            "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts",
                            "column": 67,
                            "line": 98
                          },
                          "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator(':text(\"Build DNXWC\")').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator(':text(\"Build DNXWC\")').first()\u001b[22m\n\n\n   96 |         // Verify connection was created - check for both entity names in the connection list\n   97 |         await expect(page.locator(`:text(\"${conn.from}\")`).first()).toBeVisible({ timeout: 10000 });\n>  98 |         await expect(page.locator(`:text(\"${conn.to}\")`).first()).toBeVisible({ timeout: 10000 });\n      |                                                                   ^\n   99 |       } catch (error: any) {\n  100 |         // If connection creation fails, check if it already exists\n  101 |         console.log(`Connection creation failed for ${conn.from} → ${conn.to}, checking if it exists...`);\n    at /Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts:98:67"
                        }
                      ],
                      "stdout": [
                        {
                          "text": "🧹 Starting pre-test cleanup...\n"
                        },
                        {
                          "text": "  Found 130 total entities in database\n"
                        },
                        {
                          "text": "  Identified 0 test entities to delete\n"
                        },
                        {
                          "text": "🧹 Pre-test cleanup complete in 27ms:\n"
                        },
                        {
                          "text": "  • Initial entities: 130\n"
                        },
                        {
                          "text": "  • Entities deleted: 0\n"
                        },
                        {
                          "text": "  • Failed deletions: 0\n"
                        },
                        {
                          "text": "  • Final entity count: 130\n"
                        },
                        {
                          "text": "  • Net reduction: 0\n"
                        },
                        {
                          "text": "Creating test entities for comparison tests...\n"
                        },
                        {
                          "text": "Creating entity: Human DNXWA\n"
                        },
                        {
                          "text": "Setting up API monitoring for entity creation...\n"
                        },
                        {
                          "text": "Submitting entity creation form...\n"
                        },
                        {
                          "text": "Waiting for API response...\n"
                        },
                        {
                          "text": "Entity API Response: http://localhost:8000/api/v1/entities/ - Status: 201\n"
                        },
                        {
                          "text": "Request Body: {\"name\":\"Human DNXWA\"}\n"
                        },
                        {
                          "text": "Response Body: {\"name\":\"Human DNXWA\",\"id\":3084,\"created_at\":\"2025-06-27T18:50:48.125766\",\"updated_at\":\"2025-06-27T18:50:48.125770\"}\n"
                        },
                        {
                          "text": "Response Headers: {\"access-control-allow-origin\":\"http://localhost:3000\",\"content-length\":\"116\",\"date\":\"Fri, 27 Jun 2025 18:50:47 GMT\",\"content-type\":\"application/json\",\"vary\":\"Origin\",\"server\":\"uvicorn\",\"access-control-allow-credentials\":\"true\"}\n"
                        },
                        {
                          "text": "API response successful: 201\n"
                        },
                        {
                          "text": "Verifying entity exists in database...\n"
                        },
                        {
                          "text": "✅ Entity confirmed in database: ID 3084\n"
                        },
                        {
                          "text": "Form closed successfully\n"
                        },
                        {
                          "text": "Waiting for entity to appear in UI...\n"
                        },
                        {
                          "text": "Entity creation completed for \"Human DNXWA\" in 1158ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Human DNXWA (ID: 3084)\n"
                        },
                        {
                          "text": "Creating entity: Ball DNXWB\n"
                        },
                        {
                          "text": "Setting up API monitoring for entity creation...\n"
                        },
                        {
                          "text": "Submitting entity creation form...\n"
                        },
                        {
                          "text": "Waiting for API response...\n"
                        },
                        {
                          "text": "Entity API Response: http://localhost:8000/api/v1/entities/ - Status: 201\n"
                        },
                        {
                          "text": "Request Body: {\"name\":\"Ball DNXWB\"}\n"
                        },
                        {
                          "text": "Response Body: {\"name\":\"Ball DNXWB\",\"id\":3085,\"created_at\":\"2025-06-27T18:50:49.757165\",\"updated_at\":\"2025-06-27T18:50:49.757169\"}\n"
                        },
                        {
                          "text": "Response Headers: {\"access-control-allow-origin\":\"http://localhost:3000\",\"content-length\":\"115\",\"date\":\"Fri, 27 Jun 2025 18:50:49 GMT\",\"content-type\":\"application/json\",\"vary\":\"Origin\",\"server\":\"uvicorn\",\"access-control-allow-credentials\":\"true\"}\n"
                        },
                        {
                          "text": "API response successful: 201\n"
                        },
                        {
                          "text": "Verifying entity exists in database...\n"
                        },
                        {
                          "text": "✅ Entity confirmed in database: ID 3085\n"
                        },
                        {
                          "text": "Form closed successfully\n"
                        },
                        {
                          "text": "Waiting for entity to appear in UI...\n"
                        },
                        {
                          "text": "Entity creation completed for \"Ball DNXWB\" in 1124ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Ball DNXWB (ID: 3085)\n"
                        },
                        {
                          "text": "Creating entity: Build DNXWC\n"
                        },
                        {
                          "text": "Setting up API monitoring for entity creation...\n"
                        },
                        {
                          "text": "Submitting entity creation form...\n"
                        },
                        {
                          "text": "Waiting for API response...\n"
                        },
                        {
                          "text": "Entity API Response: http://localhost:8000/api/v1/entities/ - Status: 201\n"
                        },
                        {
                          "text": "Request Body: {\"name\":\"Build DNXWC\"}\n"
                        },
                        {
                          "text": "Response Body: {\"name\":\"Build DNXWC\",\"id\":3086,\"created_at\":\"2025-06-27T18:50:51.490698\",\"updated_at\":\"2025-06-27T18:50:51.490702\"}\n"
                        },
                        {
                          "text": "Response Headers: {\"access-control-allow-origin\":\"http://localhost:3000\",\"content-length\":\"116\",\"date\":\"Fri, 27 Jun 2025 18:50:50 GMT\",\"content-type\":\"application/json\",\"vary\":\"Origin\",\"server\":\"uvicorn\",\"access-control-allow-credentials\":\"true\"}\n"
                        },
                        {
                          "text": "API response successful: 201\n"
                        },
                        {
                          "text": "Verifying entity exists in database...\n"
                        },
                        {
                          "text": "✅ Entity confirmed in database: ID 3086\n"
                        },
                        {
                          "text": "Form closed successfully\n"
                        },
                        {
                          "text": "Waiting for entity to appear in UI...\n"
                        },
                        {
                          "text": "Entity creation completed for \"Build DNXWC\" in 1228ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Build DNXWC (ID: 3086)\n"
                        },
                        {
                          "text": "Creating entity: Car DNXWD\n"
                        },
                        {
                          "text": "Setting up API monitoring for entity creation...\n"
                        },
                        {
                          "text": "Submitting entity creation form...\n"
                        },
                        {
                          "text": "Waiting for API response...\n"
                        },
                        {
                          "text": "Entity API Response: http://localhost:8000/api/v1/entities/ - Status: 201\n"
                        },
                        {
                          "text": "Request Body: {\"name\":\"Car DNXWD\"}\n"
                        },
                        {
                          "text": "Response Body: {\"name\":\"Car DNXWD\",\"id\":3087,\"created_at\":\"2025-06-27T18:50:53.139937\",\"updated_at\":\"2025-06-27T18:50:53.139941\"}\n"
                        },
                        {
                          "text": "Response Headers: {\"access-control-allow-origin\":\"http://localhost:3000\",\"content-length\":\"114\",\"date\":\"Fri, 27 Jun 2025 18:50:52 GMT\",\"content-type\":\"application/json\",\"vary\":\"Origin\",\"server\":\"uvicorn\",\"access-control-allow-credentials\":\"true\"}\n"
                        },
                        {
                          "text": "API response successful: 201\n"
                        },
                        {
                          "text": "Verifying entity exists in database...\n"
                        },
                        {
                          "text": "✅ Entity confirmed in database: ID 3087\n"
                        },
                        {
                          "text": "Form closed successfully\n"
                        },
                        {
                          "text": "Waiting for entity to appear in UI...\n"
                        },
                        {
                          "text": "Entity creation completed for \"Car DNXWD\" in 1140ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Car DNXWD (ID: 3087)\n"
                        },
                        {
                          "text": "Creating entity: Eleph DNXWE\n"
                        },
                        {
                          "text": "Setting up API monitoring for entity creation...\n"
                        },
                        {
                          "text": "Submitting entity creation form...\n"
                        },
                        {
                          "text": "Waiting for API response...\n"
                        },
                        {
                          "text": "Entity API Response: http://localhost:8000/api/v1/entities/ - Status: 201\n"
                        },
                        {
                          "text": "Request Body: {\"name\":\"Eleph DNXWE\"}\n"
                        },
                        {
                          "text": "Response Body: {\"name\":\"Eleph DNXWE\",\"id\":3088,\"created_at\":\"2025-06-27T18:50:54.774540\",\"updated_at\":\"2025-06-27T18:50:54.774545\"}\n"
                        },
                        {
                          "text": "Response Headers: {\"access-control-allow-origin\":\"http://localhost:3000\",\"content-length\":\"116\",\"date\":\"Fri, 27 Jun 2025 18:50:53 GMT\",\"content-type\":\"application/json\",\"vary\":\"Origin\",\"server\":\"uvicorn\",\"access-control-allow-credentials\":\"true\"}\n"
                        },
                        {
                          "text": "API response successful: 201\n"
                        },
                        {
                          "text": "Verifying entity exists in database...\n"
                        },
                        {
                          "text": "✅ Entity confirmed in database: ID 3088\n"
                        },
                        {
                          "text": "Form closed successfully\n"
                        },
                        {
                          "text": "Waiting for entity to appear in UI...\n"
                        },
                        {
                          "text": "Entity creation completed for \"Eleph DNXWE\" in 1116ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Eleph DNXWE (ID: 3088)\n"
                        },
                        {
                          "text": "Creating entity: Mouse DNXWF\n"
                        },
                        {
                          "text": "Setting up API monitoring for entity creation...\n"
                        },
                        {
                          "text": "Submitting entity creation form...\n"
                        },
                        {
                          "text": "Waiting for API response...\n"
                        },
                        {
                          "text": "Entity API Response: http://localhost:8000/api/v1/entities/ - Status: 201\n"
                        },
                        {
                          "text": "Request Body: {\"name\":\"Mouse DNXWF\"}\n"
                        },
                        {
                          "text": "Response Body: {\"name\":\"Mouse DNXWF\",\"id\":3089,\"created_at\":\"2025-06-27T18:50:56.408145\",\"updated_at\":\"2025-06-27T18:50:56.408150\"}\n"
                        },
                        {
                          "text": "Response Headers: {\"access-control-allow-origin\":\"http://localhost:3000\",\"content-length\":\"116\",\"date\":\"Fri, 27 Jun 2025 18:50:55 GMT\",\"content-type\":\"application/json\",\"vary\":\"Origin\",\"server\":\"uvicorn\",\"access-control-allow-credentials\":\"true\"}\n"
                        },
                        {
                          "text": "API response successful: 201\n"
                        },
                        {
                          "text": "Verifying entity exists in database...\n"
                        },
                        {
                          "text": "✅ Entity confirmed in database: ID 3089\n"
                        },
                        {
                          "text": "Form closed successfully\n"
                        },
                        {
                          "text": "Waiting for entity to appear in UI...\n"
                        },
                        {
                          "text": "Entity creation completed for \"Mouse DNXWF\" in 1126ms\n"
                        },
                        {
                          "text": "  ✓ Created and tracked entity: Mouse DNXWF (ID: 3089)\n"
                        },
                        {
                          "text": "Creating test connections...\n"
                        },
                        {
                          "text": "Creating connection: Human DNXWA → Ball DNXWB (10.0x)\n"
                        },
                        {
                          "text": "Selecting AutoComplete option: Human DNXWA\n"
                        },
                        {
                          "text": "Successfully selected: Human DNXWA using .autocomplete-dropdown/.autocomplete-option\n"
                        },
                        {
                          "text": "Selecting AutoComplete option: Ball DNXWB\n"
                        },
                        {
                          "text": "Successfully selected: Ball DNXWB using .autocomplete-dropdown/.autocomplete-option\n"
                        },
                        {
                          "text": "Selecting unit: Length\n"
                        },
                        {
                          "text": "Selected unit: Length (m)\n"
                        },
                        {
                          "text": "Filling multiplier: 10.0\n"
                        },
                        {
                          "text": "Waiting for submit button to be enabled...\n"
                        },
                        {
                          "text": "Setting up API monitoring for connection creation...\n"
                        },
                        {
                          "text": "Submitting connection creation form...\n"
                        },
                        {
                          "text": "Waiting for API response...\n"
                        },
                        {
                          "text": "Connection API response: 201\n"
                        },
                        {
                          "text": "API response successful: 201\n"
                        },
                        {
                          "text": "Connection created with ID: 745\n"
                        },
                        {
                          "text": "Verifying connection exists in database...\n"
                        },
                        {
                          "text": "✅ Connection confirmed in database: ID 745 (Human DNXWA[3084] → Ball DNXWB[3085] × 10.0)\n"
                        },
                        {
                          "text": "Form closed successfully\n"
                        },
                        {
                          "text": "Connection creation completed for \"Human DNXWA → Ball DNXWB\" in 4076ms\n"
                        },
                        {
                          "text": "  ℹ️  Connection tracking deprecated: 3084 → 3085 (auto-deleted via CASCADE)\n"
                        },
                        {
                          "text": "Creating connection: Ball DNXWB → Build DNXWC (50.0x)\n"
                        },
                        {
                          "text": "Selecting AutoComplete option: Ball DNXWB\n"
                        },
                        {
                          "text": "Successfully selected: Ball DNXWB using .autocomplete-dropdown/.autocomplete-option\n"
                        },
                        {
                          "text": "Selecting AutoComplete option: Build DNXWC\n"
                        },
                        {
                          "text": "Successfully selected: Build DNXWC using .autocomplete-dropdown/.autocomplete-option\n"
                        },
                        {
                          "text": "Selecting unit: Length\n"
                        },
                        {
                          "text": "Selected unit: Length (m)\n"
                        },
                        {
                          "text": "Filling multiplier: 50.0\n"
                        },
                        {
                          "text": "Waiting for submit button to be enabled...\n"
                        },
                        {
                          "text": "Setting up API monitoring for connection creation...\n"
                        },
                        {
                          "text": "Submitting connection creation form...\n"
                        },
                        {
                          "text": "Waiting for API response...\n"
                        },
                        {
                          "text": "Connection API response: 400\n"
                        },
                        {
                          "text": "Connection creation failed due to constraints, assuming connection exists...\n"
                        },
                        {
                          "text": "Skipping connection creation for \"Ball DNXWB → Build DNXWC\" due to constraints\n"
                        },
                        {
                          "text": "Successfully handled existing connection for \"Ball DNXWB → Build DNXWC\"\n"
                        },
                        {
                          "text": "  ℹ️  Connection tracking deprecated: 3085 → 3086 (auto-deleted via CASCADE)\n"
                        },
                        {
                          "text": "Connection creation failed for Ball DNXWB → Build DNXWC, checking if it exists...\n"
                        },
                        {
                          "text": "🧹 Starting post-test cleanup...\n"
                        },
                        {
                          "text": "  Cleaning 6 tracked entity IDs...\n"
                        },
                        {
                          "text": "  ℹ️  Connections will be auto-deleted via CASCADE DELETE\n"
                        },
                        {
                          "text": "  ✓ Deleted entity ID: 3084 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  ✓ Deleted entity ID: 3085 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  ✓ Deleted entity ID: 3086 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  ✓ Deleted entity ID: 3087 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  ✓ Deleted entity ID: 3088 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  ✓ Deleted entity ID: 3089 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  Fallback cleanup for 6 named entities...\n"
                        },
                        {
                          "text": "🧹 Post-test cleanup complete in 33ms:\n"
                        },
                        {
                          "text": "  • Entities deleted: 6 (+ cascaded connections)\n"
                        },
                        {
                          "text": "  • Failed operations: 0\n"
                        },
                        {
                          "text": "  ✅ All cleanup operations successful\n"
                        }
                      ],
                      "stderr": [],
                      "retry": 0,
                      "startTime": "2025-06-27T18:50:47.199Z",
                      "annotations": [],
                      "attachments": [
                        {
                          "name": "screenshot",
                          "contentType": "image/png",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-07c84-e-reverse-path-calculations-chromium/test-failed-1.png"
                        },
                        {
                          "name": "video",
                          "contentType": "video/webm",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-07c84-e-reverse-path-calculations-chromium/video.webm"
                        },
                        {
                          "name": "error-context",
                          "contentType": "text/markdown",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-07c84-e-reverse-path-calculations-chromium/error-context.md"
                        },
                        {
                          "name": "trace",
                          "contentType": "application/zip",
                          "path": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results/comparisons-Entity-Compari-07c84-e-reverse-path-calculations-chromium/trace.zip"
                        }
                      ],
                      "errorLocation": {
                        "file": "/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/e2e/tests/comparisons.spec.ts",
                        "column": 67,
                        "line": 98
                      }
                    }
                  ],
                  "status": "unexpected"
                }
              ],
              "id": "0f325a1a01063f2777e1-983edd654911dead2884",
              "file": "comparisons.spec.ts",
              "line": 181,
              "column": 7
            },
            {
              "title": "should handle different unit entities with no connection",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-8dd14ae43fbe55b3930e",
              "file": "comparisons.spec.ts",
              "line": 195,
              "column": 7
            },
            {
              "title": "should handle same entity comparison",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-b8244a8bfedfe296979d",
              "file": "comparisons.spec.ts",
              "line": 211,
              "column": 7
            },
            {
              "title": "should handle custom from count values",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-4cbcfdb664b6aa97687a",
              "file": "comparisons.spec.ts",
              "line": 222,
              "column": 7
            },
            {
              "title": "should validate entity selection",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-d27a3ee7a10378599fab",
              "file": "comparisons.spec.ts",
              "line": 236,
              "column": 7
            },
            {
              "title": "should validate non-existent entities",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-dde12c529cc2fa44585a",
              "file": "comparisons.spec.ts",
              "line": 244,
              "column": 7
            },
            {
              "title": "should handle decimal precision in results",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-adbe99fcb7a761f29e9b",
              "file": "comparisons.spec.ts",
              "line": 253,
              "column": 7
            },
            {
              "title": "should clear form after successful comparison",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-4469f619db661f2ed843",
              "file": "comparisons.spec.ts",
              "line": 274,
              "column": 7
            },
            {
              "title": "should handle rapid successive comparisons",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-eb74e6b2805b080a35a6",
              "file": "comparisons.spec.ts",
              "line": 291,
              "column": 7
            },
            {
              "title": "should respect maximum path length limits",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-32c9662514bb00c6ac2a",
              "file": "comparisons.spec.ts",
              "line": 308,
              "column": 7
            },
            {
              "title": "should handle autocomplete in entity inputs",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-996a59fe8054d81075b7",
              "file": "comparisons.spec.ts",
              "line": 345,
              "column": 7
            },
            {
              "title": "should display comparison page correctly",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-09f169bb4fbfca7ebd21",
              "file": "comparisons.spec.ts",
              "line": 130,
              "column": 7
            },
            {
              "title": "should calculate direct relationships",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-ddc570b641e608d31e0d",
              "file": "comparisons.spec.ts",
              "line": 138,
              "column": 7
            },
            {
              "title": "should calculate transitive relationships",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-5489436aa17a4b7fbc85",
              "file": "comparisons.spec.ts",
              "line": 152,
              "column": 7
            },
            {
              "title": "should calculate complex multi-hop paths",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-ee0d07af83af076fd0a1",
              "file": "comparisons.spec.ts",
              "line": 166,
              "column": 7
            },
            {
              "title": "should handle reverse path calculations",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-72fe5a43e7281b7fa1e5",
              "file": "comparisons.spec.ts",
              "line": 181,
              "column": 7
            },
            {
              "title": "should handle different unit entities with no connection",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-4cf38eb6479501bb3f92",
              "file": "comparisons.spec.ts",
              "line": 195,
              "column": 7
            },
            {
              "title": "should handle same entity comparison",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-ad84851910c7df27242b",
              "file": "comparisons.spec.ts",
              "line": 211,
              "column": 7
            },
            {
              "title": "should handle custom from count values",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-3fc0c039e1b8229d21c4",
              "file": "comparisons.spec.ts",
              "line": 222,
              "column": 7
            },
            {
              "title": "should validate entity selection",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-e46039d79939d723b870",
              "file": "comparisons.spec.ts",
              "line": 236,
              "column": 7
            },
            {
              "title": "should validate non-existent entities",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-e489e6fe49d68af04ab1",
              "file": "comparisons.spec.ts",
              "line": 244,
              "column": 7
            },
            {
              "title": "should handle decimal precision in results",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-7dc564482b2588ac1d04",
              "file": "comparisons.spec.ts",
              "line": 253,
              "column": 7
            },
            {
              "title": "should clear form after successful comparison",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-09181f1aa7ef65aaf6ce",
              "file": "comparisons.spec.ts",
              "line": 274,
              "column": 7
            },
            {
              "title": "should handle rapid successive comparisons",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-130953dd609a31d64aa8",
              "file": "comparisons.spec.ts",
              "line": 291,
              "column": 7
            },
            {
              "title": "should respect maximum path length limits",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-849363998e7d171ea9db",
              "file": "comparisons.spec.ts",
              "line": 308,
              "column": 7
            },
            {
              "title": "should handle autocomplete in entity inputs",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-fa8c5bf7bc2e7f79b791",
              "file": "comparisons.spec.ts",
              "line": 345,
              "column": 7
            },
            {
              "title": "should display comparison page correctly",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-cc7d6aa7748091b4da50",
              "file": "comparisons.spec.ts",
              "line": 130,
              "column": 7
            },
            {
              "title": "should calculate direct relationships",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-98396ab11a92e1da98a3",
              "file": "comparisons.spec.ts",
              "line": 138,
              "column": 7
            },
            {
              "title": "should calculate transitive relationships",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-74838f8e5f8f53560cbc",
              "file": "comparisons.spec.ts",
              "line": 152,
              "column": 7
            },
            {
              "title": "should calculate complex multi-hop paths",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-e9dcd07c7ea93c2cd475",
              "file": "comparisons.spec.ts",
              "line": 166,
              "column": 7
            },
            {
              "title": "should handle reverse path calculations",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-0d3bf65d2af148c70122",
              "file": "comparisons.spec.ts",
              "line": 181,
              "column": 7
            },
            {
              "title": "should handle different unit entities with no connection",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-3a8a0803f811373c8495",
              "file": "comparisons.spec.ts",
              "line": 195,
              "column": 7
            },
            {
              "title": "should handle same entity comparison",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-91df89957049a9b6d444",
              "file": "comparisons.spec.ts",
              "line": 211,
              "column": 7
            },
            {
              "title": "should handle custom from count values",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-8f788e0136057c58b206",
              "file": "comparisons.spec.ts",
              "line": 222,
              "column": 7
            },
            {
              "title": "should validate entity selection",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-274a366a2aed2e4b66ac",
              "file": "comparisons.spec.ts",
              "line": 236,
              "column": 7
            },
            {
              "title": "should validate non-existent entities",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-6cce352380dc802e92a5",
              "file": "comparisons.spec.ts",
              "line": 244,
              "column": 7
            },
            {
              "title": "should handle decimal precision in results",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-9eefbfe3b6e95db8e363",
              "file": "comparisons.spec.ts",
              "line": 253,
              "column": 7
            },
            {
              "title": "should clear form after successful comparison",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-91753e5ae820da7e1a61",
              "file": "comparisons.spec.ts",
              "line": 274,
              "column": 7
            },
            {
              "title": "should handle rapid successive comparisons",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-944e794bf780c819258f",
              "file": "comparisons.spec.ts",
              "line": 291,
              "column": 7
            },
            {
              "title": "should respect maximum path length limits",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-bf85fff8e956886ac07c",
              "file": "comparisons.spec.ts",
              "line": 308,
              "column": 7
            },
            {
              "title": "should handle autocomplete in entity inputs",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "0f325a1a01063f2777e1-c35bbd3c255c6e77f874",
              "file": "comparisons.spec.ts",
              "line": 345,
              "column": 7
            }
          ]
        }
      ]
    },
    {
      "title": "connections.spec.ts",
      "file": "connections.spec.ts",
      "column": 0,
      "line": 0,
      "specs": [],
      "suites": [
        {
          "title": "Connection Management",
          "file": "connections.spec.ts",
          "line": 6,
          "column": 6,
          "specs": [
            {
              "title": "should display connection management page correctly",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-89cf8d0005d7ae37c3c7",
              "file": "connections.spec.ts",
              "line": 44,
              "column": 7
            },
            {
              "title": "should show and hide connection form correctly",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-1f5e0a82fd166256a222",
              "file": "connections.spec.ts",
              "line": 50,
              "column": 7
            },
            {
              "title": "should create bidirectional connections",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-7147252b7e2bb76da71a",
              "file": "connections.spec.ts",
              "line": 66,
              "column": 7
            },
            {
              "title": "should validate positive relationship values with real-time validation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-4389931d41d61e987072",
              "file": "connections.spec.ts",
              "line": 95,
              "column": 7
            },
            {
              "title": "should validate decimal precision with real-time validation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-10850e19363b0ff9346c",
              "file": "connections.spec.ts",
              "line": 139,
              "column": 7
            },
            {
              "title": "should prevent zero multiplier values",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-3625df6327a9fcf271ac",
              "file": "connections.spec.ts",
              "line": 181,
              "column": 7
            },
            {
              "title": "should validate same-unit connections only",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-8c3f4a4f0186bb588024",
              "file": "connections.spec.ts",
              "line": 213,
              "column": 7
            },
            {
              "title": "should handle autocomplete in entity selection",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-c4355c1749789a1f565a",
              "file": "connections.spec.ts",
              "line": 234,
              "column": 7
            },
            {
              "title": "should delete a connection successfully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-5641ac6865251a11ba7f",
              "file": "connections.spec.ts",
              "line": 279,
              "column": 7
            },
            {
              "title": "should handle connection form validation for required fields with real-time feedback",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-b09906742cede2456c84",
              "file": "connections.spec.ts",
              "line": 305,
              "column": 7
            },
            {
              "title": "should show real-time validation states across all connection form fields",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-9522dbd52555b4fec117",
              "file": "connections.spec.ts",
              "line": 359,
              "column": 7
            },
            {
              "title": "should prevent duplicate connections",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-a2d4c3e92f2a9e66de0f",
              "file": "connections.spec.ts",
              "line": 425,
              "column": 7
            },
            {
              "title": "should handle rapid connection creation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-3d7c0be7bf1892ba5445",
              "file": "connections.spec.ts",
              "line": 448,
              "column": 7
            },
            {
              "title": "should maintain connection list after page refresh",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-8ef74e2d22ee76d96ebc",
              "file": "connections.spec.ts",
              "line": 470,
              "column": 7
            },
            {
              "title": "should display connection management page correctly",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-b6c5a9e69b8e3a64823c",
              "file": "connections.spec.ts",
              "line": 44,
              "column": 7
            },
            {
              "title": "should show and hide connection form correctly",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-9354c5ece45689452f22",
              "file": "connections.spec.ts",
              "line": 50,
              "column": 7
            },
            {
              "title": "should create bidirectional connections",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-9f5a2be5c109466e3caf",
              "file": "connections.spec.ts",
              "line": 66,
              "column": 7
            },
            {
              "title": "should validate positive relationship values with real-time validation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-49e49d5270b2c898833a",
              "file": "connections.spec.ts",
              "line": 95,
              "column": 7
            },
            {
              "title": "should validate decimal precision with real-time validation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-d2e12139574eef4fb70c",
              "file": "connections.spec.ts",
              "line": 139,
              "column": 7
            },
            {
              "title": "should prevent zero multiplier values",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-6312f2558cdbf8c285ad",
              "file": "connections.spec.ts",
              "line": 181,
              "column": 7
            },
            {
              "title": "should validate same-unit connections only",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-23188e2309ede443f729",
              "file": "connections.spec.ts",
              "line": 213,
              "column": 7
            },
            {
              "title": "should handle autocomplete in entity selection",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-d222a1bb2b75cb8fa3c4",
              "file": "connections.spec.ts",
              "line": 234,
              "column": 7
            },
            {
              "title": "should delete a connection successfully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-6108fff9474bb4422d2b",
              "file": "connections.spec.ts",
              "line": 279,
              "column": 7
            },
            {
              "title": "should handle connection form validation for required fields with real-time feedback",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-e723355a0daa8c0870de",
              "file": "connections.spec.ts",
              "line": 305,
              "column": 7
            },
            {
              "title": "should show real-time validation states across all connection form fields",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-58e91db3df14f9685e4b",
              "file": "connections.spec.ts",
              "line": 359,
              "column": 7
            },
            {
              "title": "should prevent duplicate connections",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-d8dbe01708fa4d161591",
              "file": "connections.spec.ts",
              "line": 425,
              "column": 7
            },
            {
              "title": "should handle rapid connection creation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-12cbcef4239b401ae054",
              "file": "connections.spec.ts",
              "line": 448,
              "column": 7
            },
            {
              "title": "should maintain connection list after page refresh",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-04fa295f72d5f01599f9",
              "file": "connections.spec.ts",
              "line": 470,
              "column": 7
            },
            {
              "title": "should display connection management page correctly",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-3a83219f65d5c160e751",
              "file": "connections.spec.ts",
              "line": 44,
              "column": 7
            },
            {
              "title": "should show and hide connection form correctly",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-0eb551b4b22c895004ab",
              "file": "connections.spec.ts",
              "line": 50,
              "column": 7
            },
            {
              "title": "should create bidirectional connections",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-2bdc409db41585270be9",
              "file": "connections.spec.ts",
              "line": 66,
              "column": 7
            },
            {
              "title": "should validate positive relationship values with real-time validation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-2e14dc6f0c685d8908af",
              "file": "connections.spec.ts",
              "line": 95,
              "column": 7
            },
            {
              "title": "should validate decimal precision with real-time validation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-68b874b02eebf5411f39",
              "file": "connections.spec.ts",
              "line": 139,
              "column": 7
            },
            {
              "title": "should prevent zero multiplier values",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-62bf02938e24c360b22c",
              "file": "connections.spec.ts",
              "line": 181,
              "column": 7
            },
            {
              "title": "should validate same-unit connections only",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-06fe7ade318cd6c1d1e7",
              "file": "connections.spec.ts",
              "line": 213,
              "column": 7
            },
            {
              "title": "should handle autocomplete in entity selection",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-d3e881b0728a64594ac0",
              "file": "connections.spec.ts",
              "line": 234,
              "column": 7
            },
            {
              "title": "should delete a connection successfully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-5eef9928df27181c2779",
              "file": "connections.spec.ts",
              "line": 279,
              "column": 7
            },
            {
              "title": "should handle connection form validation for required fields with real-time feedback",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-8cab69022afb1c838d6c",
              "file": "connections.spec.ts",
              "line": 305,
              "column": 7
            },
            {
              "title": "should show real-time validation states across all connection form fields",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-a45806e7499f53dc3fbe",
              "file": "connections.spec.ts",
              "line": 359,
              "column": 7
            },
            {
              "title": "should prevent duplicate connections",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-6989eaec183fff268824",
              "file": "connections.spec.ts",
              "line": 425,
              "column": 7
            },
            {
              "title": "should handle rapid connection creation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-2ca10f4cc13fa70b66a8",
              "file": "connections.spec.ts",
              "line": 448,
              "column": 7
            },
            {
              "title": "should maintain connection list after page refresh",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "b748afb17871306c20bc-82bed2f3416f50137897",
              "file": "connections.spec.ts",
              "line": 470,
              "column": 7
            }
          ]
        }
      ]
    },
    {
      "title": "entities.spec.ts",
      "file": "entities.spec.ts",
      "column": 0,
      "line": 0,
      "specs": [],
      "suites": [
        {
          "title": "Entity Management",
          "file": "entities.spec.ts",
          "line": 6,
          "column": 6,
          "specs": [
            {
              "title": "should display entity management page correctly",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-7301148abc4cbc2e0449",
              "file": "entities.spec.ts",
              "line": 41,
              "column": 7
            },
            {
              "title": "should create a new entity successfully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-f9b8fa840e3bf5b25b15",
              "file": "entities.spec.ts",
              "line": 47,
              "column": 7
            },
            {
              "title": "should show and hide entity form correctly",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-2897fc9b86cd3e336219",
              "file": "entities.spec.ts",
              "line": 59,
              "column": 7
            },
            {
              "title": "should validate entity name requirements with real-time validation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-78165309abd2f93ac6d9",
              "file": "entities.spec.ts",
              "line": 74,
              "column": 7
            },
            {
              "title": "should accept valid entity names",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-25e1ba607c2e7a8df80d",
              "file": "entities.spec.ts",
              "line": 142,
              "column": 7
            },
            {
              "title": "should prevent duplicate entity names",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-6d5aceca1e1c0a9efeae",
              "file": "entities.spec.ts",
              "line": 154,
              "column": 7
            },
            {
              "title": "should delete an entity successfully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-f56026af2309c45f89b4",
              "file": "entities.spec.ts",
              "line": 172,
              "column": 7
            },
            {
              "title": "should edit an entity successfully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-e2fd5bf243ca7cc9093b",
              "file": "entities.spec.ts",
              "line": 189,
              "column": 7
            },
            {
              "title": "should handle entity form validation for name length",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-f1b1f32fd78b4bd1eb82",
              "file": "entities.spec.ts",
              "line": 205,
              "column": 7
            },
            {
              "title": "should handle entity form validation for special characters with real-time feedback",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-d9306e4ad89f23e0ead1",
              "file": "entities.spec.ts",
              "line": 228,
              "column": 7
            },
            {
              "title": "should provide real-time validation feedback during typing",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-e5f2472ce680390f0777",
              "file": "entities.spec.ts",
              "line": 252,
              "column": 7
            },
            {
              "title": "should handle empty entity list gracefully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-6c3de81d6fdd3a5a4406",
              "file": "entities.spec.ts",
              "line": 285,
              "column": 7
            },
            {
              "title": "should maintain entity list after page refresh",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-767a128aa307272989da",
              "file": "entities.spec.ts",
              "line": 294,
              "column": 7
            },
            {
              "title": "should handle rapid entity creation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-8f71abff0626d5153d1d",
              "file": "entities.spec.ts",
              "line": 309,
              "column": 7
            },
            {
              "title": "should maintain validation consistency across form interactions",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-001fc6127f420c5826f7",
              "file": "entities.spec.ts",
              "line": 330,
              "column": 7
            },
            {
              "title": "should display entity management page correctly",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-45e5f5d8b6d8d2e33d7e",
              "file": "entities.spec.ts",
              "line": 41,
              "column": 7
            },
            {
              "title": "should create a new entity successfully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-59035f7914db90cddccd",
              "file": "entities.spec.ts",
              "line": 47,
              "column": 7
            },
            {
              "title": "should show and hide entity form correctly",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-b623675f4b5bfbae7a2c",
              "file": "entities.spec.ts",
              "line": 59,
              "column": 7
            },
            {
              "title": "should validate entity name requirements with real-time validation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-f904ff9354448a431bcd",
              "file": "entities.spec.ts",
              "line": 74,
              "column": 7
            },
            {
              "title": "should accept valid entity names",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-b3390d70a987084c18d9",
              "file": "entities.spec.ts",
              "line": 142,
              "column": 7
            },
            {
              "title": "should prevent duplicate entity names",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-b62f88db87af155669b1",
              "file": "entities.spec.ts",
              "line": 154,
              "column": 7
            },
            {
              "title": "should delete an entity successfully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-b127091ea7dd91083349",
              "file": "entities.spec.ts",
              "line": 172,
              "column": 7
            },
            {
              "title": "should edit an entity successfully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-a1c01dc30b2439fd20ec",
              "file": "entities.spec.ts",
              "line": 189,
              "column": 7
            },
            {
              "title": "should handle entity form validation for name length",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-5aea8dad5724970fe76c",
              "file": "entities.spec.ts",
              "line": 205,
              "column": 7
            },
            {
              "title": "should handle entity form validation for special characters with real-time feedback",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-83138fd69bcb8a3070e8",
              "file": "entities.spec.ts",
              "line": 228,
              "column": 7
            },
            {
              "title": "should provide real-time validation feedback during typing",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-d08c863f6f75ebb386b0",
              "file": "entities.spec.ts",
              "line": 252,
              "column": 7
            },
            {
              "title": "should handle empty entity list gracefully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-8ea30d7d05f742f449cd",
              "file": "entities.spec.ts",
              "line": 285,
              "column": 7
            },
            {
              "title": "should maintain entity list after page refresh",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-1d37a4693a7af3e54cf6",
              "file": "entities.spec.ts",
              "line": 294,
              "column": 7
            },
            {
              "title": "should handle rapid entity creation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-aea741ce072c4c79a9de",
              "file": "entities.spec.ts",
              "line": 309,
              "column": 7
            },
            {
              "title": "should maintain validation consistency across form interactions",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-76b61ac1cbf9eb271752",
              "file": "entities.spec.ts",
              "line": 330,
              "column": 7
            },
            {
              "title": "should display entity management page correctly",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-955506e5559a9041e0bf",
              "file": "entities.spec.ts",
              "line": 41,
              "column": 7
            },
            {
              "title": "should create a new entity successfully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-08b525741cd9913c0b9d",
              "file": "entities.spec.ts",
              "line": 47,
              "column": 7
            },
            {
              "title": "should show and hide entity form correctly",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-2e8e8f5e83966aad66c7",
              "file": "entities.spec.ts",
              "line": 59,
              "column": 7
            },
            {
              "title": "should validate entity name requirements with real-time validation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-1dfc9184050068d58f3a",
              "file": "entities.spec.ts",
              "line": 74,
              "column": 7
            },
            {
              "title": "should accept valid entity names",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-c3070039b12a40938799",
              "file": "entities.spec.ts",
              "line": 142,
              "column": 7
            },
            {
              "title": "should prevent duplicate entity names",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-1c8636db88eae63bf945",
              "file": "entities.spec.ts",
              "line": 154,
              "column": 7
            },
            {
              "title": "should delete an entity successfully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-270d2c1473495aa2ed92",
              "file": "entities.spec.ts",
              "line": 172,
              "column": 7
            },
            {
              "title": "should edit an entity successfully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-b7042503d6c2e9d07a69",
              "file": "entities.spec.ts",
              "line": 189,
              "column": 7
            },
            {
              "title": "should handle entity form validation for name length",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-b3ed2c4c7c31efcd7c7d",
              "file": "entities.spec.ts",
              "line": 205,
              "column": 7
            },
            {
              "title": "should handle entity form validation for special characters with real-time feedback",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-805807b72833b49cf022",
              "file": "entities.spec.ts",
              "line": 228,
              "column": 7
            },
            {
              "title": "should provide real-time validation feedback during typing",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-52f9f01956f38bbb02c7",
              "file": "entities.spec.ts",
              "line": 252,
              "column": 7
            },
            {
              "title": "should handle empty entity list gracefully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-003d4deecefc521b3a75",
              "file": "entities.spec.ts",
              "line": 285,
              "column": 7
            },
            {
              "title": "should maintain entity list after page refresh",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-65e1763ab1da1ca3f61a",
              "file": "entities.spec.ts",
              "line": 294,
              "column": 7
            },
            {
              "title": "should handle rapid entity creation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-9cc958cba5f4ce175ba0",
              "file": "entities.spec.ts",
              "line": 309,
              "column": 7
            },
            {
              "title": "should maintain validation consistency across form interactions",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "e590b0e25242679a26f1-6c7d978fdd239fed0157",
              "file": "entities.spec.ts",
              "line": 330,
              "column": 7
            }
          ]
        }
      ]
    },
    {
      "title": "error-handling.spec.ts",
      "file": "error-handling.spec.ts",
      "column": 0,
      "line": 0,
      "specs": [],
      "suites": [
        {
          "title": "Error Handling and Edge Cases",
          "file": "error-handling.spec.ts",
          "line": 5,
          "column": 6,
          "specs": [
            {
              "title": "should handle API error gracefully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-ca859f9949a8c2a31afc",
              "file": "error-handling.spec.ts",
              "line": 24,
              "column": 7
            },
            {
              "title": "should handle network timeout gracefully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-e1f054bd740f09a6af63",
              "file": "error-handling.spec.ts",
              "line": 46,
              "column": 7
            },
            {
              "title": "should handle malformed API responses",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-cea6557acb8ba78fd76d",
              "file": "error-handling.spec.ts",
              "line": 65,
              "column": 7
            },
            {
              "title": "should handle server 500 errors",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-7a6c9fe05e80529e9036",
              "file": "error-handling.spec.ts",
              "line": 82,
              "column": 7
            },
            {
              "title": "should handle 404 errors for missing entities",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-8522d838261a58188975",
              "file": "error-handling.spec.ts",
              "line": 105,
              "column": 7
            },
            {
              "title": "should handle browser console errors",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-95d03b3f00a52226f367",
              "file": "error-handling.spec.ts",
              "line": 118,
              "column": 7
            },
            {
              "title": "should handle empty/whitespace-only inputs",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-ce960f9efe78d30397ad",
              "file": "error-handling.spec.ts",
              "line": 141,
              "column": 7
            },
            {
              "title": "should handle very long entity names",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-527a9644caab136ce976",
              "file": "error-handling.spec.ts",
              "line": 161,
              "column": 7
            },
            {
              "title": "should handle special Unicode characters",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-f587986b86018e1ce827",
              "file": "error-handling.spec.ts",
              "line": 174,
              "column": 7
            },
            {
              "title": "should handle concurrent user actions",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-ea8c11647c1abe3494aa",
              "file": "error-handling.spec.ts",
              "line": 204,
              "column": 7
            },
            {
              "title": "should handle page refresh during form submission",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-cd1f0c5d813ff8d2d537",
              "file": "error-handling.spec.ts",
              "line": 229,
              "column": 7
            },
            {
              "title": "should handle browser back during form editing",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-3f9babbfa1fe18458083",
              "file": "error-handling.spec.ts",
              "line": 245,
              "column": 7
            },
            {
              "title": "should handle extremely large numbers in connections",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-3d554af004bd5a9751e0",
              "file": "error-handling.spec.ts",
              "line": 261,
              "column": 7
            },
            {
              "title": "should handle scientific notation in connections",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-ca613b308615c4034ba0",
              "file": "error-handling.spec.ts",
              "line": 283,
              "column": 7
            },
            {
              "title": "should handle API error gracefully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-38aae48e3c532d330abc",
              "file": "error-handling.spec.ts",
              "line": 24,
              "column": 7
            },
            {
              "title": "should handle network timeout gracefully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-c7fa5cc7ea6fd919aa76",
              "file": "error-handling.spec.ts",
              "line": 46,
              "column": 7
            },
            {
              "title": "should handle malformed API responses",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-2aaab293510cc3086b91",
              "file": "error-handling.spec.ts",
              "line": 65,
              "column": 7
            },
            {
              "title": "should handle server 500 errors",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-a83d139b173e43665506",
              "file": "error-handling.spec.ts",
              "line": 82,
              "column": 7
            },
            {
              "title": "should handle 404 errors for missing entities",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-238d4b3a44d5f7591bca",
              "file": "error-handling.spec.ts",
              "line": 105,
              "column": 7
            },
            {
              "title": "should handle browser console errors",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-95762cca6409391a2152",
              "file": "error-handling.spec.ts",
              "line": 118,
              "column": 7
            },
            {
              "title": "should handle empty/whitespace-only inputs",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-eef9f59e04e027c4a69c",
              "file": "error-handling.spec.ts",
              "line": 141,
              "column": 7
            },
            {
              "title": "should handle very long entity names",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-fe8b417e6dc573df16d5",
              "file": "error-handling.spec.ts",
              "line": 161,
              "column": 7
            },
            {
              "title": "should handle special Unicode characters",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-45b1968fe759452e44d0",
              "file": "error-handling.spec.ts",
              "line": 174,
              "column": 7
            },
            {
              "title": "should handle concurrent user actions",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-c96368441054b965c34a",
              "file": "error-handling.spec.ts",
              "line": 204,
              "column": 7
            },
            {
              "title": "should handle page refresh during form submission",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-ee06fa93285bbb47b3f0",
              "file": "error-handling.spec.ts",
              "line": 229,
              "column": 7
            },
            {
              "title": "should handle browser back during form editing",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-bc01480e1dfa8f2dda53",
              "file": "error-handling.spec.ts",
              "line": 245,
              "column": 7
            },
            {
              "title": "should handle extremely large numbers in connections",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-4b9de9ff0248d911f70d",
              "file": "error-handling.spec.ts",
              "line": 261,
              "column": 7
            },
            {
              "title": "should handle scientific notation in connections",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-d94114d7ae42fec859d4",
              "file": "error-handling.spec.ts",
              "line": 283,
              "column": 7
            },
            {
              "title": "should handle API error gracefully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-36fb5c7303aaf4f2b6ae",
              "file": "error-handling.spec.ts",
              "line": 24,
              "column": 7
            },
            {
              "title": "should handle network timeout gracefully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-a5708fd52186e4719d78",
              "file": "error-handling.spec.ts",
              "line": 46,
              "column": 7
            },
            {
              "title": "should handle malformed API responses",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-ddf0c5e4dcaf69938e54",
              "file": "error-handling.spec.ts",
              "line": 65,
              "column": 7
            },
            {
              "title": "should handle server 500 errors",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-044c88f51fb0692ea3ca",
              "file": "error-handling.spec.ts",
              "line": 82,
              "column": 7
            },
            {
              "title": "should handle 404 errors for missing entities",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-69a5a5106f067c7f6c02",
              "file": "error-handling.spec.ts",
              "line": 105,
              "column": 7
            },
            {
              "title": "should handle browser console errors",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-0c9cd87c9c27888c6ef9",
              "file": "error-handling.spec.ts",
              "line": 118,
              "column": 7
            },
            {
              "title": "should handle empty/whitespace-only inputs",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-026d7a5a972aa2aa9647",
              "file": "error-handling.spec.ts",
              "line": 141,
              "column": 7
            },
            {
              "title": "should handle very long entity names",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-d47d27f6b6bcac66daff",
              "file": "error-handling.spec.ts",
              "line": 161,
              "column": 7
            },
            {
              "title": "should handle special Unicode characters",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-faeaaec4aaab3976ae38",
              "file": "error-handling.spec.ts",
              "line": 174,
              "column": 7
            },
            {
              "title": "should handle concurrent user actions",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-e50e79fe6ec0faa42dff",
              "file": "error-handling.spec.ts",
              "line": 204,
              "column": 7
            },
            {
              "title": "should handle page refresh during form submission",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-17732d458766d4c28db1",
              "file": "error-handling.spec.ts",
              "line": 229,
              "column": 7
            },
            {
              "title": "should handle browser back during form editing",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-2cb99ec1402289383fe6",
              "file": "error-handling.spec.ts",
              "line": 245,
              "column": 7
            },
            {
              "title": "should handle extremely large numbers in connections",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-63a565e819ec1f488c6f",
              "file": "error-handling.spec.ts",
              "line": 261,
              "column": 7
            },
            {
              "title": "should handle scientific notation in connections",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "1a72721d9ac5c938e1fe-2d6b4b2cca2e87610cae",
              "file": "error-handling.spec.ts",
              "line": 283,
              "column": 7
            }
          ]
        }
      ]
    },
    {
      "title": "navigation.spec.ts",
      "file": "navigation.spec.ts",
      "column": 0,
      "line": 0,
      "specs": [],
      "suites": [
        {
          "title": "Navigation",
          "file": "navigation.spec.ts",
          "line": 5,
          "column": 6,
          "specs": [
            {
              "title": "should load homepage successfully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "5d2868f7d23420c27cca-7c8d07ae606044b23924",
              "file": "navigation.spec.ts",
              "line": 15,
              "column": 7
            },
            {
              "title": "should navigate between all pages",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "5d2868f7d23420c27cca-1ae7e8272e895bd23cd5",
              "file": "navigation.spec.ts",
              "line": 20,
              "column": 7
            },
            {
              "title": "should highlight active navigation item",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "5d2868f7d23420c27cca-8c32442308196962cf34",
              "file": "navigation.spec.ts",
              "line": 39,
              "column": 7
            },
            {
              "title": "should handle browser back/forward navigation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "5d2868f7d23420c27cca-4d3dad8fe99836d99504",
              "file": "navigation.spec.ts",
              "line": 58,
              "column": 7
            },
            {
              "title": "should maintain navigation state on page refresh",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "5d2868f7d23420c27cca-821abdd12a244527b08d",
              "file": "navigation.spec.ts",
              "line": 74,
              "column": 7
            },
            {
              "title": "should handle invalid routes gracefully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "5d2868f7d23420c27cca-2bcb424aa225584d405e",
              "file": "navigation.spec.ts",
              "line": 87,
              "column": 7
            },
            {
              "title": "should load homepage successfully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "5d2868f7d23420c27cca-06c2ff18bde51594b953",
              "file": "navigation.spec.ts",
              "line": 15,
              "column": 7
            },
            {
              "title": "should navigate between all pages",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "5d2868f7d23420c27cca-58112e7c8bbad3ae8e79",
              "file": "navigation.spec.ts",
              "line": 20,
              "column": 7
            },
            {
              "title": "should highlight active navigation item",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "5d2868f7d23420c27cca-2fa59172e6173ea03053",
              "file": "navigation.spec.ts",
              "line": 39,
              "column": 7
            },
            {
              "title": "should handle browser back/forward navigation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "5d2868f7d23420c27cca-6fa7834a643219c7a58b",
              "file": "navigation.spec.ts",
              "line": 58,
              "column": 7
            },
            {
              "title": "should maintain navigation state on page refresh",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "5d2868f7d23420c27cca-a82860f00d65937995ae",
              "file": "navigation.spec.ts",
              "line": 74,
              "column": 7
            },
            {
              "title": "should handle invalid routes gracefully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "5d2868f7d23420c27cca-ccc1b38004a43af3a3d7",
              "file": "navigation.spec.ts",
              "line": 87,
              "column": 7
            },
            {
              "title": "should load homepage successfully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "5d2868f7d23420c27cca-2b1ef374bcd2b82f07c3",
              "file": "navigation.spec.ts",
              "line": 15,
              "column": 7
            },
            {
              "title": "should navigate between all pages",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "5d2868f7d23420c27cca-944f429d2e01bda3386f",
              "file": "navigation.spec.ts",
              "line": 20,
              "column": 7
            },
            {
              "title": "should highlight active navigation item",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "5d2868f7d23420c27cca-25afbb7005f1925d43a2",
              "file": "navigation.spec.ts",
              "line": 39,
              "column": 7
            },
            {
              "title": "should handle browser back/forward navigation",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "5d2868f7d23420c27cca-e0cc1f592dc5c5f852be",
              "file": "navigation.spec.ts",
              "line": 58,
              "column": 7
            },
            {
              "title": "should maintain navigation state on page refresh",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "5d2868f7d23420c27cca-364b7c19ddfb350b4e7b",
              "file": "navigation.spec.ts",
              "line": 74,
              "column": 7
            },
            {
              "title": "should handle invalid routes gracefully",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "5d2868f7d23420c27cca-5e3949d649b3832680ed",
              "file": "navigation.spec.ts",
              "line": 87,
              "column": 7
            }
          ]
        }
      ]
    },
    {
      "title": "setup-verification.spec.ts",
      "file": "setup-verification.spec.ts",
      "column": 0,
      "line": 0,
      "specs": [],
      "suites": [
        {
          "title": "Setup Verification",
          "file": "setup-verification.spec.ts",
          "line": 3,
          "column": 6,
          "specs": [
            {
              "title": "should verify basic Playwright setup",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "d3ae683f9066f919f969-20c0d3fde9535b962ab2",
              "file": "setup-verification.spec.ts",
              "line": 4,
              "column": 7
            },
            {
              "title": "should verify localhost accessibility",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "d3ae683f9066f919f969-8dd9f647ec1f7b0617eb",
              "file": "setup-verification.spec.ts",
              "line": 10,
              "column": 7
            },
            {
              "title": "should verify backend API accessibility",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "chromium",
                  "projectName": "chromium",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "d3ae683f9066f919f969-35dadd570e478a551ba8",
              "file": "setup-verification.spec.ts",
              "line": 22,
              "column": 7
            },
            {
              "title": "should verify basic Playwright setup",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "d3ae683f9066f919f969-f694b85f133ed63960e0",
              "file": "setup-verification.spec.ts",
              "line": 4,
              "column": 7
            },
            {
              "title": "should verify localhost accessibility",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "d3ae683f9066f919f969-fdb354fa095a51eb2506",
              "file": "setup-verification.spec.ts",
              "line": 10,
              "column": 7
            },
            {
              "title": "should verify backend API accessibility",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "firefox",
                  "projectName": "firefox",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "d3ae683f9066f919f969-853196aad21c5a0c5b1b",
              "file": "setup-verification.spec.ts",
              "line": 22,
              "column": 7
            },
            {
              "title": "should verify basic Playwright setup",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "d3ae683f9066f919f969-dac09f63f06fcdcd0e16",
              "file": "setup-verification.spec.ts",
              "line": 4,
              "column": 7
            },
            {
              "title": "should verify localhost accessibility",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "d3ae683f9066f919f969-7e2ef5d5b8013d8b70d7",
              "file": "setup-verification.spec.ts",
              "line": 10,
              "column": 7
            },
            {
              "title": "should verify backend API accessibility",
              "ok": true,
              "tags": [],
              "tests": [
                {
                  "timeout": 120000,
                  "annotations": [],
                  "expectedStatus": "passed",
                  "projectId": "webkit",
                  "projectName": "webkit",
                  "results": [],
                  "status": "skipped"
                }
              ],
              "id": "d3ae683f9066f919f969-93b27af1fc161ea7ebf4",
              "file": "setup-verification.spec.ts",
              "line": 22,
              "column": 7
            }
          ]
        }
      ]
    }
  ],
  "errors": [
    {
      "message": "\u001b[31mTesting stopped early after 5 maximum allowed failures.\u001b[39m"
    }
  ],
  "stats": {
    "startTime": "2025-06-27T18:48:44.958Z",
    "duration": 152296.79200000002,
    "expected": 0,
    "skipped": 196,
    "unexpected": 5,
    "flaky": 0
  }
}
