# Page snapshot

```yaml
- navigation:
  - link "SIMILE Entity Comparison System":
    - /url: /
    - heading "SIMILE" [level=1]
    - paragraph: Entity Comparison System
  - link "Compare":
    - /url: /
  - link "Entities":
    - /url: /entities
  - link "Connections":
    - /url: /connections
- main:
  - heading "Entity Comparison" [level=2]
  - heading "Compare Entities" [level=3]
  - text: Did you know that
  - spinbutton: "1"
  - combobox "entity": Human BMDHA
  - text: is as
  - combobox:
    - option "measure"
    - option "big"
    - option "tall" [selected]
    - option "heavy"
    - option "long"
    - option "voluminous"
  - text: as 500.0
  - combobox "entity": Build BMDHC
  - text: "?"
  - button "Clear"
  - text: Did you know that1Human BMDHAis as tall as500Build BMDHC?
  - heading "Calculation Path:" [level=4]
  - text: 1 Human BMDHAis10xBall BMDHB ↓ 2 Ball BMDHBis50xBuild BMDHC
  - heading "Final Calculation:" [level=5]
  - paragraph: 10 × 50 = 500
  - strong: "From:"
  - text: "Human BMDHA (ID: 147)"
  - strong: "To:"
  - text: "Build BMDHC (ID: 148)"
  - strong: "Unit:"
  - text: Length (m)
  - strong: "Path Length:"
  - text: 2 hops
```