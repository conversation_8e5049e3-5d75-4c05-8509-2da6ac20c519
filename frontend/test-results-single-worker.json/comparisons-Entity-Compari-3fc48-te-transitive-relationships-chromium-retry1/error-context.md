# Page snapshot

```yaml
- navigation:
  - link "SIMILE Entity Comparison System":
    - /url: /
    - heading "SIMILE" [level=1]
    - paragraph: Entity Comparison System
  - link "Compare":
    - /url: /
  - link "Entities":
    - /url: /entities
  - link "Connections":
    - /url: /connections
- main:
  - heading "Entity Comparison" [level=2]
  - heading "Compare Entities" [level=3]
  - text: Did you know that
  - spinbutton: "1"
  - combobox "entity": Human ONSBA
  - text: is as
  - combobox:
    - option "measure"
    - option "big"
    - option "tall" [selected]
    - option "heavy"
    - option "long"
    - option "voluminous"
  - text: as 500.0
  - combobox "entity": Build ONSBC
  - text: "?"
  - button "Clear"
  - text: Did you know that1Human ONSBAis as tall as500Build ONSBC?
  - heading "Calculation Path:" [level=4]
  - text: 1 Human ONSBAis10xBall ONSBB ↓ 2 Ball ONSBBis50xBuild ONSBC
  - heading "Final Calculation:" [level=5]
  - paragraph: 10 × 50 = 500
  - strong: "From:"
  - text: "Human ONSBA (ID: 153)"
  - strong: "To:"
  - text: "Build ONSBC (ID: 158)"
  - strong: "Unit:"
  - text: Length (m)
  - strong: "Path Length:"
  - text: 2 hops
```