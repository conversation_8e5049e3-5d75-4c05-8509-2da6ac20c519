# Page snapshot

```yaml
- navigation:
  - link "SIMILE Entity Comparison System":
    - /url: /
    - heading "SIMILE" [level=1]
    - paragraph: Entity Comparison System
  - link "Compare":
    - /url: /
  - link "Entities":
    - /url: /entities
  - link "Connections":
    - /url: /connections
- main:
  - heading "Entity Comparison" [level=2]
  - heading "Compare Entities" [level=3]
  - text: Did you know that
  - spinbutton: "1"
  - combobox "entity": Human UXUNA
  - text: is as
  - combobox:
    - option "measure"
    - option "big"
    - option "tall" [selected]
    - option "heavy"
    - option "long"
    - option "voluminous"
  - text: as 50.0
  - combobox "entity": Mouse UXUNF
  - text: "?"
  - button "Clear"
  - text: Did you know that1Human UXUNAis as tall as50Mouse UXUNF?
  - heading "Calculation Path:" [level=4]
  - text: 1 Human UXUNAis10xBall UXUNB ↓ 2 Ball UXUNBis50xBuild UXUNC ↓ 3 Build UXUNCis0.1xMouse UXUNF
  - heading "Final Calculation:" [level=5]
  - paragraph: 10 × 50 × 0.1 = 50
  - strong: "From:"
  - text: "Human UXUNA (ID: 159)"
  - strong: "To:"
  - text: "Mouse UXUNF (ID: 163)"
  - strong: "Unit:"
  - text: Length (m)
  - strong: "Path Length:"
  - text: 3 hops
```