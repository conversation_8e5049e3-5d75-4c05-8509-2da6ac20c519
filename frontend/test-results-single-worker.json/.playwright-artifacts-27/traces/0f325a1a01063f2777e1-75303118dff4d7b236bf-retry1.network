{"type":"resource-snapshot","snapshot":{"_monotonicTime":133417.115,"startedDateTime":"2025-07-06T02:27:01.232Z","time":13.081000000005588,"request":{"method":"GET","url":"http://localhost:8000/api/v1/entities","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/entities/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/entities/","_transferSize":-1},"cache":{},"timings":{"send":6.529999999998836,"wait":1.4109999999927823,"receive":0.1820000000006985,"dns":5.483000000007451,"connect":6.005000000004657,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":133426.264,"startedDateTime":"2025-07-06T02:27:01.241Z","time":4.886999999987893,"request":{"method":"GET","url":"http://localhost:8000/api/v1/entities/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"1753"},{"name":"content-type","value":"application/json"}],"content":{"size":1753,"mimeType":"application/json","_sha1":"5b29ff4df2baf95c2d53bebf17acd9a3bca08bcb.json"},"headersSize":-1,"bodySize":1753,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.5529999999853317,"wait":3.8950000000186265,"receive":0.22399999998742715,"dns":0.30299999998533167,"connect":0.46499999999650754,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":133433.431,"startedDateTime":"2025-07-06T02:27:01.249Z","time":1.0799999999871943,"request":{"method":"GET","url":"http://localhost:8000/api/v1/entities","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/entities/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/entities/","_transferSize":-1},"cache":{},"timings":{"send":0.24500000002444722,"wait":1.032999999995809,"receive":0.046999999991385266,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.1780000000144355},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":133435.024,"startedDateTime":"2025-07-06T02:27:01.250Z","time":4.741999999969266,"request":{"method":"GET","url":"http://localhost:8000/api/v1/entities/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"1753"},{"name":"content-type","value":"application/json"}],"content":{"size":1753,"mimeType":"application/json","_sha1":"5b29ff4df2baf95c2d53bebf17acd9a3bca08bcb.json"},"headersSize":-1,"bodySize":1753,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.635999999998603,"wait":3.7119999999995343,"receive":0.09399999998277053,"dns":0.375,"connect":0.5609999999869615,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":133444.887,"startedDateTime":"2025-07-06T02:27:01.260Z","time":1.2850000000034925,"request":{"method":"POST","url":"http://localhost:8000/api/v1/entities","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"34"}],"queryString":[],"headersSize":-1,"bodySize":34,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"****************************************.json"}},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/entities/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/entities/","_transferSize":-1},"cache":{},"timings":{"send":0.29099999999743886,"wait":1.2520000000076834,"receive":0.03299999999580905,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.19800000000395812},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":133445.357,"startedDateTime":"2025-07-06T02:27:01.260Z","time":3.1309999999648426,"request":{"method":"POST","url":"http://localhost:8000/api/v1/entities","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"34"}],"queryString":[],"headersSize":-1,"bodySize":34,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"40d57fd98eebc6622b7b7b3a267853e85e24a4ce.json"}},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/entities/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/entities/","_transferSize":-1},"cache":{},"timings":{"send":0.8049999999930151,"wait":1.9649999999965075,"receive":0.027999999991152436,"dns":0.41499999997904524,"connect":0.7229999999981374,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":133445.563,"startedDateTime":"2025-07-06T02:27:01.261Z","time":3.636999999929685,"request":{"method":"POST","url":"http://localhost:8000/api/v1/entities","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"34"}],"queryString":[],"headersSize":-1,"bodySize":34,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"76493207c95d1a371e3e02d64eff6436e624fe58.json"}},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/entities/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/entities/","_transferSize":-1},"cache":{},"timings":{"send":0.7859999999927823,"wait":2.4909999999799766,"receive":0.01900000000023283,"dns":0.3879999999771826,"connect":0.7389999999722932,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":133445.231,"startedDateTime":"2025-07-06T02:27:01.260Z","time":4.798999999999069,"request":{"method":"POST","url":"http://localhost:8000/api/v1/entities","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"33"}],"queryString":[],"headersSize":-1,"bodySize":33,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"8d896abbf265cd54e978215c9c79d366c36110ce.json"}},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/entities/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/entities/","_transferSize":-1},"cache":{},"timings":{"send":0.9970000000030268,"wait":3.1929999999993015,"receive":0.03200000000651926,"dns":0.6339999999909196,"connect":0.9400000000023283,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":133445.66,"startedDateTime":"2025-07-06T02:27:01.261Z","time":4.735000000015134,"request":{"method":"POST","url":"http://localhost:8000/api/v1/entities","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"34"}],"queryString":[],"headersSize":-1,"bodySize":34,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"f61e71f24fbb7386afb932a4f06616e2075ff144.json"}},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/entities/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/entities/","_transferSize":-1},"cache":{},"timings":{"send":1.0310000000172295,"wait":3.146999999997206,"receive":0.03500000000349246,"dns":0.5860000000102445,"connect":0.967000000004191,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":133445.456,"startedDateTime":"2025-07-06T02:27:01.261Z","time":5.204000000027008,"request":{"method":"POST","url":"http://localhost:8000/api/v1/entities","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"32"}],"queryString":[],"headersSize":-1,"bodySize":32,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"e291d94b6d9901c40eff39021a802148008d2599.json"}},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/entities/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/entities/","_transferSize":-1},"cache":{},"timings":{"send":0.9600000000209548,"wait":3.7109999999811407,"receive":0.015000000013969839,"dns":0.5720000000146683,"connect":0.9060000000172295,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":133449.025,"startedDateTime":"2025-07-06T02:27:01.264Z","time":5.529999999998836,"request":{"method":"POST","url":"http://localhost:8000/api/v1/entities/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"34"}],"queryString":[],"headersSize":-1,"bodySize":34,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"76493207c95d1a371e3e02d64eff6436e624fe58.json"}},"response":{"status":201,"statusText":"Created","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"115"},{"name":"content-type","value":"application/json"}],"content":{"size":115,"mimeType":"application/json","_sha1":"329808fef8459c3c3bc31a1a317257ae9370025f.json"},"headersSize":-1,"bodySize":115,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.7930000000051223,"wait":4.426000000006752,"receive":0.08199999999487773,"dns":0.3099999999976717,"connect":0.7119999999995343,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":133446.771,"startedDateTime":"2025-07-06T02:27:01.262Z","time":9.553000000014435,"request":{"method":"POST","url":"http://localhost:8000/api/v1/entities/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"34"}],"queryString":[],"headersSize":-1,"bodySize":34,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"****************************************.json"}},"response":{"status":201,"statusText":"Created","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"115"},{"name":"content-type","value":"application/json"}],"content":{"size":115,"mimeType":"application/json","_sha1":"5e4980c67bbff91e47e9a57931340b94bb62975e.json"},"headersSize":-1,"bodySize":115,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.46299999998882413,"wait":8.776000000012573,"receive":0.0629999999946449,"dns":0.3040000000037253,"connect":0.41000000000349246,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":133448.356,"startedDateTime":"2025-07-06T02:27:01.263Z","time":8.088999999978114,"request":{"method":"POST","url":"http://localhost:8000/api/v1/entities/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"34"}],"queryString":[],"headersSize":-1,"bodySize":34,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"40d57fd98eebc6622b7b7b3a267853e85e24a4ce.json"}},"response":{"status":201,"statusText":"Created","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"115"},{"name":"content-type","value":"application/json"}],"content":{"size":115,"mimeType":"application/json","_sha1":"398539a7b8c1d1bfb5ed544e558217e33d607bce.json"},"headersSize":-1,"bodySize":115,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.4280000000144355,"wait":7.430999999982305,"receive":0.03599999999278225,"dns":0.2479999999923166,"connect":0.3740000000107102,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":133450.414,"startedDateTime":"2025-07-06T02:27:01.266Z","time":6.779999999998836,"request":{"method":"POST","url":"http://localhost:8000/api/v1/entities/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"34"}],"queryString":[],"headersSize":-1,"bodySize":34,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"f61e71f24fbb7386afb932a4f06616e2075ff144.json"}},"response":{"status":201,"statusText":"Created","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"115"},{"name":"content-type","value":"application/json"}],"content":{"size":115,"mimeType":"application/json","_sha1":"9cafc91d32a393e36bcbce56199f5d56bd2b7fe3.json"},"headersSize":-1,"bodySize":115,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.5979999999981374,"wait":5.815000000002328,"receive":0.03599999999278225,"dns":0.3660000000090804,"connect":0.5629999999946449,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":133450.314,"startedDateTime":"2025-07-06T02:27:01.265Z","time":6.930000000051223,"request":{"method":"POST","url":"http://localhost:8000/api/v1/entities/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"33"}],"queryString":[],"headersSize":-1,"bodySize":33,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"8d896abbf265cd54e978215c9c79d366c36110ce.json"}},"response":{"status":201,"statusText":"Created","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"114"},{"name":"content-type","value":"application/json"}],"content":{"size":114,"mimeType":"application/json","_sha1":"2245d369d2f4e439eb2a18a7a208359519445645.json"},"headersSize":-1,"bodySize":114,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.635999999998603,"wait":5.982000000018161,"receive":0.04199999998672865,"dns":0.34000000002561137,"connect":0.5660000000207219,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":133450.519,"startedDateTime":"2025-07-06T02:27:01.266Z","time":6.87599999998929,"request":{"method":"POST","url":"http://localhost:8000/api/v1/entities/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"32"}],"queryString":[],"headersSize":-1,"bodySize":32,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"e291d94b6d9901c40eff39021a802148008d2599.json"}},"response":{"status":201,"statusText":"Created","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"113"},{"name":"content-type","value":"application/json"}],"content":{"size":113,"mimeType":"application/json","_sha1":"dba2da26bf03adef8aa7b9ba270fb63a22aebed8.json"},"headersSize":-1,"bodySize":113,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.5409999999974389,"wait":6.013000000006286,"receive":0.03599999999278225,"dns":0.3209999999962747,"connect":0.5059999999939464,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":133460.099,"startedDateTime":"2025-07-06T02:27:01.275Z","time":0.650999999983469,"request":{"method":"POST","url":"http://localhost:8000/api/v1/connections","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"69"}],"queryString":[],"headersSize":-1,"bodySize":69,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"327cf18ff64cf310ef0a9e61b4813aeffff0e840.json"}},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/connections/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/connections/","_transferSize":-1},"cache":{},"timings":{"send":0.06900000001769513,"wait":0.6279999999969732,"receive":0.022999999986495823,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.04900000002817251},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":133459.94,"startedDateTime":"2025-07-06T02:27:01.275Z","time":0.8910000000032596,"request":{"method":"POST","url":"http://localhost:8000/api/v1/connections","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"69"}],"queryString":[],"headersSize":-1,"bodySize":69,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"3e43eea3ad1909f6294352e836ea34b329066e55.json"}},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/connections/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/connections/","_transferSize":-1},"cache":{},"timings":{"send":0.1279999999969732,"wait":0.8790000000153668,"receive":0.011999999987892807,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.08699999999953434},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":133460.186,"startedDateTime":"2025-07-06T02:27:01.275Z","time":0.8619999999937136,"request":{"method":"POST","url":"http://localhost:8000/api/v1/connections","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"70"}],"queryString":[],"headersSize":-1,"bodySize":70,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"7ca56e555596d2e56202ca6ff0d03c223c6fb42e.json"}},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/connections/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/connections/","_transferSize":-1},"cache":{},"timings":{"send":0.10000000000582077,"wait":0.8429999999934807,"receive":0.01900000000023283,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.07000000000698492},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":133460.312,"startedDateTime":"2025-07-06T02:27:01.275Z","time":0.864000000001397,"request":{"method":"POST","url":"http://localhost:8000/api/v1/connections","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"70"}],"queryString":[],"headersSize":-1,"bodySize":70,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"017295145d078de749f8fade2c7e1b30decc15a7.json"}},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/connections/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/connections/","_transferSize":-1},"cache":{},"timings":{"send":0.09099999998579733,"wait":0.853000000002794,"receive":0.010999999998603016,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.07099999999627471},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":133461.407,"startedDateTime":"2025-07-06T02:27:01.277Z","time":5.638000000006286,"request":{"method":"POST","url":"http://localhost:8000/api/v1/connections/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"69"}],"queryString":[],"headersSize":-1,"bodySize":69,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"327cf18ff64cf310ef0a9e61b4813aeffff0e840.json"}},"response":{"status":201,"statusText":"Created","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"165"},{"name":"content-type","value":"application/json"}],"content":{"size":165,"mimeType":"application/json","_sha1":"6fb47ca418b6187b14a9e92c871ab106d6f8d634.json"},"headersSize":-1,"bodySize":165,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.10099999999511056,"wait":5.565000000002328,"receive":0.07300000000395812,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.07500000001164153},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":133461.537,"startedDateTime":"2025-07-06T02:27:01.277Z","time":6.365000000019791,"request":{"method":"POST","url":"http://localhost:8000/api/v1/connections/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"69"}],"queryString":[],"headersSize":-1,"bodySize":69,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"3e43eea3ad1909f6294352e836ea34b329066e55.json"}},"response":{"status":201,"statusText":"Created","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"165"},{"name":"content-type","value":"application/json"}],"content":{"size":165,"mimeType":"application/json","_sha1":"b783cb6d825fff88844cb9cd169e64b8dbea33a2.json"},"headersSize":-1,"bodySize":165,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.0659999999916181,"wait":6.3250000000116415,"receive":0.04000000000814907,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.046000000002095476},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":133461.618,"startedDateTime":"2025-07-06T02:27:01.277Z","time":7.463000000017928,"request":{"method":"POST","url":"http://localhost:8000/api/v1/connections/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"70"}],"queryString":[],"headersSize":-1,"bodySize":70,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"7ca56e555596d2e56202ca6ff0d03c223c6fb42e.json"}},"response":{"status":201,"statusText":"Created","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"164"},{"name":"content-type","value":"application/json"}],"content":{"size":164,"mimeType":"application/json","_sha1":"677d3163dd7c27233619c95765eccfc71b7f10fd.json"},"headersSize":-1,"bodySize":164,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.7509999999892898,"wait":6.119000000006054,"receive":0.046000000002095476,"dns":0.5789999999979045,"connect":0.7190000000118744,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":133461.677,"startedDateTime":"2025-07-06T02:27:01.277Z","time":7.539999999979045,"request":{"method":"POST","url":"http://localhost:8000/api/v1/connections/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"70"}],"queryString":[],"headersSize":-1,"bodySize":70,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"017295145d078de749f8fade2c7e1b30decc15a7.json"}},"response":{"status":201,"statusText":"Created","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"164"},{"name":"content-type","value":"application/json"}],"content":{"size":164,"mimeType":"application/json","_sha1":"5a92ba6a8aa1b3c0f4c408a0b31706d1a7c7b72e.json"},"headersSize":-1,"bodySize":164,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.6499999999941792,"wait":6.497999999992317,"receive":0.031000000017229468,"dns":0.41999999998370185,"connect":0.5909999999857973,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_frameref":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","_monotonicTime":133477.283,"startedDateTime":"2025-07-06T02:27:01.292Z","time":3.408,"request":{"method":"GET","url":"http://localhost:3000/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"Accept","value":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"},{"name":"Accept-Encoding","value":"gzip, deflate, br, zstd"},{"name":"Accept-Language","value":"en-US"},{"name":"Connection","value":"keep-alive"},{"name":"Host","value":"localhost:3000"},{"name":"Sec-Fetch-Dest","value":"document"},{"name":"Sec-Fetch-Mode","value":"navigate"},{"name":"Sec-Fetch-Site","value":"none"},{"name":"Sec-Fetch-User","value":"?1"},{"name":"Upgrade-Insecure-Requests","value":"1"},{"name":"User-Agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"sec-ch-ua","value":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"HeadlessChrome\";v=\"138\""},{"name":"sec-ch-ua-mobile","value":"?0"},{"name":"sec-ch-ua-platform","value":"\"Windows\""},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-isolation","value":"enabled"},{"name":"x-test-worker-id","value":"27"}],"queryString":[],"headersSize":749,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"Accept-Ranges","value":"bytes"},{"name":"Access-Control-Allow-Headers","value":"*"},{"name":"Access-Control-Allow-Methods","value":"*"},{"name":"Access-Control-Allow-Origin","value":"*"},{"name":"Connection","value":"keep-alive"},{"name":"Content-Length","value":"674"},{"name":"Content-Type","value":"text/html; charset=utf-8"},{"name":"Date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"ETag","value":"W/\"2a2-d+UIh9BpXbcZyToMQ2q0u2Vkiwc\""},{"name":"Keep-Alive","value":"timeout=5"},{"name":"Vary","value":"Accept-Encoding"},{"name":"X-Powered-By","value":"Express"}],"content":{"size":674,"mimeType":"text/html; charset=utf-8","compression":0,"_sha1":"77e50887d0695db719c93a0c436ab4bb65648b07.html"},"headersSize":373,"bodySize":674,"redirectURL":"","_transferSize":1047},"cache":{},"timings":{"dns":0.003,"connect":0.138,"ssl":1.411,"send":0,"wait":1.376,"receive":0.48},"pageref":"page@a986070605cbb1375f79f3bc4860ec2b","serverIPAddress":"[::1]","_serverPort":3000,"_securityDetails":{}}}
{"type":"resource-snapshot","snapshot":{"_frameref":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","_monotonicTime":133479.631,"startedDateTime":"2025-07-06T02:27:01.295Z","time":22.524,"request":{"method":"GET","url":"http://localhost:3000/static/js/bundle.js","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"Accept","value":"*/*"},{"name":"Accept-Encoding","value":"gzip, deflate, br, zstd"},{"name":"Accept-Language","value":"en-US"},{"name":"Connection","value":"keep-alive"},{"name":"Host","value":"localhost:3000"},{"name":"Referer","value":"http://localhost:3000/"},{"name":"Sec-Fetch-Dest","value":"script"},{"name":"Sec-Fetch-Mode","value":"no-cors"},{"name":"Sec-Fetch-Site","value":"same-origin"},{"name":"User-Agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"sec-ch-ua","value":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"HeadlessChrome\";v=\"138\""},{"name":"sec-ch-ua-mobile","value":"?0"},{"name":"sec-ch-ua-platform","value":"\"Windows\""},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-isolation","value":"enabled"},{"name":"x-test-worker-id","value":"27"}],"queryString":[],"headersSize":623,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"Accept-Ranges","value":"bytes"},{"name":"Access-Control-Allow-Headers","value":"*"},{"name":"Access-Control-Allow-Methods","value":"*"},{"name":"Access-Control-Allow-Origin","value":"*"},{"name":"Connection","value":"keep-alive"},{"name":"Content-Encoding","value":"br"},{"name":"Content-Type","value":"application/javascript; charset=utf-8"},{"name":"Date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"ETag","value":"W/\"245f7b-/+2KF+JnqQpNPg5X9L7eGnBeubI\""},{"name":"Keep-Alive","value":"timeout=5"},{"name":"Transfer-Encoding","value":"chunked"},{"name":"Vary","value":"Accept-Encoding"},{"name":"X-Powered-By","value":"Express"}],"content":{"size":-1,"mimeType":"application/javascript; charset=utf-8","compression":0},"headersSize":418,"bodySize":459484,"redirectURL":"","_transferSize":459902},"cache":{},"timings":{"dns":-1,"connect":-1,"ssl":-1,"send":0,"wait":19.248,"receive":3.276},"pageref":"page@a986070605cbb1375f79f3bc4860ec2b","serverIPAddress":"[::1]","_serverPort":3000,"_securityDetails":{}}}
{"type":"resource-snapshot","snapshot":{"_frameref":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","_monotonicTime":133579.565,"startedDateTime":"2025-07-06T02:27:01.395Z","time":4.242999999999999,"request":{"method":"GET","url":"http://localhost:8000/api/v1/entities/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"Accept","value":"application/json, text/plain, */*"},{"name":"Accept-Encoding","value":"gzip, deflate, br, zstd"},{"name":"Accept-Language","value":"en-US"},{"name":"Connection","value":"keep-alive"},{"name":"Host","value":"localhost:8000"},{"name":"Referer","value":"http://localhost:3000/"},{"name":"Sec-Fetch-Dest","value":"empty"},{"name":"Sec-Fetch-Mode","value":"cors"},{"name":"Sec-Fetch-Site","value":"same-site"},{"name":"User-Agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"sec-ch-ua","value":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"HeadlessChrome\";v=\"138\""},{"name":"sec-ch-ua-mobile","value":"?0"},{"name":"sec-ch-ua-platform","value":"\"Windows\""},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-isolation","value":"enabled"},{"name":"x-test-worker-id","value":"27"}],"queryString":[],"headersSize":644,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"content-length","value":"2446"},{"name":"content-type","value":"application/json"},{"name":"date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"server","value":"uvicorn"}],"content":{"size":2446,"mimeType":"application/json","compression":0,"_sha1":"04c2acad8552a71d68dbc60e585d99095347d7dd.json"},"headersSize":127,"bodySize":2446,"redirectURL":"","_transferSize":2573},"cache":{},"timings":{"dns":0.001,"connect":0.191,"ssl":1.248,"send":0,"wait":2.356,"receive":0.447},"pageref":"page@a986070605cbb1375f79f3bc4860ec2b","serverIPAddress":"[::1]","_serverPort":8000,"_securityDetails":{}}}
{"type":"resource-snapshot","snapshot":{"_frameref":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","_monotonicTime":133579.611,"startedDateTime":"2025-07-06T02:27:01.395Z","time":3.999,"request":{"method":"GET","url":"http://localhost:8000/api/v1/units/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"Accept","value":"application/json, text/plain, */*"},{"name":"Accept-Encoding","value":"gzip, deflate, br, zstd"},{"name":"Accept-Language","value":"en-US"},{"name":"Connection","value":"keep-alive"},{"name":"Host","value":"localhost:8000"},{"name":"Referer","value":"http://localhost:3000/"},{"name":"Sec-Fetch-Dest","value":"empty"},{"name":"Sec-Fetch-Mode","value":"cors"},{"name":"Sec-Fetch-Site","value":"same-site"},{"name":"User-Agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"sec-ch-ua","value":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"HeadlessChrome\";v=\"138\""},{"name":"sec-ch-ua-mobile","value":"?0"},{"name":"sec-ch-ua-platform","value":"\"Windows\""},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-isolation","value":"enabled"},{"name":"x-test-worker-id","value":"27"}],"queryString":[],"headersSize":641,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"content-length","value":"607"},{"name":"content-type","value":"application/json"},{"name":"date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"server","value":"uvicorn"}],"content":{"size":607,"mimeType":"application/json","compression":0,"_sha1":"ad2b685749d6e01cd886bbf041a3f16a1ef18bbf.json"},"headersSize":126,"bodySize":607,"redirectURL":"","_transferSize":733},"cache":{},"timings":{"dns":0.001,"connect":0.104,"ssl":1.137,"send":0,"wait":2.416,"receive":0.341},"pageref":"page@a986070605cbb1375f79f3bc4860ec2b","serverIPAddress":"[::1]","_serverPort":8000,"_securityDetails":{}}}
{"type":"resource-snapshot","snapshot":{"_frameref":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","_monotonicTime":133579.639,"startedDateTime":"2025-07-06T02:27:01.395Z","time":1.658,"request":{"method":"GET","url":"http://localhost:8000/api/v1/entities/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"Accept","value":"application/json, text/plain, */*"},{"name":"Accept-Encoding","value":"gzip, deflate, br, zstd"},{"name":"Accept-Language","value":"en-US"},{"name":"Connection","value":"keep-alive"},{"name":"Host","value":"localhost:8000"},{"name":"Referer","value":"http://localhost:3000/"},{"name":"Sec-Fetch-Dest","value":"empty"},{"name":"Sec-Fetch-Mode","value":"cors"},{"name":"Sec-Fetch-Site","value":"same-site"},{"name":"User-Agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"sec-ch-ua","value":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"HeadlessChrome\";v=\"138\""},{"name":"sec-ch-ua-mobile","value":"?0"},{"name":"sec-ch-ua-platform","value":"\"Windows\""},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-isolation","value":"enabled"},{"name":"x-test-worker-id","value":"27"}],"queryString":[],"headersSize":644,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"content-length","value":"2446"},{"name":"content-type","value":"application/json"},{"name":"date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"server","value":"uvicorn"}],"content":{"size":2446,"mimeType":"application/json","compression":0,"_sha1":"04c2acad8552a71d68dbc60e585d99095347d7dd.json"},"headersSize":127,"bodySize":2446,"redirectURL":"","_transferSize":2573},"cache":{},"timings":{"dns":-1,"connect":-1,"ssl":-1,"send":0,"wait":1.434,"receive":0.224},"pageref":"page@a986070605cbb1375f79f3bc4860ec2b","serverIPAddress":"[::1]","_serverPort":8000,"_securityDetails":{}}}
{"type":"resource-snapshot","snapshot":{"_frameref":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","_monotonicTime":133579.667,"startedDateTime":"2025-07-06T02:27:01.395Z","time":5.549,"request":{"method":"GET","url":"http://localhost:8000/api/v1/units/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"Accept","value":"application/json, text/plain, */*"},{"name":"Accept-Encoding","value":"gzip, deflate, br, zstd"},{"name":"Accept-Language","value":"en-US"},{"name":"Connection","value":"keep-alive"},{"name":"Host","value":"localhost:8000"},{"name":"Referer","value":"http://localhost:3000/"},{"name":"Sec-Fetch-Dest","value":"empty"},{"name":"Sec-Fetch-Mode","value":"cors"},{"name":"Sec-Fetch-Site","value":"same-site"},{"name":"User-Agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"sec-ch-ua","value":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"HeadlessChrome\";v=\"138\""},{"name":"sec-ch-ua-mobile","value":"?0"},{"name":"sec-ch-ua-platform","value":"\"Windows\""},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-isolation","value":"enabled"},{"name":"x-test-worker-id","value":"27"}],"queryString":[],"headersSize":641,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"content-length","value":"607"},{"name":"content-type","value":"application/json"},{"name":"date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"server","value":"uvicorn"}],"content":{"size":607,"mimeType":"application/json","compression":0,"_sha1":"ad2b685749d6e01cd886bbf041a3f16a1ef18bbf.json"},"headersSize":126,"bodySize":607,"redirectURL":"","_transferSize":733},"cache":{},"timings":{"dns":0.002,"connect":0.125,"ssl":3.661,"send":0,"wait":1.531,"receive":0.23},"pageref":"page@a986070605cbb1375f79f3bc4860ec2b","serverIPAddress":"[::1]","_serverPort":8000,"_securityDetails":{}}}
{"type":"resource-snapshot","snapshot":{"_frameref":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","_monotonicTime":146375.107,"startedDateTime":"2025-07-06T02:27:14.190Z","time":19.771,"request":{"method":"GET","url":"http://localhost:8000/api/v1/compare/?from=165&to=169&unit=1","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"Accept","value":"application/json, text/plain, */*"},{"name":"Accept-Encoding","value":"gzip, deflate, br, zstd"},{"name":"Accept-Language","value":"en-US"},{"name":"Connection","value":"keep-alive"},{"name":"Host","value":"localhost:8000"},{"name":"Referer","value":"http://localhost:3000/"},{"name":"Sec-Fetch-Dest","value":"empty"},{"name":"Sec-Fetch-Mode","value":"cors"},{"name":"Sec-Fetch-Site","value":"same-site"},{"name":"User-Agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"sec-ch-ua","value":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"HeadlessChrome\";v=\"138\""},{"name":"sec-ch-ua-mobile","value":"?0"},{"name":"sec-ch-ua-platform","value":"\"Windows\""},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-isolation","value":"enabled"},{"name":"x-test-worker-id","value":"27"}],"queryString":[{"name":"from","value":"165"},{"name":"to","value":"169"},{"name":"unit","value":"1"}],"headersSize":643,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"content-length","value":"825"},{"name":"content-type","value":"application/json"},{"name":"date","value":"Sun, 06 Jul 2025 02:27:13 GMT"},{"name":"server","value":"uvicorn"}],"content":{"size":825,"mimeType":"application/json","compression":0,"_sha1":"2f3301072104e45f1cb7388d760e669a83840d3a.json"},"headersSize":126,"bodySize":825,"redirectURL":"","_transferSize":951},"cache":{},"timings":{"dns":-1,"connect":-1,"ssl":-1,"send":0,"wait":19.369,"receive":0.402},"pageref":"page@a986070605cbb1375f79f3bc4860ec2b","serverIPAddress":"[::1]","_serverPort":8000,"_securityDetails":{}}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":161339.56,"startedDateTime":"2025-07-06T02:27:29.154Z","time":11.661999999982072,"request":{"method":"DELETE","url":"http://localhost:8000/api/v1/entities/167","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:28 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"content":{"size":41,"mimeType":"application/json","_sha1":"f3bc11351f85225c37b8b0d7d402c26ec4665a8f.json"},"headersSize":-1,"bodySize":41,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.7339999999967404,"wait":10.478999999992084,"receive":0.13800000000628643,"dns":0.3949999999895226,"connect":0.6499999999941792,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":161376.526,"startedDateTime":"2025-07-06T02:27:29.191Z","time":4.570000000006985,"request":{"method":"DELETE","url":"http://localhost:8000/api/v1/entities/165","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:28 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"content":{"size":41,"mimeType":"application/json","_sha1":"f3bc11351f85225c37b8b0d7d402c26ec4665a8f.json"},"headersSize":-1,"bodySize":41,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.15499999999883585,"wait":4.490000000019791,"receive":0.07999999998719431,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.10300000000279397},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":161406.371,"startedDateTime":"2025-07-06T02:27:29.221Z","time":4.839000000007218,"request":{"method":"DELETE","url":"http://localhost:8000/api/v1/entities/169","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:28 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"content":{"size":41,"mimeType":"application/json","_sha1":"f3bc11351f85225c37b8b0d7d402c26ec4665a8f.json"},"headersSize":-1,"bodySize":41,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.11599999997997656,"wait":4.7920000000158325,"receive":0.046999999991385266,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.08499999999185093},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":161437.739,"startedDateTime":"2025-07-06T02:27:29.252Z","time":4.989999999990687,"request":{"method":"DELETE","url":"http://localhost:8000/api/v1/entities/170","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:28 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"content":{"size":41,"mimeType":"application/json","_sha1":"f3bc11351f85225c37b8b0d7d402c26ec4665a8f.json"},"headersSize":-1,"bodySize":41,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.14600000000791624,"wait":4.925999999977648,"receive":0.06400000001303852,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.10499999998137355},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":161468.673,"startedDateTime":"2025-07-06T02:27:29.283Z","time":4.763000000006286,"request":{"method":"DELETE","url":"http://localhost:8000/api/v1/entities/166","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:28 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"content":{"size":41,"mimeType":"application/json","_sha1":"f3bc11351f85225c37b8b0d7d402c26ec4665a8f.json"},"headersSize":-1,"bodySize":41,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.13800000000628643,"wait":4.709000000002561,"receive":0.05400000000372529,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.09799999999813735},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":161500.199,"startedDateTime":"2025-07-06T02:27:29.315Z","time":5.130999999993946,"request":{"method":"DELETE","url":"http://localhost:8000/api/v1/entities/168","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:28 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"content":{"size":41,"mimeType":"application/json","_sha1":"f3bc11351f85225c37b8b0d7d402c26ec4665a8f.json"},"headersSize":-1,"bodySize":41,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.1339999999909196,"wait":5.084000000002561,"receive":0.046999999991385266,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.09299999999348074},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":161506.312,"startedDateTime":"2025-07-06T02:27:29.321Z","time":0.6879999999946449,"request":{"method":"GET","url":"http://localhost:8000/api/v1/entities","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:28 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/entities/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/entities/","_transferSize":-1},"cache":{},"timings":{"send":0.09200000000419095,"wait":0.6529999999911524,"receive":0.03500000000349246,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.06799999999930151},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":161507.295,"startedDateTime":"2025-07-06T02:27:29.322Z","time":3.5429999999760184,"request":{"method":"GET","url":"http://localhost:8000/api/v1/entities/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:28 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"1753"},{"name":"content-type","value":"application/json"}],"content":{"size":1753,"mimeType":"application/json","_sha1":"5b29ff4df2baf95c2d53bebf17acd9a3bca08bcb.json"},"headersSize":-1,"bodySize":1753,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.5269999999727588,"wait":2.6880000000237487,"receive":0.03999999997904524,"dns":0.3359999999811407,"connect":0.47899999999208376,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":161511.605,"startedDateTime":"2025-07-06T02:27:29.326Z","time":0.8070000000006985,"request":{"method":"GET","url":"http://localhost:8000/api/v1/entities","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:28 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/entities/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/entities/","_transferSize":-1},"cache":{},"timings":{"send":0.08799999998882413,"wait":0.7880000000004657,"receive":0.01900000000023283,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.0689999999885913},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":161512.687,"startedDateTime":"2025-07-06T02:27:29.327Z","time":2.981999999959953,"request":{"method":"GET","url":"http://localhost:8000/api/v1/entities/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:28 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"1753"},{"name":"content-type","value":"application/json"}],"content":{"size":1753,"mimeType":"application/json","_sha1":"5b29ff4df2baf95c2d53bebf17acd9a3bca08bcb.json"},"headersSize":-1,"bodySize":1753,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.4370000000053551,"wait":2.2669999999925494,"receive":0.0650000000023283,"dns":0.2569999999832362,"connect":0.3929999999818392,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":161516.988,"startedDateTime":"2025-07-06T02:27:29.332Z","time":0.4589999999734573,"request":{"method":"GET","url":"http://localhost:8000/api/v1/entities","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:28 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/entities/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/entities/","_transferSize":-1},"cache":{},"timings":{"send":0.08500000002095476,"wait":0.4429999999993015,"receive":0.0159999999741558,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.0629999999946449},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":161517.684,"startedDateTime":"2025-07-06T02:27:29.332Z","time":2.3030000000435393,"request":{"method":"GET","url":"http://localhost:8000/api/v1/entities/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:28 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"1753"},{"name":"content-type","value":"application/json"}],"content":{"size":1753,"mimeType":"application/json","_sha1":"5b29ff4df2baf95c2d53bebf17acd9a3bca08bcb.json"},"headersSize":-1,"bodySize":1753,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.4919999999983702,"wait":1.4990000000107102,"receive":0.05400000000372529,"dns":0.3030000000144355,"connect":0.44700000001466833,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
