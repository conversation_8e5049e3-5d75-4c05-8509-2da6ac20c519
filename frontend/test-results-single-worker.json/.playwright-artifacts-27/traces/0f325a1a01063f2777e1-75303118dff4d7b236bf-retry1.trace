{"version":8,"type":"context-options","origin":"library","browserName":"chromium","options":{"noDefaultViewport":false,"viewport":{"width":1280,"height":720},"ignoreHTTPSErrors":true,"javaScriptEnabled":true,"bypassCSP":true,"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36","locale":"en-US","extraHTTPHeaders":[{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"}],"offline":false,"deviceScaleFactor":1,"isMobile":false,"hasTouch":false,"colorScheme":"light","acceptDownloads":"accept","baseURL":"http://localhost:3000","recordVideo":{"dir":"/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/test-results-single-worker.json/.playwright-artifacts-27","size":{"width":800,"height":450}},"serviceWorkers":"allow","selectorEngines":[],"testIdAttributeName":"data-testid"},"platform":"darwin","wallTime":1751768821177,"monotonicTime":133362.355,"sdkLanguage":"javascript","testIdAttributeName":"data-testid","contextId":"browser-context@d945cb091ea8dc7105ed95cbfd0b2616","title":"comparisons.spec.ts:229 › Entity Comparisons and Pathfinding › should calculate complex multi-hop paths"}
{"type":"before","callId":"call@6","startTime":133363.813,"class":"BrowserContext","method":"newPage","params":{},"stepId":"pw:api@8","beforeSnapshot":"before@call@6"}
{"type":"event","time":133410.494,"class":"BrowserContext","method":"page","params":{"pageId":"page@a986070605cbb1375f79f3bc4860ec2b"}}
{"type":"after","callId":"call@6","endTime":133410.529,"result":{"page":"<Page>"},"afterSnapshot":"after@call@6"}
{"type":"before","callId":"call@9","startTime":133416.309,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities","method":"GET","timeout":8000},"stepId":"pw:api@9","beforeSnapshot":"before@call@9"}
{"type":"log","callId":"call@9","time":133421.654,"message":"→ GET http://localhost:8000/api/v1/entities"}
{"type":"log","callId":"call@9","time":133421.666,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@9","time":133421.67,"message":"  accept: */*"}
{"type":"log","callId":"call@9","time":133421.672,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@9","time":133421.675,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@9","time":133421.681,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@9","time":133421.685,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@9","time":133426.125,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@9","time":133426.134,"message":"  date: Sun, 06 Jul 2025 02:27:01 GMT"}
{"type":"log","callId":"call@9","time":133426.139,"message":"  server: uvicorn"}
{"type":"log","callId":"call@9","time":133426.143,"message":"  content-length: 0"}
{"type":"log","callId":"call@9","time":133426.145,"message":"  location: http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@9","time":133426.416,"message":"→ GET http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@9","time":133426.42,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@9","time":133426.421,"message":"  accept: */*"}
{"type":"log","callId":"call@9","time":133426.423,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@9","time":133426.424,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@9","time":133426.425,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@9","time":133426.427,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@9","time":133431.219,"message":"← 200 OK"}
{"type":"log","callId":"call@9","time":133431.223,"message":"  date: Sun, 06 Jul 2025 02:27:01 GMT"}
{"type":"log","callId":"call@9","time":133431.224,"message":"  server: uvicorn"}
{"type":"log","callId":"call@9","time":133431.226,"message":"  content-length: 1753"}
{"type":"log","callId":"call@9","time":133431.227,"message":"  content-type: application/json"}
{"type":"after","callId":"call@9","endTime":133431.29,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/","status":200,"statusText":"OK","headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"1753"},{"name":"content-type","value":"application/json"}],"fetchUid":"2deefc3dd28bd1d77fb0f69306c14420"}},"afterSnapshot":"after@call@9"}
{"type":"before","callId":"call@12","startTime":133433.127,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities","method":"GET","timeout":8000},"stepId":"pw:api@10","beforeSnapshot":"before@call@12"}
{"type":"log","callId":"call@12","time":133433.603,"message":"→ GET http://localhost:8000/api/v1/entities"}
{"type":"log","callId":"call@12","time":133433.609,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@12","time":133433.612,"message":"  accept: */*"}
{"type":"log","callId":"call@12","time":133433.613,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@12","time":133433.615,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@12","time":133433.616,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@12","time":133433.617,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@12","time":133434.947,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@12","time":133434.952,"message":"  date: Sun, 06 Jul 2025 02:27:01 GMT"}
{"type":"log","callId":"call@12","time":133434.954,"message":"  server: uvicorn"}
{"type":"log","callId":"call@12","time":133434.961,"message":"  content-length: 0"}
{"type":"log","callId":"call@12","time":133434.963,"message":"  location: http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@12","time":133435.131,"message":"→ GET http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@12","time":133435.134,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@12","time":133435.135,"message":"  accept: */*"}
{"type":"log","callId":"call@12","time":133435.137,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@12","time":133435.138,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@12","time":133435.139,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@12","time":133435.142,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@12","time":133439.638,"message":"← 200 OK"}
{"type":"log","callId":"call@12","time":133439.641,"message":"  date: Sun, 06 Jul 2025 02:27:01 GMT"}
{"type":"log","callId":"call@12","time":133439.642,"message":"  server: uvicorn"}
{"type":"log","callId":"call@12","time":133439.644,"message":"  content-length: 1753"}
{"type":"log","callId":"call@12","time":133439.645,"message":"  content-type: application/json"}
{"type":"after","callId":"call@12","endTime":133439.681,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/","status":200,"statusText":"OK","headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"1753"},{"name":"content-type","value":"application/json"}],"fetchUid":"105cd660778ea12faf05e80fe60a1aee"}},"afterSnapshot":"after@call@12"}
{"type":"before","callId":"call@15","startTime":133443.218,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities","method":"POST","jsonData":"{\"name\":\"Human BAWGA\",\"unit_id\":1}","timeout":8000},"stepId":"pw:api@11","beforeSnapshot":"before@call@15"}
{"type":"before","callId":"call@17","startTime":133443.408,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities","method":"POST","jsonData":"{\"name\":\"Ball BAWGB\",\"unit_id\":1}","timeout":8000},"stepId":"pw:api@12","beforeSnapshot":"before@call@17"}
{"type":"before","callId":"call@19","startTime":133443.551,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities","method":"POST","jsonData":"{\"name\":\"Build BAWGC\",\"unit_id\":1}","timeout":8000},"stepId":"pw:api@13","beforeSnapshot":"before@call@19"}
{"type":"before","callId":"call@21","startTime":133443.686,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities","method":"POST","jsonData":"{\"name\":\"Car BAWGD\",\"unit_id\":1}","timeout":8000},"stepId":"pw:api@14","beforeSnapshot":"before@call@21"}
{"type":"before","callId":"call@23","startTime":133443.815,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities","method":"POST","jsonData":"{\"name\":\"Eleph BAWGE\",\"unit_id\":1}","timeout":8000},"stepId":"pw:api@15","beforeSnapshot":"before@call@23"}
{"type":"before","callId":"call@25","startTime":133443.951,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities","method":"POST","jsonData":"{\"name\":\"Mouse BAWGF\",\"unit_id\":1}","timeout":8000},"stepId":"pw:api@16","beforeSnapshot":"before@call@25"}
{"type":"screencast-frame","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":133444.171,"frameSwapWallTime":1751768821254.636}
{"type":"log","callId":"call@15","time":133445.101,"message":"→ POST http://localhost:8000/api/v1/entities"}
{"type":"log","callId":"call@15","time":133445.104,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@15","time":133445.105,"message":"  accept: */*"}
{"type":"log","callId":"call@15","time":133445.106,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@15","time":133445.107,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@15","time":133445.108,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@15","time":133445.109,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@15","time":133445.11,"message":"  content-type: application/json"}
{"type":"log","callId":"call@15","time":133445.111,"message":"  content-length: 34"}
{"type":"log","callId":"call@17","time":133445.333,"message":"→ POST http://localhost:8000/api/v1/entities"}
{"type":"log","callId":"call@17","time":133445.336,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@17","time":133445.338,"message":"  accept: */*"}
{"type":"log","callId":"call@17","time":133445.339,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@17","time":133445.34,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@17","time":133445.341,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@17","time":133445.343,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@17","time":133445.344,"message":"  content-type: application/json"}
{"type":"log","callId":"call@17","time":133445.344,"message":"  content-length: 33"}
{"type":"log","callId":"call@19","time":133445.437,"message":"→ POST http://localhost:8000/api/v1/entities"}
{"type":"log","callId":"call@19","time":133445.438,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@19","time":133445.439,"message":"  accept: */*"}
{"type":"log","callId":"call@19","time":133445.44,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@19","time":133445.441,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@19","time":133445.442,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@19","time":133445.444,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@19","time":133445.445,"message":"  content-type: application/json"}
{"type":"log","callId":"call@19","time":133445.446,"message":"  content-length: 34"}
{"type":"log","callId":"call@21","time":133445.539,"message":"→ POST http://localhost:8000/api/v1/entities"}
{"type":"log","callId":"call@21","time":133445.541,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@21","time":133445.542,"message":"  accept: */*"}
{"type":"log","callId":"call@21","time":133445.543,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@21","time":133445.543,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@21","time":133445.544,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@21","time":133445.545,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@21","time":133445.548,"message":"  content-type: application/json"}
{"type":"log","callId":"call@21","time":133445.548,"message":"  content-length: 32"}
{"type":"log","callId":"call@23","time":133445.634,"message":"→ POST http://localhost:8000/api/v1/entities"}
{"type":"log","callId":"call@23","time":133445.635,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@23","time":133445.636,"message":"  accept: */*"}
{"type":"log","callId":"call@23","time":133445.637,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@23","time":133445.638,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@23","time":133445.639,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@23","time":133445.64,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@23","time":133445.641,"message":"  content-type: application/json"}
{"type":"log","callId":"call@23","time":133445.642,"message":"  content-length: 34"}
{"type":"log","callId":"call@25","time":133445.762,"message":"→ POST http://localhost:8000/api/v1/entities"}
{"type":"log","callId":"call@25","time":133445.763,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@25","time":133445.764,"message":"  accept: */*"}
{"type":"log","callId":"call@25","time":133445.765,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@25","time":133445.767,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@25","time":133445.768,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@25","time":133445.769,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@25","time":133445.77,"message":"  content-type: application/json"}
{"type":"log","callId":"call@25","time":133445.77,"message":"  content-length: 34"}
{"type":"log","callId":"call@15","time":133446.64,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@15","time":133446.643,"message":"  date: Sun, 06 Jul 2025 02:27:01 GMT"}
{"type":"log","callId":"call@15","time":133446.645,"message":"  server: uvicorn"}
{"type":"log","callId":"call@15","time":133446.646,"message":"  content-length: 0"}
{"type":"log","callId":"call@15","time":133446.647,"message":"  location: http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@15","time":133446.858,"message":"→ POST http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@15","time":133446.86,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@15","time":133446.862,"message":"  accept: */*"}
{"type":"log","callId":"call@15","time":133446.863,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@15","time":133446.864,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@15","time":133446.865,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@15","time":133446.865,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@15","time":133446.866,"message":"  content-type: application/json"}
{"type":"log","callId":"call@15","time":133446.867,"message":"  content-length: 34"}
{"type":"log","callId":"call@19","time":133448.307,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@19","time":133448.309,"message":"  date: Sun, 06 Jul 2025 02:27:01 GMT"}
{"type":"log","callId":"call@19","time":133448.31,"message":"  server: uvicorn"}
{"type":"log","callId":"call@19","time":133448.311,"message":"  content-length: 0"}
{"type":"log","callId":"call@19","time":133448.312,"message":"  location: http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@19","time":133448.427,"message":"→ POST http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@19","time":133448.429,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@19","time":133448.43,"message":"  accept: */*"}
{"type":"log","callId":"call@19","time":133448.431,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@19","time":133448.432,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@19","time":133448.432,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@19","time":133448.433,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@19","time":133448.434,"message":"  content-type: application/json"}
{"type":"log","callId":"call@19","time":133448.435,"message":"  content-length: 34"}
{"type":"log","callId":"call@23","time":133448.98,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@23","time":133448.982,"message":"  date: Sun, 06 Jul 2025 02:27:01 GMT"}
{"type":"log","callId":"call@23","time":133448.983,"message":"  server: uvicorn"}
{"type":"log","callId":"call@23","time":133448.984,"message":"  content-length: 0"}
{"type":"log","callId":"call@23","time":133448.985,"message":"  location: http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@23","time":133449.087,"message":"→ POST http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@23","time":133449.094,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@23","time":133449.101,"message":"  accept: */*"}
{"type":"log","callId":"call@23","time":133449.103,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@23","time":133449.109,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@23","time":133449.11,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@23","time":133449.111,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@23","time":133449.112,"message":"  content-type: application/json"}
{"type":"log","callId":"call@23","time":133449.112,"message":"  content-length: 34"}
{"type":"log","callId":"call@17","time":133449.707,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@17","time":133449.717,"message":"  date: Sun, 06 Jul 2025 02:27:01 GMT"}
{"type":"log","callId":"call@17","time":133449.719,"message":"  server: uvicorn"}
{"type":"log","callId":"call@17","time":133449.72,"message":"  content-length: 0"}
{"type":"log","callId":"call@17","time":133449.721,"message":"  location: http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@25","time":133450.054,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@25","time":133450.057,"message":"  date: Sun, 06 Jul 2025 02:27:01 GMT"}
{"type":"log","callId":"call@25","time":133450.059,"message":"  server: uvicorn"}
{"type":"log","callId":"call@25","time":133450.059,"message":"  content-length: 0"}
{"type":"log","callId":"call@25","time":133450.06,"message":"  location: http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@21","time":133450.292,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@21","time":133450.294,"message":"  date: Sun, 06 Jul 2025 02:27:01 GMT"}
{"type":"log","callId":"call@21","time":133450.295,"message":"  server: uvicorn"}
{"type":"log","callId":"call@21","time":133450.296,"message":"  content-length: 0"}
{"type":"log","callId":"call@21","time":133450.297,"message":"  location: http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@17","time":133450.398,"message":"→ POST http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@17","time":133450.4,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@17","time":133450.401,"message":"  accept: */*"}
{"type":"log","callId":"call@17","time":133450.401,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@17","time":133450.402,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@17","time":133450.403,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@17","time":133450.403,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@17","time":133450.404,"message":"  content-type: application/json"}
{"type":"log","callId":"call@17","time":133450.405,"message":"  content-length: 33"}
{"type":"log","callId":"call@25","time":133450.464,"message":"→ POST http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@25","time":133450.466,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@25","time":133450.47,"message":"  accept: */*"}
{"type":"log","callId":"call@25","time":133450.471,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@25","time":133450.472,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@25","time":133450.473,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@25","time":133450.474,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@25","time":133450.475,"message":"  content-type: application/json"}
{"type":"log","callId":"call@25","time":133450.476,"message":"  content-length: 34"}
{"type":"log","callId":"call@21","time":133450.569,"message":"→ POST http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@21","time":133450.571,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@21","time":133450.572,"message":"  accept: */*"}
{"type":"log","callId":"call@21","time":133450.573,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@21","time":133450.574,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@21","time":133450.574,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@21","time":133450.575,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@21","time":133450.576,"message":"  content-type: application/json"}
{"type":"log","callId":"call@21","time":133450.577,"message":"  content-length: 32"}
{"type":"log","callId":"call@23","time":133454.46,"message":"← 201 Created"}
{"type":"log","callId":"call@23","time":133454.464,"message":"  date: Sun, 06 Jul 2025 02:27:01 GMT"}
{"type":"log","callId":"call@23","time":133454.467,"message":"  server: uvicorn"}
{"type":"log","callId":"call@23","time":133454.468,"message":"  content-length: 115"}
{"type":"log","callId":"call@23","time":133454.469,"message":"  content-type: application/json"}
{"type":"after","callId":"call@23","endTime":133454.504,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/","status":201,"statusText":"Created","headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"115"},{"name":"content-type","value":"application/json"}],"fetchUid":"9e895951c0c5bcefd011896ebc2f6365"}},"afterSnapshot":"after@call@23"}
{"type":"log","callId":"call@15","time":133456.168,"message":"← 201 Created"}
{"type":"log","callId":"call@15","time":133456.171,"message":"  date: Sun, 06 Jul 2025 02:27:01 GMT"}
{"type":"log","callId":"call@15","time":133456.173,"message":"  server: uvicorn"}
{"type":"log","callId":"call@15","time":133456.173,"message":"  content-length: 115"}
{"type":"log","callId":"call@15","time":133456.174,"message":"  content-type: application/json"}
{"type":"after","callId":"call@15","endTime":133456.203,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/","status":201,"statusText":"Created","headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"115"},{"name":"content-type","value":"application/json"}],"fetchUid":"21df6e98d033eb5d1f8de72f68d82ac1"}},"afterSnapshot":"after@call@15"}
{"type":"log","callId":"call@19","time":133456.314,"message":"← 201 Created"}
{"type":"log","callId":"call@19","time":133456.316,"message":"  date: Sun, 06 Jul 2025 02:27:01 GMT"}
{"type":"log","callId":"call@19","time":133456.317,"message":"  server: uvicorn"}
{"type":"log","callId":"call@19","time":133456.318,"message":"  content-length: 115"}
{"type":"log","callId":"call@19","time":133456.319,"message":"  content-type: application/json"}
{"type":"after","callId":"call@19","endTime":133456.344,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/","status":201,"statusText":"Created","headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"115"},{"name":"content-type","value":"application/json"}],"fetchUid":"790948cf8b53a720fabfb6cbdfdf0723"}},"afterSnapshot":"after@call@19"}
{"type":"log","callId":"call@25","time":133456.913,"message":"← 201 Created"}
{"type":"log","callId":"call@25","time":133456.915,"message":"  date: Sun, 06 Jul 2025 02:27:01 GMT"}
{"type":"log","callId":"call@25","time":133456.916,"message":"  server: uvicorn"}
{"type":"log","callId":"call@25","time":133456.917,"message":"  content-length: 115"}
{"type":"log","callId":"call@25","time":133456.918,"message":"  content-type: application/json"}
{"type":"after","callId":"call@25","endTime":133456.933,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/","status":201,"statusText":"Created","headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"115"},{"name":"content-type","value":"application/json"}],"fetchUid":"4c799dfcd07bfe6e4f82b0b8497b005b"}},"afterSnapshot":"after@call@25"}
{"type":"log","callId":"call@17","time":133457.038,"message":"← 201 Created"}
{"type":"log","callId":"call@17","time":133457.04,"message":"  date: Sun, 06 Jul 2025 02:27:01 GMT"}
{"type":"log","callId":"call@17","time":133457.04,"message":"  server: uvicorn"}
{"type":"log","callId":"call@17","time":133457.041,"message":"  content-length: 114"}
{"type":"log","callId":"call@17","time":133457.042,"message":"  content-type: application/json"}
{"type":"after","callId":"call@17","endTime":133457.06,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/","status":201,"statusText":"Created","headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"114"},{"name":"content-type","value":"application/json"}],"fetchUid":"ce9cebcfead08d792808e0440239d729"}},"afterSnapshot":"after@call@17"}
{"type":"log","callId":"call@21","time":133457.159,"message":"← 201 Created"}
{"type":"log","callId":"call@21","time":133457.161,"message":"  date: Sun, 06 Jul 2025 02:27:01 GMT"}
{"type":"log","callId":"call@21","time":133457.164,"message":"  server: uvicorn"}
{"type":"log","callId":"call@21","time":133457.165,"message":"  content-length: 113"}
{"type":"log","callId":"call@21","time":133457.166,"message":"  content-type: application/json"}
{"type":"after","callId":"call@21","endTime":133457.186,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/","status":201,"statusText":"Created","headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"113"},{"name":"content-type","value":"application/json"}],"fetchUid":"43b12d391e4b002c8b751d47dd9281d1"}},"afterSnapshot":"after@call@21"}
{"type":"before","callId":"call@33","startTime":133459.404,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/connections","method":"POST","jsonData":"{\"from_entity_id\":165,\"to_entity_id\":168,\"multiplier\":10,\"unit_id\":1}","timeout":8000},"stepId":"pw:api@17","beforeSnapshot":"before@call@33"}
{"type":"before","callId":"call@35","startTime":133459.578,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/connections","method":"POST","jsonData":"{\"from_entity_id\":168,\"to_entity_id\":166,\"multiplier\":50,\"unit_id\":1}","timeout":8000},"stepId":"pw:api@18","beforeSnapshot":"before@call@35"}
{"type":"before","callId":"call@37","startTime":133459.703,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/connections","method":"POST","jsonData":"{\"from_entity_id\":166,\"to_entity_id\":169,\"multiplier\":0.1,\"unit_id\":1}","timeout":8000},"stepId":"pw:api@19","beforeSnapshot":"before@call@37"}
{"type":"before","callId":"call@39","startTime":133459.804,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/connections","method":"POST","jsonData":"{\"from_entity_id\":170,\"to_entity_id\":167,\"multiplier\":0.3,\"unit_id\":2}","timeout":8000},"stepId":"pw:api@20","beforeSnapshot":"before@call@39"}
{"type":"log","callId":"call@33","time":133460.031,"message":"→ POST http://localhost:8000/api/v1/connections"}
{"type":"log","callId":"call@33","time":133460.033,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@33","time":133460.035,"message":"  accept: */*"}
{"type":"log","callId":"call@33","time":133460.035,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@33","time":133460.036,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@33","time":133460.037,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@33","time":133460.038,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@33","time":133460.038,"message":"  content-type: application/json"}
{"type":"log","callId":"call@33","time":133460.039,"message":"  content-length: 69"}
{"type":"log","callId":"call@35","time":133460.146,"message":"→ POST http://localhost:8000/api/v1/connections"}
{"type":"log","callId":"call@35","time":133460.148,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@35","time":133460.149,"message":"  accept: */*"}
{"type":"log","callId":"call@35","time":133460.15,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@35","time":133460.15,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@35","time":133460.151,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@35","time":133460.152,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@35","time":133460.153,"message":"  content-type: application/json"}
{"type":"log","callId":"call@35","time":133460.154,"message":"  content-length: 69"}
{"type":"log","callId":"call@37","time":133460.229,"message":"→ POST http://localhost:8000/api/v1/connections"}
{"type":"log","callId":"call@37","time":133460.231,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@37","time":133460.232,"message":"  accept: */*"}
{"type":"log","callId":"call@37","time":133460.249,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@37","time":133460.253,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@37","time":133460.254,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@37","time":133460.255,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@37","time":133460.255,"message":"  content-type: application/json"}
{"type":"log","callId":"call@37","time":133460.256,"message":"  content-length: 70"}
{"type":"log","callId":"call@39","time":133460.388,"message":"→ POST http://localhost:8000/api/v1/connections"}
{"type":"log","callId":"call@39","time":133460.39,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@39","time":133460.391,"message":"  accept: */*"}
{"type":"log","callId":"call@39","time":133460.392,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@39","time":133460.393,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@39","time":133460.394,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@39","time":133460.395,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@39","time":133460.395,"message":"  content-type: application/json"}
{"type":"log","callId":"call@39","time":133460.396,"message":"  content-length: 70"}
{"type":"log","callId":"call@35","time":133460.951,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@35","time":133460.953,"message":"  date: Sun, 06 Jul 2025 02:27:01 GMT"}
{"type":"log","callId":"call@35","time":133460.954,"message":"  server: uvicorn"}
{"type":"log","callId":"call@35","time":133460.955,"message":"  content-length: 0"}
{"type":"log","callId":"call@35","time":133460.956,"message":"  location: http://localhost:8000/api/v1/connections/"}
{"type":"log","callId":"call@33","time":133461.076,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@33","time":133461.081,"message":"  date: Sun, 06 Jul 2025 02:27:01 GMT"}
{"type":"log","callId":"call@33","time":133461.082,"message":"  server: uvicorn"}
{"type":"log","callId":"call@33","time":133461.083,"message":"  content-length: 0"}
{"type":"log","callId":"call@33","time":133461.084,"message":"  location: http://localhost:8000/api/v1/connections/"}
{"type":"log","callId":"call@37","time":133461.256,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@37","time":133461.258,"message":"  date: Sun, 06 Jul 2025 02:27:01 GMT"}
{"type":"log","callId":"call@37","time":133461.259,"message":"  server: uvicorn"}
{"type":"log","callId":"call@37","time":133461.26,"message":"  content-length: 0"}
{"type":"log","callId":"call@37","time":133461.261,"message":"  location: http://localhost:8000/api/v1/connections/"}
{"type":"log","callId":"call@39","time":133461.366,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@39","time":133461.368,"message":"  date: Sun, 06 Jul 2025 02:27:01 GMT"}
{"type":"log","callId":"call@39","time":133461.369,"message":"  server: uvicorn"}
{"type":"log","callId":"call@39","time":133461.37,"message":"  content-length: 0"}
{"type":"log","callId":"call@39","time":133461.371,"message":"  location: http://localhost:8000/api/v1/connections/"}
{"type":"log","callId":"call@35","time":133461.488,"message":"→ POST http://localhost:8000/api/v1/connections/"}
{"type":"log","callId":"call@35","time":133461.49,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@35","time":133461.491,"message":"  accept: */*"}
{"type":"log","callId":"call@35","time":133461.492,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@35","time":133461.493,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@35","time":133461.493,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@35","time":133461.494,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@35","time":133461.495,"message":"  content-type: application/json"}
{"type":"log","callId":"call@35","time":133461.496,"message":"  content-length: 69"}
{"type":"log","callId":"call@33","time":133461.581,"message":"→ POST http://localhost:8000/api/v1/connections/"}
{"type":"log","callId":"call@33","time":133461.583,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@33","time":133461.583,"message":"  accept: */*"}
{"type":"log","callId":"call@33","time":133461.584,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@33","time":133461.585,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@33","time":133461.586,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@33","time":133461.586,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@33","time":133461.587,"message":"  content-type: application/json"}
{"type":"log","callId":"call@33","time":133461.588,"message":"  content-length: 69"}
{"type":"log","callId":"call@37","time":133461.663,"message":"→ POST http://localhost:8000/api/v1/connections/"}
{"type":"log","callId":"call@37","time":133461.664,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@37","time":133461.665,"message":"  accept: */*"}
{"type":"log","callId":"call@37","time":133461.666,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@37","time":133461.667,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@37","time":133461.667,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@37","time":133461.668,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@37","time":133461.669,"message":"  content-type: application/json"}
{"type":"log","callId":"call@37","time":133461.669,"message":"  content-length: 70"}
{"type":"log","callId":"call@39","time":133461.715,"message":"→ POST http://localhost:8000/api/v1/connections/"}
{"type":"log","callId":"call@39","time":133461.717,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@39","time":133461.717,"message":"  accept: */*"}
{"type":"log","callId":"call@39","time":133461.718,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@39","time":133461.719,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@39","time":133461.72,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@39","time":133461.72,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@39","time":133461.721,"message":"  content-type: application/json"}
{"type":"log","callId":"call@39","time":133461.722,"message":"  content-length: 70"}
{"type":"log","callId":"call@35","time":133467.255,"message":"← 201 Created"}
{"type":"log","callId":"call@35","time":133467.258,"message":"  date: Sun, 06 Jul 2025 02:27:01 GMT"}
{"type":"log","callId":"call@35","time":133467.259,"message":"  server: uvicorn"}
{"type":"log","callId":"call@35","time":133467.26,"message":"  content-length: 165"}
{"type":"log","callId":"call@35","time":133467.261,"message":"  content-type: application/json"}
{"type":"after","callId":"call@35","endTime":133467.298,"result":{"response":{"url":"http://localhost:8000/api/v1/connections/","status":201,"statusText":"Created","headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"165"},{"name":"content-type","value":"application/json"}],"fetchUid":"9478838a56bc6fc7c9df2c8e9fc318db"}},"afterSnapshot":"after@call@35"}
{"type":"log","callId":"call@33","time":133468.023,"message":"← 201 Created"}
{"type":"log","callId":"call@33","time":133468.025,"message":"  date: Sun, 06 Jul 2025 02:27:01 GMT"}
{"type":"log","callId":"call@33","time":133468.026,"message":"  server: uvicorn"}
{"type":"log","callId":"call@33","time":133468.027,"message":"  content-length: 165"}
{"type":"log","callId":"call@33","time":133468.028,"message":"  content-type: application/json"}
{"type":"after","callId":"call@33","endTime":133468.046,"result":{"response":{"url":"http://localhost:8000/api/v1/connections/","status":201,"statusText":"Created","headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"165"},{"name":"content-type","value":"application/json"}],"fetchUid":"9940a4274f56c7ced551373893882942"}},"afterSnapshot":"after@call@33"}
{"type":"log","callId":"call@37","time":133468.598,"message":"← 201 Created"}
{"type":"log","callId":"call@37","time":133468.6,"message":"  date: Sun, 06 Jul 2025 02:27:01 GMT"}
{"type":"log","callId":"call@37","time":133468.601,"message":"  server: uvicorn"}
{"type":"log","callId":"call@37","time":133468.602,"message":"  content-length: 164"}
{"type":"log","callId":"call@37","time":133468.603,"message":"  content-type: application/json"}
{"type":"after","callId":"call@37","endTime":133468.624,"result":{"response":{"url":"http://localhost:8000/api/v1/connections/","status":201,"statusText":"Created","headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"164"},{"name":"content-type","value":"application/json"}],"fetchUid":"89dfe5b79970825419e1e476d2cb70ee"}},"afterSnapshot":"after@call@37"}
{"type":"log","callId":"call@39","time":133468.893,"message":"← 201 Created"}
{"type":"log","callId":"call@39","time":133468.895,"message":"  date: Sun, 06 Jul 2025 02:27:01 GMT"}
{"type":"log","callId":"call@39","time":133468.896,"message":"  server: uvicorn"}
{"type":"log","callId":"call@39","time":133468.896,"message":"  content-length: 164"}
{"type":"log","callId":"call@39","time":133468.897,"message":"  content-type: application/json"}
{"type":"after","callId":"call@39","endTime":133468.913,"result":{"response":{"url":"http://localhost:8000/api/v1/connections/","status":201,"statusText":"Created","headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:01 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"164"},{"name":"content-type","value":"application/json"}],"fetchUid":"a0d7a4418320571ac860664cd71a6b4f"}},"afterSnapshot":"after@call@39"}
{"type":"before","callId":"call@45","startTime":133473.386,"class":"Frame","method":"goto","params":{"url":"/","timeout":15000,"waitUntil":"load"},"stepId":"pw:api@21","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","beforeSnapshot":"before@call@45"}
{"type":"frame-snapshot","snapshot":{"callId":"call@45","snapshotName":"before@call@45","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"about:blank","html":["HTML",{},["HEAD",{},["BASE",{"href":"about:blank"}]],["BODY"]],"viewport":{"width":1280,"height":720},"timestamp":133474.602,"wallTime":1751768821290,"collectionTime":0.5999999940395355,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@45","time":133475.151,"message":"navigating to \"http://localhost:3000/\", waiting until \"load\""}
{"type":"screencast-frame","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":133490.775,"frameSwapWallTime":1751768821305.104}
{"type":"console","messageType":"info","text":"%cDownload the React DevTools for a better development experience: https://reactjs.org/link/react-devtools font-weight:bold","args":[{"preview":"%cDownload the React DevTools for a better development experience: https://reactjs.org/link/react-devtools","value":"%cDownload the React DevTools for a better development experience: https://reactjs.org/link/react-devtools"},{"preview":"font-weight:bold","value":"font-weight:bold"}],"location":{"url":"http://localhost:3000/static/js/bundle.js","lineNumber":38990,"columnNumber":20},"time":133546.539,"pageId":"page@a986070605cbb1375f79f3bc4860ec2b"}
{"type":"after","callId":"call@45","endTime":133578.44,"result":{"response":"<Response>"},"afterSnapshot":"after@call@45"}
{"type":"screencast-frame","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":133578.753,"frameSwapWallTime":1751768821389.155}
{"type":"console","messageType":"warning","text":"⚠️ React Router Future Flag Warning: React Router will begin wrapping state updates in `React.startTransition` in v7. You can use the `v7_startTransition` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_starttransition.","args":[{"preview":"⚠️ React Router Future Flag Warning: React Router will begin wrapping state updates in `React.startTransition` in v7. You can use the `v7_startTransition` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_starttransition.","value":"⚠️ React Router Future Flag Warning: React Router will begin wrapping state updates in `React.startTransition` in v7. You can use the `v7_startTransition` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_starttransition."}],"location":{"url":"http://localhost:3000/static/js/bundle.js","lineNumber":42276,"columnNumber":12},"time":133579.275,"pageId":"page@a986070605cbb1375f79f3bc4860ec2b"}
{"type":"console","messageType":"warning","text":"⚠️ React Router Future Flag Warning: Relative route resolution within Splat routes is changing in v7. You can use the `v7_relativeSplatPath` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath.","args":[{"preview":"⚠️ React Router Future Flag Warning: Relative route resolution within Splat routes is changing in v7. You can use the `v7_relativeSplatPath` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath.","value":"⚠️ React Router Future Flag Warning: Relative route resolution within Splat routes is changing in v7. You can use the `v7_relativeSplatPath` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath."}],"location":{"url":"http://localhost:3000/static/js/bundle.js","lineNumber":42276,"columnNumber":12},"time":133579.305,"pageId":"page@a986070605cbb1375f79f3bc4860ec2b"}
{"type":"console","messageType":"log","text":"API Request: GET /entities/ undefined","args":[{"preview":"API Request:","value":"API Request:"},{"preview":"GET","value":"GET"},{"preview":"/entities/","value":"/entities/"},{"preview":"undefined"}],"location":{"url":"http://localhost:3000/static/js/bundle.js","lineNumber":54565,"columnNumber":10},"time":133579.343,"pageId":"page@a986070605cbb1375f79f3bc4860ec2b"}
{"type":"console","messageType":"log","text":"API Request: GET /units/ undefined","args":[{"preview":"API Request:","value":"API Request:"},{"preview":"GET","value":"GET"},{"preview":"/units/","value":"/units/"},{"preview":"undefined"}],"location":{"url":"http://localhost:3000/static/js/bundle.js","lineNumber":54565,"columnNumber":10},"time":133579.395,"pageId":"page@a986070605cbb1375f79f3bc4860ec2b"}
{"type":"console","messageType":"log","text":"API Request: GET /entities/ undefined","args":[{"preview":"API Request:","value":"API Request:"},{"preview":"GET","value":"GET"},{"preview":"/entities/","value":"/entities/"},{"preview":"undefined"}],"location":{"url":"http://localhost:3000/static/js/bundle.js","lineNumber":54565,"columnNumber":10},"time":133579.428,"pageId":"page@a986070605cbb1375f79f3bc4860ec2b"}
{"type":"console","messageType":"log","text":"API Request: GET /units/ undefined","args":[{"preview":"API Request:","value":"API Request:"},{"preview":"GET","value":"GET"},{"preview":"/units/","value":"/units/"},{"preview":"undefined"}],"location":{"url":"http://localhost:3000/static/js/bundle.js","lineNumber":54565,"columnNumber":10},"time":133579.454,"pageId":"page@a986070605cbb1375f79f3bc4860ec2b"}
{"type":"frame-snapshot","snapshot":{"callId":"call@45","snapshotName":"after@call@45","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},["HEAD",{},["BASE",{"href":"http://localhost:3000/"}],"\n    ",["META",{"charset":"utf-8"}],"\n    ",["LINK",{"rel":"icon","href":"/favicon.ico"}],"\n    ",["META",{"name":"viewport","content":"width=device-width, initial-scale=1"}],"\n    ",["META",{"name":"theme-color","content":"#000000"}],"\n    ",["META",{"name":"description","content":"SIMILE - Compare entities based on measurable relationships"}],"\n    ",["LINK",{"rel":"apple-touch-icon","href":"/logo192.png"}],"\n    ",["LINK",{"rel":"manifest","href":"/manifest.json"}],"\n    ",["TITLE",{},"SIMILE"],"\n  ",["STYLE",{},"body {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9pbmRleC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxTQUFTO0VBQ1Q7O2NBRVk7RUFDWixtQ0FBbUM7RUFDbkMsa0NBQWtDO0FBQ3BDOztBQUVBO0VBQ0U7YUFDVztBQUNiIiwic291cmNlc0NvbnRlbnQiOlsiYm9keSB7XG4gIG1hcmdpbjogMDtcbiAgZm9udC1mYW1pbHk6IC1hcHBsZS1zeXN0ZW0sIEJsaW5rTWFjU3lzdGVtRm9udCwgJ1NlZ29lIFVJJywgJ1JvYm90bycsICdPeHlnZW4nLFxuICAgICdVYnVudHUnLCAnQ2FudGFyZWxsJywgJ0ZpcmEgU2FucycsICdEcm9pZCBTYW5zJywgJ0hlbHZldGljYSBOZXVlJyxcbiAgICBzYW5zLXNlcmlmO1xuICAtd2Via2l0LWZvbnQtc21vb3RoaW5nOiBhbnRpYWxpYXNlZDtcbiAgLW1vei1vc3gtZm9udC1zbW9vdGhpbmc6IGdyYXlzY2FsZTtcbn1cblxuY29kZSB7XG4gIGZvbnQtZmFtaWx5OiBzb3VyY2UtY29kZS1wcm8sIE1lbmxvLCBNb25hY28sIENvbnNvbGFzLCAnQ291cmllciBOZXcnLFxuICAgIG1vbm9zcGFjZTtcbn0iXSwic291cmNlUm9vdCI6IiJ9 */"],["STYLE",{},"/* Reset and base styles */\n* {\n  box-sizing: border-box;\n  margin: 0;\n  padding: 0;\n}\n\nbody {\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  background-color: #f5f5f5;\n}\n\n.App {\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n}\n\n/* Navigation */\n.navigation {\n  background-color: #282c34;\n  padding: 1rem 2rem;\n  color: white;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.nav-brand .brand-link {\n  color: white;\n  text-decoration: none;\n}\n\n.nav-brand h1 {\n  font-size: 1.8rem;\n  margin-bottom: 0.2rem;\n}\n\n.nav-brand p {\n  font-size: 0.9rem;\n  color: #61dafb;\n}\n\n.nav-links {\n  display: flex;\n  gap: 1.5rem;\n}\n\n.nav-link {\n  color: white;\n  text-decoration: none;\n  padding: 0.5rem 1rem;\n  border-radius: 4px;\n  transition: background-color 0.2s;\n}\n\n.nav-link:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n}\n\n.nav-link.active {\n  background-color: #61dafb;\n  color: #282c34;\n}\n\n/* Main content */\n.main-content {\n  flex: 1 1;\n  padding: 2rem;\n  max-width: 1200px;\n  margin: 0 auto;\n  width: 100%;\n}\n\n/* Manager headers */\n.manager-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 2rem;\n  padding-bottom: 1rem;\n  border-bottom: 2px solid #e0e0e0;\n}\n\n.manager-header h2 {\n  color: #333;\n  font-size: 1.8rem;\n}\n\n/* Buttons */\n.btn-primary, .btn-secondary, .btn-select, .btn-edit, .btn-delete {\n  padding: 0.5rem 1rem;\n  border-radius: 4px;\n  border: none;\n  cursor: pointer;\n  font-size: 0.9rem;\n  transition: background-color 0.2s;\n}\n\n.btn-primary {\n  background-color: #007bff;\n  color: white;\n}\n\n.btn-primary:hover:not(:disabled) {\n  background-color: #0056b3;\n}\n\n.btn-secondary {\n  background-color: #6c757d;\n  color: white;\n}\n\n.btn-secondary:hover:not(:disabled) {\n  background-color: #545b62;\n}\n\n.btn-select {\n  background-color: #28a745;\n  color: white;\n}\n\n.btn-edit {\n  background-color: #ffc107;\n  color: #212529;\n}\n\n.btn-delete {\n  background-color: #dc3545;\n  color: white;\n}\n\n.btn-delete:hover:not(:disabled) {\n  background-color: #c82333;\n}\n\nbutton:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n/* Cards and grids */\n.entity-grid, .connections-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 1rem;\n}\n\n.entity-card, .connection-card {\n  background: white;\n  border-radius: 8px;\n  padding: 1.5rem;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  border: 1px solid #e0e0e0;\n}\n\n.entity-card h4 {\n  margin-bottom: 0.5rem;\n  color: #333;\n}\n\n.entity-id {\n  color: #666;\n  font-size: 0.8rem;\n  margin-bottom: 1rem;\n}\n\n.entity-actions {\n  display: flex;\n  gap: 0.5rem;\n  flex-wrap: wrap;\n}\n\n.connection-relationship {\n  margin-bottom: 1rem;\n  font-size: 1.1rem;\n  line-height: 1.4;\n}\n\n.entity-name {\n  font-weight: bold;\n  color: #007bff;\n}\n\n.multiplier {\n  font-weight: bold;\n  color: #28a745;\n  font-size: 1.2em;\n}\n\n.relationship-text, .times-text {\n  color: #666;\n  margin: 0 0.3rem;\n}\n\n.unit-text {\n  color: #6c757d;\n  font-style: italic;\n}\n\n.connection-details {\n  display: flex;\n  gap: 1rem;\n  margin-bottom: 1rem;\n  font-size: 0.8rem;\n  color: #666;\n}\n\n/* Forms */\n.entity-form, .connection-form, .comparison-form {\n  background: white;\n  border-radius: 8px;\n  padding: 2rem;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  border: 1px solid #e0e0e0;\n}\n\n/* Form validation states */\n.form-input {\n  transition: border-color 0.2s, box-shadow 0.2s;\n}\n\n.form-input.valid {\n  border-color: #28a745;\n  box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.25);\n}\n\n.form-input.invalid {\n  border-color: #dc3545;\n  box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.25);\n}\n\n.validation-success {\n  color: #28a745;\n  font-weight: 500;\n  margin-left: 0.5rem;\n}\n\n.form-group {\n  margin-bottom: 1rem;\n}\n\n.form-row {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n}\n\n.form-group label {\n  display: block;\n  margin-bottom: 0.5rem;\n  font-weight: 500;\n  color: #333;\n}\n\n.form-group input, .form-group select {\n  width: 100%;\n  padding: 0.5rem;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  font-size: 1rem;\n}\n\n.form-group input:focus, .form-group select:focus {\n  outline: none;\n  border-color: #007bff;\n  box-shadow: 0 0 0 2px rgba(0,123,255,0.25);\n}\n\n.form-help {\n  display: block;\n  margin-top: 0.25rem;\n  color: #666;\n  font-size: 0.8rem;\n}\n\n.form-actions {\n  display: flex;\n  gap: 1rem;\n  margin-top: 1.5rem;\n}\n\n.form-note {\n  margin-top: 1rem;\n  padding: 1rem;\n  background-color: #f8f9fa;\n  border-radius: 4px;\n  border-left: 4px solid #007bff;\n}\n\n/* Previews */\n.connection-preview, .comparison-preview {\n  background-color: #f8f9fa;\n  padding: 1rem;\n  border-radius: 4px;\n  margin-bottom: 1.5rem;\n  border-left: 4px solid #28a745;\n}\n\n/* Results */\n.comparison-result {\n  background: white;\n  border-radius: 8px;\n  padding: 2rem;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  border: 1px solid #e0e0e0;\n  margin-top: 2rem;\n}\n\n.main-result {\n  text-align: center;\n  margin-bottom: 2rem;\n}\n\n.result-statement {\n  font-size: 1.5rem;\n  line-height: 1.6;\n  padding: 1rem;\n  background-color: #f8f9fa;\n  border-radius: 8px;\n  border: 2px solid #28a745;\n}\n\n/* Template-style result statement */\n.result-statement-template {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 3rem;\n  font-weight: 300;\n  line-height: 1.5;\n  padding: 3rem 2rem;\n  text-align: center;\n  letter-spacing: 0.02em;\n  margin-bottom: 2rem;\n}\n\n.template-prefix {\n  color: #d2691e; /* Orange/brown color */\n  margin-right: 0.3rem;\n}\n\n.template-from-count {\n  color: #333;\n  font-weight: 400;\n  border-bottom: 2px solid #333;\n  padding: 0.1rem 0.3rem;\n  margin: 0 0.4rem;\n}\n\n.template-from-entity {\n  color: #008B8B; /* Teal */\n  font-weight: 400;\n  border-bottom: 2px solid #333;\n  padding: 0.1rem 0.3rem;\n  margin: 0 0.4rem;\n}\n\n.template-relationship {\n  color: #333;\n  font-weight: 400;\n  margin: 0 0.4rem;\n}\n\n.template-multiplier {\n  color: #333;\n  font-weight: 400;\n  border-bottom: 2px solid #333;\n  padding: 0.1rem 0.3rem;\n  margin: 0 0.4rem;\n}\n\n.template-to-entity {\n  color: #808080; /* Gray */\n  font-weight: 400;\n  border-bottom: 2px solid #333;\n  padding: 0.1rem 0.3rem;\n  margin: 0 0.4rem;\n}\n\n.template-suffix {\n  color: #D2691E; /* Orange color */\n  font-weight: 400;\n  font-size: 3.2rem;\n  margin-left: 0.4rem;\n}\n\n.calculation-path {\n  margin-bottom: 2rem;\n}\n\n.calculation-path h4 {\n  margin-bottom: 1rem;\n  color: #333;\n}\n\n.path-steps {\n  background-color: #f8f9fa;\n  padding: 1rem;\n  border-radius: 8px;\n  margin-bottom: 1rem;\n}\n\n.path-step {\n  display: flex;\n  align-items: center;\n  margin-bottom: 0.5rem;\n}\n\n.step-number {\n  background-color: #007bff;\n  color: white;\n  width: 24px;\n  height: 24px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 0.8rem;\n  margin-right: 1rem;\n  flex-shrink: 0;\n}\n\n.step-content {\n  flex: 1 1;\n}\n\n.step-arrow {\n  text-align: center;\n  color: #666;\n  margin: 0.5rem 0;\n  font-size: 1.2rem;\n}\n\n.calculation-formula {\n  background-color: white;\n  padding: 1rem;\n  border-radius: 4px;\n  border: 1px solid #ddd;\n}\n\n.result-details {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n  padding-top: 1rem;\n  border-top: 1px solid #e0e0e0;\n}\n\n.detail-item {\n  color: #666;\n}\n\n/* Loading and error states */\n.loading {\n  text-align: center;\n  padding: 2rem;\n  color: #666;\n}\n\n.error {\n  background-color: #f8d7da;\n  color: #721c24;\n  padding: 1rem;\n  border-radius: 4px;\n  border: 1px solid #f5c6cb;\n}\n\n.error-message {\n  background-color: #f8d7da;\n  color: #721c24;\n  padding: 0.75rem;\n  border-radius: 4px;\n  border: 1px solid #f5c6cb;\n  margin-bottom: 1rem;\n}\n\n.no-data {\n  text-align: center;\n  padding: 2rem;\n  color: #666;\n}\n\n/* Responsive design */\n@media (max-width: 768px) {\n  .navigation {\n    flex-direction: column;\n    gap: 1rem;\n  }\n  \n  .nav-links {\n    gap: 1rem;\n  }\n  \n  .main-content {\n    padding: 1rem;\n  }\n  \n  .manager-header {\n    flex-direction: column;\n    gap: 1rem;\n    align-items: stretch;\n  }\n  \n  .entity-grid, .connections-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .form-row {\n    grid-template-columns: 1fr;\n  }\n  \n  .form-actions {\n    flex-direction: column;\n  }\n  \n  .result-details {\n    grid-template-columns: 1fr;\n  }\n  \n  .result-statement-template {\n    font-size: 2rem;\n    padding: 2rem 1rem;\n    line-height: 1.4;\n  }\n  \n  .result-statement-template .template-suffix {\n    font-size: 2.2rem;\n  }\n}\n\n/* Skeleton loading animations */\n.skeleton {\n  background: linear-gradient(\n    90deg,\n    #f0f0f0 25%,\n    #e0e0e0 50%,\n    #f0f0f0 75%\n  );\n  background-size: 200% 100%;\n  animation: skeleton-loading 1.5s infinite ease-in-out;\n  border-radius: 4px;\n  margin-bottom: 0.5rem;\n}\n\n@keyframes skeleton-loading {\n  0% {\n    background-position: 200% 0;\n  }\n  100% {\n    background-position: -200% 0;\n  }\n}\n\n.skeleton-card {\n  opacity: 0.7;\n}\n\n.skeleton-form {\n  opacity: 0.7;\n}\n\n.skeleton-form .skeleton-form-title {\n  margin-bottom: 1rem;\n}\n\n.skeleton-form .skeleton-label {\n  margin-bottom: 0.5rem;\n}\n\n.skeleton-form .skeleton-input {\n  margin-bottom: 1rem;\n}\n\n.skeleton-list {\n  opacity: 0.7;\n}\n\n.skeleton-list .skeleton-list-title {\n  margin-bottom: 1rem;\n}\n\n.skeleton-list-item {\n  margin-bottom: 1rem;\n  padding: 0.5rem 0;\n}\n\n.skeleton-list-item .skeleton:last-child {\n  margin-bottom: 0;\n}\n\n/* Loading states */\n.loading-overlay {\n  position: relative;\n}\n\n.loading-overlay::after {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(255, 255, 255, 0.7);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 8px;\n  z-index: 10;\n}\n\n.loading-spinner {\n  width: 24px;\n  height: 24px;\n  border: 2px solid #f3f3f3;\n  border-top: 2px solid #007bff;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* Error Boundary Styles */\n.error-boundary, .api-error-fallback {\n  background: white;\n  border: 1px solid #f5c6cb;\n  border-radius: 8px;\n  padding: 2rem;\n  margin: 1rem;\n  text-align: center;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n}\n\n.error-boundary h2, .api-error-fallback h3 {\n  color: #721c24;\n  margin-bottom: 1rem;\n}\n\n.error-boundary p, .api-error-fallback p {\n  color: #721c24;\n  margin-bottom: 1rem;\n}\n\n.api-error-fallback .error-icon {\n  font-size: 3rem;\n  margin-bottom: 1rem;\n}\n\n.api-error-fallback .error-message {\n  font-weight: 600;\n  font-size: 1.1rem;\n}\n\n.api-error-fallback .error-suggestion {\n  color: #6c757d;\n  font-size: 0.9rem;\n}\n\n.error-actions {\n  display: flex;\n  gap: 1rem;\n  justify-content: center;\n  margin-top: 1.5rem;\n}\n\n.error-details {\n  margin-top: 1.5rem;\n  text-align: left;\n  background: #f8f9fa;\n  border: 1px solid #dee2e6;\n  border-radius: 4px;\n  padding: 1rem;\n}\n\n.error-details summary {\n  cursor: pointer;\n  font-weight: 600;\n  margin-bottom: 0.5rem;\n}\n\n.error-details pre {\n  background: #ffffff;\n  border: 1px solid #e9ecef;\n  border-radius: 4px;\n  padding: 0.5rem;\n  overflow-x: auto;\n  font-size: 0.8rem;\n  margin: 0.5rem 0;\n}\n\n/* Template Form Styles */\n.template-form {\n  background: white;\n  border-radius: 8px;\n  padding: 3rem;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  border: 1px solid #e0e0e0;\n  margin-top: 1rem;\n}\n\n.template-sentence {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 3rem;\n  font-weight: 300;\n  line-height: 1.5;\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 0.6rem;\n  justify-content: center;\n  margin-bottom: 2rem;\n  letter-spacing: 0.02em;\n}\n\n.template-prefix {\n  color: #D2691E; /* Orange color to match template */\n  font-weight: 400;\n}\n\n.template-suffix {\n  color: #D2691E; /* Orange color to match template */\n  font-weight: 400;\n  font-size: 3.2rem;\n}\n\n.template-relationship {\n  color: #333;\n  font-weight: normal;\n}\n\n.template-input {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 3rem;\n  font-weight: 300;\n  border: none !important;\n  border-bottom: 2px solid #333 !important;\n  border-radius: 0 !important;\n  padding: 0.1rem 0.2rem !important;\n  text-align: center;\n  min-width: 100px;\n  background: transparent !important;\n  outline: none !important;\n  box-shadow: none !important;\n  -webkit-box-shadow: none !important;\n  -moz-box-shadow: none !important;\n  appearance: none !important;\n}\n\n.template-input:focus {\n  outline: none;\n  border-bottom-color: #007bff;\n  box-shadow: none;\n}\n\n.template-count {\n  color: #333;\n  font-weight: 300;\n  width: 120px;\n  border: none !important;\n  border-bottom: 2px solid #333 !important;\n  background: transparent !important;\n  background-color: transparent !important;\n  margin: 0 0.1rem;\n  outline: none !important;\n  box-shadow: none !important;\n  -webkit-box-shadow: none !important;\n  appearance: none !important;\n  padding: 0 0.2rem !important;\n  line-height: 1.2 !important;\n  vertical-align: baseline !important;\n}\n\n.template-measure {\n  color: #333;\n  font-weight: 300;\n  min-width: 150px;\n  border: none !important;\n  border-bottom: 2px solid #333 !important;\n  background: transparent !important;\n  background-color: transparent !important;\n  margin: 0 0.1rem;\n  outline: none !important;\n  box-shadow: none !important;\n  -webkit-box-shadow: none !important;\n  appearance: none !important;\n  padding: 0 0.2rem !important;\n  line-height: 1.2 !important;\n  vertical-align: baseline !important;\n}\n\n.template-calculated-value {\n  color: #333;\n  font-weight: 300;\n  border-bottom: 2px solid #333;\n  min-width: 100px;\n  text-align: center;\n  padding: 0.1rem 0.3rem;\n  background: transparent;\n  display: inline-block;\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 3rem;\n  margin: 0 0.2rem;\n}\n\n.template-dropdown {\n  position: relative;\n  display: inline-block;\n}\n\n/* Seamless entity input styling */\n.template-entity-input {\n  font-size: 3rem !important;\n  font-family: 'Helvetica Neue', 'Arial', sans-serif !important;\n  font-weight: 300 !important;\n  border: none !important;\n  border-bottom: 2px solid #333 !important;\n  border-radius: 0 !important;\n  border-top: none !important;\n  border-left: none !important;\n  border-right: none !important;\n  background: transparent !important;\n  background-color: transparent !important;\n  min-width: 180px;\n  text-align: center;\n  outline: none !important;\n  padding: 0 0.2rem !important;\n  margin: 0 0.1rem !important;\n  box-shadow: none !important;\n  -webkit-box-shadow: none !important;\n  -moz-box-shadow: none !important;\n  appearance: none !important;\n  line-height: 1.2 !important;\n  vertical-align: baseline !important;\n}\n\n/* Alternative: Contenteditable span approach */\n.template-entity-editable {\n  font-size: 3rem;\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-weight: 300;\n  border-bottom: 2px solid #333;\n  background: transparent;\n  min-width: 180px;\n  text-align: center;\n  outline: none;\n  padding: 0 0.2rem;\n  margin: 0 0.1rem;\n  display: inline-block;\n  line-height: 1.2;\n  vertical-align: baseline;\n  cursor: text;\n}\n\n.template-entity-editable:focus {\n  border-bottom-color: #007bff;\n}\n\n.template-entity-editable:empty:before {\n  content: attr(data-placeholder);\n  color: #ccc;\n  font-style: italic;\n}\n\n.template-entity-input.from-entity {\n  color: #008B8B !important; /* Teal for first entity */\n}\n\n.template-entity-input.to-entity {\n  color: #808080 !important; /* Gray for second entity */\n}\n\n/* Make autocomplete dropdown match the input styling */\n.template-entity-input.autocomplete-input {\n  background: transparent !important;\n  box-shadow: none !important;\n  border: none !important;\n  border-bottom: 2px solid #333 !important;\n}\n\n.template-entity-input:focus {\n  border-bottom-color: #333 !important;\n  box-shadow: none !important;\n}\n\n/* Style placeholder text to match */\n.template-entity-input::placeholder {\n  color: #ccc !important;\n  font-weight: 300 !important;\n  font-style: italic !important;\n}\n\n.template-actions {\n  text-align: center;\n  margin-top: 1rem;\n}\n\n/* Additional underline styling */\n.template-input:hover {\n  border-bottom-color: #666;\n}\n\n.template-entity-input:focus {\n  border-bottom-color: #007bff !important;\n}\n\n.template-relationship {\n  color: #333;\n  font-weight: 400;\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n}\n\n/* Template form responsive design */\n@media (max-width: 768px) {\n  .template-sentence {\n    font-size: 2rem;\n    justify-content: flex-start;\n    line-height: 1.4;\n    gap: 0.4rem;\n  }\n  \n  .template-input {\n    font-size: 2rem;\n    min-width: 80px;\n  }\n  \n  .template-count {\n    width: 90px;\n  }\n  \n  .template-measure {\n    min-width: 120px;\n  }\n  \n  .template-entity-input {\n    font-size: 2rem !important;\n    min-width: 140px;\n    font-weight: 300 !important;\n    padding: 0.1rem 0.2rem !important;\n  }\n  \n  .template-calculated-value {\n    font-size: 2rem;\n    font-weight: 300;\n    min-width: 80px;\n    padding: 0.1rem 0.2rem;\n  }\n  \n  .template-count {\n    font-weight: 300;\n    width: 100px;\n  }\n  \n  .template-measure {\n    font-weight: 300;\n    min-width: 120px;\n  }\n  \n  .template-suffix {\n    font-size: 2.2rem;\n  }\n}\n\n/* AutoComplete Styles */\n.autocomplete {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n}\n\n.autocomplete-input {\n  width: 100%;\n  padding: 0.5rem;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  font-size: 1rem;\n}\n\n.autocomplete-input:focus {\n  outline: none;\n  border-color: #007bff;\n  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n/* Additional AutoComplete styling for template */\n.template-dropdown .autocomplete {\n  display: inline-block;\n  position: relative;\n}\n\n.template-dropdown .autocomplete-dropdown {\n  font-size: 1rem;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\n  font-weight: 400;\n  z-index: 1001;\n  min-width: 200px;\n  max-width: 300px;\n  border-radius: 4px;\n  margin-top: 0.5rem;\n}\n\n.autocomplete-dropdown {\n  position: absolute;\n  top: 100%;\n  left: 0;\n  right: 0;\n  background: white;\n  border: 1px solid #ddd;\n  border-top: none;\n  border-radius: 0 0 4px 4px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  max-height: 200px;\n  overflow-y: auto;\n  z-index: 1000;\n}\n\n.autocomplete-option {\n  padding: 0.5rem;\n  cursor: pointer;\n  border-bottom: 1px solid #f0f0f0;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.autocomplete-option:last-child {\n  border-bottom: none;\n}\n\n.autocomplete-option:hover,\n.autocomplete-option.highlighted {\n  background-color: #f8f9fa;\n}\n\n.autocomplete-option.highlighted {\n  background-color: #e3f2fd;\n}\n\n.option-name {\n  font-weight: 500;\n}\n\n.option-id {\n  font-size: 0.8rem;\n  color: #6c757d;\n}\n\n.autocomplete-no-results {\n  padding: 0.5rem;\n  color: #6c757d;\n  font-style: italic;\n  text-align: center;\n}\n\n/* AutoComplete enhanced states */\n.autocomplete-input-wrapper {\n  position: relative;\n  display: flex;\n  align-items: center;\n}\n\n.autocomplete-loading, \n.autocomplete-confirmed {\n  position: absolute;\n  right: 0.5rem;\n  font-size: 0.9rem;\n  pointer-events: none;\n}\n\n.autocomplete-confirmed {\n  color: #28a745;\n  font-weight: bold;\n}\n\n.autocomplete-loading {\n  animation: pulse 1.5s infinite;\n}\n\n@keyframes pulse {\n  0%, 100% { opacity: 1; }\n  50% { opacity: 0.5; }\n}\n\n.autocomplete.loading .autocomplete-input {\n  background-color: #f8f9fa;\n  cursor: wait;\n}\n\n.autocomplete.confirmed .autocomplete-input {\n  border-color: #28a745;\n  background-color: #f8fff8;\n}\n\n.autocomplete.error .autocomplete-input {\n  border-color: #dc3545;\n  background-color: #fff5f5;\n}\n\n.autocomplete-error {\n  color: #dc3545;\n  font-size: 0.875rem;\n  margin-top: 0.25rem;\n  padding-left: 0.5rem;\n}\n\n/* EditableSpan color styling */\n.template-entity-editable.from-entity {\n  color: #008B8B; /* Teal for first entity */\n}\n\n.template-entity-editable.to-entity {\n  color: #808080; /* Gray for second entity */\n}\n\n.template-entity-editable.placeholder {\n  color: #ccc;\n  font-style: italic;\n}\n\n.template-entity-editable:focus {\n  border-bottom-color: #333;\n  outline: none;\n}\n\n/* Remove input number controls */\ninput[type=\"number\"].template-count::-webkit-outer-spin-button,\ninput[type=\"number\"].template-count::-webkit-inner-spin-button {\n  -webkit-appearance: none;\n  margin: 0;\n}\n\ninput[type=\"number\"].template-count {\n  -moz-appearance: textfield;\n}\n\n/* Remove select dropdown styling */\nselect.template-measure {\n  background-image: none;\n  padding-right: 0.5rem;\n}\n\nselect.template-measure::-ms-expand {\n  display: none;\n}\n\n/* Ultra-seamless entity input styling */\n.template-entity-seamless {\n  font-size: 3rem !important;\n  font-family: 'Helvetica Neue', 'Arial', sans-serif !important;\n  font-weight: 300 !important;\n  border: none !important;\n  border-bottom: 2px solid #333 !important;\n  border-top: none !important;\n  border-left: none !important;\n  border-right: none !important;\n  background: transparent !important;\n  background-color: transparent !important;\n  background-image: none !important;\n  min-width: 180px;\n  text-align: center;\n  outline: none !important;\n  padding: 0 0.2rem !important;\n  margin: 0 0.1rem !important;\n  box-shadow: none !important;\n  -webkit-box-shadow: none !important;\n  -moz-box-shadow: none !important;\n  appearance: none !important;\n  line-height: 1.2 !important;\n  vertical-align: baseline !important;\n  border-radius: 0 !important;\n}\n\n.template-entity-seamless.autocomplete-input {\n  font-size: 3rem !important;\n  font-family: 'Helvetica Neue', 'Arial', sans-serif !important;\n  font-weight: 300 !important;\n  border: none !important;\n  border-bottom: 2px solid #333 !important;\n  background: transparent !important;\n  box-shadow: none !important;\n  outline: none !important;\n}\n\n.template-entity-seamless.from-entity {\n  color: #008B8B !important;\n}\n\n.template-entity-seamless.to-entity {\n  color: #808080 !important;\n}\n\n.template-entity-seamless:focus {\n  border: none !important;\n  border-bottom: 2px solid #333 !important;\n  box-shadow: none !important;\n  outline: none !important;\n}\n\n.template-entity-wrapper {\n  display: inline-block;\n  position: relative;\n}\n\n.template-entity-wrapper .autocomplete {\n  display: inline-block;\n  width: auto;\n}\n\n.template-entity-wrapper .autocomplete-dropdown {\n  font-size: 1rem;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;\n  font-weight: 400;\n}\n\n/* Ultra-seamless input styling - complete invisibility except underline */\n.template-input-seamless {\n  font-size: 3rem !important;\n  font-family: 'Helvetica Neue', 'Arial', sans-serif !important;\n  font-weight: 300 !important;\n  \n  /* Completely remove all borders and backgrounds */\n  border: none !important;\n  border-top: none !important;\n  border-left: none !important;\n  border-right: none !important;\n  border-bottom: 2px solid #333 !important;\n  border-radius: 0 !important;\n  \n  background: transparent !important;\n  background-color: transparent !important;\n  background-image: none !important;\n  \n  /* Remove all shadows and outlines */\n  box-shadow: none !important;\n  -webkit-box-shadow: none !important;\n  -moz-box-shadow: none !important;\n  outline: none !important;\n  \n  /* Remove browser default styling */\n  appearance: none !important;\n  \n  /* Positioning and sizing */\n  min-width: 180px;\n  max-width: 200px;\n  text-align: center;\n  padding: 0 0.2rem !important;\n  margin: 0 0.1rem !important;\n  line-height: 1.2 !important;\n  vertical-align: baseline !important;\n}\n\n/* Color variations */\n.template-input-seamless.from-entity {\n  color: #008B8B !important; /* Teal */\n}\n\n.template-input-seamless.to-entity {\n  color: #808080 !important; /* Gray */\n}\n\n/* Focus state - keep the same styling */\n.template-input-seamless:focus {\n  border: none !important;\n  border-bottom: 2px solid #333 !important;\n  background: transparent !important;\n  box-shadow: none !important;\n  outline: none !important;\n}\n\n/* Placeholder styling */\n.template-input-seamless::placeholder {\n  color: #ccc !important;\n  font-style: italic !important;\n  font-weight: 300 !important;\n}\n\n/* Remove any autofill styling */\n.template-input-seamless:-webkit-autofill,\n.template-input-seamless:-webkit-autofill:hover,\n.template-input-seamless:-webkit-autofill:focus {\n  -webkit-box-shadow: 0 0 0 1000px transparent inset !important;\n  -webkit-text-fill-color: inherit !important;\n  background-color: transparent !important;\n  background: transparent !important;\n}\n/*# sourceMappingURL=data:application/json;base64,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 */"]],"\n  ",["BODY",{},"\n    ","\n    ",["DIV",{"id":"root"},["DIV",{"class":"App"},["NAV",{"class":"navigation"},["DIV",{"class":"nav-brand"},["A",{"class":"brand-link","data-testid":"nav-brand-link","href":"/"},["H1",{},"SIMILE"],["P",{},"Entity Comparison System"]]],["DIV",{"class":"nav-links"},["A",{"class":"nav-link active","data-testid":"nav-compare-link","href":"/"},"Compare"],["A",{"class":"nav-link ","data-testid":"nav-entities-link","href":"/entities"},"Entities"],["A",{"class":"nav-link ","data-testid":"nav-connections-link","href":"/connections"},"Connections"]]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},["DIV",{"class":"manager-header"},["H2",{},"Entity Comparison"]],["DIV",{"class":"comparison-content"},["DIV",{"class":"skeleton-form"},["DIV",{"class":"skeleton skeleton-form-title","style":"width: 200px; height: 1.5rem;"}],["DIV",{"class":"form-row"},["DIV",{"class":"form-group"},["DIV",{"class":"skeleton skeleton-label","style":"width: 80px; height: 1rem;"}],["DIV",{"class":"skeleton skeleton-input","style":"width: 100%; height: 2.5rem;"}]],["DIV",{"class":"form-group"},["DIV",{"class":"skeleton skeleton-label","style":"width: 80px; height: 1rem;"}],["DIV",{"class":"skeleton skeleton-input","style":"width: 100%; height: 2.5rem;"}]],["DIV",{"class":"form-group"},["DIV",{"class":"skeleton skeleton-label","style":"width: 80px; height: 1rem;"}],["DIV",{"class":"skeleton skeleton-input","style":"width: 100%; height: 2.5rem;"}]]],["DIV",{"class":"form-actions"},["DIV",{"class":"skeleton ","style":"width: 100px; height: 2.5rem;"}],["DIV",{"class":"skeleton ","style":"width: 80px; height: 2.5rem;"}]]]]]]]],"\n  \n"]],"viewport":{"width":1280,"height":720},"timestamp":133585.317,"wallTime":1751768821400,"collectionTime":5.600000001490116,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@47","startTime":133586.742,"title":"Wait for load state \"networkidle\"","class":"Page","method":"waitForEventInfo","params":{"info":{"waitId":"f1a28ab98759f46a9987d028a378882c","phase":"before","event":""}},"stepId":"pw:api@22","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","beforeSnapshot":"before@call@47"}
{"type":"console","messageType":"log","text":"API Response: GET /entities/ 200 [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]","args":[{"preview":"API Response:","value":"API Response:"},{"preview":"GET","value":"GET"},{"preview":"/entities/","value":"/entities/"},{"preview":"200","value":200},{"preview":"[Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]"}],"location":{"url":"http://localhost:3000/static/js/bundle.js","lineNumber":54575,"columnNumber":10},"time":133587.093,"pageId":"page@a986070605cbb1375f79f3bc4860ec2b"}
{"type":"console","messageType":"log","text":"API Response: GET /units/ 200 [Object, Object, Object, Object, Object]","args":[{"preview":"API Response:","value":"API Response:"},{"preview":"GET","value":"GET"},{"preview":"/units/","value":"/units/"},{"preview":"200","value":200},{"preview":"[Object, Object, Object, Object, Object]"}],"location":{"url":"http://localhost:3000/static/js/bundle.js","lineNumber":54575,"columnNumber":10},"time":133587.231,"pageId":"page@a986070605cbb1375f79f3bc4860ec2b"}
{"type":"console","messageType":"log","text":"API Response: GET /entities/ 200 [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]","args":[{"preview":"API Response:","value":"API Response:"},{"preview":"GET","value":"GET"},{"preview":"/entities/","value":"/entities/"},{"preview":"200","value":200},{"preview":"[Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]"}],"location":{"url":"http://localhost:3000/static/js/bundle.js","lineNumber":54575,"columnNumber":10},"time":133587.34,"pageId":"page@a986070605cbb1375f79f3bc4860ec2b"}
{"type":"console","messageType":"log","text":"API Response: GET /units/ 200 [Object, Object, Object, Object, Object]","args":[{"preview":"API Response:","value":"API Response:"},{"preview":"GET","value":"GET"},{"preview":"/units/","value":"/units/"},{"preview":"200","value":200},{"preview":"[Object, Object, Object, Object, Object]"}],"location":{"url":"http://localhost:3000/static/js/bundle.js","lineNumber":54575,"columnNumber":10},"time":133587.493,"pageId":"page@a986070605cbb1375f79f3bc4860ec2b"}
{"type":"frame-snapshot","snapshot":{"callId":"call@47","snapshotName":"before@call@47","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[1,23]],[[1,24]],["BODY",{},[[1,25]],[[1,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[1,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[1,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},["H3",{},"Compare Entities"],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},["SPAN",{"class":"template-prefix"},"Did you know that"],["INPUT",{"__playwright_value_":"1","type":"number","class":"template-input template-count","min":"1","value":"1"}],["INPUT",{"__playwright_value_":"","type":"text","placeholder":"entity","class":"template-input-seamless from-entity","list":"from-entities","data-testid":"from-entity-input","value":""}],["DATALIST",{"id":"from-entities"},["OPTION",{"__playwright_selected_":"false","value":"A"}],["OPTION",{"__playwright_selected_":"false","value":"B"}],["OPTION",{"__playwright_selected_":"false","value":"Ball BAWGB"}],["OPTION",{"__playwright_selected_":"false","value":"Build BAWGC"}],["OPTION",{"__playwright_selected_":"false","value":"C"}],["OPTION",{"__playwright_selected_":"false","value":"Car BAWGD"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W D AAB Y"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W D AAB Z"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W D AAC J"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W D AAC W"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W D AAD R"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W D AAD Z"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W L AAB U"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W L AAC E"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W L AAD H"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W U AAB Q"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W U AAC D"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W U AAD O"}],["OPTION",{"__playwright_selected_":"false","value":"Eleph BAWGE"}],["OPTION",{"__playwright_selected_":"false","value":"Human BAWGA"}],["OPTION",{"__playwright_selected_":"false","value":"Mouse BAWGF"}]],["SPAN",{"class":"template-relationship"},"is as"],["SELECT",{"class":"template-input template-measure"},["OPTION",{"__playwright_selected_":"true","value":""},"measure"],["OPTION",{"__playwright_selected_":"false","value":"5"},"big"],["OPTION",{"__playwright_selected_":"false","value":"1"},"tall"],["OPTION",{"__playwright_selected_":"false","value":"2"},"heavy"],["OPTION",{"__playwright_selected_":"false","value":"4"},"long"],["OPTION",{"__playwright_selected_":"false","value":"3"},"voluminous"]],["SPAN",{"class":"template-relationship"},"as"],["SPAN",{"class":"template-calculated-value","data-testid":"comparison-result"},"?"],["INPUT",{"__playwright_value_":"","type":"text","placeholder":"entity","class":"template-input-seamless to-entity","list":"to-entities","data-testid":"to-entity-input","value":""}],["DATALIST",{"id":"to-entities"},["OPTION",{"__playwright_selected_":"false","value":"A"}],["OPTION",{"__playwright_selected_":"false","value":"B"}],["OPTION",{"__playwright_selected_":"false","value":"Ball BAWGB"}],["OPTION",{"__playwright_selected_":"false","value":"Build BAWGC"}],["OPTION",{"__playwright_selected_":"false","value":"C"}],["OPTION",{"__playwright_selected_":"false","value":"Car BAWGD"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W D AAB Y"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W D AAB Z"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W D AAC J"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W D AAC W"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W D AAD R"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W D AAD Z"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W L AAB U"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W L AAC E"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W L AAD H"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W U AAB Q"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W U AAC D"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W U AAD O"}],["OPTION",{"__playwright_selected_":"false","value":"Eleph BAWGE"}],["OPTION",{"__playwright_selected_":"false","value":"Human BAWGA"}],["OPTION",{"__playwright_selected_":"false","value":"Mouse BAWGF"}]],["SPAN",{"class":"template-suffix"},"?"]],["DIV",{"class":"template-actions"},["BUTTON",{"type":"button","class":"btn-secondary"},"Clear"]]]]]]]]],[[1,64]]]],"viewport":{"width":1280,"height":720},"timestamp":133593.497,"wallTime":1751768821408,"collectionTime":3.5,"resourceOverrides":[],"isMainFrame":true}}
{"type":"screencast-frame","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":133602.47,"frameSwapWallTime":1751768821416.6638}
{"type":"screencast-frame","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":133615.956,"frameSwapWallTime":1751768821429.926}
{"type":"log","callId":"call@47","time":134099.715,"message":"  \"networkidle\" event fired"}
{"type":"after","callId":"call@47","endTime":134099.738,"afterSnapshot":"after@call@47"}
{"type":"before","callId":"call@51","startTime":134100.164,"title":"Wait for load state \"networkidle\"","class":"Page","method":"waitForEventInfo","params":{"info":{"waitId":"0199f917ae8494714379e0940dadbd53","phase":"before","event":""}},"stepId":"pw:api@23","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","beforeSnapshot":"before@call@51"}
{"type":"log","callId":"call@51","time":134100.366,"message":"  not waiting, \"networkidle\" event already fired"}
{"type":"after","callId":"call@51","endTime":134100.376,"afterSnapshot":"after@call@51"}
{"type":"before","callId":"call@55","startTime":134100.625,"class":"Frame","method":"waitForSelector","params":{"selector":"nav","timeout":10000},"stepId":"pw:api@24","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","beforeSnapshot":"before@call@55"}
{"type":"frame-snapshot","snapshot":{"callId":"call@47","snapshotName":"after@call@47","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,84]],"viewport":{"width":1280,"height":720},"timestamp":134100.941,"wallTime":1751768821916,"collectionTime":0.3999999985098839,"resourceOverrides":[],"isMainFrame":true}}
{"type":"frame-snapshot","snapshot":{"callId":"call@51","snapshotName":"before@call@51","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[2,84]],"viewport":{"width":1280,"height":720},"timestamp":134101.013,"wallTime":1751768821916,"collectionTime":0.30000000447034836,"resourceOverrides":[],"isMainFrame":true}}
{"type":"frame-snapshot","snapshot":{"callId":"call@51","snapshotName":"after@call@51","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[3,84]],"viewport":{"width":1280,"height":720},"timestamp":134101.276,"wallTime":1751768821916,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"frame-snapshot","snapshot":{"callId":"call@55","snapshotName":"before@call@55","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[4,84]],"viewport":{"width":1280,"height":720},"timestamp":134101.557,"wallTime":1751768821916,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@55","time":134102.456,"message":"waiting for locator('nav') to be visible"}
{"type":"log","callId":"call@55","time":134112.261,"message":"  locator resolved to visible <nav class=\"navigation\">…</nav>"}
{"type":"after","callId":"call@55","endTime":134113.969,"result":{"element":"<ElementHandle>"},"afterSnapshot":"after@call@55"}
{"type":"frame-snapshot","snapshot":{"callId":"call@55","snapshotName":"after@call@55","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[5,84]],"viewport":{"width":1280,"height":720},"timestamp":134116.819,"wallTime":1751768821932,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@57","startTime":134118.206,"class":"Frame","method":"waitForSelector","params":{"selector":"[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"] >> nth=0","strict":true,"timeout":10000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@25","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","beforeSnapshot":"before@call@57"}
{"type":"frame-snapshot","snapshot":{"callId":"call@57","snapshotName":"before@call@57","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[6,84]],"viewport":{"width":1280,"height":720},"timestamp":134118.485,"wallTime":1751768821934,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@57","time":134118.709,"message":"waiting for locator('[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"]').first() to be visible"}
{"type":"log","callId":"call@57","time":134119.768,"message":"  locator resolved to visible <input value=\"\" type=\"text\" placeholder=\"entity\" list=\"from-entities\" data-testid=\"from-entity-input\" class=\"template-input-seamless from-entity\"/>"}
{"type":"after","callId":"call@57","endTime":134119.785,"result":{},"afterSnapshot":"after@call@57"}
{"type":"frame-snapshot","snapshot":{"callId":"call@57","snapshotName":"after@call@57","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[7,84]],"viewport":{"width":1280,"height":720},"timestamp":134120.135,"wallTime":1751768821935,"collectionTime":0.10000000149011612,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@59","startTime":134120.826,"class":"Frame","method":"click","params":{"selector":"[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"] >> nth=0","strict":true,"timeout":8000},"stepId":"pw:api@26","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","beforeSnapshot":"before@call@59"}
{"type":"frame-snapshot","snapshot":{"callId":"call@59","snapshotName":"before@call@59","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[8,84]],"viewport":{"width":1280,"height":720},"timestamp":134121.107,"wallTime":1751768821936,"collectionTime":0.19999999552965164,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@59","time":134121.372,"message":"waiting for locator('[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"]').first()"}
{"type":"log","callId":"call@59","time":134122.263,"message":"  locator resolved to <input value=\"\" type=\"text\" placeholder=\"entity\" list=\"from-entities\" data-testid=\"from-entity-input\" class=\"template-input-seamless from-entity\"/>"}
{"type":"log","callId":"call@59","time":134122.56,"message":"attempting click action"}
{"type":"log","callId":"call@59","time":134122.685,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@59","time":134137.439,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@59","time":134137.445,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@59","time":134137.669,"message":"  done scrolling"}
{"type":"input","callId":"call@59","point":{"x":853.98,"y":358.18},"inputSnapshot":"input@call@59"}
{"type":"frame-snapshot","snapshot":{"callId":"call@59","snapshotName":"input@call@59","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[10,23]],[[10,24]],["BODY",{},[[10,25]],[[10,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[10,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[10,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[9,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[9,3]],[[9,4]],["INPUT",{"__playwright_value_":"","__playwright_target__":"call@59","type":"text","placeholder":"entity","class":"template-input-seamless from-entity","list":"from-entities","data-testid":"from-entity-input","value":""}],[[9,27]],[[9,29]],[[9,42]],[[9,44]],[[9,46]],[[9,47]],[[9,69]],[[9,71]]],[[9,75]]]]]]]]],[[10,64]]]],"viewport":{"width":1280,"height":720},"timestamp":134138.392,"wallTime":1751768821953,"collectionTime":0.30000000447034836,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@59","time":134139.269,"message":"  performing click action"}
{"type":"log","callId":"call@59","time":134149.807,"message":"  click action done"}
{"type":"log","callId":"call@59","time":134149.816,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@59","time":134149.938,"message":"  navigations have finished"}
{"type":"after","callId":"call@59","endTime":134149.98,"point":{"x":853.98,"y":358.18},"afterSnapshot":"after@call@59"}
{"type":"frame-snapshot","snapshot":{"callId":"call@59","snapshotName":"after@call@59","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,10]],"viewport":{"width":1280,"height":720},"timestamp":134150.325,"wallTime":1751768821965,"collectionTime":0.10000000149011612,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@61","startTime":134151.006,"class":"Frame","method":"fill","params":{"selector":"[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"] >> nth=0","strict":true,"value":"","timeout":8000},"stepId":"pw:api@27","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","beforeSnapshot":"before@call@61"}
{"type":"frame-snapshot","snapshot":{"callId":"call@61","snapshotName":"before@call@61","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[2,10]],"viewport":{"width":1280,"height":720},"timestamp":134151.256,"wallTime":1751768821966,"collectionTime":0.10000000149011612,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@61","time":134151.447,"message":"waiting for locator('[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"]').first()"}
{"type":"log","callId":"call@61","time":134152.263,"message":"  locator resolved to <input value=\"\" type=\"text\" placeholder=\"entity\" list=\"from-entities\" data-testid=\"from-entity-input\" class=\"template-input-seamless from-entity\"/>"}
{"type":"log","callId":"call@61","time":134152.55,"message":"  fill(\"\")"}
{"type":"log","callId":"call@61","time":134152.554,"message":"attempting fill action"}
{"type":"input","callId":"call@61","inputSnapshot":"input@call@61"}
{"type":"frame-snapshot","snapshot":{"callId":"call@61","snapshotName":"input@call@61","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[13,23]],[[13,24]],["BODY",{},[[13,25]],[[13,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[13,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[13,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[12,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[12,3]],[[12,4]],["INPUT",{"__playwright_value_":"","__playwright_target__":"call@61","type":"text","placeholder":"entity","class":"template-input-seamless from-entity","list":"from-entities","data-testid":"from-entity-input","value":""}],[[12,27]],[[12,29]],[[12,42]],[[12,44]],[[12,46]],[[12,47]],[[12,69]],[[12,71]]],[[12,75]]]]]]]]],[[13,64]]]],"viewport":{"width":1280,"height":720},"timestamp":134152.869,"wallTime":1751768821968,"collectionTime":0.10000000149011612,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@61","time":134152.919,"message":"  waiting for element to be visible, enabled and editable"}
{"type":"after","callId":"call@61","endTime":134169.58,"afterSnapshot":"after@call@61"}
{"type":"frame-snapshot","snapshot":{"callId":"call@61","snapshotName":"after@call@61","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,10]],"viewport":{"width":1280,"height":720},"timestamp":134169.93,"wallTime":1751768821985,"collectionTime":0.10000000149011612,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@63","startTime":134170.638,"class":"Frame","method":"waitForSelector","params":{"selector":"[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"] >> nth=0","strict":true,"timeout":8000,"state":"attached","omitReturnValue":true},"stepId":"pw:api@28","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","beforeSnapshot":"before@call@63"}
{"type":"frame-snapshot","snapshot":{"callId":"call@63","snapshotName":"before@call@63","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[2,10]],"viewport":{"width":1280,"height":720},"timestamp":134170.933,"wallTime":1751768821986,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@63","time":134171.07,"message":"waiting for locator('[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"]').first()"}
{"type":"log","callId":"call@63","time":134171.831,"message":"  locator resolved to visible <input value=\"\" type=\"text\" placeholder=\"entity\" list=\"from-entities\" data-testid=\"from-entity-input\" class=\"template-input-seamless from-entity\"/>"}
{"type":"after","callId":"call@63","endTime":134171.843,"result":{},"afterSnapshot":"after@call@63"}
{"type":"frame-snapshot","snapshot":{"callId":"call@63","snapshotName":"after@call@63","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[3,10]],"viewport":{"width":1280,"height":720},"timestamp":134172.12,"wallTime":1751768821987,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@65","startTime":134172.615,"class":"Frame","method":"fill","params":{"selector":"[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"] >> nth=0","strict":true,"value":"Human","timeout":8000},"stepId":"pw:api@29","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","beforeSnapshot":"before@call@65"}
{"type":"frame-snapshot","snapshot":{"callId":"call@65","snapshotName":"before@call@65","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[4,10]],"viewport":{"width":1280,"height":720},"timestamp":134172.854,"wallTime":1751768821988,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@65","time":134172.973,"message":"waiting for locator('[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"]').first()"}
{"type":"log","callId":"call@65","time":134173.633,"message":"  locator resolved to <input value=\"\" type=\"text\" placeholder=\"entity\" list=\"from-entities\" data-testid=\"from-entity-input\" class=\"template-input-seamless from-entity\"/>"}
{"type":"log","callId":"call@65","time":134173.885,"message":"  fill(\"Human\")"}
{"type":"log","callId":"call@65","time":134173.888,"message":"attempting fill action"}
{"type":"input","callId":"call@65","inputSnapshot":"input@call@65"}
{"type":"frame-snapshot","snapshot":{"callId":"call@65","snapshotName":"input@call@65","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[18,23]],[[18,24]],["BODY",{},[[18,25]],[[18,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[18,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[18,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[17,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[17,3]],[[17,4]],["INPUT",{"__playwright_value_":"","__playwright_target__":"call@65","type":"text","placeholder":"entity","class":"template-input-seamless from-entity","list":"from-entities","data-testid":"from-entity-input","value":""}],[[17,27]],[[17,29]],[[17,42]],[[17,44]],[[17,46]],[[17,47]],[[17,69]],[[17,71]]],[[17,75]]]]]]]]],[[18,64]]]],"viewport":{"width":1280,"height":720},"timestamp":134174.222,"wallTime":1751768821989,"collectionTime":0.10000000149011612,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@65","time":134174.265,"message":"  waiting for element to be visible, enabled and editable"}
{"type":"screencast-frame","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":134176.222,"frameSwapWallTime":1751768821990.1929}
{"type":"after","callId":"call@65","endTime":134176.334,"afterSnapshot":"after@call@65"}
{"type":"frame-snapshot","snapshot":{"callId":"call@65","snapshotName":"after@call@65","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[19,23]],[[19,24]],["BODY",{},[[19,25]],[[19,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[19,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[19,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[18,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[18,3]],[[18,4]],["INPUT",{"__playwright_value_":"Human","__playwright_scroll_left_":"15","__playwright_target__":"call@65","type":"text","placeholder":"entity","class":"template-input-seamless from-entity","list":"from-entities","data-testid":"from-entity-input","value":"Human"}],[[18,27]],[[18,29]],[[18,42]],[[18,44]],[[18,46]],[[18,47]],[[18,69]],[[18,71]]],[[18,75]]]]]]]]],[[19,64]]]],"viewport":{"width":1280,"height":720},"timestamp":134176.74,"wallTime":1751768821992,"collectionTime":0,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@67","startTime":134177.5,"class":"Frame","method":"waitForSelector","params":{"selector":".autocomplete-dropdown","strict":true,"timeout":3000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@30","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","beforeSnapshot":"before@call@67"}
{"type":"frame-snapshot","snapshot":{"callId":"call@67","snapshotName":"before@call@67","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,10]],"viewport":{"width":1280,"height":720},"timestamp":134177.799,"wallTime":1751768821993,"collectionTime":0.10000000149011612,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@67","time":134177.951,"message":"waiting for locator('.autocomplete-dropdown') to be visible"}
{"type":"screencast-frame","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":134191.704,"frameSwapWallTime":1751768822005.6182}
{"type":"screencast-frame","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":134695.21,"frameSwapWallTime":1751768822507.3481}
{"type":"screencast-frame","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":135193.906,"frameSwapWallTime":1751768823007.601}
{"type":"screencast-frame","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":135694.905,"frameSwapWallTime":1751768823507.596}
{"type":"screencast-frame","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":136193.254,"frameSwapWallTime":1751768824005.589}
{"type":"screencast-frame","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":136695.964,"frameSwapWallTime":1751768824508.2368}
{"type":"after","callId":"call@67","endTime":137178.999,"error":{"message":"Timeout 3000ms exceeded.","stack":"TimeoutError: Timeout 3000ms exceeded.\n    at ProgressController.run (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/progress.js:76:26)\n    at Frame.waitForSelector (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/frames.js:615:23)\n    at FrameDispatcher.waitForSelector (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/dispatchers/frameDispatcher.js:82:116)\n    at FrameDispatcher._handleCommand (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/dispatchers/dispatcher.js:88:40)\n    at DispatcherConnection.dispatch (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/dispatchers/dispatcher.js:309:39)","name":"TimeoutError"},"afterSnapshot":"after@call@67"}
{"type":"frame-snapshot","snapshot":{"callId":"call@67","snapshotName":"after@call@67","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[2,10]],"viewport":{"width":1280,"height":720},"timestamp":137181.578,"wallTime":1751768824996,"collectionTime":0.6000000014901161,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@69","startTime":137184.442,"class":"Frame","method":"waitForSelector","params":{"selector":".autocomplete-dropdown >> nth=0","strict":true,"timeout":3000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@31","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","beforeSnapshot":"before@call@69"}
{"type":"frame-snapshot","snapshot":{"callId":"call@69","snapshotName":"before@call@69","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[3,10]],"viewport":{"width":1280,"height":720},"timestamp":137185.039,"wallTime":1751768825000,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@69","time":137185.392,"message":"waiting for locator('.autocomplete-dropdown').first() to be visible"}
{"type":"screencast-frame","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":137190.767,"frameSwapWallTime":1751768825004.8088}
{"type":"screencast-frame","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":137695.589,"frameSwapWallTime":1751768825507.952}
{"type":"screencast-frame","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":138194.357,"frameSwapWallTime":1751768826006.754}
{"type":"screencast-frame","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":138695.866,"frameSwapWallTime":1751768826508.158}
{"type":"screencast-frame","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":139195.217,"frameSwapWallTime":1751768827007.4092}
{"type":"screencast-frame","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":139694.098,"frameSwapWallTime":1751768827506.361}
{"type":"after","callId":"call@69","endTime":140184.766,"error":{"message":"Timeout 3000ms exceeded.","stack":"TimeoutError: Timeout 3000ms exceeded.\n    at ProgressController.run (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/progress.js:76:26)\n    at Frame.waitForSelector (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/frames.js:615:23)\n    at FrameDispatcher.waitForSelector (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/dispatchers/frameDispatcher.js:82:116)\n    at FrameDispatcher._handleCommand (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/dispatchers/dispatcher.js:88:40)\n    at DispatcherConnection.dispatch (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/dispatchers/dispatcher.js:309:39)","name":"TimeoutError"},"afterSnapshot":"after@call@69"}
{"type":"frame-snapshot","snapshot":{"callId":"call@69","snapshotName":"after@call@69","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[4,10]],"viewport":{"width":1280,"height":720},"timestamp":140185.332,"wallTime":1751768828000,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@71","startTime":140186.105,"class":"Frame","method":"queryCount","params":{"selector":"datalist option"},"stepId":"pw:api@32","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","beforeSnapshot":"before@call@71"}
{"type":"frame-snapshot","snapshot":{"callId":"call@71","snapshotName":"before@call@71","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[5,10]],"viewport":{"width":1280,"height":720},"timestamp":140186.431,"wallTime":1751768828001,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"after","callId":"call@71","endTime":140187.221,"result":{"value":42},"afterSnapshot":"after@call@71"}
{"type":"frame-snapshot","snapshot":{"callId":"call@71","snapshotName":"after@call@71","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[6,10]],"viewport":{"width":1280,"height":720},"timestamp":140187.564,"wallTime":1751768828002,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@73","startTime":140188.22,"class":"Frame","method":"queryCount","params":{"selector":"datalist option >> internal:has-text=\"Human BAWGA\"i"},"stepId":"pw:api@33","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","beforeSnapshot":"before@call@73"}
{"type":"frame-snapshot","snapshot":{"callId":"call@73","snapshotName":"before@call@73","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[7,10]],"viewport":{"width":1280,"height":720},"timestamp":140188.526,"wallTime":1751768828003,"collectionTime":0,"resourceOverrides":[],"isMainFrame":true}}
{"type":"after","callId":"call@73","endTime":140189.271,"result":{"value":0},"afterSnapshot":"after@call@73"}
{"type":"frame-snapshot","snapshot":{"callId":"call@73","snapshotName":"after@call@73","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[8,10]],"viewport":{"width":1280,"height":720},"timestamp":140189.51,"wallTime":1751768828004,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@75","startTime":140190.082,"class":"Frame","method":"fill","params":{"selector":"[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"] >> nth=0","strict":true,"value":"Human BAWGA","timeout":8000},"stepId":"pw:api@34","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","beforeSnapshot":"before@call@75"}
{"type":"frame-snapshot","snapshot":{"callId":"call@75","snapshotName":"before@call@75","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[9,10]],"viewport":{"width":1280,"height":720},"timestamp":140190.328,"wallTime":1751768828005,"collectionTime":0.10000000149011612,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@75","time":140190.498,"message":"waiting for locator('[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"]').first()"}
{"type":"screencast-frame","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":140191.146,"frameSwapWallTime":1751768828005.137}
{"type":"log","callId":"call@75","time":140191.225,"message":"  locator resolved to <input type=\"text\" value=\"Human\" placeholder=\"entity\" list=\"from-entities\" data-testid=\"from-entity-input\" class=\"template-input-seamless from-entity\"/>"}
{"type":"log","callId":"call@75","time":140191.426,"message":"  fill(\"Human BAWGA\")"}
{"type":"log","callId":"call@75","time":140191.427,"message":"attempting fill action"}
{"type":"input","callId":"call@75","inputSnapshot":"input@call@75"}
{"type":"frame-snapshot","snapshot":{"callId":"call@75","snapshotName":"input@call@75","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[29,23]],[[29,24]],["BODY",{},[[29,25]],[[29,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[29,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[29,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[28,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[28,3]],[[28,4]],["INPUT",{"__playwright_value_":"Human","__playwright_scroll_left_":"15","__playwright_target__":"call@75","type":"text","placeholder":"entity","class":"template-input-seamless from-entity","list":"from-entities","data-testid":"from-entity-input","value":"Human"}],[[28,27]],[[28,29]],[[28,42]],[[28,44]],[[28,46]],[[28,47]],[[28,69]],[[28,71]]],[[28,75]]]]]]]]],[[29,64]]]],"viewport":{"width":1280,"height":720},"timestamp":140191.72,"wallTime":1751768828007,"collectionTime":0.10000000149011612,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@75","time":140191.753,"message":"  waiting for element to be visible, enabled and editable"}
{"type":"after","callId":"call@75","endTime":140193.647,"afterSnapshot":"after@call@75"}
{"type":"frame-snapshot","snapshot":{"callId":"call@75","snapshotName":"after@call@75","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[30,23]],[[30,24]],["BODY",{},[[30,25]],[[30,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[30,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[30,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[29,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[29,3]],[[29,4]],["INPUT",{"__playwright_value_":"Human BAWGA","__playwright_scroll_left_":"193","__playwright_target__":"call@75","type":"text","placeholder":"entity","class":"template-input-seamless from-entity","list":"from-entities","data-testid":"from-entity-input","value":"Human BAWGA"}],[[29,27]],[[29,29]],[[29,42]],[[29,44]],[[29,46]],[[29,47]],["DATALIST",{"id":"to-entities"},[[29,48]],[[29,49]],[[29,50]],[[29,51]],[[29,52]],[[29,53]],[[29,54]],[[29,55]],[[29,56]],[[29,57]],[[29,58]],[[29,59]],[[29,60]],[[29,61]],[[29,62]],[[29,63]],[[29,64]],[[29,65]],[[29,66]],[[29,68]]],[[29,71]]],[[29,75]]]]]]]]],[[30,64]]]],"viewport":{"width":1280,"height":720},"timestamp":140194.074,"wallTime":1751768828009,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@77","startTime":140194.661,"class":"Page","method":"keyboardPress","params":{"key":"Tab"},"stepId":"pw:api@35","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","beforeSnapshot":"before@call@77"}
{"type":"frame-snapshot","snapshot":{"callId":"call@77","snapshotName":"before@call@77","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,11]],"viewport":{"width":1280,"height":720},"timestamp":140195.048,"wallTime":1751768828010,"collectionTime":0.19999999552965164,"resourceOverrides":[],"isMainFrame":true}}
{"type":"after","callId":"call@77","endTime":140195.752,"afterSnapshot":"after@call@77"}
{"type":"frame-snapshot","snapshot":{"callId":"call@77","snapshotName":"after@call@77","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[32,23]],[[32,24]],["BODY",{},[[32,25]],[[32,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[32,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[32,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[31,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[31,3]],[[31,4]],["INPUT",{"__playwright_value_":"Human BAWGA","__playwright_target__":"call@75","type":"text","placeholder":"entity","class":"template-input-seamless from-entity","list":"from-entities","data-testid":"from-entity-input","value":"Human BAWGA"}],[[31,27]],[[31,29]],[[31,42]],[[31,44]],[[31,46]],[[31,47]],[[2,1]],[[31,71]]],[[31,75]]]]]]]]],[[32,64]]]],"viewport":{"width":1280,"height":720},"timestamp":140196.055,"wallTime":1751768828011,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@79","startTime":140196.716,"class":"Frame","method":"inputValue","params":{"selector":"[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"] >> nth=0","strict":true,"timeout":8000},"stepId":"pw:api@36","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","beforeSnapshot":"before@call@79"}
{"type":"frame-snapshot","snapshot":{"callId":"call@79","snapshotName":"before@call@79","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,10]],"viewport":{"width":1280,"height":720},"timestamp":140196.976,"wallTime":1751768828012,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@79","time":140197.322,"message":"waiting for locator('[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"]').first()"}
{"type":"log","callId":"call@79","time":140197.875,"message":"  locator resolved to <input type=\"text\" value=\"Human BAWGA\" placeholder=\"entity\" list=\"from-entities\" data-testid=\"from-entity-input\" class=\"template-input-seamless from-entity\"/>"}
{"type":"after","callId":"call@79","endTime":140197.885,"result":{"value":"Human BAWGA"},"afterSnapshot":"after@call@79"}
{"type":"frame-snapshot","snapshot":{"callId":"call@79","snapshotName":"after@call@79","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[34,23]],[[34,24]],["BODY",{},[[34,25]],[[34,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[34,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[34,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[33,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[33,3]],[[33,4]],["INPUT",{"__playwright_value_":"Human BAWGA","__playwright_target__":"call@79","type":"text","placeholder":"entity","class":"template-input-seamless from-entity","list":"from-entities","data-testid":"from-entity-input","value":"Human BAWGA"}],[[33,27]],[[33,29]],[[33,42]],[[33,44]],[[33,46]],[[33,47]],[[4,1]],[[33,71]]],[[33,75]]]]]]]]],[[34,64]]]],"viewport":{"width":1280,"height":720},"timestamp":140198.138,"wallTime":1751768828013,"collectionTime":0.10000000149011612,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@81","startTime":140198.683,"class":"Frame","method":"inputValue","params":{"selector":"[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"] >> nth=0","strict":true,"timeout":8000},"stepId":"pw:api@37","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","beforeSnapshot":"before@call@81"}
{"type":"frame-snapshot","snapshot":{"callId":"call@81","snapshotName":"before@call@81","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,10]],"viewport":{"width":1280,"height":720},"timestamp":140198.937,"wallTime":1751768828014,"collectionTime":0.10000000149011612,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@81","time":140199.12,"message":"waiting for locator('[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"]').first()"}
{"type":"log","callId":"call@81","time":140199.552,"message":"  locator resolved to <input type=\"text\" value=\"Human BAWGA\" placeholder=\"entity\" list=\"from-entities\" data-testid=\"from-entity-input\" class=\"template-input-seamless from-entity\"/>"}
{"type":"after","callId":"call@81","endTime":140199.56,"result":{"value":"Human BAWGA"},"afterSnapshot":"after@call@81"}
{"type":"frame-snapshot","snapshot":{"callId":"call@81","snapshotName":"after@call@81","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[36,23]],[[36,24]],["BODY",{},[[36,25]],[[36,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[36,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[36,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[35,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[35,3]],[[35,4]],["INPUT",{"__playwright_value_":"Human BAWGA","__playwright_target__":"call@81","type":"text","placeholder":"entity","class":"template-input-seamless from-entity","list":"from-entities","data-testid":"from-entity-input","value":"Human BAWGA"}],[[35,27]],[[35,29]],[[35,42]],[[35,44]],[[35,46]],[[35,47]],[[6,1]],[[35,71]]],[[35,75]]]]]]]]],[[36,64]]]],"viewport":{"width":1280,"height":720},"timestamp":140199.808,"wallTime":1751768828015,"collectionTime":0.10000000149011612,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@83","startTime":140200.382,"class":"Frame","method":"click","params":{"selector":"[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"] >> nth=0","strict":true,"timeout":8000},"stepId":"pw:api@38","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","beforeSnapshot":"before@call@83"}
{"type":"frame-snapshot","snapshot":{"callId":"call@83","snapshotName":"before@call@83","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,10]],"viewport":{"width":1280,"height":720},"timestamp":140200.601,"wallTime":1751768828016,"collectionTime":0.10000000149011612,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@83","time":140200.724,"message":"waiting for locator('[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"]').first()"}
{"type":"log","callId":"call@83","time":140201.333,"message":"  locator resolved to <input value=\"\" type=\"text\" list=\"to-entities\" placeholder=\"entity\" data-testid=\"to-entity-input\" class=\"template-input-seamless to-entity\"/>"}
{"type":"log","callId":"call@83","time":140201.532,"message":"attempting click action"}
{"type":"log","callId":"call@83","time":140201.545,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"screencast-frame","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":140209.67,"frameSwapWallTime":1751768828023.501}
{"type":"log","callId":"call@83","time":140219.016,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@83","time":140219.025,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@83","time":140219.126,"message":"  done scrolling"}
{"type":"input","callId":"call@83","point":{"x":834.89,"y":442.37},"inputSnapshot":"input@call@83"}
{"type":"frame-snapshot","snapshot":{"callId":"call@83","snapshotName":"input@call@83","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[38,23]],[[38,24]],["BODY",{},[[38,25]],[[38,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[38,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[38,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[37,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[37,3]],[[37,4]],[[2,0]],[[37,27]],[[37,29]],[[37,42]],[[37,44]],[[37,46]],["INPUT",{"__playwright_value_":"","__playwright_target__":"call@83","type":"text","placeholder":"entity","class":"template-input-seamless to-entity","list":"to-entities","data-testid":"to-entity-input","value":""}],[[8,1]],[[37,71]]],[[37,75]]]]]]]]],[[38,64]]]],"viewport":{"width":1280,"height":720},"timestamp":140219.695,"wallTime":1751768828035,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@83","time":140220.065,"message":"  performing click action"}
{"type":"log","callId":"call@83","time":140221.066,"message":"  click action done"}
{"type":"log","callId":"call@83","time":140221.069,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@83","time":140221.215,"message":"  navigations have finished"}
{"type":"after","callId":"call@83","endTime":140221.256,"point":{"x":834.89,"y":442.37},"afterSnapshot":"after@call@83"}
{"type":"frame-snapshot","snapshot":{"callId":"call@83","snapshotName":"after@call@83","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,10]],"viewport":{"width":1280,"height":720},"timestamp":140221.545,"wallTime":1751768828036,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@85","startTime":140222.145,"class":"Frame","method":"fill","params":{"selector":"[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"] >> nth=0","strict":true,"value":"","timeout":8000},"stepId":"pw:api@39","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","beforeSnapshot":"before@call@85"}
{"type":"frame-snapshot","snapshot":{"callId":"call@85","snapshotName":"before@call@85","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[2,10]],"viewport":{"width":1280,"height":720},"timestamp":140222.363,"wallTime":1751768828037,"collectionTime":0.10000000149011612,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@85","time":140222.485,"message":"waiting for locator('[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"]').first()"}
{"type":"log","callId":"call@85","time":140223.047,"message":"  locator resolved to <input value=\"\" type=\"text\" list=\"to-entities\" placeholder=\"entity\" data-testid=\"to-entity-input\" class=\"template-input-seamless to-entity\"/>"}
{"type":"log","callId":"call@85","time":140223.257,"message":"  fill(\"\")"}
{"type":"log","callId":"call@85","time":140223.259,"message":"attempting fill action"}
{"type":"input","callId":"call@85","inputSnapshot":"input@call@85"}
{"type":"frame-snapshot","snapshot":{"callId":"call@85","snapshotName":"input@call@85","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[41,23]],[[41,24]],["BODY",{},[[41,25]],[[41,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[41,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[41,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[40,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[40,3]],[[40,4]],[[5,0]],[[40,27]],[[40,29]],[[40,42]],[[40,44]],[[40,46]],["INPUT",{"__playwright_value_":"","__playwright_target__":"call@85","type":"text","placeholder":"entity","class":"template-input-seamless to-entity","list":"to-entities","data-testid":"to-entity-input","value":""}],[[11,1]],[[40,71]]],[[40,75]]]]]]]]],[[41,64]]]],"viewport":{"width":1280,"height":720},"timestamp":140223.496,"wallTime":1751768828038,"collectionTime":0.10000000149011612,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@85","time":140223.526,"message":"  waiting for element to be visible, enabled and editable"}
{"type":"after","callId":"call@85","endTime":140224.031,"afterSnapshot":"after@call@85"}
{"type":"frame-snapshot","snapshot":{"callId":"call@85","snapshotName":"after@call@85","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,10]],"viewport":{"width":1280,"height":720},"timestamp":140224.234,"wallTime":1751768828039,"collectionTime":0,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@87","startTime":140224.73,"class":"Frame","method":"waitForSelector","params":{"selector":"[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"] >> nth=0","strict":true,"timeout":8000,"state":"attached","omitReturnValue":true},"stepId":"pw:api@40","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","beforeSnapshot":"before@call@87"}
{"type":"frame-snapshot","snapshot":{"callId":"call@87","snapshotName":"before@call@87","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[2,10]],"viewport":{"width":1280,"height":720},"timestamp":140224.927,"wallTime":1751768828040,"collectionTime":0,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@87","time":140225.062,"message":"waiting for locator('[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"]').first()"}
{"type":"log","callId":"call@87","time":140225.558,"message":"  locator resolved to visible <input value=\"\" type=\"text\" list=\"to-entities\" placeholder=\"entity\" data-testid=\"to-entity-input\" class=\"template-input-seamless to-entity\"/>"}
{"type":"after","callId":"call@87","endTime":140225.568,"result":{},"afterSnapshot":"after@call@87"}
{"type":"frame-snapshot","snapshot":{"callId":"call@87","snapshotName":"after@call@87","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[3,10]],"viewport":{"width":1280,"height":720},"timestamp":140225.757,"wallTime":1751768828041,"collectionTime":0,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@89","startTime":140227.78,"class":"Frame","method":"fill","params":{"selector":"[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"] >> nth=0","strict":true,"value":"Mouse","timeout":8000},"stepId":"pw:api@41","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","beforeSnapshot":"before@call@89"}
{"type":"frame-snapshot","snapshot":{"callId":"call@89","snapshotName":"before@call@89","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[4,10]],"viewport":{"width":1280,"height":720},"timestamp":140228.133,"wallTime":1751768828043,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@89","time":140228.293,"message":"waiting for locator('[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"]').first()"}
{"type":"log","callId":"call@89","time":140228.982,"message":"  locator resolved to <input value=\"\" type=\"text\" list=\"to-entities\" placeholder=\"entity\" data-testid=\"to-entity-input\" class=\"template-input-seamless to-entity\"/>"}
{"type":"log","callId":"call@89","time":140229.192,"message":"  fill(\"Mouse\")"}
{"type":"log","callId":"call@89","time":140229.195,"message":"attempting fill action"}
{"type":"input","callId":"call@89","inputSnapshot":"input@call@89"}
{"type":"frame-snapshot","snapshot":{"callId":"call@89","snapshotName":"input@call@89","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[46,23]],[[46,24]],["BODY",{},[[46,25]],[[46,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[46,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[46,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[45,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[45,3]],[[45,4]],[[10,0]],[[45,27]],[[45,29]],[[45,42]],[[45,44]],[[45,46]],["INPUT",{"__playwright_value_":"","__playwright_target__":"call@89","type":"text","placeholder":"entity","class":"template-input-seamless to-entity","list":"to-entities","data-testid":"to-entity-input","value":""}],[[16,1]],[[45,71]]],[[45,75]]]]]]]]],[[46,64]]]],"viewport":{"width":1280,"height":720},"timestamp":140229.542,"wallTime":1751768828044,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@89","time":140229.584,"message":"  waiting for element to be visible, enabled and editable"}
{"type":"after","callId":"call@89","endTime":140230.94,"afterSnapshot":"after@call@89"}
{"type":"frame-snapshot","snapshot":{"callId":"call@89","snapshotName":"after@call@89","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[47,23]],[[47,24]],["BODY",{},[[47,25]],[[47,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[47,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[47,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[46,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[46,3]],[[46,4]],[[11,0]],[[46,27]],[[46,29]],[[46,42]],[[46,44]],[[46,46]],["INPUT",{"__playwright_value_":"Mouse","__playwright_scroll_left_":"9","__playwright_target__":"call@89","type":"text","placeholder":"entity","class":"template-input-seamless to-entity","list":"to-entities","data-testid":"to-entity-input","value":"Mouse"}],[[17,1]],[[46,71]]],[[46,75]]]]]]]]],[[47,64]]]],"viewport":{"width":1280,"height":720},"timestamp":140231.261,"wallTime":1751768828046,"collectionTime":0.10000000149011612,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@91","startTime":140232.826,"class":"Frame","method":"waitForSelector","params":{"selector":".autocomplete-dropdown","strict":true,"timeout":3000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@42","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","beforeSnapshot":"before@call@91"}
{"type":"frame-snapshot","snapshot":{"callId":"call@91","snapshotName":"before@call@91","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,10]],"viewport":{"width":1280,"height":720},"timestamp":140233.145,"wallTime":1751768828048,"collectionTime":0.10000000149011612,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@91","time":140233.279,"message":"waiting for locator('.autocomplete-dropdown') to be visible"}
{"type":"screencast-frame","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":140241.523,"frameSwapWallTime":1751768828055.376}
{"type":"screencast-frame","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":140743.218,"frameSwapWallTime":1751768828555.519}
{"type":"screencast-frame","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":141245.335,"frameSwapWallTime":1751768829057.307}
{"type":"screencast-frame","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":141743.333,"frameSwapWallTime":1751768829555.633}
{"type":"screencast-frame","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":142246.035,"frameSwapWallTime":1751768830056.285}
{"type":"screencast-frame","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":142742.61,"frameSwapWallTime":1751768830554.925}
{"type":"after","callId":"call@91","endTime":143234.255,"error":{"message":"Timeout 3000ms exceeded.","stack":"TimeoutError: Timeout 3000ms exceeded.\n    at ProgressController.run (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/progress.js:76:26)\n    at Frame.waitForSelector (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/frames.js:615:23)\n    at FrameDispatcher.waitForSelector (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/dispatchers/frameDispatcher.js:82:116)\n    at FrameDispatcher._handleCommand (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/dispatchers/dispatcher.js:88:40)\n    at DispatcherConnection.dispatch (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/dispatchers/dispatcher.js:309:39)","name":"TimeoutError"},"afterSnapshot":"after@call@91"}
{"type":"frame-snapshot","snapshot":{"callId":"call@91","snapshotName":"after@call@91","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[2,10]],"viewport":{"width":1280,"height":720},"timestamp":143236.64,"wallTime":1751768831051,"collectionTime":0.6999999955296516,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@93","startTime":143240.183,"class":"Frame","method":"waitForSelector","params":{"selector":".autocomplete-dropdown >> nth=0","strict":true,"timeout":3000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@43","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","beforeSnapshot":"before@call@93"}
{"type":"frame-snapshot","snapshot":{"callId":"call@93","snapshotName":"before@call@93","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[3,10]],"viewport":{"width":1280,"height":720},"timestamp":143241.308,"wallTime":1751768831056,"collectionTime":0.29999999701976776,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@93","time":143241.62,"message":"waiting for locator('.autocomplete-dropdown').first() to be visible"}
{"type":"screencast-frame","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":143246.158,"frameSwapWallTime":1751768831057.8909}
{"type":"screencast-frame","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":143746.701,"frameSwapWallTime":1751768831557.903}
{"type":"screencast-frame","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":144247.287,"frameSwapWallTime":1751768832058.6082}
{"type":"screencast-frame","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":144746.834,"frameSwapWallTime":1751768832558.294}
{"type":"screencast-frame","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":145243.895,"frameSwapWallTime":1751768833057.0012}
{"type":"screencast-frame","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":145745.593,"frameSwapWallTime":1751768833556.9402}
{"type":"after","callId":"call@93","endTime":146242.521,"error":{"message":"Timeout 3000ms exceeded.","stack":"TimeoutError: Timeout 3000ms exceeded.\n    at ProgressController.run (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/progress.js:76:26)\n    at Frame.waitForSelector (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/frames.js:615:23)\n    at FrameDispatcher.waitForSelector (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/dispatchers/frameDispatcher.js:82:116)\n    at FrameDispatcher._handleCommand (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/dispatchers/dispatcher.js:88:40)\n    at DispatcherConnection.dispatch (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/dispatchers/dispatcher.js:309:39)","name":"TimeoutError"},"afterSnapshot":"after@call@93"}
{"type":"frame-snapshot","snapshot":{"callId":"call@93","snapshotName":"after@call@93","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[4,10]],"viewport":{"width":1280,"height":720},"timestamp":146245.592,"wallTime":1751768834060,"collectionTime":0.8000000044703484,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@95","startTime":146247.384,"class":"Frame","method":"queryCount","params":{"selector":"datalist option"},"stepId":"pw:api@44","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","beforeSnapshot":"before@call@95"}
{"type":"screencast-frame","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":146247.863,"frameSwapWallTime":1751768834058.65}
{"type":"frame-snapshot","snapshot":{"callId":"call@95","snapshotName":"before@call@95","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[5,10]],"viewport":{"width":1280,"height":720},"timestamp":146248.105,"wallTime":1751768834063,"collectionTime":0.29999999701976776,"resourceOverrides":[],"isMainFrame":true}}
{"type":"after","callId":"call@95","endTime":146249.314,"result":{"value":41},"afterSnapshot":"after@call@95"}
{"type":"frame-snapshot","snapshot":{"callId":"call@95","snapshotName":"after@call@95","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[6,10]],"viewport":{"width":1280,"height":720},"timestamp":146249.985,"wallTime":1751768834065,"collectionTime":0.30000000447034836,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@97","startTime":146251.089,"class":"Frame","method":"queryCount","params":{"selector":"datalist option >> internal:has-text=\"Mouse BAWGF\"i"},"stepId":"pw:api@45","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","beforeSnapshot":"before@call@97"}
{"type":"frame-snapshot","snapshot":{"callId":"call@97","snapshotName":"before@call@97","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[7,10]],"viewport":{"width":1280,"height":720},"timestamp":146251.63,"wallTime":1751768834066,"collectionTime":0.19999999552965164,"resourceOverrides":[],"isMainFrame":true}}
{"type":"after","callId":"call@97","endTime":146252.502,"result":{"value":0},"afterSnapshot":"after@call@97"}
{"type":"frame-snapshot","snapshot":{"callId":"call@97","snapshotName":"after@call@97","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[8,10]],"viewport":{"width":1280,"height":720},"timestamp":146252.984,"wallTime":1751768834068,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@99","startTime":146253.921,"class":"Frame","method":"fill","params":{"selector":"[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"] >> nth=0","strict":true,"value":"Mouse BAWGF","timeout":8000},"stepId":"pw:api@46","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","beforeSnapshot":"before@call@99"}
{"type":"frame-snapshot","snapshot":{"callId":"call@99","snapshotName":"before@call@99","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[9,10]],"viewport":{"width":1280,"height":720},"timestamp":146254.306,"wallTime":1751768834069,"collectionTime":0.10000000149011612,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@99","time":146254.557,"message":"waiting for locator('[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"]').first()"}
{"type":"log","callId":"call@99","time":146255.514,"message":"  locator resolved to <input type=\"text\" value=\"Mouse\" list=\"to-entities\" placeholder=\"entity\" data-testid=\"to-entity-input\" class=\"template-input-seamless to-entity\"/>"}
{"type":"log","callId":"call@99","time":146255.896,"message":"  fill(\"Mouse BAWGF\")"}
{"type":"log","callId":"call@99","time":146255.902,"message":"attempting fill action"}
{"type":"input","callId":"call@99","inputSnapshot":"input@call@99"}
{"type":"frame-snapshot","snapshot":{"callId":"call@99","snapshotName":"input@call@99","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[57,23]],[[57,24]],["BODY",{},[[57,25]],[[57,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[57,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[57,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[56,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[56,3]],[[56,4]],[[21,0]],[[56,27]],[[56,29]],[[56,42]],[[56,44]],[[56,46]],["INPUT",{"__playwright_value_":"Mouse","__playwright_scroll_left_":"9","__playwright_target__":"call@99","type":"text","placeholder":"entity","class":"template-input-seamless to-entity","list":"to-entities","data-testid":"to-entity-input","value":"Mouse"}],[[27,1]],[[56,71]]],[[56,75]]]]]]]]],[[57,64]]]],"viewport":{"width":1280,"height":720},"timestamp":146256.333,"wallTime":1751768834071,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@99","time":146256.377,"message":"  waiting for element to be visible, enabled and editable"}
{"type":"after","callId":"call@99","endTime":146258.965,"afterSnapshot":"after@call@99"}
{"type":"frame-snapshot","snapshot":{"callId":"call@99","snapshotName":"after@call@99","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[58,23]],[[58,24]],["BODY",{},[[58,25]],[[58,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[58,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[58,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[57,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[57,3]],[[57,4]],[[22,0]],[[57,27]],[[57,29]],[[57,42]],[[57,44]],[[57,46]],["INPUT",{"__playwright_value_":"Mouse BAWGF","__playwright_scroll_left_":"183","__playwright_target__":"call@99","type":"text","placeholder":"entity","class":"template-input-seamless to-entity","list":"to-entities","data-testid":"to-entity-input","value":"Mouse BAWGF"}],[[28,1]],[[57,71]]],[[57,75]]]]]]]]],[[58,64]]]],"viewport":{"width":1280,"height":720},"timestamp":146259.552,"wallTime":1751768834074,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@101","startTime":146260.353,"class":"Page","method":"keyboardPress","params":{"key":"Tab"},"stepId":"pw:api@47","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","beforeSnapshot":"before@call@101"}
{"type":"frame-snapshot","snapshot":{"callId":"call@101","snapshotName":"before@call@101","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,10]],"viewport":{"width":1280,"height":720},"timestamp":146260.797,"wallTime":1751768834076,"collectionTime":0.10000000149011612,"resourceOverrides":[],"isMainFrame":true}}
{"type":"after","callId":"call@101","endTime":146261.631,"afterSnapshot":"after@call@101"}
{"type":"frame-snapshot","snapshot":{"callId":"call@101","snapshotName":"after@call@101","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[60,23]],[[60,24]],["BODY",{},[[60,25]],[[60,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[60,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[60,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[59,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[59,3]],[[59,4]],[[24,0]],[[59,27]],[[59,29]],[[59,42]],[[59,44]],[[59,46]],["INPUT",{"__playwright_value_":"Mouse BAWGF","__playwright_target__":"call@99","type":"text","placeholder":"entity","class":"template-input-seamless to-entity","list":"to-entities","data-testid":"to-entity-input","value":"Mouse BAWGF"}],[[30,1]],[[59,71]]],[[59,75]]]]]]]]],[[60,64]]]],"viewport":{"width":1280,"height":720},"timestamp":146262.025,"wallTime":1751768834077,"collectionTime":0.19999999552965164,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@103","startTime":146262.911,"class":"Frame","method":"inputValue","params":{"selector":"[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"] >> nth=0","strict":true,"timeout":8000},"stepId":"pw:api@48","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","beforeSnapshot":"before@call@103"}
{"type":"frame-snapshot","snapshot":{"callId":"call@103","snapshotName":"before@call@103","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,10]],"viewport":{"width":1280,"height":720},"timestamp":146263.273,"wallTime":1751768834078,"collectionTime":0.10000000149011612,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@103","time":146263.474,"message":"waiting for locator('[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"]').first()"}
{"type":"log","callId":"call@103","time":146264.102,"message":"  locator resolved to <input type=\"text\" list=\"to-entities\" value=\"Mouse BAWGF\" placeholder=\"entity\" data-testid=\"to-entity-input\" class=\"template-input-seamless to-entity\"/>"}
{"type":"after","callId":"call@103","endTime":146264.119,"result":{"value":"Mouse BAWGF"},"afterSnapshot":"after@call@103"}
{"type":"screencast-frame","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":146264.704,"frameSwapWallTime":1751768834077.644}
{"type":"frame-snapshot","snapshot":{"callId":"call@103","snapshotName":"after@call@103","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[62,23]],[[62,24]],["BODY",{},[[62,25]],[[62,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[62,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[62,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[61,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[61,3]],[[61,4]],[[26,0]],[[61,27]],[[61,29]],[[61,42]],[[61,44]],[[61,46]],["INPUT",{"__playwright_value_":"Mouse BAWGF","__playwright_target__":"call@103","type":"text","placeholder":"entity","class":"template-input-seamless to-entity","list":"to-entities","data-testid":"to-entity-input","value":"Mouse BAWGF"}],[[32,1]],[[61,71]]],[[61,75]]]]]]]]],[[62,64]]]],"viewport":{"width":1280,"height":720},"timestamp":146264.765,"wallTime":1751768834079,"collectionTime":0.19999999552965164,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@105","startTime":146265.577,"class":"Frame","method":"inputValue","params":{"selector":"[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"] >> nth=0","strict":true,"timeout":8000},"stepId":"pw:api@49","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","beforeSnapshot":"before@call@105"}
{"type":"frame-snapshot","snapshot":{"callId":"call@105","snapshotName":"before@call@105","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,10]],"viewport":{"width":1280,"height":720},"timestamp":146266.041,"wallTime":1751768834081,"collectionTime":0.19999999552965164,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@105","time":146266.318,"message":"waiting for locator('[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"]').first()"}
{"type":"log","callId":"call@105","time":146266.914,"message":"  locator resolved to <input type=\"text\" list=\"to-entities\" value=\"Mouse BAWGF\" placeholder=\"entity\" data-testid=\"to-entity-input\" class=\"template-input-seamless to-entity\"/>"}
{"type":"after","callId":"call@105","endTime":146266.928,"result":{"value":"Mouse BAWGF"},"afterSnapshot":"after@call@105"}
{"type":"frame-snapshot","snapshot":{"callId":"call@105","snapshotName":"after@call@105","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[64,23]],[[64,24]],["BODY",{},[[64,25]],[[64,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[64,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[64,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[63,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[63,3]],[[63,4]],[[28,0]],[[63,27]],[[63,29]],[[63,42]],[[63,44]],[[63,46]],["INPUT",{"__playwright_value_":"Mouse BAWGF","__playwright_target__":"call@105","type":"text","placeholder":"entity","class":"template-input-seamless to-entity","list":"to-entities","data-testid":"to-entity-input","value":"Mouse BAWGF"}],[[34,1]],[[63,71]]],[[63,75]]]]]]]]],[[64,64]]]],"viewport":{"width":1280,"height":720},"timestamp":146267.283,"wallTime":1751768834082,"collectionTime":0.10000000149011612,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@107","startTime":146268.081,"class":"Frame","method":"selectOption","params":{"selector":".template-measure, select.template-input","strict":true,"options":[{"label":"tall"}],"timeout":8000},"stepId":"pw:api@50","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","beforeSnapshot":"before@call@107"}
{"type":"frame-snapshot","snapshot":{"callId":"call@107","snapshotName":"before@call@107","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,10]],"viewport":{"width":1280,"height":720},"timestamp":146268.544,"wallTime":1751768834083,"collectionTime":0.29999999701976776,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@107","time":146268.775,"message":"waiting for locator('.template-measure, select.template-input')"}
{"type":"log","callId":"call@107","time":146269.451,"message":"  locator resolved to <select class=\"template-input template-measure\">…</select>"}
{"type":"log","callId":"call@107","time":146269.785,"message":"attempting select option action"}
{"type":"input","callId":"call@107","inputSnapshot":"input@call@107"}
{"type":"frame-snapshot","snapshot":{"callId":"call@107","snapshotName":"input@call@107","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[66,23]],[[66,24]],["BODY",{},[[66,25]],[[66,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[66,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[66,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[65,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[65,3]],[[65,4]],[[30,0]],[[65,27]],[[65,29]],["SELECT",{"__playwright_target__":"call@107","class":"template-input template-measure"},[[65,31]],[[65,33]],[[65,35]],[[65,37]],[[65,39]],[[65,41]]],[[65,44]],[[65,46]],[[2,0]],[[36,1]],[[65,71]]],[[65,75]]]]]]]]],[[66,64]]]],"viewport":{"width":1280,"height":720},"timestamp":146270.51,"wallTime":1751768834085,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@107","time":146270.593,"message":"  waiting for element to be visible and enabled"}
{"type":"log","callId":"call@107","time":146272.348,"message":"  selected specified option(s)"}
{"type":"after","callId":"call@107","endTime":146272.393,"result":{"values":["1"]},"afterSnapshot":"after@call@107"}
{"type":"frame-snapshot","snapshot":{"callId":"call@107","snapshotName":"after@call@107","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[67,23]],[[67,24]],["BODY",{},[[67,25]],[[67,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[67,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[67,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[66,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[66,3]],[[66,4]],[[31,0]],[[66,27]],[[66,29]],["SELECT",{"__playwright_target__":"call@107","class":"template-input template-measure"},["OPTION",{"__playwright_selected_":"false","value":""},[[66,30]]],[[66,33]],["OPTION",{"__playwright_selected_":"true","value":"1"},[[66,34]]],[[66,37]],[[66,39]],[[66,41]]],[[66,44]],[[66,46]],[[3,0]],[[37,1]],[[66,71]]],[[66,75]]]]]]]]],[[67,64]]]],"viewport":{"width":1280,"height":720},"timestamp":146272.995,"wallTime":1751768834088,"collectionTime":0.30000000447034836,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@109","startTime":146273.623,"class":"Frame","method":"waitForSelector","params":{"selector":".template-measure, select.template-input","strict":true,"timeout":8000,"state":"attached","omitReturnValue":true},"stepId":"pw:api@51","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","beforeSnapshot":"before@call@109"}
{"type":"frame-snapshot","snapshot":{"callId":"call@109","snapshotName":"before@call@109","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,12]],"viewport":{"width":1280,"height":720},"timestamp":146273.872,"wallTime":1751768834089,"collectionTime":0,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@109","time":146273.984,"message":"waiting for locator('.template-measure, select.template-input')"}
{"type":"log","callId":"call@109","time":146274.639,"message":"  locator resolved to visible <select class=\"template-input template-measure\">…</select>"}
{"type":"after","callId":"call@109","endTime":146274.656,"result":{},"afterSnapshot":"after@call@109"}
{"type":"frame-snapshot","snapshot":{"callId":"call@109","snapshotName":"after@call@109","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[2,12]],"viewport":{"width":1280,"height":720},"timestamp":146275.24,"wallTime":1751768834090,"collectionTime":0.10000000149011612,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@111","startTime":146279.492,"class":"Page","method":"waitForEventInfo","params":{"info":{"waitId":"4405c832a936aef6f59a26e67ea97b1e","phase":"before","event":"response"}},"stepId":"pw:api@52","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","beforeSnapshot":"before@call@111"}
{"type":"before","callId":"call@114","startTime":146279.78,"class":"Frame","method":"waitForFunction","params":{"expression":"() => {\n      const calculatedValue = document.querySelector('[data-testid=\"comparison-result\"], .template-calculated-value');\n      return calculatedValue && calculatedValue.textContent !== '?' && calculatedValue.textContent !== '...';\n    }","isFunction":true,"arg":{"value":{"o":[{"k":"timeout","v":{"n":10000}}],"id":1},"handles":[]},"timeout":8000},"stepId":"pw:api@53","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","beforeSnapshot":"before@call@114"}
{"type":"screencast-frame","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":146280.02,"frameSwapWallTime":1751768834090.076}
{"type":"frame-snapshot","snapshot":{"callId":"call@111","snapshotName":"before@call@111","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[3,12]],"viewport":{"width":1280,"height":720},"timestamp":146280.062,"wallTime":1751768834095,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"frame-snapshot","snapshot":{"callId":"call@114","snapshotName":"before@call@114","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[4,12]],"viewport":{"width":1280,"height":720},"timestamp":146280.114,"wallTime":1751768834095,"collectionTime":0.10000000149011612,"resourceOverrides":[],"isMainFrame":true}}
{"type":"screencast-frame","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":146290.483,"frameSwapWallTime":1751768834103.96}
{"type":"console","messageType":"log","text":"API Request: GET /compare/ undefined","args":[{"preview":"API Request:","value":"API Request:"},{"preview":"GET","value":"GET"},{"preview":"/compare/","value":"/compare/"},{"preview":"undefined"}],"location":{"url":"http://localhost:3000/static/js/bundle.js","lineNumber":54565,"columnNumber":10},"time":146374.346,"pageId":"page@a986070605cbb1375f79f3bc4860ec2b"}
{"type":"screencast-frame","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":146392.964,"frameSwapWallTime":1751768834205.884}
{"type":"console","messageType":"log","text":"API Response: GET /compare/ 200 {from_entity: Object, to_entity: Object, unit: Object, multiplier: 50.000, path: Array(3)}","args":[{"preview":"API Response:","value":"API Response:"},{"preview":"GET","value":"GET"},{"preview":"/compare/","value":"/compare/"},{"preview":"200","value":200},{"preview":"{from_entity: Object, to_entity: Object, unit: Object, multiplier: 50.000, path: Array(3)}"}],"location":{"url":"http://localhost:3000/static/js/bundle.js","lineNumber":54575,"columnNumber":10},"time":146395.935,"pageId":"page@a986070605cbb1375f79f3bc4860ec2b"}
{"type":"after","callId":"call@114","endTime":146406.145,"result":{"handle":"<JSHandle>"},"afterSnapshot":"after@call@114"}
{"type":"frame-snapshot","snapshot":{"callId":"call@114","snapshotName":"after@call@114","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[72,23]],[[72,24]],["BODY",{},[[72,25]],[[72,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[72,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[72,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[71,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[71,3]],[[71,4]],[[36,0]],[[71,27]],[[71,29]],[[5,2]],[[71,44]],["SPAN",{"class":"template-calculated-value","data-testid":"comparison-result"},"50.0"],[[8,0]],[[42,1]],[[71,71]]],[[71,75]]]],["DIV",{"class":"result-section"},["DIV",{"class":"comparison-result"},["DIV",{"class":"main-result"},["DIV",{"class":"result-statement-template"},["SPAN",{"class":"template-prefix"},"Did you know that"],["SPAN",{"class":"template-from-count"},"1"],["SPAN",{"class":"template-from-entity"},"Human BAWGA"],["SPAN",{"class":"template-relationship"},"is as tall as"],["SPAN",{"class":"template-multiplier"},"50"],["SPAN",{"class":"template-to-entity"},"Mouse BAWGF"],["SPAN",{"class":"template-suffix"},"?"]]],["DIV",{"class":"calculation-path"},["H4",{},"Calculation Path:"],["DIV",{"class":"path-steps"},["DIV",{"class":"path-step"},["DIV",{"class":"step-number"},"1"],["DIV",{"class":"step-content"},["SPAN",{"class":"entity-name"},"Human BAWGA"],["SPAN",{"class":"relationship-text"},"is"],["SPAN",{"class":"multiplier"},"10","x"],["SPAN",{"class":"entity-name"},"Ball BAWGB"]],["DIV",{"class":"step-arrow"},"↓"]],["DIV",{"class":"path-step"},["DIV",{"class":"step-number"},"2"],["DIV",{"class":"step-content"},["SPAN",{"class":"entity-name"},"Ball BAWGB"],["SPAN",{"class":"relationship-text"},"is"],["SPAN",{"class":"multiplier"},"50","x"],["SPAN",{"class":"entity-name"},"Build BAWGC"]],["DIV",{"class":"step-arrow"},"↓"]],["DIV",{"class":"path-step"},["DIV",{"class":"step-number"},"3"],["DIV",{"class":"step-content"},["SPAN",{"class":"entity-name"},"Build BAWGC"],["SPAN",{"class":"relationship-text"},"is"],["SPAN",{"class":"multiplier"},"0.1","x"],["SPAN",{"class":"entity-name"},"Mouse BAWGF"]]]],["DIV",{"class":"calculation-formula"},["H5",{},"Final Calculation:"],["P",{},"10 × 50 × 0.1"," = ","50"]]],["DIV",{"class":"result-details"},["DIV",{"class":"detail-item"},["STRONG",{},"From:"]," ","Human BAWGA"," (ID: ","165",")"],["DIV",{"class":"detail-item"},["STRONG",{},"To:"]," ","Mouse BAWGF"," (ID: ","169",")"],["DIV",{"class":"detail-item"},["STRONG",{},"Unit:"]," ","Length"," (","m",")"],["DIV",{"class":"detail-item"},["STRONG",{},"Path Length:"]," ","3"," hop","s"]]]]]]]]],[[72,64]]]],"viewport":{"width":1280,"height":720},"timestamp":146407.181,"wallTime":1751768834222,"collectionTime":0.4000000059604645,"resourceOverrides":[],"isMainFrame":true}}
{"type":"screencast-frame","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":146413.517,"frameSwapWallTime":1751768834226.778}
{"type":"after","callId":"call@111","endTime":161280.734,"error":{"name":"Error","message":"Timeout 15000ms exceeded while waiting for event \"response\""},"afterSnapshot":"after@call@111"}
{"type":"before","callId":"call@118","startTime":161281.245,"class":"Frame","method":"waitForSelector","params":{"selector":".template-sentence","strict":true,"timeout":10000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@54","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","beforeSnapshot":"before@call@118"}
{"type":"before","callId":"call@120","startTime":161281.605,"class":"Frame","method":"waitForSelector","params":{"selector":".error-message, .error, [role=\"alert\"], .alert-error, .comparison-error","strict":true,"timeout":10000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@55","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","beforeSnapshot":"before@call@120"}
{"type":"before","callId":"call@122","startTime":161281.943,"class":"Frame","method":"waitForSelector","params":{"selector":":text(\"No connection found\"), :text(\"No path found\")","strict":true,"timeout":10000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@56","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","beforeSnapshot":"before@call@122"}
{"type":"frame-snapshot","snapshot":{"callId":"call@111","snapshotName":"after@call@111","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,115]],"viewport":{"width":1280,"height":720},"timestamp":161282.316,"wallTime":1751768849096,"collectionTime":0.6000000014901161,"resourceOverrides":[],"isMainFrame":true}}
{"type":"frame-snapshot","snapshot":{"callId":"call@118","snapshotName":"before@call@118","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[2,115]],"viewport":{"width":1280,"height":720},"timestamp":161282.558,"wallTime":1751768849097,"collectionTime":0.30000000447034836,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@118","time":161282.76,"message":"waiting for locator('.template-sentence') to be visible"}
{"type":"frame-snapshot","snapshot":{"callId":"call@120","snapshotName":"before@call@120","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[3,115]],"viewport":{"width":1280,"height":720},"timestamp":161282.961,"wallTime":1751768849097,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@120","time":161283.093,"message":"waiting for locator('.error-message, .error, [role=\"alert\"], .alert-error, .comparison-error') to be visible"}
{"type":"frame-snapshot","snapshot":{"callId":"call@122","snapshotName":"before@call@122","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[4,115]],"viewport":{"width":1280,"height":720},"timestamp":161283.263,"wallTime":1751768849097,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@122","time":161283.35,"message":"waiting for locator(':text(\"No connection found\"), :text(\"No path found\")') to be visible"}
{"type":"log","callId":"call@118","time":161284.406,"message":"  locator resolved to visible <div class=\"template-sentence\">…</div>"}
{"type":"after","callId":"call@118","endTime":161284.424,"result":{},"afterSnapshot":"after@call@118"}
{"type":"frame-snapshot","snapshot":{"callId":"call@118","snapshotName":"after@call@118","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[5,115]],"viewport":{"width":1280,"height":720},"timestamp":161284.959,"wallTime":1751768849099,"collectionTime":0.10000000149011612,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@124","startTime":161286.823,"title":"Expect \"toBeVisible\"","class":"Frame","method":"expect","params":{"selector":".template-sentence","expression":"to.be.visible","expectedValue":{"value":{"v":"undefined"},"handles":[]},"isNot":false,"timeout":5000},"stepId":"expect@57","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","beforeSnapshot":"before@call@124"}
{"type":"frame-snapshot","snapshot":{"callId":"call@124","snapshotName":"before@call@124","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[6,115]],"viewport":{"width":1280,"height":720},"timestamp":161287.152,"wallTime":1751768849102,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@124","time":161287.344,"message":"Expect \"toBeVisible\" with timeout 5000ms"}
{"type":"log","callId":"call@124","time":161287.346,"message":"waiting for locator('.template-sentence')"}
{"type":"log","callId":"call@124","time":161287.909,"message":"  locator resolved to <div class=\"template-sentence\">…</div>"}
{"type":"after","callId":"call@124","endTime":161287.964,"result":{"matches":true,"received":{"s":"visible"}},"afterSnapshot":"after@call@124"}
{"type":"frame-snapshot","snapshot":{"callId":"call@124","snapshotName":"after@call@124","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[79,23]],[[79,24]],["BODY",{},[[79,25]],[[79,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[79,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[79,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[78,1]],["DIV",{"class":"template-form"},["DIV",{"__playwright_target__":"call@124","class":"template-sentence"},[[78,3]],[[78,4]],[[43,0]],[[78,27]],[[78,29]],[[12,2]],[[78,44]],[[7,1]],[[15,0]],[[49,1]],[[78,71]]],[[78,75]]]],[[7,108]]]]]]],[[79,64]]]],"viewport":{"width":1280,"height":720},"timestamp":161288.283,"wallTime":1751768849103,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@126","startTime":161288.813,"class":"Frame","method":"isVisible","params":{"selector":".template-sentence","strict":true},"stepId":"pw:api@58","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","beforeSnapshot":"before@call@126"}
{"type":"frame-snapshot","snapshot":{"callId":"call@126","snapshotName":"before@call@126","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,9]],"viewport":{"width":1280,"height":720},"timestamp":161289.051,"wallTime":1751768849104,"collectionTime":0.19999999552965164,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@126","time":161289.209,"message":"  checking visibility of locator('.template-sentence')"}
{"type":"after","callId":"call@126","endTime":161289.456,"result":{"value":true},"afterSnapshot":"after@call@126"}
{"type":"frame-snapshot","snapshot":{"callId":"call@126","snapshotName":"after@call@126","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[2,9]],"viewport":{"width":1280,"height":720},"timestamp":161289.681,"wallTime":1751768849104,"collectionTime":0.10000000149011612,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@128","startTime":161290.046,"class":"Frame","method":"textContent","params":{"selector":".template-sentence","strict":true,"timeout":8000},"stepId":"pw:api@59","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","beforeSnapshot":"before@call@128"}
{"type":"frame-snapshot","snapshot":{"callId":"call@128","snapshotName":"before@call@128","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[3,9]],"viewport":{"width":1280,"height":720},"timestamp":161290.257,"wallTime":1751768849105,"collectionTime":0.10000000149011612,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@128","time":161290.345,"message":"waiting for locator('.template-sentence')"}
{"type":"log","callId":"call@128","time":161290.569,"message":"  locator resolved to <div class=\"template-sentence\">…</div>"}
{"type":"after","callId":"call@128","endTime":161290.574,"result":{"value":"Did you know thatis asmeasurebigtallheavylongvoluminousas50.0?"},"afterSnapshot":"after@call@128"}
{"type":"frame-snapshot","snapshot":{"callId":"call@128","snapshotName":"after@call@128","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[83,23]],[[83,24]],["BODY",{},[[83,25]],[[83,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[83,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[83,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[82,1]],["DIV",{"class":"template-form"},["DIV",{"__playwright_target__":"call@128","class":"template-sentence"},[[82,3]],[[82,4]],[[47,0]],[[82,27]],[[82,29]],[[16,2]],[[82,44]],[[11,1]],[[19,0]],[[53,1]],[[82,71]]],[[82,75]]]],[[11,108]]]]]]],[[83,64]]]],"viewport":{"width":1280,"height":720},"timestamp":161290.826,"wallTime":1751768849105,"collectionTime":0.10000000149011612,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@130","startTime":161292.68,"class":"Page","method":"screenshot","params":{"timeout":5000,"type":"png","caret":"initial"},"stepId":"pw:api@63","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","beforeSnapshot":"before@call@130"}
{"type":"frame-snapshot","snapshot":{"callId":"call@130","snapshotName":"before@call@130","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,9]],"viewport":{"width":1280,"height":720},"timestamp":161292.948,"wallTime":1751768849108,"collectionTime":0.10000000149011612,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@130","time":161293.285,"message":"taking page screenshot"}
{"type":"log","callId":"call@130","time":161293.588,"message":"waiting for fonts to load..."}
{"type":"log","callId":"call@130","time":161293.862,"message":"fonts loaded"}
{"type":"screencast-frame","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":161305.656,"frameSwapWallTime":1751768849119.266}
{"type":"after","callId":"call@130","endTime":161337.623,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@130"}
{"type":"frame-snapshot","snapshot":{"callId":"call@130","snapshotName":"after@call@130","pageId":"page@a986070605cbb1375f79f3bc4860ec2b","frameId":"frame@99c1e3499ef4cfb486c7621ea2c6f96b","frameUrl":"http://localhost:3000/","doctype":"html","html":[[2,9]],"viewport":{"width":1280,"height":720},"timestamp":161338.026,"wallTime":1751768849153,"collectionTime":0.19999999552965164,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@132","startTime":161339.349,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities/167","method":"DELETE","timeout":8000},"stepId":"pw:api@65","beforeSnapshot":"before@call@132"}
{"type":"log","callId":"call@132","time":161339.749,"message":"→ DELETE http://localhost:8000/api/v1/entities/167"}
{"type":"log","callId":"call@132","time":161339.752,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@132","time":161339.753,"message":"  accept: */*"}
{"type":"log","callId":"call@132","time":161339.754,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@132","time":161339.755,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@132","time":161339.755,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@132","time":161339.756,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@132","time":161351.067,"message":"← 200 OK"}
{"type":"log","callId":"call@132","time":161351.07,"message":"  date: Sun, 06 Jul 2025 02:27:28 GMT"}
{"type":"log","callId":"call@132","time":161351.071,"message":"  server: uvicorn"}
{"type":"log","callId":"call@132","time":161351.073,"message":"  content-length: 41"}
{"type":"log","callId":"call@132","time":161351.074,"message":"  content-type: application/json"}
{"type":"after","callId":"call@132","endTime":161351.124,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/167","status":200,"statusText":"OK","headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:28 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"fetchUid":"74d4f35b8c09ffd3c8b445ddd25e3be7"}},"afterSnapshot":"after@call@132"}
{"type":"before","callId":"call@134","startTime":161376.306,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities/165","method":"DELETE","timeout":8000},"stepId":"pw:api@66","beforeSnapshot":"before@call@134"}
{"type":"log","callId":"call@134","time":161376.627,"message":"→ DELETE http://localhost:8000/api/v1/entities/165"}
{"type":"log","callId":"call@134","time":161376.63,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@134","time":161376.631,"message":"  accept: */*"}
{"type":"log","callId":"call@134","time":161376.632,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@134","time":161376.633,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@134","time":161376.635,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@134","time":161376.635,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@134","time":161381.364,"message":"← 200 OK"}
{"type":"log","callId":"call@134","time":161381.367,"message":"  date: Sun, 06 Jul 2025 02:27:28 GMT"}
{"type":"log","callId":"call@134","time":161381.368,"message":"  server: uvicorn"}
{"type":"log","callId":"call@134","time":161381.369,"message":"  content-length: 41"}
{"type":"log","callId":"call@134","time":161381.37,"message":"  content-type: application/json"}
{"type":"after","callId":"call@134","endTime":161381.404,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/165","status":200,"statusText":"OK","headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:28 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"fetchUid":"e1faed69421823aded91b8adeb09df7f"}},"afterSnapshot":"after@call@134"}
{"type":"before","callId":"call@136","startTime":161406.131,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities/169","method":"DELETE","timeout":8000},"stepId":"pw:api@67","beforeSnapshot":"before@call@136"}
{"type":"log","callId":"call@136","time":161406.454,"message":"→ DELETE http://localhost:8000/api/v1/entities/169"}
{"type":"log","callId":"call@136","time":161406.456,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@136","time":161406.458,"message":"  accept: */*"}
{"type":"log","callId":"call@136","time":161406.459,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@136","time":161406.46,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@136","time":161406.461,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@136","time":161406.461,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@136","time":161411.415,"message":"← 200 OK"}
{"type":"log","callId":"call@136","time":161411.418,"message":"  date: Sun, 06 Jul 2025 02:27:28 GMT"}
{"type":"log","callId":"call@136","time":161411.42,"message":"  server: uvicorn"}
{"type":"log","callId":"call@136","time":161411.421,"message":"  content-length: 41"}
{"type":"log","callId":"call@136","time":161411.436,"message":"  content-type: application/json"}
{"type":"after","callId":"call@136","endTime":161411.466,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/169","status":200,"statusText":"OK","headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:28 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"fetchUid":"f4f1edaaefa6c613cde0c279245b3951"}},"afterSnapshot":"after@call@136"}
{"type":"before","callId":"call@138","startTime":161437.437,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities/170","method":"DELETE","timeout":8000},"stepId":"pw:api@68","beforeSnapshot":"before@call@138"}
{"type":"log","callId":"call@138","time":161437.84,"message":"→ DELETE http://localhost:8000/api/v1/entities/170"}
{"type":"log","callId":"call@138","time":161437.845,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@138","time":161437.847,"message":"  accept: */*"}
{"type":"log","callId":"call@138","time":161437.847,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@138","time":161437.848,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@138","time":161437.849,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@138","time":161437.85,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@138","time":161442.977,"message":"← 200 OK"}
{"type":"log","callId":"call@138","time":161442.98,"message":"  date: Sun, 06 Jul 2025 02:27:28 GMT"}
{"type":"log","callId":"call@138","time":161442.981,"message":"  server: uvicorn"}
{"type":"log","callId":"call@138","time":161442.982,"message":"  content-length: 41"}
{"type":"log","callId":"call@138","time":161442.983,"message":"  content-type: application/json"}
{"type":"after","callId":"call@138","endTime":161443.017,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/170","status":200,"statusText":"OK","headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:28 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"fetchUid":"5ce446bdd0db50c7419093bd09fe5590"}},"afterSnapshot":"after@call@138"}
{"type":"before","callId":"call@140","startTime":161468.437,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities/166","method":"DELETE","timeout":8000},"stepId":"pw:api@69","beforeSnapshot":"before@call@140"}
{"type":"log","callId":"call@140","time":161468.764,"message":"→ DELETE http://localhost:8000/api/v1/entities/166"}
{"type":"log","callId":"call@140","time":161468.768,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@140","time":161468.77,"message":"  accept: */*"}
{"type":"log","callId":"call@140","time":161468.771,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@140","time":161468.772,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@140","time":161468.773,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@140","time":161468.774,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@140","time":161473.67,"message":"← 200 OK"}
{"type":"log","callId":"call@140","time":161473.672,"message":"  date: Sun, 06 Jul 2025 02:27:28 GMT"}
{"type":"log","callId":"call@140","time":161473.673,"message":"  server: uvicorn"}
{"type":"log","callId":"call@140","time":161473.674,"message":"  content-length: 41"}
{"type":"log","callId":"call@140","time":161473.675,"message":"  content-type: application/json"}
{"type":"after","callId":"call@140","endTime":161473.707,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/166","status":200,"statusText":"OK","headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:28 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"fetchUid":"04076aac9dd1f4209e4eb144f268304b"}},"afterSnapshot":"after@call@140"}
{"type":"before","callId":"call@142","startTime":161499.933,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities/168","method":"DELETE","timeout":8000},"stepId":"pw:api@70","beforeSnapshot":"before@call@142"}
{"type":"log","callId":"call@142","time":161500.289,"message":"→ DELETE http://localhost:8000/api/v1/entities/168"}
{"type":"log","callId":"call@142","time":161500.292,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@142","time":161500.293,"message":"  accept: */*"}
{"type":"log","callId":"call@142","time":161500.294,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@142","time":161500.295,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@142","time":161500.296,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@142","time":161500.298,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@142","time":161505.554,"message":"← 200 OK"}
{"type":"log","callId":"call@142","time":161505.557,"message":"  date: Sun, 06 Jul 2025 02:27:28 GMT"}
{"type":"log","callId":"call@142","time":161505.558,"message":"  server: uvicorn"}
{"type":"log","callId":"call@142","time":161505.559,"message":"  content-length: 41"}
{"type":"log","callId":"call@142","time":161505.56,"message":"  content-type: application/json"}
{"type":"after","callId":"call@142","endTime":161505.588,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/168","status":200,"statusText":"OK","headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:28 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"fetchUid":"3d77ecc850cafc3b9c40464d7be0de1f"}},"afterSnapshot":"after@call@142"}
{"type":"before","callId":"call@144","startTime":161506.144,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities","method":"GET","timeout":8000},"stepId":"pw:api@71","beforeSnapshot":"before@call@144"}
{"type":"log","callId":"call@144","time":161506.373,"message":"→ GET http://localhost:8000/api/v1/entities"}
{"type":"log","callId":"call@144","time":161506.376,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@144","time":161506.378,"message":"  accept: */*"}
{"type":"log","callId":"call@144","time":161506.379,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@144","time":161506.38,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@144","time":161506.381,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@144","time":161506.382,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@144","time":161507.249,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@144","time":161507.252,"message":"  date: Sun, 06 Jul 2025 02:27:28 GMT"}
{"type":"log","callId":"call@144","time":161507.253,"message":"  server: uvicorn"}
{"type":"log","callId":"call@144","time":161507.254,"message":"  content-length: 0"}
{"type":"log","callId":"call@144","time":161507.255,"message":"  location: http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@144","time":161507.36,"message":"→ GET http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@144","time":161507.362,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@144","time":161507.364,"message":"  accept: */*"}
{"type":"log","callId":"call@144","time":161507.365,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@144","time":161507.366,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@144","time":161507.366,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@144","time":161507.367,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@144","time":161510.621,"message":"← 200 OK"}
{"type":"log","callId":"call@144","time":161510.623,"message":"  date: Sun, 06 Jul 2025 02:27:28 GMT"}
{"type":"log","callId":"call@144","time":161510.624,"message":"  server: uvicorn"}
{"type":"log","callId":"call@144","time":161510.625,"message":"  content-length: 1753"}
{"type":"log","callId":"call@144","time":161510.626,"message":"  content-type: application/json"}
{"type":"after","callId":"call@144","endTime":161510.655,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/","status":200,"statusText":"OK","headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:28 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"1753"},{"name":"content-type","value":"application/json"}],"fetchUid":"d09b0859406e117c6e0c18ba3f86876d"}},"afterSnapshot":"after@call@144"}
{"type":"before","callId":"call@147","startTime":161511.454,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities","method":"GET","timeout":8000},"stepId":"pw:api@72","beforeSnapshot":"before@call@147"}
{"type":"log","callId":"call@147","time":161511.661,"message":"→ GET http://localhost:8000/api/v1/entities"}
{"type":"log","callId":"call@147","time":161511.664,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@147","time":161511.665,"message":"  accept: */*"}
{"type":"log","callId":"call@147","time":161511.666,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@147","time":161511.666,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@147","time":161511.676,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@147","time":161511.677,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@147","time":161512.634,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@147","time":161512.637,"message":"  date: Sun, 06 Jul 2025 02:27:28 GMT"}
{"type":"log","callId":"call@147","time":161512.638,"message":"  server: uvicorn"}
{"type":"log","callId":"call@147","time":161512.639,"message":"  content-length: 0"}
{"type":"log","callId":"call@147","time":161512.641,"message":"  location: http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@147","time":161512.753,"message":"→ GET http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@147","time":161512.755,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@147","time":161512.756,"message":"  accept: */*"}
{"type":"log","callId":"call@147","time":161512.757,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@147","time":161512.758,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@147","time":161512.758,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@147","time":161512.759,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@147","time":161515.548,"message":"← 200 OK"}
{"type":"log","callId":"call@147","time":161515.553,"message":"  date: Sun, 06 Jul 2025 02:27:28 GMT"}
{"type":"log","callId":"call@147","time":161515.554,"message":"  server: uvicorn"}
{"type":"log","callId":"call@147","time":161515.555,"message":"  content-length: 1753"}
{"type":"log","callId":"call@147","time":161515.556,"message":"  content-type: application/json"}
{"type":"after","callId":"call@147","endTime":161515.588,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/","status":200,"statusText":"OK","headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:28 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"1753"},{"name":"content-type","value":"application/json"}],"fetchUid":"53775e433ff8fc6b1954f16b2fb3f4c6"}},"afterSnapshot":"after@call@147"}
{"type":"before","callId":"call@150","startTime":161516.811,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities","method":"GET","timeout":8000},"stepId":"pw:api@73","beforeSnapshot":"before@call@150"}
{"type":"log","callId":"call@150","time":161517.048,"message":"→ GET http://localhost:8000/api/v1/entities"}
{"type":"log","callId":"call@150","time":161517.05,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@150","time":161517.052,"message":"  accept: */*"}
{"type":"log","callId":"call@150","time":161517.052,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@150","time":161517.053,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@150","time":161517.054,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@150","time":161517.055,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@150","time":161517.633,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@150","time":161517.636,"message":"  date: Sun, 06 Jul 2025 02:27:28 GMT"}
{"type":"log","callId":"call@150","time":161517.637,"message":"  server: uvicorn"}
{"type":"log","callId":"call@150","time":161517.637,"message":"  content-length: 0"}
{"type":"log","callId":"call@150","time":161517.639,"message":"  location: http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@150","time":161517.743,"message":"→ GET http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@150","time":161517.745,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@150","time":161517.746,"message":"  accept: */*"}
{"type":"log","callId":"call@150","time":161517.747,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@150","time":161517.747,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@150","time":161517.748,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@150","time":161517.749,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@150","time":161519.82,"message":"← 200 OK"}
{"type":"log","callId":"call@150","time":161519.823,"message":"  date: Sun, 06 Jul 2025 02:27:28 GMT"}
{"type":"log","callId":"call@150","time":161519.824,"message":"  server: uvicorn"}
{"type":"log","callId":"call@150","time":161519.825,"message":"  content-length: 1753"}
{"type":"log","callId":"call@150","time":161519.825,"message":"  content-type: application/json"}
{"type":"after","callId":"call@150","endTime":161519.862,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/","status":200,"statusText":"OK","headers":[{"name":"date","value":"Sun, 06 Jul 2025 02:27:28 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"1753"},{"name":"content-type","value":"application/json"}],"fetchUid":"59c84cb15c4d8b12aaa2659feaa5a200"}},"afterSnapshot":"after@call@150"}
