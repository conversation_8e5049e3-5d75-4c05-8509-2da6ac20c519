# Page snapshot

```yaml
- navigation:
  - link "SIMILE Entity Comparison System":
    - /url: /
    - heading "SIMILE" [level=1]
    - paragraph: Entity Comparison System
  - link "Compare":
    - /url: /
  - link "Entities":
    - /url: /entities
  - link "Connections":
    - /url: /connections
- main:
  - heading "Entity Comparison" [level=2]
  - heading "Compare Entities" [level=3]
  - text: Did you know that
  - spinbutton: "1"
  - combobox "entity": Human BAWGA
  - text: is as
  - combobox:
    - option "measure"
    - option "big"
    - option "tall" [selected]
    - option "heavy"
    - option "long"
    - option "voluminous"
  - text: as 50.0
  - combobox "entity": Mouse BAWGF
  - text: "?"
  - button "Clear"
  - text: Did you know that1Human BAWGAis as tall as50Mouse BAWGF?
  - heading "Calculation Path:" [level=4]
  - text: 1 Human BAWGAis10xBall BAWGB ↓ 2 Ball BAWGBis50xBuild BAWGC ↓ 3 Build BAWGCis0.1xMouse BAWGF
  - heading "Final Calculation:" [level=5]
  - paragraph: 10 × 50 × 0.1 = 50
  - strong: "From:"
  - text: "Human BAWGA (ID: 165)"
  - strong: "To:"
  - text: "Mouse BAWGF (ID: 169)"
  - strong: "Unit:"
  - text: Length (m)
  - strong: "Path Length:"
  - text: 3 hops
```