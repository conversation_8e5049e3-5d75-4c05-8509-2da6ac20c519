{"version":8,"type":"context-options","origin":"library","browserName":"chromium","options":{"noDefaultViewport":false,"viewport":{"width":1280,"height":720},"ignoreHTTPSErrors":true,"javaScriptEnabled":true,"bypassCSP":true,"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36","locale":"en-US","extraHTTPHeaders":[{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"26"},{"name":"x-test-isolation","value":"enabled"}],"offline":false,"deviceScaleFactor":1,"isMobile":false,"hasTouch":false,"colorScheme":"light","acceptDownloads":"accept","baseURL":"http://localhost:3000","recordVideo":{"dir":"/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/latest-e2e-results.json/.playwright-artifacts-26","size":{"width":800,"height":450}},"serviceWorkers":"allow","selectorEngines":[],"testIdAttributeName":"data-testid"},"platform":"darwin","wallTime":1751813120270,"monotonicTime":38433.255,"sdkLanguage":"javascript","testIdAttributeName":"data-testid","contextId":"browser-context@deb1125212d8a44ace6790fad50d98d3","title":"comparisons.spec.ts:215 › Entity Comparisons and Pathfinding › should calculate transitive relationships"}
{"type":"before","callId":"call@6","startTime":38435.075,"class":"BrowserContext","method":"newPage","params":{},"stepId":"pw:api@8","beforeSnapshot":"before@call@6"}
{"type":"event","time":38486.363,"class":"BrowserContext","method":"page","params":{"pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635"}}
{"type":"after","callId":"call@6","endTime":38486.396,"result":{"page":"<Page>"},"afterSnapshot":"after@call@6"}
{"type":"before","callId":"call@9","startTime":38491.793,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities","method":"GET","timeout":8000},"stepId":"pw:api@9","beforeSnapshot":"before@call@9"}
{"type":"log","callId":"call@9","time":38496.796,"message":"→ GET http://localhost:8000/api/v1/entities"}
{"type":"log","callId":"call@9","time":38496.808,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@9","time":38496.812,"message":"  accept: */*"}
{"type":"log","callId":"call@9","time":38496.814,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@9","time":38496.817,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@9","time":38496.823,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@9","time":38496.827,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@9","time":38500.424,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@9","time":38500.432,"message":"  date: Sun, 06 Jul 2025 14:45:19 GMT"}
{"type":"log","callId":"call@9","time":38500.437,"message":"  server: uvicorn"}
{"type":"log","callId":"call@9","time":38500.442,"message":"  content-length: 0"}
{"type":"log","callId":"call@9","time":38500.444,"message":"  location: http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@9","time":38500.685,"message":"→ GET http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@9","time":38500.688,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@9","time":38500.689,"message":"  accept: */*"}
{"type":"log","callId":"call@9","time":38500.691,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@9","time":38500.692,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@9","time":38500.693,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@9","time":38500.695,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@9","time":38504.755,"message":"← 200 OK"}
{"type":"log","callId":"call@9","time":38504.758,"message":"  date: Sun, 06 Jul 2025 14:45:19 GMT"}
{"type":"log","callId":"call@9","time":38504.76,"message":"  server: uvicorn"}
{"type":"log","callId":"call@9","time":38504.761,"message":"  content-length: 1732"}
{"type":"log","callId":"call@9","time":38504.762,"message":"  content-type: application/json"}
{"type":"after","callId":"call@9","endTime":38504.816,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/","status":200,"statusText":"OK","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"1732"},{"name":"content-type","value":"application/json"}],"fetchUid":"32467142b47c01d42cb3c1db21463f96"}},"afterSnapshot":"after@call@9"}
{"type":"before","callId":"call@12","startTime":38509.504,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities/473","method":"DELETE","timeout":8000},"stepId":"pw:api@10","beforeSnapshot":"before@call@12"}
{"type":"before","callId":"call@14","startTime":38509.698,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities/474","method":"DELETE","timeout":8000},"stepId":"pw:api@11","beforeSnapshot":"before@call@14"}
{"type":"before","callId":"call@16","startTime":38509.832,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities/472","method":"DELETE","timeout":8000},"stepId":"pw:api@12","beforeSnapshot":"before@call@16"}
{"type":"before","callId":"call@18","startTime":38509.949,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities/475","method":"DELETE","timeout":8000},"stepId":"pw:api@13","beforeSnapshot":"before@call@18"}
{"type":"before","callId":"call@20","startTime":38510.067,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities/471","method":"DELETE","timeout":8000},"stepId":"pw:api@14","beforeSnapshot":"before@call@20"}
{"type":"before","callId":"call@22","startTime":38510.192,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities/476","method":"DELETE","timeout":8000},"stepId":"pw:api@15","beforeSnapshot":"before@call@22"}
{"type":"log","callId":"call@12","time":38510.514,"message":"→ DELETE http://localhost:8000/api/v1/entities/473"}
{"type":"log","callId":"call@12","time":38510.517,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@12","time":38510.518,"message":"  accept: */*"}
{"type":"log","callId":"call@12","time":38510.52,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@12","time":38510.521,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@12","time":38510.522,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@12","time":38510.523,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@14","time":38510.694,"message":"→ DELETE http://localhost:8000/api/v1/entities/474"}
{"type":"log","callId":"call@14","time":38510.697,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@14","time":38510.698,"message":"  accept: */*"}
{"type":"log","callId":"call@14","time":38510.705,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@14","time":38510.706,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@14","time":38510.707,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@14","time":38510.709,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@16","time":38510.781,"message":"→ DELETE http://localhost:8000/api/v1/entities/472"}
{"type":"log","callId":"call@16","time":38510.784,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@16","time":38510.785,"message":"  accept: */*"}
{"type":"log","callId":"call@16","time":38510.788,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@16","time":38510.789,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@16","time":38510.79,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@16","time":38510.791,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@18","time":38510.87,"message":"→ DELETE http://localhost:8000/api/v1/entities/475"}
{"type":"log","callId":"call@18","time":38510.872,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@18","time":38510.873,"message":"  accept: */*"}
{"type":"log","callId":"call@18","time":38510.874,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@18","time":38510.875,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@18","time":38510.878,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@18","time":38510.879,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@20","time":38510.935,"message":"→ DELETE http://localhost:8000/api/v1/entities/471"}
{"type":"log","callId":"call@20","time":38510.937,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@20","time":38510.938,"message":"  accept: */*"}
{"type":"log","callId":"call@20","time":38510.939,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@20","time":38510.94,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@20","time":38510.941,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@20","time":38510.942,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@22","time":38511.007,"message":"→ DELETE http://localhost:8000/api/v1/entities/476"}
{"type":"log","callId":"call@22","time":38511.009,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@22","time":38511.01,"message":"  accept: */*"}
{"type":"log","callId":"call@22","time":38511.011,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@22","time":38511.012,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@22","time":38511.013,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@22","time":38511.014,"message":"  x-test-isolation: enabled"}
{"type":"screencast-frame","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":38517.936,"frameSwapWallTime":1751813120353.571}
{"type":"log","callId":"call@12","time":38519.929,"message":"← 200 OK"}
{"type":"log","callId":"call@12","time":38519.932,"message":"  date: Sun, 06 Jul 2025 14:45:19 GMT"}
{"type":"log","callId":"call@12","time":38519.934,"message":"  server: uvicorn"}
{"type":"log","callId":"call@12","time":38519.934,"message":"  content-length: 41"}
{"type":"log","callId":"call@12","time":38519.935,"message":"  content-type: application/json"}
{"type":"after","callId":"call@12","endTime":38519.967,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/473","status":200,"statusText":"OK","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"fetchUid":"920d6007a1e5b8b2a7cf9d7e7be0e87b"}},"afterSnapshot":"after@call@12"}
{"type":"log","callId":"call@16","time":38520.19,"message":"← 200 OK"}
{"type":"log","callId":"call@16","time":38520.193,"message":"  date: Sun, 06 Jul 2025 14:45:19 GMT"}
{"type":"log","callId":"call@16","time":38520.194,"message":"  server: uvicorn"}
{"type":"log","callId":"call@16","time":38520.195,"message":"  content-length: 41"}
{"type":"log","callId":"call@16","time":38520.196,"message":"  content-type: application/json"}
{"type":"after","callId":"call@16","endTime":38520.213,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/472","status":200,"statusText":"OK","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"fetchUid":"49b8a46459ac90d6254827daf39bed88"}},"afterSnapshot":"after@call@16"}
{"type":"log","callId":"call@20","time":38520.324,"message":"← 200 OK"}
{"type":"log","callId":"call@20","time":38520.326,"message":"  date: Sun, 06 Jul 2025 14:45:19 GMT"}
{"type":"log","callId":"call@20","time":38520.326,"message":"  server: uvicorn"}
{"type":"log","callId":"call@20","time":38520.327,"message":"  content-length: 41"}
{"type":"log","callId":"call@20","time":38520.328,"message":"  content-type: application/json"}
{"type":"after","callId":"call@20","endTime":38520.349,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/471","status":200,"statusText":"OK","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"fetchUid":"bf3d8b89ad400fc95ec9a11e22482d30"}},"afterSnapshot":"after@call@20"}
{"type":"log","callId":"call@14","time":38520.58,"message":"← 200 OK"}
{"type":"log","callId":"call@14","time":38520.581,"message":"  date: Sun, 06 Jul 2025 14:45:19 GMT"}
{"type":"log","callId":"call@14","time":38520.584,"message":"  server: uvicorn"}
{"type":"log","callId":"call@14","time":38520.585,"message":"  content-length: 41"}
{"type":"log","callId":"call@14","time":38520.586,"message":"  content-type: application/json"}
{"type":"after","callId":"call@14","endTime":38520.599,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/474","status":200,"statusText":"OK","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"fetchUid":"195954937dcbe3c8bf969b4bfd1c5d41"}},"afterSnapshot":"after@call@14"}
{"type":"log","callId":"call@22","time":38520.754,"message":"← 200 OK"}
{"type":"log","callId":"call@22","time":38520.755,"message":"  date: Sun, 06 Jul 2025 14:45:19 GMT"}
{"type":"log","callId":"call@22","time":38520.756,"message":"  server: uvicorn"}
{"type":"log","callId":"call@22","time":38520.757,"message":"  content-length: 41"}
{"type":"log","callId":"call@22","time":38520.758,"message":"  content-type: application/json"}
{"type":"after","callId":"call@22","endTime":38520.772,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/476","status":200,"statusText":"OK","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"fetchUid":"f9c8e4fdcc3bfd0c17d30082de253839"}},"afterSnapshot":"after@call@22"}
{"type":"log","callId":"call@18","time":38520.869,"message":"← 200 OK"}
{"type":"log","callId":"call@18","time":38520.87,"message":"  date: Sun, 06 Jul 2025 14:45:19 GMT"}
{"type":"log","callId":"call@18","time":38520.871,"message":"  server: uvicorn"}
{"type":"log","callId":"call@18","time":38520.872,"message":"  content-length: 41"}
{"type":"log","callId":"call@18","time":38520.873,"message":"  content-type: application/json"}
{"type":"after","callId":"call@18","endTime":38520.89,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/475","status":200,"statusText":"OK","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"fetchUid":"feda625bb1897f0531070a65185f573a"}},"afterSnapshot":"after@call@18"}
{"type":"before","callId":"call@24","startTime":38521.509,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities","method":"GET","timeout":8000},"stepId":"pw:api@16","beforeSnapshot":"before@call@24"}
{"type":"log","callId":"call@24","time":38521.909,"message":"→ GET http://localhost:8000/api/v1/entities"}
{"type":"log","callId":"call@24","time":38521.914,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@24","time":38521.915,"message":"  accept: */*"}
{"type":"log","callId":"call@24","time":38521.916,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@24","time":38521.917,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@24","time":38521.918,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@24","time":38521.919,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@24","time":38522.681,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@24","time":38522.684,"message":"  date: Sun, 06 Jul 2025 14:45:19 GMT"}
{"type":"log","callId":"call@24","time":38522.686,"message":"  server: uvicorn"}
{"type":"log","callId":"call@24","time":38522.687,"message":"  content-length: 0"}
{"type":"log","callId":"call@24","time":38522.688,"message":"  location: http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@24","time":38522.857,"message":"→ GET http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@24","time":38522.86,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@24","time":38522.861,"message":"  accept: */*"}
{"type":"log","callId":"call@24","time":38522.862,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@24","time":38522.863,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@24","time":38522.864,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@24","time":38522.865,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@24","time":38524.196,"message":"← 200 OK"}
{"type":"log","callId":"call@24","time":38524.201,"message":"  date: Sun, 06 Jul 2025 14:45:19 GMT"}
{"type":"log","callId":"call@24","time":38524.202,"message":"  server: uvicorn"}
{"type":"log","callId":"call@24","time":38524.203,"message":"  content-length: 1039"}
{"type":"log","callId":"call@24","time":38524.204,"message":"  content-type: application/json"}
{"type":"after","callId":"call@24","endTime":38524.237,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/","status":200,"statusText":"OK","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"1039"},{"name":"content-type","value":"application/json"}],"fetchUid":"e7507c29bd2a10c77c0b9a56a48d6f8b"}},"afterSnapshot":"after@call@24"}
{"type":"before","callId":"call@27","startTime":38528.267,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities","method":"POST","jsonData":"{\"name\":\"Human XFHZA\",\"unit_id\":1}","timeout":8000},"stepId":"pw:api@17","beforeSnapshot":"before@call@27"}
{"type":"before","callId":"call@29","startTime":38528.463,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities","method":"POST","jsonData":"{\"name\":\"Ball XFHZB\",\"unit_id\":1}","timeout":8000},"stepId":"pw:api@18","beforeSnapshot":"before@call@29"}
{"type":"before","callId":"call@31","startTime":38528.58,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities","method":"POST","jsonData":"{\"name\":\"Build XFHZC\",\"unit_id\":1}","timeout":8000},"stepId":"pw:api@19","beforeSnapshot":"before@call@31"}
{"type":"before","callId":"call@33","startTime":38528.678,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities","method":"POST","jsonData":"{\"name\":\"Car XFHZD\",\"unit_id\":1}","timeout":8000},"stepId":"pw:api@20","beforeSnapshot":"before@call@33"}
{"type":"before","callId":"call@35","startTime":38528.774,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities","method":"POST","jsonData":"{\"name\":\"Eleph XFHZE\",\"unit_id\":1}","timeout":8000},"stepId":"pw:api@21","beforeSnapshot":"before@call@35"}
{"type":"before","callId":"call@37","startTime":38528.869,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities","method":"POST","jsonData":"{\"name\":\"Mouse XFHZF\",\"unit_id\":1}","timeout":8000},"stepId":"pw:api@22","beforeSnapshot":"before@call@37"}
{"type":"log","callId":"call@27","time":38529.193,"message":"→ POST http://localhost:8000/api/v1/entities"}
{"type":"log","callId":"call@27","time":38529.195,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@27","time":38529.196,"message":"  accept: */*"}
{"type":"log","callId":"call@27","time":38529.197,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@27","time":38529.198,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@27","time":38529.199,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@27","time":38529.2,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@27","time":38529.2,"message":"  content-type: application/json"}
{"type":"log","callId":"call@27","time":38529.201,"message":"  content-length: 34"}
{"type":"log","callId":"call@29","time":38529.379,"message":"→ POST http://localhost:8000/api/v1/entities"}
{"type":"log","callId":"call@29","time":38529.381,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@29","time":38529.382,"message":"  accept: */*"}
{"type":"log","callId":"call@29","time":38529.383,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@29","time":38529.384,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@29","time":38529.384,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@29","time":38529.385,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@29","time":38529.386,"message":"  content-type: application/json"}
{"type":"log","callId":"call@29","time":38529.387,"message":"  content-length: 33"}
{"type":"log","callId":"call@31","time":38529.467,"message":"→ POST http://localhost:8000/api/v1/entities"}
{"type":"log","callId":"call@31","time":38529.469,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@31","time":38529.47,"message":"  accept: */*"}
{"type":"log","callId":"call@31","time":38529.471,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@31","time":38529.471,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@31","time":38529.472,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@31","time":38529.473,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@31","time":38529.474,"message":"  content-type: application/json"}
{"type":"log","callId":"call@31","time":38529.474,"message":"  content-length: 34"}
{"type":"log","callId":"call@33","time":38529.545,"message":"→ POST http://localhost:8000/api/v1/entities"}
{"type":"log","callId":"call@33","time":38529.547,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@33","time":38529.548,"message":"  accept: */*"}
{"type":"log","callId":"call@33","time":38529.548,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@33","time":38529.549,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@33","time":38529.55,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@33","time":38529.551,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@33","time":38529.552,"message":"  content-type: application/json"}
{"type":"log","callId":"call@33","time":38529.552,"message":"  content-length: 32"}
{"type":"log","callId":"call@35","time":38529.619,"message":"→ POST http://localhost:8000/api/v1/entities"}
{"type":"log","callId":"call@35","time":38529.62,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@35","time":38529.621,"message":"  accept: */*"}
{"type":"log","callId":"call@35","time":38529.622,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@35","time":38529.623,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@35","time":38529.624,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@35","time":38529.624,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@35","time":38529.625,"message":"  content-type: application/json"}
{"type":"log","callId":"call@35","time":38529.626,"message":"  content-length: 34"}
{"type":"log","callId":"call@37","time":38529.708,"message":"→ POST http://localhost:8000/api/v1/entities"}
{"type":"log","callId":"call@37","time":38529.71,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@37","time":38529.711,"message":"  accept: */*"}
{"type":"log","callId":"call@37","time":38529.711,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@37","time":38529.712,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@37","time":38529.713,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@37","time":38529.714,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@37","time":38529.714,"message":"  content-type: application/json"}
{"type":"log","callId":"call@37","time":38529.719,"message":"  content-length: 34"}
{"type":"log","callId":"call@27","time":38530.009,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@27","time":38530.012,"message":"  date: Sun, 06 Jul 2025 14:45:19 GMT"}
{"type":"log","callId":"call@27","time":38530.013,"message":"  server: uvicorn"}
{"type":"log","callId":"call@27","time":38530.014,"message":"  content-length: 0"}
{"type":"log","callId":"call@27","time":38530.015,"message":"  location: http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@27","time":38530.255,"message":"→ POST http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@27","time":38530.257,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@27","time":38530.258,"message":"  accept: */*"}
{"type":"log","callId":"call@27","time":38530.259,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@27","time":38530.26,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@27","time":38530.261,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@27","time":38530.262,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@27","time":38530.262,"message":"  content-type: application/json"}
{"type":"log","callId":"call@27","time":38530.263,"message":"  content-length: 34"}
{"type":"log","callId":"call@33","time":38530.47,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@33","time":38530.472,"message":"  date: Sun, 06 Jul 2025 14:45:19 GMT"}
{"type":"log","callId":"call@33","time":38530.473,"message":"  server: uvicorn"}
{"type":"log","callId":"call@33","time":38530.474,"message":"  content-length: 0"}
{"type":"log","callId":"call@33","time":38530.475,"message":"  location: http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@29","time":38530.56,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@29","time":38530.562,"message":"  date: Sun, 06 Jul 2025 14:45:19 GMT"}
{"type":"log","callId":"call@29","time":38530.563,"message":"  server: uvicorn"}
{"type":"log","callId":"call@29","time":38530.563,"message":"  content-length: 0"}
{"type":"log","callId":"call@29","time":38530.564,"message":"  location: http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@35","time":38530.64,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@35","time":38530.642,"message":"  date: Sun, 06 Jul 2025 14:45:19 GMT"}
{"type":"log","callId":"call@35","time":38530.642,"message":"  server: uvicorn"}
{"type":"log","callId":"call@35","time":38530.643,"message":"  content-length: 0"}
{"type":"log","callId":"call@35","time":38530.644,"message":"  location: http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@31","time":38530.732,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@31","time":38530.733,"message":"  date: Sun, 06 Jul 2025 14:45:19 GMT"}
{"type":"log","callId":"call@31","time":38530.734,"message":"  server: uvicorn"}
{"type":"log","callId":"call@31","time":38530.735,"message":"  content-length: 0"}
{"type":"log","callId":"call@31","time":38530.735,"message":"  location: http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@33","time":38530.926,"message":"→ POST http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@33","time":38530.928,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@33","time":38530.929,"message":"  accept: */*"}
{"type":"log","callId":"call@33","time":38530.93,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@33","time":38530.93,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@33","time":38530.931,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@33","time":38530.932,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@33","time":38530.933,"message":"  content-type: application/json"}
{"type":"log","callId":"call@33","time":38530.933,"message":"  content-length: 32"}
{"type":"log","callId":"call@29","time":38530.998,"message":"→ POST http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@29","time":38531,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@29","time":38531.001,"message":"  accept: */*"}
{"type":"log","callId":"call@29","time":38531.002,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@29","time":38531.003,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@29","time":38531.003,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@29","time":38531.004,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@29","time":38531.005,"message":"  content-type: application/json"}
{"type":"log","callId":"call@29","time":38531.006,"message":"  content-length: 33"}
{"type":"log","callId":"call@35","time":38531.054,"message":"→ POST http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@35","time":38531.055,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@35","time":38531.056,"message":"  accept: */*"}
{"type":"log","callId":"call@35","time":38531.057,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@35","time":38531.057,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@35","time":38531.058,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@35","time":38531.059,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@35","time":38531.06,"message":"  content-type: application/json"}
{"type":"log","callId":"call@35","time":38531.06,"message":"  content-length: 34"}
{"type":"log","callId":"call@31","time":38531.1,"message":"→ POST http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@31","time":38531.101,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@31","time":38531.102,"message":"  accept: */*"}
{"type":"log","callId":"call@31","time":38531.103,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@31","time":38531.104,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@31","time":38531.105,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@31","time":38531.105,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@31","time":38531.106,"message":"  content-type: application/json"}
{"type":"log","callId":"call@31","time":38531.107,"message":"  content-length: 34"}
{"type":"log","callId":"call@37","time":38531.332,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@37","time":38531.334,"message":"  date: Sun, 06 Jul 2025 14:45:19 GMT"}
{"type":"log","callId":"call@37","time":38531.334,"message":"  server: uvicorn"}
{"type":"log","callId":"call@37","time":38531.335,"message":"  content-length: 0"}
{"type":"log","callId":"call@37","time":38531.336,"message":"  location: http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@37","time":38531.743,"message":"→ POST http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@37","time":38531.746,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@37","time":38531.747,"message":"  accept: */*"}
{"type":"log","callId":"call@37","time":38531.748,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@37","time":38531.749,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@37","time":38531.749,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@37","time":38531.75,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@37","time":38531.751,"message":"  content-type: application/json"}
{"type":"log","callId":"call@37","time":38531.752,"message":"  content-length: 34"}
{"type":"log","callId":"call@27","time":38536.765,"message":"← 201 Created"}
{"type":"log","callId":"call@27","time":38536.767,"message":"  date: Sun, 06 Jul 2025 14:45:19 GMT"}
{"type":"log","callId":"call@27","time":38536.768,"message":"  server: uvicorn"}
{"type":"log","callId":"call@27","time":38536.777,"message":"  content-length: 115"}
{"type":"log","callId":"call@27","time":38536.783,"message":"  content-type: application/json"}
{"type":"after","callId":"call@27","endTime":38536.833,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/","status":201,"statusText":"Created","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"115"},{"name":"content-type","value":"application/json"}],"fetchUid":"ab868c5b9b301b4b9f5c6e6e7004fc1a"}},"afterSnapshot":"after@call@27"}
{"type":"log","callId":"call@33","time":38537.261,"message":"← 201 Created"}
{"type":"log","callId":"call@33","time":38537.264,"message":"  date: Sun, 06 Jul 2025 14:45:19 GMT"}
{"type":"log","callId":"call@33","time":38537.265,"message":"  server: uvicorn"}
{"type":"log","callId":"call@33","time":38537.266,"message":"  content-length: 113"}
{"type":"log","callId":"call@33","time":38537.267,"message":"  content-type: application/json"}
{"type":"after","callId":"call@33","endTime":38537.287,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/","status":201,"statusText":"Created","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"113"},{"name":"content-type","value":"application/json"}],"fetchUid":"989e0c6efcc59f8ebcb56c2bc5819a39"}},"afterSnapshot":"after@call@33"}
{"type":"log","callId":"call@35","time":38537.377,"message":"← 201 Created"}
{"type":"log","callId":"call@35","time":38537.378,"message":"  date: Sun, 06 Jul 2025 14:45:19 GMT"}
{"type":"log","callId":"call@35","time":38537.379,"message":"  server: uvicorn"}
{"type":"log","callId":"call@35","time":38537.38,"message":"  content-length: 115"}
{"type":"log","callId":"call@35","time":38537.381,"message":"  content-type: application/json"}
{"type":"after","callId":"call@35","endTime":38537.393,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/","status":201,"statusText":"Created","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"115"},{"name":"content-type","value":"application/json"}],"fetchUid":"7ef441d2a65b48425b38200e44a2f3e7"}},"afterSnapshot":"after@call@35"}
{"type":"log","callId":"call@29","time":38537.477,"message":"← 201 Created"}
{"type":"log","callId":"call@29","time":38537.479,"message":"  date: Sun, 06 Jul 2025 14:45:19 GMT"}
{"type":"log","callId":"call@29","time":38537.48,"message":"  server: uvicorn"}
{"type":"log","callId":"call@29","time":38537.481,"message":"  content-length: 114"}
{"type":"log","callId":"call@29","time":38537.481,"message":"  content-type: application/json"}
{"type":"after","callId":"call@29","endTime":38537.491,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/","status":201,"statusText":"Created","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"114"},{"name":"content-type","value":"application/json"}],"fetchUid":"b1fc5999b08419d412a8dd30cf407f95"}},"afterSnapshot":"after@call@29"}
{"type":"log","callId":"call@31","time":38537.566,"message":"← 201 Created"}
{"type":"log","callId":"call@31","time":38537.567,"message":"  date: Sun, 06 Jul 2025 14:45:19 GMT"}
{"type":"log","callId":"call@31","time":38537.568,"message":"  server: uvicorn"}
{"type":"log","callId":"call@31","time":38537.569,"message":"  content-length: 115"}
{"type":"log","callId":"call@31","time":38537.57,"message":"  content-type: application/json"}
{"type":"after","callId":"call@31","endTime":38537.579,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/","status":201,"statusText":"Created","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"115"},{"name":"content-type","value":"application/json"}],"fetchUid":"1fbd851c1c8c6795a381dc3208319e7d"}},"afterSnapshot":"after@call@31"}
{"type":"log","callId":"call@37","time":38537.636,"message":"← 201 Created"}
{"type":"log","callId":"call@37","time":38537.637,"message":"  date: Sun, 06 Jul 2025 14:45:19 GMT"}
{"type":"log","callId":"call@37","time":38537.638,"message":"  server: uvicorn"}
{"type":"log","callId":"call@37","time":38537.639,"message":"  content-length: 115"}
{"type":"log","callId":"call@37","time":38537.639,"message":"  content-type: application/json"}
{"type":"after","callId":"call@37","endTime":38537.651,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/","status":201,"statusText":"Created","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"115"},{"name":"content-type","value":"application/json"}],"fetchUid":"f656d391417ab7ab7fc23a441e40a4f0"}},"afterSnapshot":"after@call@37"}
{"type":"before","callId":"call@45","startTime":38540.146,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/connections","method":"POST","jsonData":"{\"from_entity_id\":477,\"to_entity_id\":480,\"multiplier\":10,\"unit_id\":1}","timeout":8000},"stepId":"pw:api@23","beforeSnapshot":"before@call@45"}
{"type":"before","callId":"call@47","startTime":38540.282,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/connections","method":"POST","jsonData":"{\"from_entity_id\":480,\"to_entity_id\":481,\"multiplier\":50,\"unit_id\":1}","timeout":8000},"stepId":"pw:api@24","beforeSnapshot":"before@call@47"}
{"type":"before","callId":"call@49","startTime":38540.382,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/connections","method":"POST","jsonData":"{\"from_entity_id\":481,\"to_entity_id\":482,\"multiplier\":0.1,\"unit_id\":1}","timeout":8000},"stepId":"pw:api@25","beforeSnapshot":"before@call@49"}
{"type":"before","callId":"call@51","startTime":38540.48,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/connections","method":"POST","jsonData":"{\"from_entity_id\":478,\"to_entity_id\":479,\"multiplier\":0.3,\"unit_id\":2}","timeout":8000},"stepId":"pw:api@26","beforeSnapshot":"before@call@51"}
{"type":"log","callId":"call@45","time":38540.656,"message":"→ POST http://localhost:8000/api/v1/connections"}
{"type":"log","callId":"call@45","time":38540.658,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@45","time":38540.659,"message":"  accept: */*"}
{"type":"log","callId":"call@45","time":38540.66,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@45","time":38540.661,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@45","time":38540.662,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@45","time":38540.663,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@45","time":38540.663,"message":"  content-type: application/json"}
{"type":"log","callId":"call@45","time":38540.664,"message":"  content-length: 69"}
{"type":"log","callId":"call@47","time":38540.761,"message":"→ POST http://localhost:8000/api/v1/connections"}
{"type":"log","callId":"call@47","time":38540.763,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@47","time":38540.764,"message":"  accept: */*"}
{"type":"log","callId":"call@47","time":38540.765,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@47","time":38540.766,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@47","time":38540.767,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@47","time":38540.768,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@47","time":38540.768,"message":"  content-type: application/json"}
{"type":"log","callId":"call@47","time":38540.769,"message":"  content-length: 69"}
{"type":"log","callId":"call@49","time":38540.833,"message":"→ POST http://localhost:8000/api/v1/connections"}
{"type":"log","callId":"call@49","time":38540.834,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@49","time":38540.835,"message":"  accept: */*"}
{"type":"log","callId":"call@49","time":38540.836,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@49","time":38540.836,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@49","time":38540.837,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@49","time":38540.838,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@49","time":38540.839,"message":"  content-type: application/json"}
{"type":"log","callId":"call@49","time":38540.839,"message":"  content-length: 70"}
{"type":"log","callId":"call@51","time":38540.895,"message":"→ POST http://localhost:8000/api/v1/connections"}
{"type":"log","callId":"call@51","time":38540.896,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@51","time":38540.897,"message":"  accept: */*"}
{"type":"log","callId":"call@51","time":38540.898,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@51","time":38540.899,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@51","time":38540.899,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@51","time":38540.9,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@51","time":38540.901,"message":"  content-type: application/json"}
{"type":"log","callId":"call@51","time":38540.901,"message":"  content-length: 70"}
{"type":"log","callId":"call@45","time":38541.504,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@45","time":38541.508,"message":"  date: Sun, 06 Jul 2025 14:45:19 GMT"}
{"type":"log","callId":"call@45","time":38541.509,"message":"  server: uvicorn"}
{"type":"log","callId":"call@45","time":38541.51,"message":"  content-length: 0"}
{"type":"log","callId":"call@45","time":38541.511,"message":"  location: http://localhost:8000/api/v1/connections/"}
{"type":"log","callId":"call@47","time":38541.619,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@47","time":38541.621,"message":"  date: Sun, 06 Jul 2025 14:45:19 GMT"}
{"type":"log","callId":"call@47","time":38541.622,"message":"  server: uvicorn"}
{"type":"log","callId":"call@47","time":38541.623,"message":"  content-length: 0"}
{"type":"log","callId":"call@47","time":38541.623,"message":"  location: http://localhost:8000/api/v1/connections/"}
{"type":"log","callId":"call@49","time":38541.731,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@49","time":38541.733,"message":"  date: Sun, 06 Jul 2025 14:45:19 GMT"}
{"type":"log","callId":"call@49","time":38541.734,"message":"  server: uvicorn"}
{"type":"log","callId":"call@49","time":38541.734,"message":"  content-length: 0"}
{"type":"log","callId":"call@49","time":38541.735,"message":"  location: http://localhost:8000/api/v1/connections/"}
{"type":"log","callId":"call@51","time":38541.798,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@51","time":38541.799,"message":"  date: Sun, 06 Jul 2025 14:45:19 GMT"}
{"type":"log","callId":"call@51","time":38541.8,"message":"  server: uvicorn"}
{"type":"log","callId":"call@51","time":38541.801,"message":"  content-length: 0"}
{"type":"log","callId":"call@51","time":38541.801,"message":"  location: http://localhost:8000/api/v1/connections/"}
{"type":"log","callId":"call@45","time":38541.884,"message":"→ POST http://localhost:8000/api/v1/connections/"}
{"type":"log","callId":"call@45","time":38541.886,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@45","time":38541.887,"message":"  accept: */*"}
{"type":"log","callId":"call@45","time":38541.887,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@45","time":38541.888,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@45","time":38541.889,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@45","time":38541.89,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@45","time":38541.89,"message":"  content-type: application/json"}
{"type":"log","callId":"call@45","time":38541.891,"message":"  content-length: 69"}
{"type":"log","callId":"call@47","time":38541.966,"message":"→ POST http://localhost:8000/api/v1/connections/"}
{"type":"log","callId":"call@47","time":38541.967,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@47","time":38541.968,"message":"  accept: */*"}
{"type":"log","callId":"call@47","time":38541.968,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@47","time":38541.969,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@47","time":38541.97,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@47","time":38541.97,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@47","time":38541.971,"message":"  content-type: application/json"}
{"type":"log","callId":"call@47","time":38541.972,"message":"  content-length: 69"}
{"type":"log","callId":"call@49","time":38542.073,"message":"→ POST http://localhost:8000/api/v1/connections/"}
{"type":"log","callId":"call@49","time":38542.074,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@49","time":38542.075,"message":"  accept: */*"}
{"type":"log","callId":"call@49","time":38542.075,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@49","time":38542.076,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@49","time":38542.077,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@49","time":38542.077,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@49","time":38542.078,"message":"  content-type: application/json"}
{"type":"log","callId":"call@49","time":38542.079,"message":"  content-length: 70"}
{"type":"log","callId":"call@51","time":38542.116,"message":"→ POST http://localhost:8000/api/v1/connections/"}
{"type":"log","callId":"call@51","time":38542.117,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@51","time":38542.118,"message":"  accept: */*"}
{"type":"log","callId":"call@51","time":38542.119,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@51","time":38542.119,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@51","time":38542.12,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@51","time":38542.121,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@51","time":38542.121,"message":"  content-type: application/json"}
{"type":"log","callId":"call@51","time":38542.122,"message":"  content-length: 70"}
{"type":"log","callId":"call@45","time":38547.959,"message":"← 201 Created"}
{"type":"log","callId":"call@45","time":38547.962,"message":"  date: Sun, 06 Jul 2025 14:45:19 GMT"}
{"type":"log","callId":"call@45","time":38547.963,"message":"  server: uvicorn"}
{"type":"log","callId":"call@45","time":38547.964,"message":"  content-length: 166"}
{"type":"log","callId":"call@45","time":38547.965,"message":"  content-type: application/json"}
{"type":"after","callId":"call@45","endTime":38547.996,"result":{"response":{"url":"http://localhost:8000/api/v1/connections/","status":201,"statusText":"Created","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"166"},{"name":"content-type","value":"application/json"}],"fetchUid":"702b9b43c5df4be32b6170c3b83e85ca"}},"afterSnapshot":"after@call@45"}
{"type":"log","callId":"call@47","time":38548.308,"message":"← 201 Created"}
{"type":"log","callId":"call@47","time":38548.31,"message":"  date: Sun, 06 Jul 2025 14:45:19 GMT"}
{"type":"log","callId":"call@47","time":38548.311,"message":"  server: uvicorn"}
{"type":"log","callId":"call@47","time":38548.312,"message":"  content-length: 166"}
{"type":"log","callId":"call@47","time":38548.312,"message":"  content-type: application/json"}
{"type":"after","callId":"call@47","endTime":38548.325,"result":{"response":{"url":"http://localhost:8000/api/v1/connections/","status":201,"statusText":"Created","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"166"},{"name":"content-type","value":"application/json"}],"fetchUid":"f7ee71bb350bb24e70cc33d283abbecc"}},"afterSnapshot":"after@call@47"}
{"type":"log","callId":"call@51","time":38548.646,"message":"← 201 Created"}
{"type":"log","callId":"call@51","time":38548.647,"message":"  date: Sun, 06 Jul 2025 14:45:19 GMT"}
{"type":"log","callId":"call@51","time":38548.648,"message":"  server: uvicorn"}
{"type":"log","callId":"call@51","time":38548.65,"message":"  content-length: 165"}
{"type":"log","callId":"call@51","time":38548.651,"message":"  content-type: application/json"}
{"type":"after","callId":"call@51","endTime":38548.664,"result":{"response":{"url":"http://localhost:8000/api/v1/connections/","status":201,"statusText":"Created","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"165"},{"name":"content-type","value":"application/json"}],"fetchUid":"215f4677a7a3a4ca4a9bf15376526528"}},"afterSnapshot":"after@call@51"}
{"type":"log","callId":"call@49","time":38549.016,"message":"← 201 Created"}
{"type":"log","callId":"call@49","time":38549.018,"message":"  date: Sun, 06 Jul 2025 14:45:19 GMT"}
{"type":"log","callId":"call@49","time":38549.019,"message":"  server: uvicorn"}
{"type":"log","callId":"call@49","time":38549.019,"message":"  content-length: 165"}
{"type":"log","callId":"call@49","time":38549.02,"message":"  content-type: application/json"}
{"type":"after","callId":"call@49","endTime":38549.033,"result":{"response":{"url":"http://localhost:8000/api/v1/connections/","status":201,"statusText":"Created","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"165"},{"name":"content-type","value":"application/json"}],"fetchUid":"72823826276f321442004fafbb903dea"}},"afterSnapshot":"after@call@49"}
{"type":"before","callId":"call@57","startTime":38555.297,"class":"Frame","method":"goto","params":{"url":"/","timeout":15000,"waitUntil":"load"},"stepId":"pw:api@27","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","beforeSnapshot":"before@call@57"}
{"type":"frame-snapshot","snapshot":{"callId":"call@57","snapshotName":"before@call@57","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"about:blank","html":["HTML",{},["HEAD",{},["BASE",{"href":"about:blank"}]],["BODY"]],"viewport":{"width":1280,"height":720},"timestamp":38556.459,"wallTime":1751813120393,"collectionTime":0.5,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@57","time":38556.952,"message":"navigating to \"http://localhost:3000/\", waiting until \"load\""}
{"type":"screencast-frame","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":38579.099,"frameSwapWallTime":1751813120414.886}
{"type":"console","messageType":"info","text":"%cDownload the React DevTools for a better development experience: https://reactjs.org/link/react-devtools font-weight:bold","args":[{"preview":"%cDownload the React DevTools for a better development experience: https://reactjs.org/link/react-devtools","value":"%cDownload the React DevTools for a better development experience: https://reactjs.org/link/react-devtools"},{"preview":"font-weight:bold","value":"font-weight:bold"}],"location":{"url":"http://localhost:3000/static/js/bundle.js","lineNumber":38990,"columnNumber":20},"time":38624.029,"pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635"}
{"type":"after","callId":"call@57","endTime":38656.343,"result":{"response":"<Response>"},"afterSnapshot":"after@call@57"}
{"type":"screencast-frame","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":38656.642,"frameSwapWallTime":1751813120488.5}
{"type":"console","messageType":"warning","text":"⚠️ React Router Future Flag Warning: React Router will begin wrapping state updates in `React.startTransition` in v7. You can use the `v7_startTransition` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_starttransition.","args":[{"preview":"⚠️ React Router Future Flag Warning: React Router will begin wrapping state updates in `React.startTransition` in v7. You can use the `v7_startTransition` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_starttransition.","value":"⚠️ React Router Future Flag Warning: React Router will begin wrapping state updates in `React.startTransition` in v7. You can use the `v7_startTransition` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_starttransition."}],"location":{"url":"http://localhost:3000/static/js/bundle.js","lineNumber":42276,"columnNumber":12},"time":38657.213,"pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635"}
{"type":"console","messageType":"warning","text":"⚠️ React Router Future Flag Warning: Relative route resolution within Splat routes is changing in v7. You can use the `v7_relativeSplatPath` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath.","args":[{"preview":"⚠️ React Router Future Flag Warning: Relative route resolution within Splat routes is changing in v7. You can use the `v7_relativeSplatPath` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath.","value":"⚠️ React Router Future Flag Warning: Relative route resolution within Splat routes is changing in v7. You can use the `v7_relativeSplatPath` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath."}],"location":{"url":"http://localhost:3000/static/js/bundle.js","lineNumber":42276,"columnNumber":12},"time":38657.245,"pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635"}
{"type":"console","messageType":"log","text":"API Request: GET /entities/ undefined","args":[{"preview":"API Request:","value":"API Request:"},{"preview":"GET","value":"GET"},{"preview":"/entities/","value":"/entities/"},{"preview":"undefined"}],"location":{"url":"http://localhost:3000/static/js/bundle.js","lineNumber":54565,"columnNumber":10},"time":38657.281,"pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635"}
{"type":"console","messageType":"log","text":"API Request: GET /units/ undefined","args":[{"preview":"API Request:","value":"API Request:"},{"preview":"GET","value":"GET"},{"preview":"/units/","value":"/units/"},{"preview":"undefined"}],"location":{"url":"http://localhost:3000/static/js/bundle.js","lineNumber":54565,"columnNumber":10},"time":38657.322,"pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635"}
{"type":"console","messageType":"log","text":"API Request: GET /entities/ undefined","args":[{"preview":"API Request:","value":"API Request:"},{"preview":"GET","value":"GET"},{"preview":"/entities/","value":"/entities/"},{"preview":"undefined"}],"location":{"url":"http://localhost:3000/static/js/bundle.js","lineNumber":54565,"columnNumber":10},"time":38657.347,"pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635"}
{"type":"console","messageType":"log","text":"API Request: GET /units/ undefined","args":[{"preview":"API Request:","value":"API Request:"},{"preview":"GET","value":"GET"},{"preview":"/units/","value":"/units/"},{"preview":"undefined"}],"location":{"url":"http://localhost:3000/static/js/bundle.js","lineNumber":54565,"columnNumber":10},"time":38657.371,"pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635"}
{"type":"frame-snapshot","snapshot":{"callId":"call@57","snapshotName":"after@call@57","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},["HEAD",{},["BASE",{"href":"http://localhost:3000/"}],"\n    ",["META",{"charset":"utf-8"}],"\n    ",["LINK",{"rel":"icon","href":"/favicon.ico"}],"\n    ",["META",{"name":"viewport","content":"width=device-width, initial-scale=1"}],"\n    ",["META",{"name":"theme-color","content":"#000000"}],"\n    ",["META",{"name":"description","content":"SIMILE - Compare entities based on measurable relationships"}],"\n    ",["LINK",{"rel":"apple-touch-icon","href":"/logo192.png"}],"\n    ",["LINK",{"rel":"manifest","href":"/manifest.json"}],"\n    ",["TITLE",{},"SIMILE"],"\n  ",["STYLE",{},"body {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9pbmRleC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxTQUFTO0VBQ1Q7O2NBRVk7RUFDWixtQ0FBbUM7RUFDbkMsa0NBQWtDO0FBQ3BDOztBQUVBO0VBQ0U7YUFDVztBQUNiIiwic291cmNlc0NvbnRlbnQiOlsiYm9keSB7XG4gIG1hcmdpbjogMDtcbiAgZm9udC1mYW1pbHk6IC1hcHBsZS1zeXN0ZW0sIEJsaW5rTWFjU3lzdGVtRm9udCwgJ1NlZ29lIFVJJywgJ1JvYm90bycsICdPeHlnZW4nLFxuICAgICdVYnVudHUnLCAnQ2FudGFyZWxsJywgJ0ZpcmEgU2FucycsICdEcm9pZCBTYW5zJywgJ0hlbHZldGljYSBOZXVlJyxcbiAgICBzYW5zLXNlcmlmO1xuICAtd2Via2l0LWZvbnQtc21vb3RoaW5nOiBhbnRpYWxpYXNlZDtcbiAgLW1vei1vc3gtZm9udC1zbW9vdGhpbmc6IGdyYXlzY2FsZTtcbn1cblxuY29kZSB7XG4gIGZvbnQtZmFtaWx5OiBzb3VyY2UtY29kZS1wcm8sIE1lbmxvLCBNb25hY28sIENvbnNvbGFzLCAnQ291cmllciBOZXcnLFxuICAgIG1vbm9zcGFjZTtcbn0iXSwic291cmNlUm9vdCI6IiJ9 */"],["STYLE",{},"/* Reset and base styles */\n* {\n  box-sizing: border-box;\n  margin: 0;\n  padding: 0;\n}\n\nbody {\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  background-color: #f5f5f5;\n}\n\n.App {\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n}\n\n/* Navigation */\n.navigation {\n  background-color: #282c34;\n  padding: 1rem 2rem;\n  color: white;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.nav-brand .brand-link {\n  color: white;\n  text-decoration: none;\n}\n\n.nav-brand h1 {\n  font-size: 1.8rem;\n  margin-bottom: 0.2rem;\n}\n\n.nav-brand p {\n  font-size: 0.9rem;\n  color: #61dafb;\n}\n\n.nav-links {\n  display: flex;\n  gap: 1.5rem;\n}\n\n.nav-link {\n  color: white;\n  text-decoration: none;\n  padding: 0.5rem 1rem;\n  border-radius: 4px;\n  transition: background-color 0.2s;\n}\n\n.nav-link:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n}\n\n.nav-link.active {\n  background-color: #61dafb;\n  color: #282c34;\n}\n\n/* Main content */\n.main-content {\n  flex: 1 1;\n  padding: 2rem;\n  max-width: 1200px;\n  margin: 0 auto;\n  width: 100%;\n}\n\n/* Manager headers */\n.manager-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 2rem;\n  padding-bottom: 1rem;\n  border-bottom: 2px solid #e0e0e0;\n}\n\n.manager-header h2 {\n  color: #333;\n  font-size: 1.8rem;\n}\n\n/* Buttons */\n.btn-primary, .btn-secondary, .btn-select, .btn-edit, .btn-delete {\n  padding: 0.5rem 1rem;\n  border-radius: 4px;\n  border: none;\n  cursor: pointer;\n  font-size: 0.9rem;\n  transition: background-color 0.2s;\n}\n\n.btn-primary {\n  background-color: #007bff;\n  color: white;\n}\n\n.btn-primary:hover:not(:disabled) {\n  background-color: #0056b3;\n}\n\n.btn-secondary {\n  background-color: #6c757d;\n  color: white;\n}\n\n.btn-secondary:hover:not(:disabled) {\n  background-color: #545b62;\n}\n\n.btn-select {\n  background-color: #28a745;\n  color: white;\n}\n\n.btn-edit {\n  background-color: #ffc107;\n  color: #212529;\n}\n\n.btn-delete {\n  background-color: #dc3545;\n  color: white;\n}\n\n.btn-delete:hover:not(:disabled) {\n  background-color: #c82333;\n}\n\nbutton:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n/* Cards and grids */\n.entity-grid, .connections-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 1rem;\n}\n\n.entity-card, .connection-card {\n  background: white;\n  border-radius: 8px;\n  padding: 1.5rem;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  border: 1px solid #e0e0e0;\n}\n\n.entity-card h4 {\n  margin-bottom: 0.5rem;\n  color: #333;\n}\n\n.entity-id {\n  color: #666;\n  font-size: 0.8rem;\n  margin-bottom: 1rem;\n}\n\n.entity-actions {\n  display: flex;\n  gap: 0.5rem;\n  flex-wrap: wrap;\n}\n\n.connection-relationship {\n  margin-bottom: 1rem;\n  font-size: 1.1rem;\n  line-height: 1.4;\n}\n\n.entity-name {\n  font-weight: bold;\n  color: #007bff;\n}\n\n.multiplier {\n  font-weight: bold;\n  color: #28a745;\n  font-size: 1.2em;\n}\n\n.relationship-text, .times-text {\n  color: #666;\n  margin: 0 0.3rem;\n}\n\n.unit-text {\n  color: #6c757d;\n  font-style: italic;\n}\n\n.connection-details {\n  display: flex;\n  gap: 1rem;\n  margin-bottom: 1rem;\n  font-size: 0.8rem;\n  color: #666;\n}\n\n/* Forms */\n.entity-form, .connection-form, .comparison-form {\n  background: white;\n  border-radius: 8px;\n  padding: 2rem;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  border: 1px solid #e0e0e0;\n}\n\n/* Form validation states */\n.form-input {\n  transition: border-color 0.2s, box-shadow 0.2s;\n}\n\n.form-input.valid {\n  border-color: #28a745;\n  box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.25);\n}\n\n.form-input.invalid {\n  border-color: #dc3545;\n  box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.25);\n}\n\n.validation-success {\n  color: #28a745;\n  font-weight: 500;\n  margin-left: 0.5rem;\n}\n\n.form-group {\n  margin-bottom: 1rem;\n}\n\n.form-row {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n}\n\n.form-group label {\n  display: block;\n  margin-bottom: 0.5rem;\n  font-weight: 500;\n  color: #333;\n}\n\n.form-group input, .form-group select {\n  width: 100%;\n  padding: 0.5rem;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  font-size: 1rem;\n}\n\n.form-group input:focus, .form-group select:focus {\n  outline: none;\n  border-color: #007bff;\n  box-shadow: 0 0 0 2px rgba(0,123,255,0.25);\n}\n\n.form-help {\n  display: block;\n  margin-top: 0.25rem;\n  color: #666;\n  font-size: 0.8rem;\n}\n\n.form-actions {\n  display: flex;\n  gap: 1rem;\n  margin-top: 1.5rem;\n}\n\n.form-note {\n  margin-top: 1rem;\n  padding: 1rem;\n  background-color: #f8f9fa;\n  border-radius: 4px;\n  border-left: 4px solid #007bff;\n}\n\n/* Previews */\n.connection-preview, .comparison-preview {\n  background-color: #f8f9fa;\n  padding: 1rem;\n  border-radius: 4px;\n  margin-bottom: 1.5rem;\n  border-left: 4px solid #28a745;\n}\n\n/* Results */\n.comparison-result {\n  background: white;\n  border-radius: 8px;\n  padding: 2rem;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  border: 1px solid #e0e0e0;\n  margin-top: 2rem;\n}\n\n.main-result {\n  text-align: center;\n  margin-bottom: 2rem;\n}\n\n.result-statement {\n  font-size: 1.5rem;\n  line-height: 1.6;\n  padding: 1rem;\n  background-color: #f8f9fa;\n  border-radius: 8px;\n  border: 2px solid #28a745;\n}\n\n/* Template-style result statement */\n.result-statement-template {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 3rem;\n  font-weight: 300;\n  line-height: 1.5;\n  padding: 3rem 2rem;\n  text-align: center;\n  letter-spacing: 0.02em;\n  margin-bottom: 2rem;\n}\n\n.template-prefix {\n  color: #d2691e; /* Orange/brown color */\n  margin-right: 0.3rem;\n}\n\n.template-from-count {\n  color: #333;\n  font-weight: 400;\n  border-bottom: 2px solid #333;\n  padding: 0.1rem 0.3rem;\n  margin: 0 0.4rem;\n}\n\n.template-from-entity {\n  color: #008B8B; /* Teal */\n  font-weight: 400;\n  border-bottom: 2px solid #333;\n  padding: 0.1rem 0.3rem;\n  margin: 0 0.4rem;\n}\n\n.template-relationship {\n  color: #333;\n  font-weight: 400;\n  margin: 0 0.4rem;\n}\n\n.template-multiplier {\n  color: #333;\n  font-weight: 400;\n  border-bottom: 2px solid #333;\n  padding: 0.1rem 0.3rem;\n  margin: 0 0.4rem;\n}\n\n.template-to-entity {\n  color: #808080; /* Gray */\n  font-weight: 400;\n  border-bottom: 2px solid #333;\n  padding: 0.1rem 0.3rem;\n  margin: 0 0.4rem;\n}\n\n.template-suffix {\n  color: #D2691E; /* Orange color */\n  font-weight: 400;\n  font-size: 3.2rem;\n  margin-left: 0.4rem;\n}\n\n.calculation-path {\n  margin-bottom: 2rem;\n}\n\n.calculation-path h4 {\n  margin-bottom: 1rem;\n  color: #333;\n}\n\n.path-steps {\n  background-color: #f8f9fa;\n  padding: 1rem;\n  border-radius: 8px;\n  margin-bottom: 1rem;\n}\n\n.path-step {\n  display: flex;\n  align-items: center;\n  margin-bottom: 0.5rem;\n}\n\n.step-number {\n  background-color: #007bff;\n  color: white;\n  width: 24px;\n  height: 24px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 0.8rem;\n  margin-right: 1rem;\n  flex-shrink: 0;\n}\n\n.step-content {\n  flex: 1 1;\n}\n\n.step-arrow {\n  text-align: center;\n  color: #666;\n  margin: 0.5rem 0;\n  font-size: 1.2rem;\n}\n\n.calculation-formula {\n  background-color: white;\n  padding: 1rem;\n  border-radius: 4px;\n  border: 1px solid #ddd;\n}\n\n.result-details {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n  padding-top: 1rem;\n  border-top: 1px solid #e0e0e0;\n}\n\n.detail-item {\n  color: #666;\n}\n\n/* Loading and error states */\n.loading {\n  text-align: center;\n  padding: 2rem;\n  color: #666;\n}\n\n.error {\n  background-color: #f8d7da;\n  color: #721c24;\n  padding: 1rem;\n  border-radius: 4px;\n  border: 1px solid #f5c6cb;\n}\n\n.error-message {\n  background-color: #f8d7da;\n  color: #721c24;\n  padding: 0.75rem;\n  border-radius: 4px;\n  border: 1px solid #f5c6cb;\n  margin-bottom: 1rem;\n}\n\n.no-data {\n  text-align: center;\n  padding: 2rem;\n  color: #666;\n}\n\n/* Responsive design */\n@media (max-width: 768px) {\n  .navigation {\n    flex-direction: column;\n    gap: 1rem;\n  }\n  \n  .nav-links {\n    gap: 1rem;\n  }\n  \n  .main-content {\n    padding: 1rem;\n  }\n  \n  .manager-header {\n    flex-direction: column;\n    gap: 1rem;\n    align-items: stretch;\n  }\n  \n  .entity-grid, .connections-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .form-row {\n    grid-template-columns: 1fr;\n  }\n  \n  .form-actions {\n    flex-direction: column;\n  }\n  \n  .result-details {\n    grid-template-columns: 1fr;\n  }\n  \n  .result-statement-template {\n    font-size: 2rem;\n    padding: 2rem 1rem;\n    line-height: 1.4;\n  }\n  \n  .result-statement-template .template-suffix {\n    font-size: 2.2rem;\n  }\n}\n\n/* Skeleton loading animations */\n.skeleton {\n  background: linear-gradient(\n    90deg,\n    #f0f0f0 25%,\n    #e0e0e0 50%,\n    #f0f0f0 75%\n  );\n  background-size: 200% 100%;\n  animation: skeleton-loading 1.5s infinite ease-in-out;\n  border-radius: 4px;\n  margin-bottom: 0.5rem;\n}\n\n@keyframes skeleton-loading {\n  0% {\n    background-position: 200% 0;\n  }\n  100% {\n    background-position: -200% 0;\n  }\n}\n\n.skeleton-card {\n  opacity: 0.7;\n}\n\n.skeleton-form {\n  opacity: 0.7;\n}\n\n.skeleton-form .skeleton-form-title {\n  margin-bottom: 1rem;\n}\n\n.skeleton-form .skeleton-label {\n  margin-bottom: 0.5rem;\n}\n\n.skeleton-form .skeleton-input {\n  margin-bottom: 1rem;\n}\n\n.skeleton-list {\n  opacity: 0.7;\n}\n\n.skeleton-list .skeleton-list-title {\n  margin-bottom: 1rem;\n}\n\n.skeleton-list-item {\n  margin-bottom: 1rem;\n  padding: 0.5rem 0;\n}\n\n.skeleton-list-item .skeleton:last-child {\n  margin-bottom: 0;\n}\n\n/* Loading states */\n.loading-overlay {\n  position: relative;\n}\n\n.loading-overlay::after {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(255, 255, 255, 0.7);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 8px;\n  z-index: 10;\n}\n\n.loading-spinner {\n  width: 24px;\n  height: 24px;\n  border: 2px solid #f3f3f3;\n  border-top: 2px solid #007bff;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* Error Boundary Styles */\n.error-boundary, .api-error-fallback {\n  background: white;\n  border: 1px solid #f5c6cb;\n  border-radius: 8px;\n  padding: 2rem;\n  margin: 1rem;\n  text-align: center;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n}\n\n.error-boundary h2, .api-error-fallback h3 {\n  color: #721c24;\n  margin-bottom: 1rem;\n}\n\n.error-boundary p, .api-error-fallback p {\n  color: #721c24;\n  margin-bottom: 1rem;\n}\n\n.api-error-fallback .error-icon {\n  font-size: 3rem;\n  margin-bottom: 1rem;\n}\n\n.api-error-fallback .error-message {\n  font-weight: 600;\n  font-size: 1.1rem;\n}\n\n.api-error-fallback .error-suggestion {\n  color: #6c757d;\n  font-size: 0.9rem;\n}\n\n.error-actions {\n  display: flex;\n  gap: 1rem;\n  justify-content: center;\n  margin-top: 1.5rem;\n}\n\n.error-details {\n  margin-top: 1.5rem;\n  text-align: left;\n  background: #f8f9fa;\n  border: 1px solid #dee2e6;\n  border-radius: 4px;\n  padding: 1rem;\n}\n\n.error-details summary {\n  cursor: pointer;\n  font-weight: 600;\n  margin-bottom: 0.5rem;\n}\n\n.error-details pre {\n  background: #ffffff;\n  border: 1px solid #e9ecef;\n  border-radius: 4px;\n  padding: 0.5rem;\n  overflow-x: auto;\n  font-size: 0.8rem;\n  margin: 0.5rem 0;\n}\n\n/* Template Form Styles */\n.template-form {\n  background: white;\n  border-radius: 8px;\n  padding: 3rem;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  border: 1px solid #e0e0e0;\n  margin-top: 1rem;\n}\n\n.template-sentence {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 3rem;\n  font-weight: 300;\n  line-height: 1.5;\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 0.6rem;\n  justify-content: center;\n  margin-bottom: 2rem;\n  letter-spacing: 0.02em;\n}\n\n.template-prefix {\n  color: #D2691E; /* Orange color to match template */\n  font-weight: 400;\n}\n\n.template-suffix {\n  color: #D2691E; /* Orange color to match template */\n  font-weight: 400;\n  font-size: 3.2rem;\n}\n\n.template-relationship {\n  color: #333;\n  font-weight: normal;\n}\n\n.template-input {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 3rem;\n  font-weight: 300;\n  border: none !important;\n  border-bottom: 2px solid #333 !important;\n  border-radius: 0 !important;\n  padding: 0.1rem 0.2rem !important;\n  text-align: center;\n  min-width: 100px;\n  background: transparent !important;\n  outline: none !important;\n  box-shadow: none !important;\n  -webkit-box-shadow: none !important;\n  -moz-box-shadow: none !important;\n  appearance: none !important;\n}\n\n.template-input:focus {\n  outline: none;\n  border-bottom-color: #007bff;\n  box-shadow: none;\n}\n\n.template-count {\n  color: #333;\n  font-weight: 300;\n  width: 120px;\n  border: none !important;\n  border-bottom: 2px solid #333 !important;\n  background: transparent !important;\n  background-color: transparent !important;\n  margin: 0 0.1rem;\n  outline: none !important;\n  box-shadow: none !important;\n  -webkit-box-shadow: none !important;\n  appearance: none !important;\n  padding: 0 0.2rem !important;\n  line-height: 1.2 !important;\n  vertical-align: baseline !important;\n}\n\n.template-measure {\n  color: #333;\n  font-weight: 300;\n  min-width: 150px;\n  border: none !important;\n  border-bottom: 2px solid #333 !important;\n  background: transparent !important;\n  background-color: transparent !important;\n  margin: 0 0.1rem;\n  outline: none !important;\n  box-shadow: none !important;\n  -webkit-box-shadow: none !important;\n  appearance: none !important;\n  padding: 0 0.2rem !important;\n  line-height: 1.2 !important;\n  vertical-align: baseline !important;\n}\n\n.template-calculated-value {\n  color: #333;\n  font-weight: 300;\n  border-bottom: 2px solid #333;\n  min-width: 100px;\n  text-align: center;\n  padding: 0.1rem 0.3rem;\n  background: transparent;\n  display: inline-block;\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 3rem;\n  margin: 0 0.2rem;\n}\n\n.template-dropdown {\n  position: relative;\n  display: inline-block;\n}\n\n/* Seamless entity input styling */\n.template-entity-input {\n  font-size: 3rem !important;\n  font-family: 'Helvetica Neue', 'Arial', sans-serif !important;\n  font-weight: 300 !important;\n  border: none !important;\n  border-bottom: 2px solid #333 !important;\n  border-radius: 0 !important;\n  border-top: none !important;\n  border-left: none !important;\n  border-right: none !important;\n  background: transparent !important;\n  background-color: transparent !important;\n  min-width: 180px;\n  text-align: center;\n  outline: none !important;\n  padding: 0 0.2rem !important;\n  margin: 0 0.1rem !important;\n  box-shadow: none !important;\n  -webkit-box-shadow: none !important;\n  -moz-box-shadow: none !important;\n  appearance: none !important;\n  line-height: 1.2 !important;\n  vertical-align: baseline !important;\n}\n\n/* Alternative: Contenteditable span approach */\n.template-entity-editable {\n  font-size: 3rem;\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-weight: 300;\n  border-bottom: 2px solid #333;\n  background: transparent;\n  min-width: 180px;\n  text-align: center;\n  outline: none;\n  padding: 0 0.2rem;\n  margin: 0 0.1rem;\n  display: inline-block;\n  line-height: 1.2;\n  vertical-align: baseline;\n  cursor: text;\n}\n\n.template-entity-editable:focus {\n  border-bottom-color: #007bff;\n}\n\n.template-entity-editable:empty:before {\n  content: attr(data-placeholder);\n  color: #ccc;\n  font-style: italic;\n}\n\n.template-entity-input.from-entity {\n  color: #008B8B !important; /* Teal for first entity */\n}\n\n.template-entity-input.to-entity {\n  color: #808080 !important; /* Gray for second entity */\n}\n\n/* Make autocomplete dropdown match the input styling */\n.template-entity-input.autocomplete-input {\n  background: transparent !important;\n  box-shadow: none !important;\n  border: none !important;\n  border-bottom: 2px solid #333 !important;\n}\n\n.template-entity-input:focus {\n  border-bottom-color: #333 !important;\n  box-shadow: none !important;\n}\n\n/* Style placeholder text to match */\n.template-entity-input::placeholder {\n  color: #ccc !important;\n  font-weight: 300 !important;\n  font-style: italic !important;\n}\n\n.template-actions {\n  text-align: center;\n  margin-top: 1rem;\n}\n\n/* Additional underline styling */\n.template-input:hover {\n  border-bottom-color: #666;\n}\n\n.template-entity-input:focus {\n  border-bottom-color: #007bff !important;\n}\n\n.template-relationship {\n  color: #333;\n  font-weight: 400;\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n}\n\n/* Template form responsive design */\n@media (max-width: 768px) {\n  .template-sentence {\n    font-size: 2rem;\n    justify-content: flex-start;\n    line-height: 1.4;\n    gap: 0.4rem;\n  }\n  \n  .template-input {\n    font-size: 2rem;\n    min-width: 80px;\n  }\n  \n  .template-count {\n    width: 90px;\n  }\n  \n  .template-measure {\n    min-width: 120px;\n  }\n  \n  .template-entity-input {\n    font-size: 2rem !important;\n    min-width: 140px;\n    font-weight: 300 !important;\n    padding: 0.1rem 0.2rem !important;\n  }\n  \n  .template-calculated-value {\n    font-size: 2rem;\n    font-weight: 300;\n    min-width: 80px;\n    padding: 0.1rem 0.2rem;\n  }\n  \n  .template-count {\n    font-weight: 300;\n    width: 100px;\n  }\n  \n  .template-measure {\n    font-weight: 300;\n    min-width: 120px;\n  }\n  \n  .template-suffix {\n    font-size: 2.2rem;\n  }\n}\n\n/* AutoComplete Styles */\n.autocomplete {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n}\n\n.autocomplete-input {\n  width: 100%;\n  padding: 0.5rem;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  font-size: 1rem;\n}\n\n.autocomplete-input:focus {\n  outline: none;\n  border-color: #007bff;\n  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n/* Additional AutoComplete styling for template */\n.template-dropdown .autocomplete {\n  display: inline-block;\n  position: relative;\n}\n\n.template-dropdown .autocomplete-dropdown {\n  font-size: 1rem;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\n  font-weight: 400;\n  z-index: 1001;\n  min-width: 200px;\n  max-width: 300px;\n  border-radius: 4px;\n  margin-top: 0.5rem;\n}\n\n.autocomplete-dropdown {\n  position: absolute;\n  top: 100%;\n  left: 0;\n  right: 0;\n  background: white;\n  border: 1px solid #ddd;\n  border-top: none;\n  border-radius: 0 0 4px 4px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  max-height: 200px;\n  overflow-y: auto;\n  z-index: 1000;\n}\n\n.autocomplete-option {\n  padding: 0.5rem;\n  cursor: pointer;\n  border-bottom: 1px solid #f0f0f0;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.autocomplete-option:last-child {\n  border-bottom: none;\n}\n\n.autocomplete-option:hover,\n.autocomplete-option.highlighted {\n  background-color: #f8f9fa;\n}\n\n.autocomplete-option.highlighted {\n  background-color: #e3f2fd;\n}\n\n.option-name {\n  font-weight: 500;\n}\n\n.option-id {\n  font-size: 0.8rem;\n  color: #6c757d;\n}\n\n.autocomplete-no-results {\n  padding: 0.5rem;\n  color: #6c757d;\n  font-style: italic;\n  text-align: center;\n}\n\n/* AutoComplete enhanced states */\n.autocomplete-input-wrapper {\n  position: relative;\n  display: flex;\n  align-items: center;\n}\n\n.autocomplete-loading, \n.autocomplete-confirmed {\n  position: absolute;\n  right: 0.5rem;\n  font-size: 0.9rem;\n  pointer-events: none;\n}\n\n.autocomplete-confirmed {\n  color: #28a745;\n  font-weight: bold;\n}\n\n.autocomplete-loading {\n  animation: pulse 1.5s infinite;\n}\n\n@keyframes pulse {\n  0%, 100% { opacity: 1; }\n  50% { opacity: 0.5; }\n}\n\n.autocomplete.loading .autocomplete-input {\n  background-color: #f8f9fa;\n  cursor: wait;\n}\n\n.autocomplete.confirmed .autocomplete-input {\n  border-color: #28a745;\n  background-color: #f8fff8;\n}\n\n.autocomplete.error .autocomplete-input {\n  border-color: #dc3545;\n  background-color: #fff5f5;\n}\n\n.autocomplete-error {\n  color: #dc3545;\n  font-size: 0.875rem;\n  margin-top: 0.25rem;\n  padding-left: 0.5rem;\n}\n\n/* EditableSpan color styling */\n.template-entity-editable.from-entity {\n  color: #008B8B; /* Teal for first entity */\n}\n\n.template-entity-editable.to-entity {\n  color: #808080; /* Gray for second entity */\n}\n\n.template-entity-editable.placeholder {\n  color: #ccc;\n  font-style: italic;\n}\n\n.template-entity-editable:focus {\n  border-bottom-color: #333;\n  outline: none;\n}\n\n/* Remove input number controls */\ninput[type=\"number\"].template-count::-webkit-outer-spin-button,\ninput[type=\"number\"].template-count::-webkit-inner-spin-button {\n  -webkit-appearance: none;\n  margin: 0;\n}\n\ninput[type=\"number\"].template-count {\n  -moz-appearance: textfield;\n}\n\n/* Remove select dropdown styling */\nselect.template-measure {\n  background-image: none;\n  padding-right: 0.5rem;\n}\n\nselect.template-measure::-ms-expand {\n  display: none;\n}\n\n/* Ultra-seamless entity input styling */\n.template-entity-seamless {\n  font-size: 3rem !important;\n  font-family: 'Helvetica Neue', 'Arial', sans-serif !important;\n  font-weight: 300 !important;\n  border: none !important;\n  border-bottom: 2px solid #333 !important;\n  border-top: none !important;\n  border-left: none !important;\n  border-right: none !important;\n  background: transparent !important;\n  background-color: transparent !important;\n  background-image: none !important;\n  min-width: 180px;\n  text-align: center;\n  outline: none !important;\n  padding: 0 0.2rem !important;\n  margin: 0 0.1rem !important;\n  box-shadow: none !important;\n  -webkit-box-shadow: none !important;\n  -moz-box-shadow: none !important;\n  appearance: none !important;\n  line-height: 1.2 !important;\n  vertical-align: baseline !important;\n  border-radius: 0 !important;\n}\n\n.template-entity-seamless.autocomplete-input {\n  font-size: 3rem !important;\n  font-family: 'Helvetica Neue', 'Arial', sans-serif !important;\n  font-weight: 300 !important;\n  border: none !important;\n  border-bottom: 2px solid #333 !important;\n  background: transparent !important;\n  box-shadow: none !important;\n  outline: none !important;\n}\n\n.template-entity-seamless.from-entity {\n  color: #008B8B !important;\n}\n\n.template-entity-seamless.to-entity {\n  color: #808080 !important;\n}\n\n.template-entity-seamless:focus {\n  border: none !important;\n  border-bottom: 2px solid #333 !important;\n  box-shadow: none !important;\n  outline: none !important;\n}\n\n.template-entity-wrapper {\n  display: inline-block;\n  position: relative;\n}\n\n.template-entity-wrapper .autocomplete {\n  display: inline-block;\n  width: auto;\n}\n\n.template-entity-wrapper .autocomplete-dropdown {\n  font-size: 1rem;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;\n  font-weight: 400;\n}\n\n/* Ultra-seamless input styling - complete invisibility except underline */\n.template-input-seamless {\n  font-size: 3rem !important;\n  font-family: 'Helvetica Neue', 'Arial', sans-serif !important;\n  font-weight: 300 !important;\n  \n  /* Completely remove all borders and backgrounds */\n  border: none !important;\n  border-top: none !important;\n  border-left: none !important;\n  border-right: none !important;\n  border-bottom: 2px solid #333 !important;\n  border-radius: 0 !important;\n  \n  background: transparent !important;\n  background-color: transparent !important;\n  background-image: none !important;\n  \n  /* Remove all shadows and outlines */\n  box-shadow: none !important;\n  -webkit-box-shadow: none !important;\n  -moz-box-shadow: none !important;\n  outline: none !important;\n  \n  /* Remove browser default styling */\n  appearance: none !important;\n  \n  /* Positioning and sizing */\n  min-width: 180px;\n  max-width: 200px;\n  text-align: center;\n  padding: 0 0.2rem !important;\n  margin: 0 0.1rem !important;\n  line-height: 1.2 !important;\n  vertical-align: baseline !important;\n}\n\n/* Color variations */\n.template-input-seamless.from-entity {\n  color: #008B8B !important; /* Teal */\n}\n\n.template-input-seamless.to-entity {\n  color: #808080 !important; /* Gray */\n}\n\n/* Focus state - keep the same styling */\n.template-input-seamless:focus {\n  border: none !important;\n  border-bottom: 2px solid #333 !important;\n  background: transparent !important;\n  box-shadow: none !important;\n  outline: none !important;\n}\n\n/* Placeholder styling */\n.template-input-seamless::placeholder {\n  color: #ccc !important;\n  font-style: italic !important;\n  font-weight: 300 !important;\n}\n\n/* Remove any autofill styling */\n.template-input-seamless:-webkit-autofill,\n.template-input-seamless:-webkit-autofill:hover,\n.template-input-seamless:-webkit-autofill:focus {\n  -webkit-box-shadow: 0 0 0 1000px transparent inset !important;\n  -webkit-text-fill-color: inherit !important;\n  background-color: transparent !important;\n  background: transparent !important;\n}\n/*# sourceMappingURL=data:application/json;base64,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 */"]],"\n  ",["BODY",{},"\n    ","\n    ",["DIV",{"id":"root"},["DIV",{"class":"App"},["NAV",{"class":"navigation"},["DIV",{"class":"nav-brand"},["A",{"class":"brand-link","data-testid":"nav-brand-link","href":"/"},["H1",{},"SIMILE"],["P",{},"Entity Comparison System"]]],["DIV",{"class":"nav-links"},["A",{"class":"nav-link active","data-testid":"nav-compare-link","href":"/"},"Compare"],["A",{"class":"nav-link ","data-testid":"nav-entities-link","href":"/entities"},"Entities"],["A",{"class":"nav-link ","data-testid":"nav-connections-link","href":"/connections"},"Connections"]]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},["DIV",{"class":"manager-header"},["H2",{},"Entity Comparison"]],["DIV",{"class":"comparison-content"},["DIV",{"class":"skeleton-form"},["DIV",{"class":"skeleton skeleton-form-title","style":"width: 200px; height: 1.5rem;"}],["DIV",{"class":"form-row"},["DIV",{"class":"form-group"},["DIV",{"class":"skeleton skeleton-label","style":"width: 80px; height: 1rem;"}],["DIV",{"class":"skeleton skeleton-input","style":"width: 100%; height: 2.5rem;"}]],["DIV",{"class":"form-group"},["DIV",{"class":"skeleton skeleton-label","style":"width: 80px; height: 1rem;"}],["DIV",{"class":"skeleton skeleton-input","style":"width: 100%; height: 2.5rem;"}]],["DIV",{"class":"form-group"},["DIV",{"class":"skeleton skeleton-label","style":"width: 80px; height: 1rem;"}],["DIV",{"class":"skeleton skeleton-input","style":"width: 100%; height: 2.5rem;"}]]],["DIV",{"class":"form-actions"},["DIV",{"class":"skeleton ","style":"width: 100px; height: 2.5rem;"}],["DIV",{"class":"skeleton ","style":"width: 80px; height: 2.5rem;"}]]]]]]]],"\n  \n"]],"viewport":{"width":1280,"height":720},"timestamp":38663.144,"wallTime":1751813120499,"collectionTime":5.4000000059604645,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@59","startTime":38664.763,"title":"Wait for load state \"networkidle\"","class":"Page","method":"waitForEventInfo","params":{"info":{"waitId":"70b4bfdf2fdb0bd2973887fcd2d2e4ff","phase":"before","event":""}},"stepId":"pw:api@28","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","beforeSnapshot":"before@call@59"}
{"type":"console","messageType":"log","text":"API Response: GET /entities/ 200 [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]","args":[{"preview":"API Response:","value":"API Response:"},{"preview":"GET","value":"GET"},{"preview":"/entities/","value":"/entities/"},{"preview":"200","value":200},{"preview":"[Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]"}],"location":{"url":"http://localhost:3000/static/js/bundle.js","lineNumber":54575,"columnNumber":10},"time":38665.25,"pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635"}
{"type":"console","messageType":"log","text":"API Response: GET /units/ 200 [Object, Object, Object, Object, Object]","args":[{"preview":"API Response:","value":"API Response:"},{"preview":"GET","value":"GET"},{"preview":"/units/","value":"/units/"},{"preview":"200","value":200},{"preview":"[Object, Object, Object, Object, Object]"}],"location":{"url":"http://localhost:3000/static/js/bundle.js","lineNumber":54575,"columnNumber":10},"time":38665.465,"pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635"}
{"type":"console","messageType":"log","text":"API Response: GET /entities/ 200 [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]","args":[{"preview":"API Response:","value":"API Response:"},{"preview":"GET","value":"GET"},{"preview":"/entities/","value":"/entities/"},{"preview":"200","value":200},{"preview":"[Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]"}],"location":{"url":"http://localhost:3000/static/js/bundle.js","lineNumber":54575,"columnNumber":10},"time":38665.581,"pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635"}
{"type":"console","messageType":"log","text":"API Response: GET /units/ 200 [Object, Object, Object, Object, Object]","args":[{"preview":"API Response:","value":"API Response:"},{"preview":"GET","value":"GET"},{"preview":"/units/","value":"/units/"},{"preview":"200","value":200},{"preview":"[Object, Object, Object, Object, Object]"}],"location":{"url":"http://localhost:3000/static/js/bundle.js","lineNumber":54575,"columnNumber":10},"time":38665.677,"pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635"}
{"type":"frame-snapshot","snapshot":{"callId":"call@59","snapshotName":"before@call@59","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[1,23]],[[1,24]],["BODY",{},[[1,25]],[[1,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[1,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[1,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},["H3",{},"Compare Entities"],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},["SPAN",{"class":"template-prefix"},"Did you know that"],["INPUT",{"__playwright_value_":"1","type":"number","class":"template-input template-count","min":"1","value":"1"}],["INPUT",{"__playwright_value_":"","type":"text","placeholder":"entity","class":"template-input-seamless from-entity","list":"from-entities","data-testid":"from-entity-input","value":""}],["DATALIST",{"id":"from-entities"},["OPTION",{"__playwright_selected_":"false","value":"A"}],["OPTION",{"__playwright_selected_":"false","value":"B"}],["OPTION",{"__playwright_selected_":"false","value":"Ball XFHZB"}],["OPTION",{"__playwright_selected_":"false","value":"Build XFHZC"}],["OPTION",{"__playwright_selected_":"false","value":"C"}],["OPTION",{"__playwright_selected_":"false","value":"Car XFHZD"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W D AAB D"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W D AAB Z"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W D AAC R"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W D AAC Z"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W D AAD L"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W D AAD V"}],["OPTION",{"__playwright_selected_":"false","value":"Eleph XFHZE"}],["OPTION",{"__playwright_selected_":"false","value":"Human XFHZA"}],["OPTION",{"__playwright_selected_":"false","value":"Mouse XFHZF"}]],["SPAN",{"class":"template-relationship"},"is as"],["SELECT",{"class":"template-input template-measure"},["OPTION",{"__playwright_selected_":"true","value":""},"measure"],["OPTION",{"__playwright_selected_":"false","value":"5"},"big"],["OPTION",{"__playwright_selected_":"false","value":"1"},"tall"],["OPTION",{"__playwright_selected_":"false","value":"2"},"heavy"],["OPTION",{"__playwright_selected_":"false","value":"4"},"long"],["OPTION",{"__playwright_selected_":"false","value":"3"},"voluminous"]],["SPAN",{"class":"template-relationship"},"as"],["SPAN",{"class":"template-calculated-value","data-testid":"comparison-result"},"?"],["INPUT",{"__playwright_value_":"","type":"text","placeholder":"entity","class":"template-input-seamless to-entity","list":"to-entities","data-testid":"to-entity-input","value":""}],["DATALIST",{"id":"to-entities"},["OPTION",{"__playwright_selected_":"false","value":"A"}],["OPTION",{"__playwright_selected_":"false","value":"B"}],["OPTION",{"__playwright_selected_":"false","value":"Ball XFHZB"}],["OPTION",{"__playwright_selected_":"false","value":"Build XFHZC"}],["OPTION",{"__playwright_selected_":"false","value":"C"}],["OPTION",{"__playwright_selected_":"false","value":"Car XFHZD"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W D AAB D"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W D AAB Z"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W D AAC R"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W D AAC Z"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W D AAD L"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W D AAD V"}],["OPTION",{"__playwright_selected_":"false","value":"Eleph XFHZE"}],["OPTION",{"__playwright_selected_":"false","value":"Human XFHZA"}],["OPTION",{"__playwright_selected_":"false","value":"Mouse XFHZF"}]],["SPAN",{"class":"template-suffix"},"?"]],["DIV",{"class":"template-actions"},["BUTTON",{"type":"button","class":"btn-secondary"},"Clear"]]]]]]]]],[[1,64]]]],"viewport":{"width":1280,"height":720},"timestamp":38670.976,"wallTime":1751813120507,"collectionTime":3.2999999970197678,"resourceOverrides":[],"isMainFrame":true}}
{"type":"screencast-frame","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":38682.885,"frameSwapWallTime":1751813120518.5881}
{"type":"screencast-frame","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":38687.089,"frameSwapWallTime":1751813120522.713}
{"type":"log","callId":"call@59","time":39172.898,"message":"  \"networkidle\" event fired"}
{"type":"after","callId":"call@59","endTime":39172.932,"afterSnapshot":"after@call@59"}
{"type":"before","callId":"call@63","startTime":39173.632,"title":"Wait for load state \"networkidle\"","class":"Page","method":"waitForEventInfo","params":{"info":{"waitId":"e011d2e52d8fdcae4ade00d079648410","phase":"before","event":""}},"stepId":"pw:api@29","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","beforeSnapshot":"before@call@63"}
{"type":"log","callId":"call@63","time":39174.11,"message":"  not waiting, \"networkidle\" event already fired"}
{"type":"after","callId":"call@63","endTime":39174.127,"afterSnapshot":"after@call@63"}
{"type":"before","callId":"call@67","startTime":39174.624,"class":"Frame","method":"waitForSelector","params":{"selector":"nav","timeout":10000},"stepId":"pw:api@30","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","beforeSnapshot":"before@call@67"}
{"type":"frame-snapshot","snapshot":{"callId":"call@59","snapshotName":"after@call@59","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,72]],"viewport":{"width":1280,"height":720},"timestamp":39175.177,"wallTime":1751813121011,"collectionTime":0.5,"resourceOverrides":[],"isMainFrame":true}}
{"type":"frame-snapshot","snapshot":{"callId":"call@63","snapshotName":"before@call@63","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[2,72]],"viewport":{"width":1280,"height":720},"timestamp":39175.226,"wallTime":1751813121011,"collectionTime":0.29999999701976776,"resourceOverrides":[],"isMainFrame":true}}
{"type":"frame-snapshot","snapshot":{"callId":"call@63","snapshotName":"after@call@63","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[3,72]],"viewport":{"width":1280,"height":720},"timestamp":39175.258,"wallTime":1751813121011,"collectionTime":0.29999999701976776,"resourceOverrides":[],"isMainFrame":true}}
{"type":"frame-snapshot","snapshot":{"callId":"call@67","snapshotName":"before@call@67","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[4,72]],"viewport":{"width":1280,"height":720},"timestamp":39175.43,"wallTime":1751813121012,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@67","time":39175.885,"message":"waiting for locator('nav') to be visible"}
{"type":"log","callId":"call@67","time":39184.014,"message":"  locator resolved to visible <nav class=\"navigation\">…</nav>"}
{"type":"after","callId":"call@67","endTime":39185.674,"result":{"element":"<ElementHandle>"},"afterSnapshot":"after@call@67"}
{"type":"frame-snapshot","snapshot":{"callId":"call@67","snapshotName":"after@call@67","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[5,72]],"viewport":{"width":1280,"height":720},"timestamp":39188.456,"wallTime":1751813121025,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@69","startTime":39189.886,"class":"Frame","method":"waitForSelector","params":{"selector":"[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"] >> nth=0","strict":true,"timeout":10000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@31","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","beforeSnapshot":"before@call@69"}
{"type":"frame-snapshot","snapshot":{"callId":"call@69","snapshotName":"before@call@69","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[6,72]],"viewport":{"width":1280,"height":720},"timestamp":39190.134,"wallTime":1751813121027,"collectionTime":0,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@69","time":39190.308,"message":"waiting for locator('[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"]').first() to be visible"}
{"type":"log","callId":"call@69","time":39191.191,"message":"  locator resolved to visible <input value=\"\" type=\"text\" placeholder=\"entity\" list=\"from-entities\" data-testid=\"from-entity-input\" class=\"template-input-seamless from-entity\"/>"}
{"type":"after","callId":"call@69","endTime":39191.203,"result":{},"afterSnapshot":"after@call@69"}
{"type":"frame-snapshot","snapshot":{"callId":"call@69","snapshotName":"after@call@69","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[7,72]],"viewport":{"width":1280,"height":720},"timestamp":39191.483,"wallTime":1751813121028,"collectionTime":0.10000000894069672,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@71","startTime":39192.267,"class":"Frame","method":"click","params":{"selector":"[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"] >> nth=0","strict":true,"timeout":8000},"stepId":"pw:api@32","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","beforeSnapshot":"before@call@71"}
{"type":"frame-snapshot","snapshot":{"callId":"call@71","snapshotName":"before@call@71","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[8,72]],"viewport":{"width":1280,"height":720},"timestamp":39192.513,"wallTime":1751813121029,"collectionTime":0.10000000894069672,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@71","time":39192.818,"message":"waiting for locator('[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"]').first()"}
{"type":"log","callId":"call@71","time":39193.695,"message":"  locator resolved to <input value=\"\" type=\"text\" placeholder=\"entity\" list=\"from-entities\" data-testid=\"from-entity-input\" class=\"template-input-seamless from-entity\"/>"}
{"type":"log","callId":"call@71","time":39194.053,"message":"attempting click action"}
{"type":"log","callId":"call@71","time":39194.19,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@71","time":39207.091,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@71","time":39207.096,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@71","time":39207.302,"message":"  done scrolling"}
{"type":"input","callId":"call@71","point":{"x":853.98,"y":358.18},"inputSnapshot":"input@call@71"}
{"type":"frame-snapshot","snapshot":{"callId":"call@71","snapshotName":"input@call@71","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[10,23]],[[10,24]],["BODY",{},[[10,25]],[[10,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[10,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[10,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[9,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[9,3]],[[9,4]],["INPUT",{"__playwright_value_":"","__playwright_target__":"call@71","type":"text","placeholder":"entity","class":"template-input-seamless from-entity","list":"from-entities","data-testid":"from-entity-input","value":""}],[[9,21]],[[9,23]],[[9,36]],[[9,38]],[[9,40]],[[9,41]],[[9,57]],[[9,59]]],[[9,63]]]]]]]]],[[10,64]]]],"viewport":{"width":1280,"height":720},"timestamp":39208.007,"wallTime":1751813121045,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@71","time":39208.766,"message":"  performing click action"}
{"type":"log","callId":"call@71","time":39218.1,"message":"  click action done"}
{"type":"log","callId":"call@71","time":39218.105,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@71","time":39218.207,"message":"  navigations have finished"}
{"type":"after","callId":"call@71","endTime":39218.247,"point":{"x":853.98,"y":358.18},"afterSnapshot":"after@call@71"}
{"type":"frame-snapshot","snapshot":{"callId":"call@71","snapshotName":"after@call@71","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,10]],"viewport":{"width":1280,"height":720},"timestamp":39218.512,"wallTime":1751813121055,"collectionTime":0,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@73","startTime":39219.204,"class":"Frame","method":"fill","params":{"selector":"[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"] >> nth=0","strict":true,"value":"","timeout":8000},"stepId":"pw:api@33","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","beforeSnapshot":"before@call@73"}
{"type":"frame-snapshot","snapshot":{"callId":"call@73","snapshotName":"before@call@73","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[2,10]],"viewport":{"width":1280,"height":720},"timestamp":39219.444,"wallTime":1751813121056,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@73","time":39219.64,"message":"waiting for locator('[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"]').first()"}
{"type":"log","callId":"call@73","time":39220.386,"message":"  locator resolved to <input value=\"\" type=\"text\" placeholder=\"entity\" list=\"from-entities\" data-testid=\"from-entity-input\" class=\"template-input-seamless from-entity\"/>"}
{"type":"log","callId":"call@73","time":39220.605,"message":"  fill(\"\")"}
{"type":"log","callId":"call@73","time":39220.607,"message":"attempting fill action"}
{"type":"input","callId":"call@73","inputSnapshot":"input@call@73"}
{"type":"frame-snapshot","snapshot":{"callId":"call@73","snapshotName":"input@call@73","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[13,23]],[[13,24]],["BODY",{},[[13,25]],[[13,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[13,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[13,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[12,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[12,3]],[[12,4]],["INPUT",{"__playwright_value_":"","__playwright_target__":"call@73","type":"text","placeholder":"entity","class":"template-input-seamless from-entity","list":"from-entities","data-testid":"from-entity-input","value":""}],[[12,21]],[[12,23]],[[12,36]],[[12,38]],[[12,40]],[[12,41]],[[12,57]],[[12,59]]],[[12,63]]]]]]]]],[[13,64]]]],"viewport":{"width":1280,"height":720},"timestamp":39220.893,"wallTime":1751813121057,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@73","time":39220.942,"message":"  waiting for element to be visible, enabled and editable"}
{"type":"after","callId":"call@73","endTime":39221.948,"afterSnapshot":"after@call@73"}
{"type":"frame-snapshot","snapshot":{"callId":"call@73","snapshotName":"after@call@73","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,10]],"viewport":{"width":1280,"height":720},"timestamp":39222.164,"wallTime":1751813121059,"collectionTime":0.10000000894069672,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@75","startTime":39222.739,"class":"Frame","method":"waitForSelector","params":{"selector":"[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"] >> nth=0","strict":true,"timeout":8000,"state":"attached","omitReturnValue":true},"stepId":"pw:api@34","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","beforeSnapshot":"before@call@75"}
{"type":"frame-snapshot","snapshot":{"callId":"call@75","snapshotName":"before@call@75","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[2,10]],"viewport":{"width":1280,"height":720},"timestamp":39222.943,"wallTime":1751813121060,"collectionTime":0.10000000894069672,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@75","time":39223.059,"message":"waiting for locator('[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"]').first()"}
{"type":"log","callId":"call@75","time":39239.112,"message":"  locator resolved to visible <input value=\"\" type=\"text\" placeholder=\"entity\" list=\"from-entities\" data-testid=\"from-entity-input\" class=\"template-input-seamless from-entity\"/>"}
{"type":"after","callId":"call@75","endTime":39239.13,"result":{},"afterSnapshot":"after@call@75"}
{"type":"frame-snapshot","snapshot":{"callId":"call@75","snapshotName":"after@call@75","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[3,10]],"viewport":{"width":1280,"height":720},"timestamp":39239.469,"wallTime":1751813121076,"collectionTime":0.10000000894069672,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@77","startTime":39240.102,"class":"Frame","method":"fill","params":{"selector":"[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"] >> nth=0","strict":true,"value":"Human","timeout":8000},"stepId":"pw:api@35","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","beforeSnapshot":"before@call@77"}
{"type":"frame-snapshot","snapshot":{"callId":"call@77","snapshotName":"before@call@77","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[4,10]],"viewport":{"width":1280,"height":720},"timestamp":39240.385,"wallTime":1751813121077,"collectionTime":0.19999998807907104,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@77","time":39240.57,"message":"waiting for locator('[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"]').first()"}
{"type":"log","callId":"call@77","time":39241.295,"message":"  locator resolved to <input value=\"\" type=\"text\" placeholder=\"entity\" list=\"from-entities\" data-testid=\"from-entity-input\" class=\"template-input-seamless from-entity\"/>"}
{"type":"log","callId":"call@77","time":39241.584,"message":"  fill(\"Human\")"}
{"type":"log","callId":"call@77","time":39241.588,"message":"attempting fill action"}
{"type":"input","callId":"call@77","inputSnapshot":"input@call@77"}
{"type":"frame-snapshot","snapshot":{"callId":"call@77","snapshotName":"input@call@77","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[18,23]],[[18,24]],["BODY",{},[[18,25]],[[18,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[18,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[18,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[17,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[17,3]],[[17,4]],["INPUT",{"__playwright_value_":"","__playwright_target__":"call@77","type":"text","placeholder":"entity","class":"template-input-seamless from-entity","list":"from-entities","data-testid":"from-entity-input","value":""}],[[17,21]],[[17,23]],[[17,36]],[[17,38]],[[17,40]],[[17,41]],[[17,57]],[[17,59]]],[[17,63]]]]]]]]],[[18,64]]]],"viewport":{"width":1280,"height":720},"timestamp":39241.913,"wallTime":1751813121078,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@77","time":39241.95,"message":"  waiting for element to be visible, enabled and editable"}
{"type":"after","callId":"call@77","endTime":39243.967,"afterSnapshot":"after@call@77"}
{"type":"frame-snapshot","snapshot":{"callId":"call@77","snapshotName":"after@call@77","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[19,23]],[[19,24]],["BODY",{},[[19,25]],[[19,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[19,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[19,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[18,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[18,3]],[[18,4]],["INPUT",{"__playwright_value_":"Human","__playwright_scroll_left_":"15","__playwright_target__":"call@77","type":"text","placeholder":"entity","class":"template-input-seamless from-entity","list":"from-entities","data-testid":"from-entity-input","value":"Human"}],[[18,21]],[[18,23]],[[18,36]],[[18,38]],[[18,40]],[[18,41]],[[18,57]],[[18,59]]],[[18,63]]]]]]]]],[[19,64]]]],"viewport":{"width":1280,"height":720},"timestamp":39244.326,"wallTime":1751813121081,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@79","startTime":39244.863,"class":"Frame","method":"waitForSelector","params":{"selector":".autocomplete-dropdown","strict":true,"timeout":3000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@36","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","beforeSnapshot":"before@call@79"}
{"type":"frame-snapshot","snapshot":{"callId":"call@79","snapshotName":"before@call@79","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,10]],"viewport":{"width":1280,"height":720},"timestamp":39245.305,"wallTime":1751813121082,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@79","time":39245.449,"message":"waiting for locator('.autocomplete-dropdown') to be visible"}
{"type":"screencast-frame","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":39246.524,"frameSwapWallTime":1751813121081.93}
{"type":"screencast-frame","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":39249.208,"frameSwapWallTime":1751813121084.697}
{"type":"screencast-frame","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":39751.506,"frameSwapWallTime":1751813121585.25}
{"type":"screencast-frame","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":40252.446,"frameSwapWallTime":1751813122086.277}
{"type":"screencast-frame","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":40752.605,"frameSwapWallTime":1751813122586.4102}
{"type":"screencast-frame","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":41254.958,"frameSwapWallTime":1751813123087.923}
{"type":"screencast-frame","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":41754.534,"frameSwapWallTime":1751813123587.568}
{"type":"after","callId":"call@79","endTime":42245.727,"error":{"message":"Timeout 3000ms exceeded.","stack":"TimeoutError: Timeout 3000ms exceeded.\n    at ProgressController.run (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/progress.js:76:26)\n    at Frame.waitForSelector (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/frames.js:615:23)\n    at FrameDispatcher.waitForSelector (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/dispatchers/frameDispatcher.js:82:116)\n    at FrameDispatcher._handleCommand (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/dispatchers/dispatcher.js:88:40)\n    at DispatcherConnection.dispatch (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/dispatchers/dispatcher.js:309:39)","name":"TimeoutError"},"afterSnapshot":"after@call@79"}
{"type":"frame-snapshot","snapshot":{"callId":"call@79","snapshotName":"after@call@79","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[2,10]],"viewport":{"width":1280,"height":720},"timestamp":42248.089,"wallTime":1751813124084,"collectionTime":0.7999999970197678,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@81","startTime":42253.485,"class":"Frame","method":"waitForSelector","params":{"selector":".autocomplete-dropdown >> nth=0","strict":true,"timeout":3000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@37","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","beforeSnapshot":"before@call@81"}
{"type":"frame-snapshot","snapshot":{"callId":"call@81","snapshotName":"before@call@81","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[3,10]],"viewport":{"width":1280,"height":720},"timestamp":42254.451,"wallTime":1751813124091,"collectionTime":0.4000000059604645,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@81","time":42254.908,"message":"waiting for locator('.autocomplete-dropdown').first() to be visible"}
{"type":"screencast-frame","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":42255.486,"frameSwapWallTime":1751813124087.97}
{"type":"screencast-frame","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":42755.602,"frameSwapWallTime":1751813124588.646}
{"type":"screencast-frame","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":43254.665,"frameSwapWallTime":1751813125087.7449}
{"type":"screencast-frame","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":43755.32,"frameSwapWallTime":1751813125588.357}
{"type":"screencast-frame","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":44256.409,"frameSwapWallTime":1751813126089.274}
{"type":"screencast-frame","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":44754.341,"frameSwapWallTime":1751813126587.412}
{"type":"after","callId":"call@81","endTime":45254.626,"error":{"message":"Timeout 3000ms exceeded.","stack":"TimeoutError: Timeout 3000ms exceeded.\n    at ProgressController.run (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/progress.js:76:26)\n    at Frame.waitForSelector (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/frames.js:615:23)\n    at FrameDispatcher.waitForSelector (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/dispatchers/frameDispatcher.js:82:116)\n    at FrameDispatcher._handleCommand (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/dispatchers/dispatcher.js:88:40)\n    at DispatcherConnection.dispatch (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/dispatchers/dispatcher.js:309:39)","name":"TimeoutError"},"afterSnapshot":"after@call@81"}
{"type":"screencast-frame","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":45256.197,"frameSwapWallTime":1751813127088.4768}
{"type":"frame-snapshot","snapshot":{"callId":"call@81","snapshotName":"after@call@81","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[4,10]],"viewport":{"width":1280,"height":720},"timestamp":45256.868,"wallTime":1751813127093,"collectionTime":0.7000000029802322,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@83","startTime":45260.331,"class":"Frame","method":"queryCount","params":{"selector":"datalist option"},"stepId":"pw:api@38","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","beforeSnapshot":"before@call@83"}
{"type":"frame-snapshot","snapshot":{"callId":"call@83","snapshotName":"before@call@83","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[5,10]],"viewport":{"width":1280,"height":720},"timestamp":45261.372,"wallTime":1751813127098,"collectionTime":0.4000000059604645,"resourceOverrides":[],"isMainFrame":true}}
{"type":"after","callId":"call@83","endTime":45263.221,"result":{"value":30},"afterSnapshot":"after@call@83"}
{"type":"frame-snapshot","snapshot":{"callId":"call@83","snapshotName":"after@call@83","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[6,10]],"viewport":{"width":1280,"height":720},"timestamp":45263.904,"wallTime":1751813127100,"collectionTime":0.29999999701976776,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@85","startTime":45265.407,"class":"Frame","method":"queryCount","params":{"selector":"datalist option >> internal:has-text=\"Human XFHZA\"i"},"stepId":"pw:api@39","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","beforeSnapshot":"before@call@85"}
{"type":"frame-snapshot","snapshot":{"callId":"call@85","snapshotName":"before@call@85","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[7,10]],"viewport":{"width":1280,"height":720},"timestamp":45266.1,"wallTime":1751813127103,"collectionTime":0.19999998807907104,"resourceOverrides":[],"isMainFrame":true}}
{"type":"after","callId":"call@85","endTime":45267.764,"result":{"value":0},"afterSnapshot":"after@call@85"}
{"type":"frame-snapshot","snapshot":{"callId":"call@85","snapshotName":"after@call@85","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[8,10]],"viewport":{"width":1280,"height":720},"timestamp":45268.38,"wallTime":1751813127105,"collectionTime":0.29999999701976776,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@87","startTime":45269.331,"class":"Frame","method":"fill","params":{"selector":"[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"] >> nth=0","strict":true,"value":"Human XFHZA","timeout":8000},"stepId":"pw:api@40","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","beforeSnapshot":"before@call@87"}
{"type":"frame-snapshot","snapshot":{"callId":"call@87","snapshotName":"before@call@87","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[9,10]],"viewport":{"width":1280,"height":720},"timestamp":45269.756,"wallTime":1751813127106,"collectionTime":0.10000000894069672,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@87","time":45270.018,"message":"waiting for locator('[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"]').first()"}
{"type":"log","callId":"call@87","time":45271.116,"message":"  locator resolved to <input type=\"text\" value=\"Human\" placeholder=\"entity\" list=\"from-entities\" data-testid=\"from-entity-input\" class=\"template-input-seamless from-entity\"/>"}
{"type":"log","callId":"call@87","time":45271.486,"message":"  fill(\"Human XFHZA\")"}
{"type":"log","callId":"call@87","time":45271.49,"message":"attempting fill action"}
{"type":"input","callId":"call@87","inputSnapshot":"input@call@87"}
{"type":"frame-snapshot","snapshot":{"callId":"call@87","snapshotName":"input@call@87","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[29,23]],[[29,24]],["BODY",{},[[29,25]],[[29,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[29,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[29,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[28,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[28,3]],[[28,4]],["INPUT",{"__playwright_value_":"Human","__playwright_scroll_left_":"15","__playwright_target__":"call@87","type":"text","placeholder":"entity","class":"template-input-seamless from-entity","list":"from-entities","data-testid":"from-entity-input","value":"Human"}],[[28,21]],[[28,23]],[[28,36]],[[28,38]],[[28,40]],[[28,41]],[[28,57]],[[28,59]]],[[28,63]]]]]]]]],[[29,64]]]],"viewport":{"width":1280,"height":720},"timestamp":45271.98,"wallTime":1751813127108,"collectionTime":0.19999998807907104,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@87","time":45272.055,"message":"  waiting for element to be visible, enabled and editable"}
{"type":"after","callId":"call@87","endTime":45274.888,"afterSnapshot":"after@call@87"}
{"type":"frame-snapshot","snapshot":{"callId":"call@87","snapshotName":"after@call@87","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[30,23]],[[30,24]],["BODY",{},[[30,25]],[[30,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[30,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[30,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[29,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[29,3]],[[29,4]],["INPUT",{"__playwright_value_":"Human XFHZA","__playwright_scroll_left_":"166","__playwright_target__":"call@87","type":"text","placeholder":"entity","class":"template-input-seamless from-entity","list":"from-entities","data-testid":"from-entity-input","value":"Human XFHZA"}],[[29,21]],[[29,23]],[[29,36]],[[29,38]],[[29,40]],[[29,41]],["DATALIST",{"id":"to-entities"},[[29,42]],[[29,43]],[[29,44]],[[29,45]],[[29,46]],[[29,47]],[[29,48]],[[29,49]],[[29,50]],[[29,51]],[[29,52]],[[29,53]],[[29,54]],[[29,56]]],[[29,59]]],[[29,63]]]]]]]]],[[30,64]]]],"viewport":{"width":1280,"height":720},"timestamp":45276.092,"wallTime":1751813127113,"collectionTime":0.5,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@89","startTime":45277.035,"class":"Page","method":"keyboardPress","params":{"key":"Tab"},"stepId":"pw:api@41","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","beforeSnapshot":"before@call@89"}
{"type":"frame-snapshot","snapshot":{"callId":"call@89","snapshotName":"before@call@89","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,11]],"viewport":{"width":1280,"height":720},"timestamp":45277.494,"wallTime":1751813127114,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"after","callId":"call@89","endTime":45278.506,"afterSnapshot":"after@call@89"}
{"type":"frame-snapshot","snapshot":{"callId":"call@89","snapshotName":"after@call@89","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[32,23]],[[32,24]],["BODY",{},[[32,25]],[[32,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[32,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[32,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[31,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[31,3]],[[31,4]],["INPUT",{"__playwright_value_":"Human XFHZA","__playwright_target__":"call@87","type":"text","placeholder":"entity","class":"template-input-seamless from-entity","list":"from-entities","data-testid":"from-entity-input","value":"Human XFHZA"}],[[31,21]],[[31,23]],[[31,36]],[[31,38]],[[31,40]],[[31,41]],[[2,1]],[[31,59]]],[[31,63]]]]]]]]],[[32,64]]]],"viewport":{"width":1280,"height":720},"timestamp":45279.252,"wallTime":1751813127116,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@91","startTime":45280.19,"class":"Frame","method":"inputValue","params":{"selector":"[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"] >> nth=0","strict":true,"timeout":8000},"stepId":"pw:api@42","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","beforeSnapshot":"before@call@91"}
{"type":"frame-snapshot","snapshot":{"callId":"call@91","snapshotName":"before@call@91","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,10]],"viewport":{"width":1280,"height":720},"timestamp":45280.549,"wallTime":1751813127117,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@91","time":45281.023,"message":"waiting for locator('[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"]').first()"}
{"type":"screencast-frame","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":45281.388,"frameSwapWallTime":1751813127115.648}
{"type":"log","callId":"call@91","time":45281.846,"message":"  locator resolved to <input type=\"text\" value=\"Human XFHZA\" placeholder=\"entity\" list=\"from-entities\" data-testid=\"from-entity-input\" class=\"template-input-seamless from-entity\"/>"}
{"type":"after","callId":"call@91","endTime":45281.861,"result":{"value":"Human XFHZA"},"afterSnapshot":"after@call@91"}
{"type":"frame-snapshot","snapshot":{"callId":"call@91","snapshotName":"after@call@91","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[34,23]],[[34,24]],["BODY",{},[[34,25]],[[34,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[34,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[34,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[33,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[33,3]],[[33,4]],["INPUT",{"__playwright_value_":"Human XFHZA","__playwright_target__":"call@91","type":"text","placeholder":"entity","class":"template-input-seamless from-entity","list":"from-entities","data-testid":"from-entity-input","value":"Human XFHZA"}],[[33,21]],[[33,23]],[[33,36]],[[33,38]],[[33,40]],[[33,41]],[[4,1]],[[33,59]]],[[33,63]]]]]]]]],[[34,64]]]],"viewport":{"width":1280,"height":720},"timestamp":45282.213,"wallTime":1751813127119,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@93","startTime":45285.85,"class":"Frame","method":"inputValue","params":{"selector":"[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"] >> nth=0","strict":true,"timeout":8000},"stepId":"pw:api@43","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","beforeSnapshot":"before@call@93"}
{"type":"frame-snapshot","snapshot":{"callId":"call@93","snapshotName":"before@call@93","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,10]],"viewport":{"width":1280,"height":720},"timestamp":45286.356,"wallTime":1751813127123,"collectionTime":0.10000000894069672,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@93","time":45286.666,"message":"waiting for locator('[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"]').first()"}
{"type":"log","callId":"call@93","time":45287.378,"message":"  locator resolved to <input type=\"text\" value=\"Human XFHZA\" placeholder=\"entity\" list=\"from-entities\" data-testid=\"from-entity-input\" class=\"template-input-seamless from-entity\"/>"}
{"type":"after","callId":"call@93","endTime":45287.395,"result":{"value":"Human XFHZA"},"afterSnapshot":"after@call@93"}
{"type":"frame-snapshot","snapshot":{"callId":"call@93","snapshotName":"after@call@93","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[36,23]],[[36,24]],["BODY",{},[[36,25]],[[36,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[36,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[36,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[35,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[35,3]],[[35,4]],["INPUT",{"__playwright_value_":"Human XFHZA","__playwright_target__":"call@93","type":"text","placeholder":"entity","class":"template-input-seamless from-entity","list":"from-entities","data-testid":"from-entity-input","value":"Human XFHZA"}],[[35,21]],[[35,23]],[[35,36]],[[35,38]],[[35,40]],[[35,41]],[[6,1]],[[35,59]]],[[35,63]]]]]]]]],[[36,64]]]],"viewport":{"width":1280,"height":720},"timestamp":45287.829,"wallTime":1751813127124,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@95","startTime":45288.689,"class":"Frame","method":"click","params":{"selector":"[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"] >> nth=0","strict":true,"timeout":8000},"stepId":"pw:api@44","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","beforeSnapshot":"before@call@95"}
{"type":"frame-snapshot","snapshot":{"callId":"call@95","snapshotName":"before@call@95","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,10]],"viewport":{"width":1280,"height":720},"timestamp":45289.02,"wallTime":1751813127126,"collectionTime":0.10000000894069672,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@95","time":45289.174,"message":"waiting for locator('[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"]').first()"}
{"type":"log","callId":"call@95","time":45290.043,"message":"  locator resolved to <input value=\"\" type=\"text\" list=\"to-entities\" placeholder=\"entity\" data-testid=\"to-entity-input\" class=\"template-input-seamless to-entity\"/>"}
{"type":"log","callId":"call@95","time":45292.51,"message":"attempting click action"}
{"type":"log","callId":"call@95","time":45292.537,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"screencast-frame","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":45296.457,"frameSwapWallTime":1751813127131.4458}
{"type":"screencast-frame","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":45312.473,"frameSwapWallTime":1751813127147.5671}
{"type":"log","callId":"call@95","time":45323.832,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@95","time":45323.836,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@95","time":45324.036,"message":"  done scrolling"}
{"type":"input","callId":"call@95","point":{"x":834.89,"y":442.37},"inputSnapshot":"input@call@95"}
{"type":"frame-snapshot","snapshot":{"callId":"call@95","snapshotName":"input@call@95","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[38,23]],[[38,24]],["BODY",{},[[38,25]],[[38,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[38,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[38,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[37,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[37,3]],[[37,4]],[[2,0]],[[37,21]],[[37,23]],[[37,36]],[[37,38]],[[37,40]],["INPUT",{"__playwright_value_":"","__playwright_target__":"call@95","type":"text","placeholder":"entity","class":"template-input-seamless to-entity","list":"to-entities","data-testid":"to-entity-input","value":""}],[[8,1]],[[37,59]]],[[37,63]]]]]]]]],[[38,64]]]],"viewport":{"width":1280,"height":720},"timestamp":45324.902,"wallTime":1751813127161,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@95","time":45325.506,"message":"  performing click action"}
{"type":"log","callId":"call@95","time":45326.809,"message":"  click action done"}
{"type":"log","callId":"call@95","time":45326.813,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@95","time":45326.959,"message":"  navigations have finished"}
{"type":"after","callId":"call@95","endTime":45327.007,"point":{"x":834.89,"y":442.37},"afterSnapshot":"after@call@95"}
{"type":"frame-snapshot","snapshot":{"callId":"call@95","snapshotName":"after@call@95","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,10]],"viewport":{"width":1280,"height":720},"timestamp":45327.31,"wallTime":1751813127164,"collectionTime":0.19999998807907104,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@97","startTime":45328.054,"class":"Frame","method":"fill","params":{"selector":"[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"] >> nth=0","strict":true,"value":"","timeout":8000},"stepId":"pw:api@45","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","beforeSnapshot":"before@call@97"}
{"type":"frame-snapshot","snapshot":{"callId":"call@97","snapshotName":"before@call@97","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[2,10]],"viewport":{"width":1280,"height":720},"timestamp":45328.301,"wallTime":1751813127165,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@97","time":45328.444,"message":"waiting for locator('[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"]').first()"}
{"type":"log","callId":"call@97","time":45329.148,"message":"  locator resolved to <input value=\"\" type=\"text\" list=\"to-entities\" placeholder=\"entity\" data-testid=\"to-entity-input\" class=\"template-input-seamless to-entity\"/>"}
{"type":"log","callId":"call@97","time":45329.34,"message":"  fill(\"\")"}
{"type":"log","callId":"call@97","time":45329.343,"message":"attempting fill action"}
{"type":"input","callId":"call@97","inputSnapshot":"input@call@97"}
{"type":"frame-snapshot","snapshot":{"callId":"call@97","snapshotName":"input@call@97","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[41,23]],[[41,24]],["BODY",{},[[41,25]],[[41,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[41,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[41,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[40,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[40,3]],[[40,4]],[[5,0]],[[40,21]],[[40,23]],[[40,36]],[[40,38]],[[40,40]],["INPUT",{"__playwright_value_":"","__playwright_target__":"call@97","type":"text","placeholder":"entity","class":"template-input-seamless to-entity","list":"to-entities","data-testid":"to-entity-input","value":""}],[[11,1]],[[40,59]]],[[40,63]]]]]]]]],[[41,64]]]],"viewport":{"width":1280,"height":720},"timestamp":45329.615,"wallTime":1751813127166,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@97","time":45329.654,"message":"  waiting for element to be visible, enabled and editable"}
{"type":"after","callId":"call@97","endTime":45330.297,"afterSnapshot":"after@call@97"}
{"type":"frame-snapshot","snapshot":{"callId":"call@97","snapshotName":"after@call@97","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,10]],"viewport":{"width":1280,"height":720},"timestamp":45330.529,"wallTime":1751813127167,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@99","startTime":45331.15,"class":"Frame","method":"waitForSelector","params":{"selector":"[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"] >> nth=0","strict":true,"timeout":8000,"state":"attached","omitReturnValue":true},"stepId":"pw:api@46","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","beforeSnapshot":"before@call@99"}
{"type":"frame-snapshot","snapshot":{"callId":"call@99","snapshotName":"before@call@99","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[2,10]],"viewport":{"width":1280,"height":720},"timestamp":45331.391,"wallTime":1751813127168,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@99","time":45331.515,"message":"waiting for locator('[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"]').first()"}
{"type":"log","callId":"call@99","time":45332.13,"message":"  locator resolved to visible <input value=\"\" type=\"text\" list=\"to-entities\" placeholder=\"entity\" data-testid=\"to-entity-input\" class=\"template-input-seamless to-entity\"/>"}
{"type":"after","callId":"call@99","endTime":45332.139,"result":{},"afterSnapshot":"after@call@99"}
{"type":"frame-snapshot","snapshot":{"callId":"call@99","snapshotName":"after@call@99","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[3,10]],"viewport":{"width":1280,"height":720},"timestamp":45332.373,"wallTime":1751813127169,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@101","startTime":45332.883,"class":"Frame","method":"fill","params":{"selector":"[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"] >> nth=0","strict":true,"value":"Build","timeout":8000},"stepId":"pw:api@47","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","beforeSnapshot":"before@call@101"}
{"type":"frame-snapshot","snapshot":{"callId":"call@101","snapshotName":"before@call@101","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[4,10]],"viewport":{"width":1280,"height":720},"timestamp":45333.098,"wallTime":1751813127170,"collectionTime":0.10000000894069672,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@101","time":45333.2,"message":"waiting for locator('[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"]').first()"}
{"type":"log","callId":"call@101","time":45333.721,"message":"  locator resolved to <input value=\"\" type=\"text\" list=\"to-entities\" placeholder=\"entity\" data-testid=\"to-entity-input\" class=\"template-input-seamless to-entity\"/>"}
{"type":"log","callId":"call@101","time":45333.889,"message":"  fill(\"Build\")"}
{"type":"log","callId":"call@101","time":45333.891,"message":"attempting fill action"}
{"type":"input","callId":"call@101","inputSnapshot":"input@call@101"}
{"type":"frame-snapshot","snapshot":{"callId":"call@101","snapshotName":"input@call@101","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[46,23]],[[46,24]],["BODY",{},[[46,25]],[[46,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[46,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[46,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[45,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[45,3]],[[45,4]],[[10,0]],[[45,21]],[[45,23]],[[45,36]],[[45,38]],[[45,40]],["INPUT",{"__playwright_value_":"","__playwright_target__":"call@101","type":"text","placeholder":"entity","class":"template-input-seamless to-entity","list":"to-entities","data-testid":"to-entity-input","value":""}],[[16,1]],[[45,59]]],[[45,63]]]]]]]]],[[46,64]]]],"viewport":{"width":1280,"height":720},"timestamp":45334.132,"wallTime":1751813127171,"collectionTime":0,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@101","time":45334.165,"message":"  waiting for element to be visible, enabled and editable"}
{"type":"after","callId":"call@101","endTime":45335.428,"afterSnapshot":"after@call@101"}
{"type":"frame-snapshot","snapshot":{"callId":"call@101","snapshotName":"after@call@101","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[47,23]],[[47,24]],["BODY",{},[[47,25]],[[47,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[47,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[47,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[46,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[46,3]],[[46,4]],[[11,0]],[[46,21]],[[46,23]],[[46,36]],[[46,38]],[[46,40]],["INPUT",{"__playwright_value_":"Build","__playwright_target__":"call@101","type":"text","placeholder":"entity","class":"template-input-seamless to-entity","list":"to-entities","data-testid":"to-entity-input","value":"Build"}],[[17,1]],[[46,59]]],[[46,63]]]]]]]]],[[47,64]]]],"viewport":{"width":1280,"height":720},"timestamp":45335.688,"wallTime":1751813127172,"collectionTime":0.10000000894069672,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@103","startTime":45336.164,"class":"Frame","method":"waitForSelector","params":{"selector":".autocomplete-dropdown","strict":true,"timeout":3000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@48","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","beforeSnapshot":"before@call@103"}
{"type":"frame-snapshot","snapshot":{"callId":"call@103","snapshotName":"before@call@103","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,10]],"viewport":{"width":1280,"height":720},"timestamp":45336.435,"wallTime":1751813127173,"collectionTime":0,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@103","time":45336.553,"message":"waiting for locator('.autocomplete-dropdown') to be visible"}
{"type":"screencast-frame","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":45346.761,"frameSwapWallTime":1751813127181.968}
{"type":"screencast-frame","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":45850.91,"frameSwapWallTime":1751813127683.702}
{"type":"screencast-frame","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":46351.407,"frameSwapWallTime":1751813128184.456}
{"type":"screencast-frame","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":46850.473,"frameSwapWallTime":1751813128683.389}
{"type":"screencast-frame","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":47352.232,"frameSwapWallTime":1751813129185.366}
{"type":"screencast-frame","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":47850.658,"frameSwapWallTime":1751813129684.074}
{"type":"after","callId":"call@103","endTime":48337.019,"error":{"message":"Timeout 3000ms exceeded.","stack":"TimeoutError: Timeout 3000ms exceeded.\n    at ProgressController.run (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/progress.js:76:26)\n    at Frame.waitForSelector (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/frames.js:615:23)\n    at FrameDispatcher.waitForSelector (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/dispatchers/frameDispatcher.js:82:116)\n    at FrameDispatcher._handleCommand (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/dispatchers/dispatcher.js:88:40)\n    at DispatcherConnection.dispatch (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/dispatchers/dispatcher.js:309:39)","name":"TimeoutError"},"afterSnapshot":"after@call@103"}
{"type":"frame-snapshot","snapshot":{"callId":"call@103","snapshotName":"after@call@103","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[2,10]],"viewport":{"width":1280,"height":720},"timestamp":48339.413,"wallTime":1751813130176,"collectionTime":0.7000000029802322,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@105","startTime":48342.571,"class":"Frame","method":"waitForSelector","params":{"selector":".autocomplete-dropdown >> nth=0","strict":true,"timeout":3000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@49","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","beforeSnapshot":"before@call@105"}
{"type":"frame-snapshot","snapshot":{"callId":"call@105","snapshotName":"before@call@105","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[3,10]],"viewport":{"width":1280,"height":720},"timestamp":48343.499,"wallTime":1751813130180,"collectionTime":0.29999999701976776,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@105","time":48343.869,"message":"waiting for locator('.autocomplete-dropdown').first() to be visible"}
{"type":"screencast-frame","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":48349.751,"frameSwapWallTime":1751813130183.497}
{"type":"screencast-frame","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":48851.386,"frameSwapWallTime":1751813130684.324}
{"type":"screencast-frame","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":49351.423,"frameSwapWallTime":1751813131184.311}
{"type":"screencast-frame","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":49846.625,"frameSwapWallTime":1751813131681.9102}
{"type":"screencast-frame","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":50349.991,"frameSwapWallTime":1751813132183.062}
{"type":"screencast-frame","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":50848.033,"frameSwapWallTime":1751813132682.062}
{"type":"after","callId":"call@105","endTime":51343.583,"error":{"message":"Timeout 3000ms exceeded.","stack":"TimeoutError: Timeout 3000ms exceeded.\n    at ProgressController.run (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/progress.js:76:26)\n    at Frame.waitForSelector (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/frames.js:615:23)\n    at FrameDispatcher.waitForSelector (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/dispatchers/frameDispatcher.js:82:116)\n    at FrameDispatcher._handleCommand (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/dispatchers/dispatcher.js:88:40)\n    at DispatcherConnection.dispatch (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/dispatchers/dispatcher.js:309:39)","name":"TimeoutError"},"afterSnapshot":"after@call@105"}
{"type":"frame-snapshot","snapshot":{"callId":"call@105","snapshotName":"after@call@105","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[4,10]],"viewport":{"width":1280,"height":720},"timestamp":51344.593,"wallTime":1751813133181,"collectionTime":0.29999999701976776,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@107","startTime":51346.251,"class":"Frame","method":"queryCount","params":{"selector":"datalist option"},"stepId":"pw:api@50","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","beforeSnapshot":"before@call@107"}
{"type":"frame-snapshot","snapshot":{"callId":"call@107","snapshotName":"before@call@107","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[5,10]],"viewport":{"width":1280,"height":720},"timestamp":51346.874,"wallTime":1751813133183,"collectionTime":0.29999999701976776,"resourceOverrides":[],"isMainFrame":true}}
{"type":"after","callId":"call@107","endTime":51347.791,"result":{"value":29},"afterSnapshot":"after@call@107"}
{"type":"screencast-frame","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":51348.406,"frameSwapWallTime":1751813133182.531}
{"type":"frame-snapshot","snapshot":{"callId":"call@107","snapshotName":"after@call@107","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[6,10]],"viewport":{"width":1280,"height":720},"timestamp":51348.56,"wallTime":1751813133185,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@109","startTime":51349.866,"class":"Frame","method":"queryCount","params":{"selector":"datalist option >> internal:has-text=\"Build XFHZC\"i"},"stepId":"pw:api@51","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","beforeSnapshot":"before@call@109"}
{"type":"frame-snapshot","snapshot":{"callId":"call@109","snapshotName":"before@call@109","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[7,10]],"viewport":{"width":1280,"height":720},"timestamp":51350.244,"wallTime":1751813133187,"collectionTime":0.10000000894069672,"resourceOverrides":[],"isMainFrame":true}}
{"type":"after","callId":"call@109","endTime":51351.058,"result":{"value":0},"afterSnapshot":"after@call@109"}
{"type":"frame-snapshot","snapshot":{"callId":"call@109","snapshotName":"after@call@109","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[8,10]],"viewport":{"width":1280,"height":720},"timestamp":51351.75,"wallTime":1751813133188,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@111","startTime":51352.318,"class":"Frame","method":"fill","params":{"selector":"[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"] >> nth=0","strict":true,"value":"Build XFHZC","timeout":8000},"stepId":"pw:api@52","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","beforeSnapshot":"before@call@111"}
{"type":"frame-snapshot","snapshot":{"callId":"call@111","snapshotName":"before@call@111","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[9,10]],"viewport":{"width":1280,"height":720},"timestamp":51352.878,"wallTime":1751813133189,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@111","time":51353.268,"message":"waiting for locator('[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"]').first()"}
{"type":"log","callId":"call@111","time":51354.516,"message":"  locator resolved to <input type=\"text\" value=\"Build\" list=\"to-entities\" placeholder=\"entity\" data-testid=\"to-entity-input\" class=\"template-input-seamless to-entity\"/>"}
{"type":"log","callId":"call@111","time":51354.838,"message":"  fill(\"Build XFHZC\")"}
{"type":"log","callId":"call@111","time":51354.85,"message":"attempting fill action"}
{"type":"input","callId":"call@111","inputSnapshot":"input@call@111"}
{"type":"frame-snapshot","snapshot":{"callId":"call@111","snapshotName":"input@call@111","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[57,23]],[[57,24]],["BODY",{},[[57,25]],[[57,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[57,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[57,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[56,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[56,3]],[[56,4]],[[21,0]],[[56,21]],[[56,23]],[[56,36]],[[56,38]],[[56,40]],["INPUT",{"__playwright_value_":"Build","__playwright_target__":"call@111","type":"text","placeholder":"entity","class":"template-input-seamless to-entity","list":"to-entities","data-testid":"to-entity-input","value":"Build"}],[[27,1]],[[56,59]]],[[56,63]]]]]]]]],[[57,64]]]],"viewport":{"width":1280,"height":720},"timestamp":51355.154,"wallTime":1751813133192,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@111","time":51355.198,"message":"  waiting for element to be visible, enabled and editable"}
{"type":"after","callId":"call@111","endTime":51358.788,"afterSnapshot":"after@call@111"}
{"type":"frame-snapshot","snapshot":{"callId":"call@111","snapshotName":"after@call@111","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[58,23]],[[58,24]],["BODY",{},[[58,25]],[[58,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[58,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[58,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[57,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[57,3]],[[57,4]],[[22,0]],[[57,21]],[[57,23]],[[57,36]],[[57,38]],[[57,40]],["INPUT",{"__playwright_value_":"Build XFHZC","__playwright_scroll_left_":"125","__playwright_target__":"call@111","type":"text","placeholder":"entity","class":"template-input-seamless to-entity","list":"to-entities","data-testid":"to-entity-input","value":"Build XFHZC"}],[[28,1]],[[57,59]]],[[57,63]]]]]]]]],[[58,64]]]],"viewport":{"width":1280,"height":720},"timestamp":51359.374,"wallTime":1751813133196,"collectionTime":0.19999998807907104,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@113","startTime":51360.276,"class":"Page","method":"keyboardPress","params":{"key":"Tab"},"stepId":"pw:api@53","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","beforeSnapshot":"before@call@113"}
{"type":"frame-snapshot","snapshot":{"callId":"call@113","snapshotName":"before@call@113","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,10]],"viewport":{"width":1280,"height":720},"timestamp":51360.69,"wallTime":1751813133197,"collectionTime":0.10000000894069672,"resourceOverrides":[],"isMainFrame":true}}
{"type":"after","callId":"call@113","endTime":51361.738,"afterSnapshot":"after@call@113"}
{"type":"frame-snapshot","snapshot":{"callId":"call@113","snapshotName":"after@call@113","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[60,23]],[[60,24]],["BODY",{},[[60,25]],[[60,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[60,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[60,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[59,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[59,3]],[[59,4]],[[24,0]],[[59,21]],[[59,23]],[[59,36]],[[59,38]],[[59,40]],["INPUT",{"__playwright_value_":"Build XFHZC","__playwright_target__":"call@111","type":"text","placeholder":"entity","class":"template-input-seamless to-entity","list":"to-entities","data-testid":"to-entity-input","value":"Build XFHZC"}],[[30,1]],[[59,59]]],[[59,63]]]]]]]]],[[60,64]]]],"viewport":{"width":1280,"height":720},"timestamp":51362.264,"wallTime":1751813133199,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@115","startTime":51363.023,"class":"Frame","method":"inputValue","params":{"selector":"[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"] >> nth=0","strict":true,"timeout":8000},"stepId":"pw:api@54","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","beforeSnapshot":"before@call@115"}
{"type":"frame-snapshot","snapshot":{"callId":"call@115","snapshotName":"before@call@115","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,10]],"viewport":{"width":1280,"height":720},"timestamp":51363.278,"wallTime":1751813133200,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@115","time":51363.457,"message":"waiting for locator('[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"]').first()"}
{"type":"log","callId":"call@115","time":51364.034,"message":"  locator resolved to <input type=\"text\" list=\"to-entities\" value=\"Build XFHZC\" placeholder=\"entity\" data-testid=\"to-entity-input\" class=\"template-input-seamless to-entity\"/>"}
{"type":"after","callId":"call@115","endTime":51364.05,"result":{"value":"Build XFHZC"},"afterSnapshot":"after@call@115"}
{"type":"frame-snapshot","snapshot":{"callId":"call@115","snapshotName":"after@call@115","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[62,23]],[[62,24]],["BODY",{},[[62,25]],[[62,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[62,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[62,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[61,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[61,3]],[[61,4]],[[26,0]],[[61,21]],[[61,23]],[[61,36]],[[61,38]],[[61,40]],["INPUT",{"__playwright_value_":"Build XFHZC","__playwright_target__":"call@115","type":"text","placeholder":"entity","class":"template-input-seamless to-entity","list":"to-entities","data-testid":"to-entity-input","value":"Build XFHZC"}],[[32,1]],[[61,59]]],[[61,63]]]]]]]]],[[62,64]]]],"viewport":{"width":1280,"height":720},"timestamp":51364.307,"wallTime":1751813133201,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@117","startTime":51364.747,"class":"Frame","method":"inputValue","params":{"selector":"[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"] >> nth=0","strict":true,"timeout":8000},"stepId":"pw:api@55","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","beforeSnapshot":"before@call@117"}
{"type":"screencast-frame","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":51364.95,"frameSwapWallTime":1751813133200.554}
{"type":"frame-snapshot","snapshot":{"callId":"call@117","snapshotName":"before@call@117","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,10]],"viewport":{"width":1280,"height":720},"timestamp":51365.195,"wallTime":1751813133202,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@117","time":51365.507,"message":"waiting for locator('[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"]').first()"}
{"type":"log","callId":"call@117","time":51365.924,"message":"  locator resolved to <input type=\"text\" list=\"to-entities\" value=\"Build XFHZC\" placeholder=\"entity\" data-testid=\"to-entity-input\" class=\"template-input-seamless to-entity\"/>"}
{"type":"after","callId":"call@117","endTime":51365.935,"result":{"value":"Build XFHZC"},"afterSnapshot":"after@call@117"}
{"type":"frame-snapshot","snapshot":{"callId":"call@117","snapshotName":"after@call@117","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[64,23]],[[64,24]],["BODY",{},[[64,25]],[[64,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[64,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[64,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[63,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[63,3]],[[63,4]],[[28,0]],[[63,21]],[[63,23]],[[63,36]],[[63,38]],[[63,40]],["INPUT",{"__playwright_value_":"Build XFHZC","__playwright_target__":"call@117","type":"text","placeholder":"entity","class":"template-input-seamless to-entity","list":"to-entities","data-testid":"to-entity-input","value":"Build XFHZC"}],[[34,1]],[[63,59]]],[[63,63]]]]]]]]],[[64,64]]]],"viewport":{"width":1280,"height":720},"timestamp":51366.188,"wallTime":1751813133203,"collectionTime":0.10000000894069672,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@119","startTime":51366.828,"class":"Frame","method":"selectOption","params":{"selector":".template-measure, select.template-input","strict":true,"options":[{"label":"tall"}],"timeout":8000},"stepId":"pw:api@56","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","beforeSnapshot":"before@call@119"}
{"type":"frame-snapshot","snapshot":{"callId":"call@119","snapshotName":"before@call@119","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,10]],"viewport":{"width":1280,"height":720},"timestamp":51367.038,"wallTime":1751813133204,"collectionTime":0,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@119","time":51367.239,"message":"waiting for locator('.template-measure, select.template-input')"}
{"type":"log","callId":"call@119","time":51367.761,"message":"  locator resolved to <select class=\"template-input template-measure\">…</select>"}
{"type":"log","callId":"call@119","time":51367.997,"message":"attempting select option action"}
{"type":"input","callId":"call@119","inputSnapshot":"input@call@119"}
{"type":"frame-snapshot","snapshot":{"callId":"call@119","snapshotName":"input@call@119","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[66,23]],[[66,24]],["BODY",{},[[66,25]],[[66,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[66,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[66,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[65,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[65,3]],[[65,4]],[[30,0]],[[65,21]],[[65,23]],["SELECT",{"__playwright_target__":"call@119","class":"template-input template-measure"},[[65,25]],[[65,27]],[[65,29]],[[65,31]],[[65,33]],[[65,35]]],[[65,38]],[[65,40]],[[2,0]],[[36,1]],[[65,59]]],[[65,63]]]]]]]]],[[66,64]]]],"viewport":{"width":1280,"height":720},"timestamp":51368.277,"wallTime":1751813133205,"collectionTime":0,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@119","time":51368.316,"message":"  waiting for element to be visible and enabled"}
{"type":"log","callId":"call@119","time":51369.332,"message":"  selected specified option(s)"}
{"type":"after","callId":"call@119","endTime":51369.367,"result":{"values":["1"]},"afterSnapshot":"after@call@119"}
{"type":"frame-snapshot","snapshot":{"callId":"call@119","snapshotName":"after@call@119","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[67,23]],[[67,24]],["BODY",{},[[67,25]],[[67,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[67,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[67,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[66,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[66,3]],[[66,4]],[[31,0]],[[66,21]],[[66,23]],["SELECT",{"__playwright_target__":"call@119","class":"template-input template-measure"},["OPTION",{"__playwright_selected_":"false","value":""},[[66,24]]],[[66,27]],["OPTION",{"__playwright_selected_":"true","value":"1"},[[66,28]]],[[66,31]],[[66,33]],[[66,35]]],[[66,38]],[[66,40]],[[3,0]],[[37,1]],[[66,59]]],[[66,63]]]]]]]]],[[67,64]]]],"viewport":{"width":1280,"height":720},"timestamp":51369.793,"wallTime":1751813133206,"collectionTime":0.29999999701976776,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@121","startTime":51370.364,"class":"Frame","method":"waitForSelector","params":{"selector":".template-measure, select.template-input","strict":true,"timeout":8000,"state":"attached","omitReturnValue":true},"stepId":"pw:api@57","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","beforeSnapshot":"before@call@121"}
{"type":"frame-snapshot","snapshot":{"callId":"call@121","snapshotName":"before@call@121","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,12]],"viewport":{"width":1280,"height":720},"timestamp":51370.673,"wallTime":1751813133207,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@121","time":51370.801,"message":"waiting for locator('.template-measure, select.template-input')"}
{"type":"log","callId":"call@121","time":51371.403,"message":"  locator resolved to visible <select class=\"template-input template-measure\">…</select>"}
{"type":"after","callId":"call@121","endTime":51371.412,"result":{},"afterSnapshot":"after@call@121"}
{"type":"frame-snapshot","snapshot":{"callId":"call@121","snapshotName":"after@call@121","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[2,12]],"viewport":{"width":1280,"height":720},"timestamp":51371.587,"wallTime":1751813133208,"collectionTime":0.10000000894069672,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@123","startTime":51372.507,"class":"Page","method":"waitForEventInfo","params":{"info":{"waitId":"e4dfefe46bd253b33de7b1cf060d8ea0","phase":"before","event":"response"}},"stepId":"pw:api@58","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","beforeSnapshot":"before@call@123"}
{"type":"before","callId":"call@126","startTime":51372.694,"class":"Frame","method":"waitForFunction","params":{"expression":"() => {\n      const calculatedValue = document.querySelector('[data-testid=\"comparison-result\"], .template-calculated-value');\n      return calculatedValue && calculatedValue.textContent !== '?' && calculatedValue.textContent !== '...';\n    }","isFunction":true,"arg":{"value":{"o":[{"k":"timeout","v":{"n":10000}}],"id":1},"handles":[]},"timeout":8000},"stepId":"pw:api@59","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","beforeSnapshot":"before@call@126"}
{"type":"frame-snapshot","snapshot":{"callId":"call@123","snapshotName":"before@call@123","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[3,12]],"viewport":{"width":1280,"height":720},"timestamp":51372.811,"wallTime":1751813133209,"collectionTime":0,"resourceOverrides":[],"isMainFrame":true}}
{"type":"frame-snapshot","snapshot":{"callId":"call@126","snapshotName":"before@call@126","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[4,12]],"viewport":{"width":1280,"height":720},"timestamp":51372.85,"wallTime":1751813133210,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"screencast-frame","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":51378.849,"frameSwapWallTime":1751813133214.515}
{"type":"console","messageType":"log","text":"API Request: GET /compare/ undefined","args":[{"preview":"API Request:","value":"API Request:"},{"preview":"GET","value":"GET"},{"preview":"/compare/","value":"/compare/"},{"preview":"undefined"}],"location":{"url":"http://localhost:3000/static/js/bundle.js","lineNumber":54565,"columnNumber":10},"time":51471.256,"pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635"}
{"type":"screencast-frame","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":51479.277,"frameSwapWallTime":1751813133314.971}
{"type":"console","messageType":"log","text":"API Response: GET /compare/ 200 {from_entity: Object, to_entity: Object, unit: Object, multiplier: 500.00, path: Array(2)}","args":[{"preview":"API Response:","value":"API Response:"},{"preview":"GET","value":"GET"},{"preview":"/compare/","value":"/compare/"},{"preview":"200","value":200},{"preview":"{from_entity: Object, to_entity: Object, unit: Object, multiplier: 500.00, path: Array(2)}"}],"location":{"url":"http://localhost:3000/static/js/bundle.js","lineNumber":54575,"columnNumber":10},"time":51485.884,"pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635"}
{"type":"after","callId":"call@126","endTime":51492.329,"result":{"handle":"<JSHandle>"},"afterSnapshot":"after@call@126"}
{"type":"frame-snapshot","snapshot":{"callId":"call@126","snapshotName":"after@call@126","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[72,23]],[[72,24]],["BODY",{},[[72,25]],[[72,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[72,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[72,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[71,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[71,3]],[[71,4]],[[36,0]],[[71,21]],[[71,23]],[[5,2]],[[71,38]],["SPAN",{"class":"template-calculated-value","data-testid":"comparison-result"},"500.0"],[[8,0]],[[42,1]],[[71,59]]],[[71,63]]]],["DIV",{"class":"result-section"},["DIV",{"class":"comparison-result"},["DIV",{"class":"main-result"},["DIV",{"class":"result-statement-template"},["SPAN",{"class":"template-prefix"},"Did you know that"],["SPAN",{"class":"template-from-count"},"1"],["SPAN",{"class":"template-from-entity"},"Human XFHZA"],["SPAN",{"class":"template-relationship"},"is as tall as"],["SPAN",{"class":"template-multiplier"},"500"],["SPAN",{"class":"template-to-entity"},"Build XFHZC"],["SPAN",{"class":"template-suffix"},"?"]]],["DIV",{"class":"calculation-path"},["H4",{},"Calculation Path:"],["DIV",{"class":"path-steps"},["DIV",{"class":"path-step"},["DIV",{"class":"step-number"},"1"],["DIV",{"class":"step-content"},["SPAN",{"class":"entity-name"},"Human XFHZA"],["SPAN",{"class":"relationship-text"},"is"],["SPAN",{"class":"multiplier"},"10","x"],["SPAN",{"class":"entity-name"},"Ball XFHZB"]],["DIV",{"class":"step-arrow"},"↓"]],["DIV",{"class":"path-step"},["DIV",{"class":"step-number"},"2"],["DIV",{"class":"step-content"},["SPAN",{"class":"entity-name"},"Ball XFHZB"],["SPAN",{"class":"relationship-text"},"is"],["SPAN",{"class":"multiplier"},"50","x"],["SPAN",{"class":"entity-name"},"Build XFHZC"]]]],["DIV",{"class":"calculation-formula"},["H5",{},"Final Calculation:"],["P",{},"10 × 50"," = ","500"]]],["DIV",{"class":"result-details"},["DIV",{"class":"detail-item"},["STRONG",{},"From:"]," ","Human XFHZA"," (ID: ","477",")"],["DIV",{"class":"detail-item"},["STRONG",{},"To:"]," ","Build XFHZC"," (ID: ","481",")"],["DIV",{"class":"detail-item"},["STRONG",{},"Unit:"]," ","Length"," (","m",")"],["DIV",{"class":"detail-item"},["STRONG",{},"Path Length:"]," ","2"," hop","s"]]]]]]]]],[[72,64]]]],"viewport":{"width":1280,"height":720},"timestamp":51493.095,"wallTime":1751813133330,"collectionTime":0.29999999701976776,"resourceOverrides":[],"isMainFrame":true}}
{"type":"screencast-frame","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":51498.674,"frameSwapWallTime":1751813133334.362}
{"type":"after","callId":"call@123","endTime":66376.766,"error":{"name":"Error","message":"Timeout 15000ms exceeded while waiting for event \"response\""},"afterSnapshot":"after@call@123"}
{"type":"before","callId":"call@130","startTime":66377.29,"class":"Frame","method":"waitForSelector","params":{"selector":".template-sentence","strict":true,"timeout":10000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@60","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","beforeSnapshot":"before@call@130"}
{"type":"before","callId":"call@132","startTime":66377.772,"class":"Frame","method":"waitForSelector","params":{"selector":".error-message, .error, [role=\"alert\"], .alert-error, .comparison-error","strict":true,"timeout":10000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@61","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","beforeSnapshot":"before@call@132"}
{"type":"before","callId":"call@134","startTime":66378.165,"class":"Frame","method":"waitForSelector","params":{"selector":":text(\"No connection found\"), :text(\"No path found\")","strict":true,"timeout":10000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@62","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","beforeSnapshot":"before@call@134"}
{"type":"frame-snapshot","snapshot":{"callId":"call@123","snapshotName":"after@call@123","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,100]],"viewport":{"width":1280,"height":720},"timestamp":66378.545,"wallTime":1751813148215,"collectionTime":0.6000000089406967,"resourceOverrides":[],"isMainFrame":true}}
{"type":"frame-snapshot","snapshot":{"callId":"call@130","snapshotName":"before@call@130","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[2,100]],"viewport":{"width":1280,"height":720},"timestamp":66378.737,"wallTime":1751813148215,"collectionTime":0.5,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@130","time":66379.004,"message":"waiting for locator('.template-sentence') to be visible"}
{"type":"frame-snapshot","snapshot":{"callId":"call@132","snapshotName":"before@call@132","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[3,100]],"viewport":{"width":1280,"height":720},"timestamp":66379.507,"wallTime":1751813148216,"collectionTime":0.5,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@132","time":66379.801,"message":"waiting for locator('.error-message, .error, [role=\"alert\"], .alert-error, .comparison-error') to be visible"}
{"type":"frame-snapshot","snapshot":{"callId":"call@134","snapshotName":"before@call@134","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[4,100]],"viewport":{"width":1280,"height":720},"timestamp":66380.46,"wallTime":1751813148216,"collectionTime":0.3999999910593033,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@134","time":66380.749,"message":"waiting for locator(':text(\"No connection found\"), :text(\"No path found\")') to be visible"}
{"type":"log","callId":"call@130","time":66382.995,"message":"  locator resolved to visible <div class=\"template-sentence\">…</div>"}
{"type":"after","callId":"call@130","endTime":66383.01,"result":{},"afterSnapshot":"after@call@130"}
{"type":"frame-snapshot","snapshot":{"callId":"call@130","snapshotName":"after@call@130","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[5,100]],"viewport":{"width":1280,"height":720},"timestamp":66383.63,"wallTime":1751813148220,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@136","startTime":66385.76,"title":"Expect \"toBeVisible\"","class":"Frame","method":"expect","params":{"selector":".template-sentence","expression":"to.be.visible","expectedValue":{"value":{"v":"undefined"},"handles":[]},"isNot":false,"timeout":5000},"stepId":"expect@63","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","beforeSnapshot":"before@call@136"}
{"type":"frame-snapshot","snapshot":{"callId":"call@136","snapshotName":"before@call@136","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[6,100]],"viewport":{"width":1280,"height":720},"timestamp":66386.101,"wallTime":1751813148223,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@136","time":66386.359,"message":"Expect \"toBeVisible\" with timeout 5000ms"}
{"type":"log","callId":"call@136","time":66386.362,"message":"waiting for locator('.template-sentence')"}
{"type":"log","callId":"call@136","time":66387.042,"message":"  locator resolved to <div class=\"template-sentence\">…</div>"}
{"type":"after","callId":"call@136","endTime":66387.1,"result":{"matches":true,"received":{"s":"visible"}},"afterSnapshot":"after@call@136"}
{"type":"frame-snapshot","snapshot":{"callId":"call@136","snapshotName":"after@call@136","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[79,23]],[[79,24]],["BODY",{},[[79,25]],[[79,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[79,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[79,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[78,1]],["DIV",{"class":"template-form"},["DIV",{"__playwright_target__":"call@136","class":"template-sentence"},[[78,3]],[[78,4]],[[43,0]],[[78,21]],[[78,23]],[[12,2]],[[78,38]],[[7,1]],[[15,0]],[[49,1]],[[78,59]]],[[78,63]]]],[[7,93]]]]]]],[[79,64]]]],"viewport":{"width":1280,"height":720},"timestamp":66387.426,"wallTime":1751813148224,"collectionTime":0.19999998807907104,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@138","startTime":66387.942,"class":"Frame","method":"isVisible","params":{"selector":".template-sentence","strict":true},"stepId":"pw:api@64","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","beforeSnapshot":"before@call@138"}
{"type":"frame-snapshot","snapshot":{"callId":"call@138","snapshotName":"before@call@138","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,9]],"viewport":{"width":1280,"height":720},"timestamp":66388.18,"wallTime":1751813148225,"collectionTime":0.10000000894069672,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@138","time":66388.365,"message":"  checking visibility of locator('.template-sentence')"}
{"type":"after","callId":"call@138","endTime":66388.672,"result":{"value":true},"afterSnapshot":"after@call@138"}
{"type":"frame-snapshot","snapshot":{"callId":"call@138","snapshotName":"after@call@138","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[2,9]],"viewport":{"width":1280,"height":720},"timestamp":66388.959,"wallTime":1751813148226,"collectionTime":0.10000000894069672,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@140","startTime":66389.388,"class":"Frame","method":"textContent","params":{"selector":".template-sentence","strict":true,"timeout":8000},"stepId":"pw:api@65","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","beforeSnapshot":"before@call@140"}
{"type":"frame-snapshot","snapshot":{"callId":"call@140","snapshotName":"before@call@140","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[3,9]],"viewport":{"width":1280,"height":720},"timestamp":66389.618,"wallTime":1751813148226,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@140","time":66389.726,"message":"waiting for locator('.template-sentence')"}
{"type":"log","callId":"call@140","time":66393.522,"message":"  locator resolved to <div class=\"template-sentence\">…</div>"}
{"type":"after","callId":"call@140","endTime":66393.561,"result":{"value":"Did you know thatis asmeasurebigtallheavylongvoluminousas500.0?"},"afterSnapshot":"after@call@140"}
{"type":"frame-snapshot","snapshot":{"callId":"call@140","snapshotName":"after@call@140","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[83,23]],[[83,24]],["BODY",{},[[83,25]],[[83,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[83,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[83,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[82,1]],["DIV",{"class":"template-form"},["DIV",{"__playwright_target__":"call@140","class":"template-sentence"},[[82,3]],[[82,4]],[[47,0]],[[82,21]],[[82,23]],[[16,2]],[[82,38]],[[11,1]],[[19,0]],[[53,1]],[[82,59]]],[[82,63]]]],[[11,93]]]]]]],[[83,64]]]],"viewport":{"width":1280,"height":720},"timestamp":66394.069,"wallTime":1751813148231,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@142","startTime":66396.798,"class":"Page","method":"screenshot","params":{"timeout":5000,"type":"png","caret":"initial"},"stepId":"pw:api@69","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","beforeSnapshot":"before@call@142"}
{"type":"frame-snapshot","snapshot":{"callId":"call@142","snapshotName":"before@call@142","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,9]],"viewport":{"width":1280,"height":720},"timestamp":66397.187,"wallTime":1751813148234,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@142","time":66397.451,"message":"taking page screenshot"}
{"type":"log","callId":"call@142","time":66398.726,"message":"waiting for fonts to load..."}
{"type":"log","callId":"call@142","time":66399.082,"message":"fonts loaded"}
{"type":"screencast-frame","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":66412.547,"frameSwapWallTime":1751813148248.169}
{"type":"after","callId":"call@142","endTime":66440.608,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@142"}
{"type":"frame-snapshot","snapshot":{"callId":"call@142","snapshotName":"after@call@142","pageId":"page@c3d5d6ea5248cd88f77b29fa874b1635","frameId":"frame@01a436083cf0c5f6ed5170fb8f6c7540","frameUrl":"http://localhost:3000/","doctype":"html","html":[[2,9]],"viewport":{"width":1280,"height":720},"timestamp":66441.032,"wallTime":1751813148278,"collectionTime":0.10000000894069672,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@144","startTime":66448.588,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities/477","method":"DELETE","timeout":8000},"stepId":"pw:api@71","beforeSnapshot":"before@call@144"}
{"type":"log","callId":"call@144","time":66448.986,"message":"→ DELETE http://localhost:8000/api/v1/entities/477"}
{"type":"log","callId":"call@144","time":66448.99,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@144","time":66448.991,"message":"  accept: */*"}
{"type":"log","callId":"call@144","time":66448.992,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@144","time":66448.993,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@144","time":66448.994,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@144","time":66448.994,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@144","time":66453.535,"message":"← 404 Not Found"}
{"type":"log","callId":"call@144","time":66453.538,"message":"  date: Sun, 06 Jul 2025 14:45:47 GMT"}
{"type":"log","callId":"call@144","time":66453.539,"message":"  server: uvicorn"}
{"type":"log","callId":"call@144","time":66453.54,"message":"  content-length: 29"}
{"type":"log","callId":"call@144","time":66453.541,"message":"  content-type: application/json"}
{"type":"after","callId":"call@144","endTime":66453.577,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/477","status":404,"statusText":"Not Found","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:47 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"29"},{"name":"content-type","value":"application/json"}],"fetchUid":"2052c48b001bca504b563bf70397eef3"}},"afterSnapshot":"after@call@144"}
{"type":"before","callId":"call@146","startTime":66478.866,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities/478","method":"DELETE","timeout":8000},"stepId":"pw:api@72","beforeSnapshot":"before@call@146"}
{"type":"log","callId":"call@146","time":66479.215,"message":"→ DELETE http://localhost:8000/api/v1/entities/478"}
{"type":"log","callId":"call@146","time":66479.219,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@146","time":66479.22,"message":"  accept: */*"}
{"type":"log","callId":"call@146","time":66479.221,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@146","time":66479.222,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@146","time":66479.223,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@146","time":66479.223,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@146","time":66483.113,"message":"← 404 Not Found"}
{"type":"log","callId":"call@146","time":66483.117,"message":"  date: Sun, 06 Jul 2025 14:45:47 GMT"}
{"type":"log","callId":"call@146","time":66483.119,"message":"  server: uvicorn"}
{"type":"log","callId":"call@146","time":66483.119,"message":"  content-length: 29"}
{"type":"log","callId":"call@146","time":66483.12,"message":"  content-type: application/json"}
{"type":"after","callId":"call@146","endTime":66483.179,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/478","status":404,"statusText":"Not Found","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:47 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"29"},{"name":"content-type","value":"application/json"}],"fetchUid":"410a2d8571fb4c5c98377814ffc354c1"}},"afterSnapshot":"after@call@146"}
{"type":"before","callId":"call@148","startTime":66508.525,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities/479","method":"DELETE","timeout":8000},"stepId":"pw:api@73","beforeSnapshot":"before@call@148"}
{"type":"log","callId":"call@148","time":66508.911,"message":"→ DELETE http://localhost:8000/api/v1/entities/479"}
{"type":"log","callId":"call@148","time":66508.916,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@148","time":66508.917,"message":"  accept: */*"}
{"type":"log","callId":"call@148","time":66508.918,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@148","time":66508.919,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@148","time":66508.92,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@148","time":66508.92,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@148","time":66511.325,"message":"← 404 Not Found"}
{"type":"log","callId":"call@148","time":66511.33,"message":"  date: Sun, 06 Jul 2025 14:45:47 GMT"}
{"type":"log","callId":"call@148","time":66511.347,"message":"  server: uvicorn"}
{"type":"log","callId":"call@148","time":66511.348,"message":"  content-length: 29"}
{"type":"log","callId":"call@148","time":66511.349,"message":"  content-type: application/json"}
{"type":"after","callId":"call@148","endTime":66511.387,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/479","status":404,"statusText":"Not Found","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:47 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"29"},{"name":"content-type","value":"application/json"}],"fetchUid":"068b7f9e6559780b26cd97937ac4934d"}},"afterSnapshot":"after@call@148"}
{"type":"before","callId":"call@150","startTime":66536.916,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities/482","method":"DELETE","timeout":8000},"stepId":"pw:api@74","beforeSnapshot":"before@call@150"}
{"type":"log","callId":"call@150","time":66537.248,"message":"→ DELETE http://localhost:8000/api/v1/entities/482"}
{"type":"log","callId":"call@150","time":66537.251,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@150","time":66537.252,"message":"  accept: */*"}
{"type":"log","callId":"call@150","time":66537.253,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@150","time":66537.254,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@150","time":66537.255,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@150","time":66537.256,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@150","time":66539.253,"message":"← 404 Not Found"}
{"type":"log","callId":"call@150","time":66539.257,"message":"  date: Sun, 06 Jul 2025 14:45:47 GMT"}
{"type":"log","callId":"call@150","time":66539.258,"message":"  server: uvicorn"}
{"type":"log","callId":"call@150","time":66539.259,"message":"  content-length: 29"}
{"type":"log","callId":"call@150","time":66539.259,"message":"  content-type: application/json"}
{"type":"after","callId":"call@150","endTime":66539.292,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/482","status":404,"statusText":"Not Found","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:47 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"29"},{"name":"content-type","value":"application/json"}],"fetchUid":"93870f5989c671ed110a00298369931d"}},"afterSnapshot":"after@call@150"}
{"type":"before","callId":"call@152","startTime":66564.64,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities/480","method":"DELETE","timeout":8000},"stepId":"pw:api@75","beforeSnapshot":"before@call@152"}
{"type":"log","callId":"call@152","time":66564.933,"message":"→ DELETE http://localhost:8000/api/v1/entities/480"}
{"type":"log","callId":"call@152","time":66564.936,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@152","time":66564.937,"message":"  accept: */*"}
{"type":"log","callId":"call@152","time":66564.938,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@152","time":66564.939,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@152","time":66564.94,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@152","time":66564.94,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@152","time":66568.376,"message":"← 404 Not Found"}
{"type":"log","callId":"call@152","time":66568.38,"message":"  date: Sun, 06 Jul 2025 14:45:47 GMT"}
{"type":"log","callId":"call@152","time":66568.381,"message":"  server: uvicorn"}
{"type":"log","callId":"call@152","time":66568.382,"message":"  content-length: 29"}
{"type":"log","callId":"call@152","time":66568.383,"message":"  content-type: application/json"}
{"type":"after","callId":"call@152","endTime":66568.418,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/480","status":404,"statusText":"Not Found","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:47 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"29"},{"name":"content-type","value":"application/json"}],"fetchUid":"d7d7c6280ca8c105ddc1ffd42f36e194"}},"afterSnapshot":"after@call@152"}
{"type":"before","callId":"call@154","startTime":66593.481,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities/481","method":"DELETE","timeout":8000},"stepId":"pw:api@76","beforeSnapshot":"before@call@154"}
{"type":"log","callId":"call@154","time":66593.854,"message":"→ DELETE http://localhost:8000/api/v1/entities/481"}
{"type":"log","callId":"call@154","time":66593.859,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@154","time":66593.86,"message":"  accept: */*"}
{"type":"log","callId":"call@154","time":66593.861,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@154","time":66593.862,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@154","time":66593.863,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@154","time":66593.864,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@154","time":66597.924,"message":"← 404 Not Found"}
{"type":"log","callId":"call@154","time":66597.927,"message":"  date: Sun, 06 Jul 2025 14:45:48 GMT"}
{"type":"log","callId":"call@154","time":66597.928,"message":"  server: uvicorn"}
{"type":"log","callId":"call@154","time":66597.929,"message":"  content-length: 29"}
{"type":"log","callId":"call@154","time":66597.929,"message":"  content-type: application/json"}
{"type":"after","callId":"call@154","endTime":66597.961,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/481","status":404,"statusText":"Not Found","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:48 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"29"},{"name":"content-type","value":"application/json"}],"fetchUid":"094f4f0cee33c6d2419122d524e150c8"}},"afterSnapshot":"after@call@154"}
{"type":"before","callId":"call@156","startTime":66598.431,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities","method":"GET","timeout":8000},"stepId":"pw:api@77","beforeSnapshot":"before@call@156"}
{"type":"log","callId":"call@156","time":66598.681,"message":"→ GET http://localhost:8000/api/v1/entities"}
{"type":"log","callId":"call@156","time":66598.684,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@156","time":66598.686,"message":"  accept: */*"}
{"type":"log","callId":"call@156","time":66598.686,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@156","time":66598.687,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@156","time":66598.688,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@156","time":66598.689,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@156","time":66599.662,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@156","time":66599.664,"message":"  date: Sun, 06 Jul 2025 14:45:48 GMT"}
{"type":"log","callId":"call@156","time":66599.665,"message":"  server: uvicorn"}
{"type":"log","callId":"call@156","time":66599.666,"message":"  content-length: 0"}
{"type":"log","callId":"call@156","time":66599.666,"message":"  location: http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@156","time":66599.779,"message":"→ GET http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@156","time":66599.781,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@156","time":66599.782,"message":"  accept: */*"}
{"type":"log","callId":"call@156","time":66599.783,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@156","time":66599.784,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@156","time":66599.785,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@156","time":66599.786,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@156","time":66604.185,"message":"← 200 OK"}
{"type":"log","callId":"call@156","time":66604.188,"message":"  date: Sun, 06 Jul 2025 14:45:48 GMT"}
{"type":"log","callId":"call@156","time":66604.189,"message":"  server: uvicorn"}
{"type":"log","callId":"call@156","time":66604.189,"message":"  content-length: 1732"}
{"type":"log","callId":"call@156","time":66604.19,"message":"  content-type: application/json"}
{"type":"after","callId":"call@156","endTime":66604.227,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/","status":200,"statusText":"OK","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:48 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"1732"},{"name":"content-type","value":"application/json"}],"fetchUid":"96413c83290230b506e2a0e450677360"}},"afterSnapshot":"after@call@156"}
{"type":"before","callId":"call@159","startTime":66604.939,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities","method":"GET","timeout":8000},"stepId":"pw:api@78","beforeSnapshot":"before@call@159"}
{"type":"log","callId":"call@159","time":66605.244,"message":"→ GET http://localhost:8000/api/v1/entities"}
{"type":"log","callId":"call@159","time":66605.252,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@159","time":66605.254,"message":"  accept: */*"}
{"type":"log","callId":"call@159","time":66605.254,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@159","time":66605.255,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@159","time":66605.256,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@159","time":66605.257,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@159","time":66605.955,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@159","time":66605.958,"message":"  date: Sun, 06 Jul 2025 14:45:48 GMT"}
{"type":"log","callId":"call@159","time":66605.959,"message":"  server: uvicorn"}
{"type":"log","callId":"call@159","time":66605.96,"message":"  content-length: 0"}
{"type":"log","callId":"call@159","time":66605.961,"message":"  location: http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@159","time":66606.088,"message":"→ GET http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@159","time":66606.091,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@159","time":66606.092,"message":"  accept: */*"}
{"type":"log","callId":"call@159","time":66606.092,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@159","time":66606.093,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@159","time":66606.094,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@159","time":66606.095,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@159","time":66608.469,"message":"← 200 OK"}
{"type":"log","callId":"call@159","time":66608.472,"message":"  date: Sun, 06 Jul 2025 14:45:48 GMT"}
{"type":"log","callId":"call@159","time":66608.473,"message":"  server: uvicorn"}
{"type":"log","callId":"call@159","time":66608.474,"message":"  content-length: 1732"}
{"type":"log","callId":"call@159","time":66608.475,"message":"  content-type: application/json"}
{"type":"after","callId":"call@159","endTime":66608.517,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/","status":200,"statusText":"OK","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:48 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"1732"},{"name":"content-type","value":"application/json"}],"fetchUid":"********************************"}},"afterSnapshot":"after@call@159"}
{"type":"before","callId":"call@162","startTime":66609.755,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities","method":"GET","timeout":8000},"stepId":"pw:api@79","beforeSnapshot":"before@call@162"}
{"type":"log","callId":"call@162","time":66609.983,"message":"→ GET http://localhost:8000/api/v1/entities"}
{"type":"log","callId":"call@162","time":66609.986,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@162","time":66609.987,"message":"  accept: */*"}
{"type":"log","callId":"call@162","time":66609.988,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@162","time":66609.989,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@162","time":66609.99,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@162","time":66609.991,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@162","time":66610.61,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@162","time":66610.614,"message":"  date: Sun, 06 Jul 2025 14:45:48 GMT"}
{"type":"log","callId":"call@162","time":66610.615,"message":"  server: uvicorn"}
{"type":"log","callId":"call@162","time":66610.616,"message":"  content-length: 0"}
{"type":"log","callId":"call@162","time":66610.617,"message":"  location: http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@162","time":66610.743,"message":"→ GET http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@162","time":66610.746,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@162","time":66610.747,"message":"  accept: */*"}
{"type":"log","callId":"call@162","time":66610.748,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@162","time":66610.749,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@162","time":66610.749,"message":"  x-test-worker-id: 26"}
{"type":"log","callId":"call@162","time":66610.75,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@162","time":66613.059,"message":"← 200 OK"}
{"type":"log","callId":"call@162","time":66613.062,"message":"  date: Sun, 06 Jul 2025 14:45:48 GMT"}
{"type":"log","callId":"call@162","time":66613.064,"message":"  server: uvicorn"}
{"type":"log","callId":"call@162","time":66613.064,"message":"  content-length: 1732"}
{"type":"log","callId":"call@162","time":66613.065,"message":"  content-type: application/json"}
{"type":"after","callId":"call@162","endTime":66613.093,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/","status":200,"statusText":"OK","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:48 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"1732"},{"name":"content-type","value":"application/json"}],"fetchUid":"c3df2044e4249ba4fb4a7c4b55dca890"}},"afterSnapshot":"after@call@162"}
