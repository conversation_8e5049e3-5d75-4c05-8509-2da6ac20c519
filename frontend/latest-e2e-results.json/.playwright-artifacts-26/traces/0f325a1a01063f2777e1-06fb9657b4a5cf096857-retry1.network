{"type":"resource-snapshot","snapshot":{"_monotonicTime":38492.583,"startedDateTime":"2025-07-06T14:45:20.329Z","time":11.194999999999709,"request":{"method":"GET","url":"http://localhost:8000/api/v1/entities","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"26"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/entities/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/entities/","_transferSize":-1},"cache":{},"timings":{"send":5.597999999998137,"wait":1.27900000000227,"receive":0.14800000000104774,"dns":4.646999999997206,"connect":5.120999999999185,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":38500.553,"startedDateTime":"2025-07-06T14:45:20.337Z","time":4.173999999991793,"request":{"method":"GET","url":"http://localhost:8000/api/v1/entities/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"26"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"1732"},{"name":"content-type","value":"application/json"}],"content":{"size":1732,"mimeType":"application/json","_sha1":"5031b6666d07729aa6b4449f8e8351aa724fd408.json"},"headersSize":-1,"bodySize":1732,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.5020000000004075,"wait":3.2710000000006403,"receive":0.18999999999505235,"dns":0.2860000000000582,"connect":0.4269999999960419,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":38510.343,"startedDateTime":"2025-07-06T14:45:20.347Z","time":9.161000000000058,"request":{"method":"DELETE","url":"http://localhost:8000/api/v1/entities/473","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"26"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"content":{"size":41,"mimeType":"application/json","_sha1":"f3bc11351f85225c37b8b0d7d402c26ec4665a8f.json"},"headersSize":-1,"bodySize":41,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.23900000000139698,"wait":9.074000000000524,"receive":0.08699999999953434,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.17499999999563443},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":38510.719,"startedDateTime":"2025-07-06T14:45:20.347Z","time":9.783000000003085,"request":{"method":"DELETE","url":"http://localhost:8000/api/v1/entities/472","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"26"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"content":{"size":41,"mimeType":"application/json","_sha1":"f3bc11351f85225c37b8b0d7d402c26ec4665a8f.json"},"headersSize":-1,"bodySize":41,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.8189999999958673,"wait":8.540000000000873,"receive":0.04800000000250293,"dns":0.48700000000098953,"connect":0.7079999999987194,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":38510.888,"startedDateTime":"2025-07-06T14:45:20.348Z","time":10.090000000003783,"request":{"method":"DELETE","url":"http://localhost:8000/api/v1/entities/471","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"26"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"content":{"size":41,"mimeType":"application/json","_sha1":"f3bc11351f85225c37b8b0d7d402c26ec4665a8f.json"},"headersSize":-1,"bodySize":41,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.9770000000062282,"wait":8.365999999994528,"receive":0.03800000000046566,"dns":0.7570000000050641,"connect":0.9290000000037253,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":38510.613,"startedDateTime":"2025-07-06T14:45:20.347Z","time":10.560999999986961,"request":{"method":"DELETE","url":"http://localhost:8000/api/v1/entities/474","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"26"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"content":{"size":41,"mimeType":"application/json","_sha1":"f3bc11351f85225c37b8b0d7d402c26ec4665a8f.json"},"headersSize":-1,"bodySize":41,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":1.0049999999973807,"wait":8.853000000002794,"receive":0.043999999994412065,"dns":0.7259999999951106,"connect":0.9379999999946449,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":38510.953,"startedDateTime":"2025-07-06T14:45:20.348Z","time":10.521999999997206,"request":{"method":"DELETE","url":"http://localhost:8000/api/v1/entities/476","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"26"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"content":{"size":41,"mimeType":"application/json","_sha1":"f3bc11351f85225c37b8b0d7d402c26ec4665a8f.json"},"headersSize":-1,"bodySize":41,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":1.0270000000018626,"wait":8.688999999998487,"receive":0.03600000000005821,"dns":0.815999999998894,"connect":0.9809999999997672,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":38510.801,"startedDateTime":"2025-07-06T14:45:20.347Z","time":10.885000000002037,"request":{"method":"DELETE","url":"http://localhost:8000/api/v1/entities/475","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"26"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"content":{"size":41,"mimeType":"application/json","_sha1":"f3bc11351f85225c37b8b0d7d402c26ec4665a8f.json"},"headersSize":-1,"bodySize":41,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":1.120999999999185,"wait":8.868000000002212,"receive":0.027999999998428393,"dns":0.9170000000012806,"connect":1.0720000000001164,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":38521.762,"startedDateTime":"2025-07-06T14:45:20.358Z","time":0.555000000000291,"request":{"method":"GET","url":"http://localhost:8000/api/v1/entities","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"26"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/entities/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/entities/","_transferSize":-1},"cache":{},"timings":{"send":0.18100000000413274,"wait":0.5190000000002328,"receive":0.03600000000005821,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.13999999999941792},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":38522.743,"startedDateTime":"2025-07-06T14:45:20.359Z","time":1.169000000001688,"request":{"method":"GET","url":"http://localhost:8000/api/v1/entities/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"26"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"1039"},{"name":"content-type","value":"application/json"}],"content":{"size":1039,"mimeType":"application/json","_sha1":"1ea3db92a73f04d920d6eb8ec1702be6e9264ad2.json"},"headersSize":-1,"bodySize":1039,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.14600000000064028,"wait":1.0899999999965075,"receive":0.07900000000518048,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.11000000000058208},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":38528.994,"startedDateTime":"2025-07-06T14:45:20.366Z","time":0.5730000000039581,"request":{"method":"POST","url":"http://localhost:8000/api/v1/entities","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"26"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"34"}],"queryString":[],"headersSize":-1,"bodySize":34,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"5584e0c55f22aa50491508fd8f98a4940fc95cc2.json"}},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/entities/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/entities/","_transferSize":-1},"cache":{},"timings":{"send":0.27899999999499414,"wait":0.5470000000059372,"receive":0.02599999999802094,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.1820000000006985},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":38529.504,"startedDateTime":"2025-07-06T14:45:20.366Z","time":0.815999999998894,"request":{"method":"POST","url":"http://localhost:8000/api/v1/entities","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"26"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"32"}],"queryString":[],"headersSize":-1,"bodySize":32,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"b1bdc4432b64b3d7ee61d535e26cb33613d609bd.json"}},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/entities/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/entities/","_transferSize":-1},"cache":{},"timings":{"send":0.05700000000069849,"wait":0.8009999999994761,"receive":0.014999999999417923,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.040000000000873115},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":38529.32,"startedDateTime":"2025-07-06T14:45:20.366Z","time":1.080999999998312,"request":{"method":"POST","url":"http://localhost:8000/api/v1/entities","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"26"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"33"}],"queryString":[],"headersSize":-1,"bodySize":33,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"c12fe3ad43d075c021555b7dd392915153baf9a2.json"}},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/entities/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/entities/","_transferSize":-1},"cache":{},"timings":{"send":0.07400000000052387,"wait":1.069999999999709,"receive":0.010999999998603016,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.05099999999947613},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":38529.58,"startedDateTime":"2025-07-06T14:45:20.366Z","time":0.9360000000015134,"request":{"method":"POST","url":"http://localhost:8000/api/v1/entities","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"26"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"34"}],"queryString":[],"headersSize":-1,"bodySize":34,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"bc8861ba47ee122b50861ade146b68ab596807d9.json"}},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/entities/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/entities/","_transferSize":-1},"cache":{},"timings":{"send":0.05500000000029104,"wait":0.9270000000033178,"receive":0.008999999998195563,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.03800000000046566},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":38529.422,"startedDateTime":"2025-07-06T14:45:20.366Z","time":1.1639999999970314,"request":{"method":"POST","url":"http://localhost:8000/api/v1/entities","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"26"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"34"}],"queryString":[],"headersSize":-1,"bodySize":34,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"6d6254773357176945ddff016e047523b477e0ba.json"}},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/entities/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/entities/","_transferSize":-1},"cache":{},"timings":{"send":0.0610000000015134,"wait":1.1549999999988358,"receive":0.008999999998195563,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.04299999999784632},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":38529.654,"startedDateTime":"2025-07-06T14:45:20.366Z","time":1.9059999999954016,"request":{"method":"POST","url":"http://localhost:8000/api/v1/entities","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"26"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"34"}],"queryString":[],"headersSize":-1,"bodySize":34,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"c70ff0439a75a0ed32427c25f13175bb35faff7c.json"}},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/entities/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/entities/","_transferSize":-1},"cache":{},"timings":{"send":0.6940000000031432,"wait":0.8919999999998254,"receive":0.011999999995168764,"dns":0.3819999999977881,"connect":0.6200000000026193,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":38530.169,"startedDateTime":"2025-07-06T14:45:20.367Z","time":7.002999999996973,"request":{"method":"POST","url":"http://localhost:8000/api/v1/entities/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"26"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"34"}],"queryString":[],"headersSize":-1,"bodySize":34,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"5584e0c55f22aa50491508fd8f98a4940fc95cc2.json"}},"response":{"status":201,"statusText":"Created","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"115"},{"name":"content-type","value":"application/json"}],"content":{"size":115,"mimeType":"application/json","_sha1":"adada2c2eb0035366ee43717255b2877eed6df26.json"},"headersSize":-1,"bodySize":115,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":1.047999999995227,"wait":5.368000000002212,"receive":0.06999999999970896,"dns":0.5669999999954598,"connect":0.9979999999995925,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":38530.856,"startedDateTime":"2025-07-06T14:45:20.368Z","time":6.530999999995402,"request":{"method":"POST","url":"http://localhost:8000/api/v1/entities/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"26"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"32"}],"queryString":[],"headersSize":-1,"bodySize":32,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"b1bdc4432b64b3d7ee61d535e26cb33613d609bd.json"}},"response":{"status":201,"statusText":"Created","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"113"},{"name":"content-type","value":"application/json"}],"content":{"size":113,"mimeType":"application/json","_sha1":"4f040e2acf59fb5544cff5fcfd0b89a98b954a39.json"},"headersSize":-1,"bodySize":113,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.6229999999995925,"wait":5.6379999999990105,"receive":0.06799999999930151,"dns":0.24799999999959255,"connect":0.5769999999974971,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":38531.012,"startedDateTime":"2025-07-06T14:45:20.368Z","time":6.5900000000037835,"request":{"method":"POST","url":"http://localhost:8000/api/v1/entities/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"26"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"34"}],"queryString":[],"headersSize":-1,"bodySize":34,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"bc8861ba47ee122b50861ade146b68ab596807d9.json"}},"response":{"status":201,"statusText":"Created","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"115"},{"name":"content-type","value":"application/json"}],"content":{"size":115,"mimeType":"application/json","_sha1":"250b65bad9fcca2538fea29a235a9fc946a7d26d.json"},"headersSize":-1,"bodySize":115,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.6109999999971478,"wait":5.681000000004133,"receive":0.030999999995401595,"dns":0.3320000000021537,"connect":0.5460000000020955,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":38530.943,"startedDateTime":"2025-07-06T14:45:20.368Z","time":6.905000000006112,"request":{"method":"POST","url":"http://localhost:8000/api/v1/entities/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"26"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"33"}],"queryString":[],"headersSize":-1,"bodySize":33,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"c12fe3ad43d075c021555b7dd392915153baf9a2.json"}},"response":{"status":201,"statusText":"Created","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"114"},{"name":"content-type","value":"application/json"}],"content":{"size":114,"mimeType":"application/json","_sha1":"8a77c9465e73762ae0f2317e958fd8d9908c1a73.json"},"headersSize":-1,"bodySize":114,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.713000000003376,"wait":5.748999999996158,"receive":0.0290000000022701,"dns":0.44600000000355067,"connect":0.6810000000041327,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":38531.067,"startedDateTime":"2025-07-06T14:45:20.368Z","time":6.8659999999945285,"request":{"method":"POST","url":"http://localhost:8000/api/v1/entities/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"26"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"34"}],"queryString":[],"headersSize":-1,"bodySize":34,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"6d6254773357176945ddff016e047523b477e0ba.json"}},"response":{"status":201,"statusText":"Created","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"115"},{"name":"content-type","value":"application/json"}],"content":{"size":115,"mimeType":"application/json","_sha1":"e4564b62c0d598f9b3d039b275d8545dd396a137.json"},"headersSize":-1,"bodySize":115,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.7359999999971478,"wait":5.694999999999709,"receive":0.021000000000640284,"dns":0.45199999999749707,"connect":0.6979999999966822,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":38531.687,"startedDateTime":"2025-07-06T14:45:20.368Z","time":6.0829999999914435,"request":{"method":"POST","url":"http://localhost:8000/api/v1/entities/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"26"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"34"}],"queryString":[],"headersSize":-1,"bodySize":34,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"c70ff0439a75a0ed32427c25f13175bb35faff7c.json"}},"response":{"status":201,"statusText":"Created","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"115"},{"name":"content-type","value":"application/json"}],"content":{"size":115,"mimeType":"application/json","_sha1":"aae23ef80525c0dc4775d5d8e87957c85abbdf0d.json"},"headersSize":-1,"bodySize":115,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.419000000001688,"wait":5.473999999994703,"receive":0.02200000000448199,"dns":0.23199999999633292,"connect":0.35499999999592546,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":38540.574,"startedDateTime":"2025-07-06T14:45:20.377Z","time":0.6310000000012224,"request":{"method":"POST","url":"http://localhost:8000/api/v1/connections","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"26"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"69"}],"queryString":[],"headersSize":-1,"bodySize":69,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"b25254f48011899870b69f30db845f2f3712ed4e.json"}},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/connections/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/connections/","_transferSize":-1},"cache":{},"timings":{"send":0.10700000000360887,"wait":0.5970000000015716,"receive":0.033999999999650754,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.07500000000436557},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":38540.712,"startedDateTime":"2025-07-06T14:45:20.377Z","time":0.7510000000038417,"request":{"method":"POST","url":"http://localhost:8000/api/v1/connections","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"26"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"69"}],"queryString":[],"headersSize":-1,"bodySize":69,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"b9e94f0e0fabb9ab69e96a67c045ab87a5e2590e.json"}},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/connections/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/connections/","_transferSize":-1},"cache":{},"timings":{"send":0.06899999999586726,"wait":0.7370000000009895,"receive":0.014000000002852175,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.05099999999947613},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":38540.797,"startedDateTime":"2025-07-06T14:45:20.377Z","time":0.8219999999928405,"request":{"method":"POST","url":"http://localhost:8000/api/v1/connections","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"26"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"70"}],"queryString":[],"headersSize":-1,"bodySize":70,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"5caabd931634984619d2af2282d63d4535e0c1f9.json"}},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/connections/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/connections/","_transferSize":-1},"cache":{},"timings":{"send":0.05100000000675209,"wait":0.8129999999946449,"receive":0.008999999998195563,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.03700000000389991},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":38540.864,"startedDateTime":"2025-07-06T14:45:20.378Z","time":0.8300000000017462,"request":{"method":"POST","url":"http://localhost:8000/api/v1/connections","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"26"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"70"}],"queryString":[],"headersSize":-1,"bodySize":70,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"d8da9c420d24d1244298247e37013889acc9909e.json"}},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/connections/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/connections/","_transferSize":-1},"cache":{},"timings":{"send":0.04899999999906868,"wait":0.8240000000005239,"receive":0.006000000001222361,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.033999999999650754},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":38541.815,"startedDateTime":"2025-07-06T14:45:20.378Z","time":5.966000000000349,"request":{"method":"POST","url":"http://localhost:8000/api/v1/connections/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"26"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"69"}],"queryString":[],"headersSize":-1,"bodySize":69,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"b25254f48011899870b69f30db845f2f3712ed4e.json"}},"response":{"status":201,"statusText":"Created","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"166"},{"name":"content-type","value":"application/json"}],"content":{"size":166,"mimeType":"application/json","_sha1":"4af76f160bd4a928ba9751c9d7027582c526c4a6.json"},"headersSize":-1,"bodySize":166,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.08399999999528518,"wait":5.908999999999651,"receive":0.05700000000069849,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.059999999997671694},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":38541.928,"startedDateTime":"2025-07-06T14:45:20.379Z","time":6.286000000000058,"request":{"method":"POST","url":"http://localhost:8000/api/v1/connections/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"26"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"69"}],"queryString":[],"headersSize":-1,"bodySize":69,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"b9e94f0e0fabb9ab69e96a67c045ab87a5e2590e.json"}},"response":{"status":201,"statusText":"Created","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"166"},{"name":"content-type","value":"application/json"}],"content":{"size":166,"mimeType":"application/json","_sha1":"8459bfc84de6a4ee67ea840977012894c1700ecd.json"},"headersSize":-1,"bodySize":166,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.05500000000029104,"wait":6.256999999997788,"receive":0.0290000000022701,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.03899999999703141},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":38542.085,"startedDateTime":"2025-07-06T14:45:20.379Z","time":6.783000000003085,"request":{"method":"POST","url":"http://localhost:8000/api/v1/connections/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"26"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"70"}],"queryString":[],"headersSize":-1,"bodySize":70,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"d8da9c420d24d1244298247e37013889acc9909e.json"}},"response":{"status":201,"statusText":"Created","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"165"},{"name":"content-type","value":"application/json"}],"content":{"size":165,"mimeType":"application/json","_sha1":"70e4782449a9742b01c14b312739ddf43aa8f6db.json"},"headersSize":-1,"bodySize":165,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.5760000000009313,"wait":5.906999999999243,"receive":0.033999999999650754,"dns":0.33400000000256114,"connect":0.5080000000016298,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":38542.032,"startedDateTime":"2025-07-06T14:45:20.379Z","time":7.394000000000233,"request":{"method":"POST","url":"http://localhost:8000/api/v1/connections/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"26"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"70"}],"queryString":[],"headersSize":-1,"bodySize":70,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"5caabd931634984619d2af2282d63d4535e0c1f9.json"}},"response":{"status":201,"statusText":"Created","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"165"},{"name":"content-type","value":"application/json"}],"content":{"size":165,"mimeType":"application/json","_sha1":"ae9de6c955b4cab54b4345607999f60997b68d58.json"},"headersSize":-1,"bodySize":165,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.6980000000039581,"wait":6.218000000000757,"receive":0.02599999999802094,"dns":0.49100000000180444,"connect":0.6589999999996508,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_frameref":"frame@01a436083cf0c5f6ed5170fb8f6c7540","_monotonicTime":38557.508,"startedDateTime":"2025-07-06T14:45:20.394Z","time":3.009,"request":{"method":"GET","url":"http://localhost:3000/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"Accept","value":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"},{"name":"Accept-Encoding","value":"gzip, deflate, br, zstd"},{"name":"Accept-Language","value":"en-US"},{"name":"Connection","value":"keep-alive"},{"name":"Host","value":"localhost:3000"},{"name":"Sec-Fetch-Dest","value":"document"},{"name":"Sec-Fetch-Mode","value":"navigate"},{"name":"Sec-Fetch-Site","value":"none"},{"name":"Sec-Fetch-User","value":"?1"},{"name":"Upgrade-Insecure-Requests","value":"1"},{"name":"User-Agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"sec-ch-ua","value":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"HeadlessChrome\";v=\"138\""},{"name":"sec-ch-ua-mobile","value":"?0"},{"name":"sec-ch-ua-platform","value":"\"Windows\""},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-isolation","value":"enabled"},{"name":"x-test-worker-id","value":"26"}],"queryString":[],"headersSize":749,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"Accept-Ranges","value":"bytes"},{"name":"Access-Control-Allow-Headers","value":"*"},{"name":"Access-Control-Allow-Methods","value":"*"},{"name":"Access-Control-Allow-Origin","value":"*"},{"name":"Connection","value":"keep-alive"},{"name":"Content-Length","value":"674"},{"name":"Content-Type","value":"text/html; charset=utf-8"},{"name":"Date","value":"Sun, 06 Jul 2025 14:45:20 GMT"},{"name":"ETag","value":"W/\"2a2-d+UIh9BpXbcZyToMQ2q0u2Vkiwc\""},{"name":"Keep-Alive","value":"timeout=5"},{"name":"Vary","value":"Accept-Encoding"},{"name":"X-Powered-By","value":"Express"}],"content":{"size":674,"mimeType":"text/html; charset=utf-8","compression":0,"_sha1":"77e50887d0695db719c93a0c436ab4bb65648b07.html"},"headersSize":373,"bodySize":674,"redirectURL":"","_transferSize":1047},"cache":{},"timings":{"dns":0.003,"connect":0.145,"ssl":1.374,"send":0,"wait":1.03,"receive":0.457},"pageref":"page@c3d5d6ea5248cd88f77b29fa874b1635","serverIPAddress":"[::1]","_serverPort":3000,"_securityDetails":{}}}
{"type":"resource-snapshot","snapshot":{"_frameref":"frame@01a436083cf0c5f6ed5170fb8f6c7540","_monotonicTime":38560.892,"startedDateTime":"2025-07-06T14:45:20.398Z","time":19.688000000000002,"request":{"method":"GET","url":"http://localhost:3000/static/js/bundle.js","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"Accept","value":"*/*"},{"name":"Accept-Encoding","value":"gzip, deflate, br, zstd"},{"name":"Accept-Language","value":"en-US"},{"name":"Connection","value":"keep-alive"},{"name":"Host","value":"localhost:3000"},{"name":"Referer","value":"http://localhost:3000/"},{"name":"Sec-Fetch-Dest","value":"script"},{"name":"Sec-Fetch-Mode","value":"no-cors"},{"name":"Sec-Fetch-Site","value":"same-origin"},{"name":"User-Agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"sec-ch-ua","value":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"HeadlessChrome\";v=\"138\""},{"name":"sec-ch-ua-mobile","value":"?0"},{"name":"sec-ch-ua-platform","value":"\"Windows\""},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-isolation","value":"enabled"},{"name":"x-test-worker-id","value":"26"}],"queryString":[],"headersSize":623,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"Accept-Ranges","value":"bytes"},{"name":"Access-Control-Allow-Headers","value":"*"},{"name":"Access-Control-Allow-Methods","value":"*"},{"name":"Access-Control-Allow-Origin","value":"*"},{"name":"Connection","value":"keep-alive"},{"name":"Content-Encoding","value":"br"},{"name":"Content-Type","value":"application/javascript; charset=utf-8"},{"name":"Date","value":"Sun, 06 Jul 2025 14:45:20 GMT"},{"name":"ETag","value":"W/\"245f7b-/+2KF+JnqQpNPg5X9L7eGnBeubI\""},{"name":"Keep-Alive","value":"timeout=5"},{"name":"Transfer-Encoding","value":"chunked"},{"name":"Vary","value":"Accept-Encoding"},{"name":"X-Powered-By","value":"Express"}],"content":{"size":-1,"mimeType":"application/javascript; charset=utf-8","compression":0},"headersSize":418,"bodySize":459484,"redirectURL":"","_transferSize":459902},"cache":{},"timings":{"dns":-1,"connect":-1,"ssl":-1,"send":0,"wait":14.435,"receive":5.253},"pageref":"page@c3d5d6ea5248cd88f77b29fa874b1635","serverIPAddress":"[::1]","_serverPort":3000,"_securityDetails":{}}}
{"type":"resource-snapshot","snapshot":{"_frameref":"frame@01a436083cf0c5f6ed5170fb8f6c7540","_monotonicTime":38657.48,"startedDateTime":"2025-07-06T14:45:20.494Z","time":4.039,"request":{"method":"GET","url":"http://localhost:8000/api/v1/entities/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"Accept","value":"application/json, text/plain, */*"},{"name":"Accept-Encoding","value":"gzip, deflate, br, zstd"},{"name":"Accept-Language","value":"en-US"},{"name":"Connection","value":"keep-alive"},{"name":"Host","value":"localhost:8000"},{"name":"Referer","value":"http://localhost:3000/"},{"name":"Sec-Fetch-Dest","value":"empty"},{"name":"Sec-Fetch-Mode","value":"cors"},{"name":"Sec-Fetch-Site","value":"same-site"},{"name":"User-Agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"sec-ch-ua","value":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"HeadlessChrome\";v=\"138\""},{"name":"sec-ch-ua-mobile","value":"?0"},{"name":"sec-ch-ua-platform","value":"\"Windows\""},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-isolation","value":"enabled"},{"name":"x-test-worker-id","value":"26"}],"queryString":[],"headersSize":644,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"content-length","value":"1732"},{"name":"content-type","value":"application/json"},{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"}],"content":{"size":1732,"mimeType":"application/json","compression":0,"_sha1":"518ca41422adc3c3130d6f21300710759cffa1b8.json"},"headersSize":127,"bodySize":1732,"redirectURL":"","_transferSize":1859},"cache":{},"timings":{"dns":0.003,"connect":0.182,"ssl":1.246,"send":0,"wait":2.169,"receive":0.439},"pageref":"page@c3d5d6ea5248cd88f77b29fa874b1635","serverIPAddress":"[::1]","_serverPort":8000,"_securityDetails":{}}}
{"type":"resource-snapshot","snapshot":{"_frameref":"frame@01a436083cf0c5f6ed5170fb8f6c7540","_monotonicTime":38657.529,"startedDateTime":"2025-07-06T14:45:20.494Z","time":3.779,"request":{"method":"GET","url":"http://localhost:8000/api/v1/units/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"Accept","value":"application/json, text/plain, */*"},{"name":"Accept-Encoding","value":"gzip, deflate, br, zstd"},{"name":"Accept-Language","value":"en-US"},{"name":"Connection","value":"keep-alive"},{"name":"Host","value":"localhost:8000"},{"name":"Referer","value":"http://localhost:3000/"},{"name":"Sec-Fetch-Dest","value":"empty"},{"name":"Sec-Fetch-Mode","value":"cors"},{"name":"Sec-Fetch-Site","value":"same-site"},{"name":"User-Agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"sec-ch-ua","value":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"HeadlessChrome\";v=\"138\""},{"name":"sec-ch-ua-mobile","value":"?0"},{"name":"sec-ch-ua-platform","value":"\"Windows\""},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-isolation","value":"enabled"},{"name":"x-test-worker-id","value":"26"}],"queryString":[],"headersSize":641,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"content-length","value":"607"},{"name":"content-type","value":"application/json"},{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"}],"content":{"size":607,"mimeType":"application/json","compression":0,"_sha1":"ad2b685749d6e01cd886bbf041a3f16a1ef18bbf.json"},"headersSize":126,"bodySize":607,"redirectURL":"","_transferSize":733},"cache":{},"timings":{"dns":0,"connect":0.114,"ssl":1.136,"send":0,"wait":2.209,"receive":0.32},"pageref":"page@c3d5d6ea5248cd88f77b29fa874b1635","serverIPAddress":"[::1]","_serverPort":8000,"_securityDetails":{}}}
{"type":"resource-snapshot","snapshot":{"_frameref":"frame@01a436083cf0c5f6ed5170fb8f6c7540","_monotonicTime":38657.559,"startedDateTime":"2025-07-06T14:45:20.494Z","time":1.648,"request":{"method":"GET","url":"http://localhost:8000/api/v1/entities/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"Accept","value":"application/json, text/plain, */*"},{"name":"Accept-Encoding","value":"gzip, deflate, br, zstd"},{"name":"Accept-Language","value":"en-US"},{"name":"Connection","value":"keep-alive"},{"name":"Host","value":"localhost:8000"},{"name":"Referer","value":"http://localhost:3000/"},{"name":"Sec-Fetch-Dest","value":"empty"},{"name":"Sec-Fetch-Mode","value":"cors"},{"name":"Sec-Fetch-Site","value":"same-site"},{"name":"User-Agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"sec-ch-ua","value":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"HeadlessChrome\";v=\"138\""},{"name":"sec-ch-ua-mobile","value":"?0"},{"name":"sec-ch-ua-platform","value":"\"Windows\""},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-isolation","value":"enabled"},{"name":"x-test-worker-id","value":"26"}],"queryString":[],"headersSize":644,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"content-length","value":"1732"},{"name":"content-type","value":"application/json"},{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"}],"content":{"size":1732,"mimeType":"application/json","compression":0,"_sha1":"518ca41422adc3c3130d6f21300710759cffa1b8.json"},"headersSize":127,"bodySize":1732,"redirectURL":"","_transferSize":1859},"cache":{},"timings":{"dns":-1,"connect":-1,"ssl":-1,"send":0,"wait":1.442,"receive":0.206},"pageref":"page@c3d5d6ea5248cd88f77b29fa874b1635","serverIPAddress":"[::1]","_serverPort":8000,"_securityDetails":{}}}
{"type":"resource-snapshot","snapshot":{"_frameref":"frame@01a436083cf0c5f6ed5170fb8f6c7540","_monotonicTime":38657.587,"startedDateTime":"2025-07-06T14:45:20.494Z","time":5.3180000000000005,"request":{"method":"GET","url":"http://localhost:8000/api/v1/units/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"Accept","value":"application/json, text/plain, */*"},{"name":"Accept-Encoding","value":"gzip, deflate, br, zstd"},{"name":"Accept-Language","value":"en-US"},{"name":"Connection","value":"keep-alive"},{"name":"Host","value":"localhost:8000"},{"name":"Referer","value":"http://localhost:3000/"},{"name":"Sec-Fetch-Dest","value":"empty"},{"name":"Sec-Fetch-Mode","value":"cors"},{"name":"Sec-Fetch-Site","value":"same-site"},{"name":"User-Agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"sec-ch-ua","value":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"HeadlessChrome\";v=\"138\""},{"name":"sec-ch-ua-mobile","value":"?0"},{"name":"sec-ch-ua-platform","value":"\"Windows\""},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-isolation","value":"enabled"},{"name":"x-test-worker-id","value":"26"}],"queryString":[],"headersSize":641,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"content-length","value":"607"},{"name":"content-type","value":"application/json"},{"name":"date","value":"Sun, 06 Jul 2025 14:45:19 GMT"},{"name":"server","value":"uvicorn"}],"content":{"size":607,"mimeType":"application/json","compression":0,"_sha1":"ad2b685749d6e01cd886bbf041a3f16a1ef18bbf.json"},"headersSize":126,"bodySize":607,"redirectURL":"","_transferSize":733},"cache":{},"timings":{"dns":0,"connect":0.109,"ssl":3.449,"send":0,"wait":1.573,"receive":0.187},"pageref":"page@c3d5d6ea5248cd88f77b29fa874b1635","serverIPAddress":"[::1]","_serverPort":8000,"_securityDetails":{}}}
{"type":"resource-snapshot","snapshot":{"_frameref":"frame@01a436083cf0c5f6ed5170fb8f6c7540","_monotonicTime":51471.641,"startedDateTime":"2025-07-06T14:45:33.308Z","time":13.338000000000001,"request":{"method":"GET","url":"http://localhost:8000/api/v1/compare/?from=477&to=481&unit=1","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"Accept","value":"application/json, text/plain, */*"},{"name":"Accept-Encoding","value":"gzip, deflate, br, zstd"},{"name":"Accept-Language","value":"en-US"},{"name":"Connection","value":"keep-alive"},{"name":"Host","value":"localhost:8000"},{"name":"Referer","value":"http://localhost:3000/"},{"name":"Sec-Fetch-Dest","value":"empty"},{"name":"Sec-Fetch-Mode","value":"cors"},{"name":"Sec-Fetch-Site","value":"same-site"},{"name":"User-Agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"sec-ch-ua","value":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"HeadlessChrome\";v=\"138\""},{"name":"sec-ch-ua-mobile","value":"?0"},{"name":"sec-ch-ua-platform","value":"\"Windows\""},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-isolation","value":"enabled"},{"name":"x-test-worker-id","value":"26"}],"queryString":[{"name":"from","value":"477"},{"name":"to","value":"481"},{"name":"unit","value":"1"}],"headersSize":643,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"content-length","value":"694"},{"name":"content-type","value":"application/json"},{"name":"date","value":"Sun, 06 Jul 2025 14:45:33 GMT"},{"name":"server","value":"uvicorn"}],"content":{"size":694,"mimeType":"application/json","compression":0,"_sha1":"ced256ca353528b90ef8f45264b20ebd7dffceba.json"},"headersSize":126,"bodySize":694,"redirectURL":"","_transferSize":820},"cache":{},"timings":{"dns":-1,"connect":-1,"ssl":-1,"send":0,"wait":13.022,"receive":0.316},"pageref":"page@c3d5d6ea5248cd88f77b29fa874b1635","serverIPAddress":"[::1]","_serverPort":8000,"_securityDetails":{}}}
