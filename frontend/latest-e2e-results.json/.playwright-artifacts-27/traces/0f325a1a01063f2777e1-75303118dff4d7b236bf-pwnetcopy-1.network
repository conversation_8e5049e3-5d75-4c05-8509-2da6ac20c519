{"type":"resource-snapshot","snapshot":{"_monotonicTime":53181.431,"startedDateTime":"2025-07-06T14:45:35.018Z","time":11.238000000012107,"request":{"method":"GET","url":"http://localhost:8000/api/v1/entities","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:34 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/entities/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/entities/","_transferSize":-1},"cache":{},"timings":{"send":5.559000000001106,"wait":1.3400000000037835,"receive":0.16900000000168802,"dns":4.608000000000175,"connect":5.121000000006461,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":53189.49,"startedDateTime":"2025-07-06T14:45:35.026Z","time":4.447000000000116,"request":{"method":"GET","url":"http://localhost:8000/api/v1/entities/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:34 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"1732"},{"name":"content-type","value":"application/json"}],"content":{"size":1732,"mimeType":"application/json","_sha1":"518ca41422adc3c3130d6f21300710759cffa1b8.json"},"headersSize":-1,"bodySize":1732,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.55899999999383,"wait":3.463000000003376,"receive":0.2110000000029686,"dns":0.30399999999644933,"connect":0.46899999999732245,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":53199.423,"startedDateTime":"2025-07-06T14:45:35.036Z","time":8.919999999998254,"request":{"method":"DELETE","url":"http://localhost:8000/api/v1/entities/480","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:34 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"content":{"size":41,"mimeType":"application/json","_sha1":"f3bc11351f85225c37b8b0d7d402c26ec4665a8f.json"},"headersSize":-1,"bodySize":41,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.2430000000022119,"wait":8.831999999994878,"receive":0.08800000000337604,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.17700000000331784},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":53199.823,"startedDateTime":"2025-07-06T14:45:35.036Z","time":9.715999999993073,"request":{"method":"DELETE","url":"http://localhost:8000/api/v1/entities/478","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"content":{"size":41,"mimeType":"application/json","_sha1":"f3bc11351f85225c37b8b0d7d402c26ec4665a8f.json"},"headersSize":-1,"bodySize":41,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.9019999999945867,"wait":8.219000000004598,"receive":0.04899999999906868,"dns":0.5819999999948777,"connect":0.8659999999945285,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":53200.01,"startedDateTime":"2025-07-06T14:45:35.037Z","time":9.832000000002154,"request":{"method":"DELETE","url":"http://localhost:8000/api/v1/entities/477","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"content":{"size":41,"mimeType":"application/json","_sha1":"f3bc11351f85225c37b8b0d7d402c26ec4665a8f.json"},"headersSize":-1,"bodySize":41,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.8280000000013388,"wait":8.35000000000582,"receive":0.04799999999522697,"dns":0.6440000000002328,"connect":0.7900000000008731,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":53199.91,"startedDateTime":"2025-07-06T14:45:35.037Z","time":9.913000000007742,"request":{"method":"DELETE","url":"http://localhost:8000/api/v1/entities/479","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"content":{"size":41,"mimeType":"application/json","_sha1":"f3bc11351f85225c37b8b0d7d402c26ec4665a8f.json"},"headersSize":-1,"bodySize":41,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.684000000001106,"wait":8.790999999997439,"receive":0.043000000005122274,"dns":0.43800000000192085,"connect":0.6410000000032596,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":53200.086,"startedDateTime":"2025-07-06T14:45:35.037Z","time":10.04200000000128,"request":{"method":"DELETE","url":"http://localhost:8000/api/v1/entities/482","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"content":{"size":41,"mimeType":"application/json","_sha1":"f3bc11351f85225c37b8b0d7d402c26ec4665a8f.json"},"headersSize":-1,"bodySize":41,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.705999999998312,"wait":8.815999999998894,"receive":0.03900000000430737,"dns":0.5270000000018626,"connect":0.6599999999962165,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":53199.701,"startedDateTime":"2025-07-06T14:45:35.036Z","time":10.580999999991036,"request":{"method":"DELETE","url":"http://localhost:8000/api/v1/entities/481","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"content":{"size":41,"mimeType":"application/json","_sha1":"f3bc11351f85225c37b8b0d7d402c26ec4665a8f.json"},"headersSize":-1,"bodySize":41,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.8340000000025611,"wait":9.282999999995809,"receive":0.029999999998835847,"dns":0.5029999999969732,"connect":0.7649999999994179,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":53210.749,"startedDateTime":"2025-07-06T14:45:35.047Z","time":0.4800000000032014,"request":{"method":"GET","url":"http://localhost:8000/api/v1/entities","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/entities/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/entities/","_transferSize":-1},"cache":{},"timings":{"send":0.14800000000104774,"wait":0.4389999999984866,"receive":0.04100000000471482,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.11200000000098953},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":53211.642,"startedDateTime":"2025-07-06T14:45:35.048Z","time":1.2189999999973224,"request":{"method":"GET","url":"http://localhost:8000/api/v1/entities/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"1039"},{"name":"content-type","value":"application/json"}],"content":{"size":1039,"mimeType":"application/json","_sha1":"1ea3db92a73f04d920d6eb8ec1702be6e9264ad2.json"},"headersSize":-1,"bodySize":1039,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.1500000000014552,"wait":1.1410000000032596,"receive":0.07799999999406282,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.11300000000483124},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":53218.006,"startedDateTime":"2025-07-06T14:45:35.055Z","time":0.6179999999949359,"request":{"method":"POST","url":"http://localhost:8000/api/v1/entities","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"34"}],"queryString":[],"headersSize":-1,"bodySize":34,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"01d96fa352a311cbb52d1f77e9949264a4f0f70c.json"}},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/entities/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/entities/","_transferSize":-1},"cache":{},"timings":{"send":0.29800000000250293,"wait":0.5899999999965075,"receive":0.027999999998428393,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.20700000000215368},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":53218.593,"startedDateTime":"2025-07-06T14:45:35.055Z","time":1.0529999999998836,"request":{"method":"POST","url":"http://localhost:8000/api/v1/entities","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"32"}],"queryString":[],"headersSize":-1,"bodySize":32,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"808103668b1b73eafe9ba1e0d8c13168ccbb4b8d.json"}},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/entities/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/entities/","_transferSize":-1},"cache":{},"timings":{"send":0.05700000000069849,"wait":1.0190000000002328,"receive":0.033999999999650754,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.040000000000873115},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":53218.505,"startedDateTime":"2025-07-06T14:45:35.055Z","time":1.2729999999937718,"request":{"method":"POST","url":"http://localhost:8000/api/v1/entities","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"34"}],"queryString":[],"headersSize":-1,"bodySize":34,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"863c2a4e0b8f863c08dc3e5712fbe6cb7b1bb447.json"}},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/entities/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/entities/","_transferSize":-1},"cache":{},"timings":{"send":0.06600000000617001,"wait":1.2599999999947613,"receive":0.01299999999901047,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.046000000002095476},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":53218.672,"startedDateTime":"2025-07-06T14:45:35.055Z","time":1.2160000000003492,"request":{"method":"POST","url":"http://localhost:8000/api/v1/entities","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"34"}],"queryString":[],"headersSize":-1,"bodySize":34,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"92ea7a44e73b9064049adc969a0d591e6cc19857.json"}},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/entities/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/entities/","_transferSize":-1},"cache":{},"timings":{"send":0.055999999996856786,"wait":1.206000000005588,"receive":0.00999999999476131,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.03800000000046566},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":53218.375,"startedDateTime":"2025-07-06T14:45:35.055Z","time":1.5429999999978463,"request":{"method":"POST","url":"http://localhost:8000/api/v1/entities","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"33"}],"queryString":[],"headersSize":-1,"bodySize":33,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"9ac2e2f19106fa67100ab13bf4d477979cb804f8.json"}},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/entities/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/entities/","_transferSize":-1},"cache":{},"timings":{"send":0.09399999999732245,"wait":1.5339999999996508,"receive":0.008999999998195563,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.0669999999954598},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":53218.747,"startedDateTime":"2025-07-06T14:45:35.055Z","time":1.7029999999940628,"request":{"method":"POST","url":"http://localhost:8000/api/v1/entities","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"34"}],"queryString":[],"headersSize":-1,"bodySize":34,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"280c01b6e514285554347bafbe3812b758f420a6.json"}},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/entities/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/entities/","_transferSize":-1},"cache":{},"timings":{"send":0.7860000000000582,"wait":0.6500000000014552,"receive":0.008999999998195563,"dns":0.3779999999969732,"connect":0.6659999999974389,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":53219.268,"startedDateTime":"2025-07-06T14:45:35.056Z","time":6.278000000005704,"request":{"method":"POST","url":"http://localhost:8000/api/v1/entities/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"34"}],"queryString":[],"headersSize":-1,"bodySize":34,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"01d96fa352a311cbb52d1f77e9949264a4f0f70c.json"}},"response":{"status":201,"statusText":"Created","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"115"},{"name":"content-type","value":"application/json"}],"content":{"size":115,"mimeType":"application/json","_sha1":"56a5e05ef433d1dc315ea76abf594747030cd786.json"},"headersSize":-1,"bodySize":115,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":1.0410000000047148,"wait":4.930999999996857,"receive":0.07200000000011642,"dns":0.2870000000038999,"connect":0.9880000000048312,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":53220.355,"startedDateTime":"2025-07-06T14:45:35.057Z","time":5.722000000008848,"request":{"method":"POST","url":"http://localhost:8000/api/v1/entities/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"32"}],"queryString":[],"headersSize":-1,"bodySize":32,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"808103668b1b73eafe9ba1e0d8c13168ccbb4b8d.json"}},"response":{"status":201,"statusText":"Created","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"113"},{"name":"content-type","value":"application/json"}],"content":{"size":113,"mimeType":"application/json","_sha1":"b875ba328edd8f304b24faaa52f4979cd4be4a92.json"},"headersSize":-1,"bodySize":113,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.5120000000024447,"wait":4.906999999999243,"receive":0.056000000004132744,"dns":0.3070000000006985,"connect":0.45200000000477303,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":53220.551,"startedDateTime":"2025-07-06T14:45:35.057Z","time":5.892999999996391,"request":{"method":"POST","url":"http://localhost:8000/api/v1/entities/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"34"}],"queryString":[],"headersSize":-1,"bodySize":34,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"92ea7a44e73b9064049adc969a0d591e6cc19857.json"}},"response":{"status":201,"statusText":"Created","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"115"},{"name":"content-type","value":"application/json"}],"content":{"size":115,"mimeType":"application/json","_sha1":"e537809f680cfcaf610cb6dde69206fc747e9b2b.json"},"headersSize":-1,"bodySize":115,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.4919999999983702,"wait":5.080999999998312,"receive":0.031000000002677552,"dns":0.33999999999650754,"connect":0.44099999999889405,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":53220.47,"startedDateTime":"2025-07-06T14:45:35.057Z","time":6.201000000000931,"request":{"method":"POST","url":"http://localhost:8000/api/v1/entities/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"34"}],"queryString":[],"headersSize":-1,"bodySize":34,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"863c2a4e0b8f863c08dc3e5712fbe6cb7b1bb447.json"}},"response":{"status":201,"statusText":"Created","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"115"},{"name":"content-type","value":"application/json"}],"content":{"size":115,"mimeType":"application/json","_sha1":"d7c6c0249ff10639185658ed047684c5e2a09f55.json"},"headersSize":-1,"bodySize":115,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.5939999999973224,"wait":5.152999999998428,"receive":0.024000000004889444,"dns":0.455999999998312,"connect":0.5679999999993015,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":53220.761,"startedDateTime":"2025-07-06T14:45:35.057Z","time":5.869999999995343,"request":{"method":"POST","url":"http://localhost:8000/api/v1/entities/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"34"}],"queryString":[],"headersSize":-1,"bodySize":34,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"280c01b6e514285554347bafbe3812b758f420a6.json"}},"response":{"status":201,"statusText":"Created","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"115"},{"name":"content-type","value":"application/json"}],"content":{"size":115,"mimeType":"application/json","_sha1":"3a95445f300a49d185280c703937cf3127a2c33d.json"},"headersSize":-1,"bodySize":115,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.45499999999447027,"wait":5.096000000005006,"receive":0.021999999997206032,"dns":0.330999999998312,"connect":0.4209999999948195,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":53220.611,"startedDateTime":"2025-07-06T14:45:35.057Z","time":6.304000000003725,"request":{"method":"POST","url":"http://localhost:8000/api/v1/entities/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"33"}],"queryString":[],"headersSize":-1,"bodySize":33,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"9ac2e2f19106fa67100ab13bf4d477979cb804f8.json"}},"response":{"status":201,"statusText":"Created","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"114"},{"name":"content-type","value":"application/json"}],"content":{"size":114,"mimeType":"application/json","_sha1":"d04c4a087af44152349a9dbc0454585ba92b064e.json"},"headersSize":-1,"bodySize":114,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.6319999999977881,"wait":5.150000000001455,"receive":0.023000000001047738,"dns":0.5210000000006403,"connect":0.6100000000005821,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":53229.069,"startedDateTime":"2025-07-06T14:45:35.066Z","time":0.4489999999932479,"request":{"method":"POST","url":"http://localhost:8000/api/v1/connections","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"69"}],"queryString":[],"headersSize":-1,"bodySize":69,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"****************************************.json"}},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/connections/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/connections/","_transferSize":-1},"cache":{},"timings":{"send":0.10400000000663567,"wait":0.41999999999825377,"receive":0.02899999999499414,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.07300000000395812},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":53229.201,"startedDateTime":"2025-07-06T14:45:35.066Z","time":0.7520000000004075,"request":{"method":"POST","url":"http://localhost:8000/api/v1/connections","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"69"}],"queryString":[],"headersSize":-1,"bodySize":69,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"b97bd958c3b54b93d559eaf938e9e069eb5711c5.json"}},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/connections/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/connections/","_transferSize":-1},"cache":{},"timings":{"send":0.06899999999586726,"wait":0.7379999999975553,"receive":0.014000000002852175,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.05199999999604188},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":53229.286,"startedDateTime":"2025-07-06T14:45:35.066Z","time":0.7900000000008731,"request":{"method":"POST","url":"http://localhost:8000/api/v1/connections","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"70"}],"queryString":[],"headersSize":-1,"bodySize":70,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"b7c350d1053f499dfcab5e2020e6208dd4343ed9.json"}},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/connections/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/connections/","_transferSize":-1},"cache":{},"timings":{"send":0.05099999999947613,"wait":0.7810000000026776,"receive":0.008999999998195563,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.03700000000389991},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":53229.352,"startedDateTime":"2025-07-06T14:45:35.066Z","time":0.8079999999972642,"request":{"method":"POST","url":"http://localhost:8000/api/v1/connections","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"70"}],"queryString":[],"headersSize":-1,"bodySize":70,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"59ee8064fad1a5c83eff4c772590c9eece2d7d0a.json"}},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/connections/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/connections/","_transferSize":-1},"cache":{},"timings":{"send":0.04800000000250293,"wait":0.8000000000029104,"receive":0.007999999994353857,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.033999999999650754},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":53229.87,"startedDateTime":"2025-07-06T14:45:35.067Z","time":6.211000000002969,"request":{"method":"POST","url":"http://localhost:8000/api/v1/connections/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"69"}],"queryString":[],"headersSize":-1,"bodySize":69,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"****************************************.json"}},"response":{"status":201,"statusText":"Created","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"166"},{"name":"content-type","value":"application/json"}],"content":{"size":166,"mimeType":"application/json","_sha1":"76b762bee83027fbd9acf564d8568873d992325e.json"},"headersSize":-1,"bodySize":166,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.09500000000116415,"wait":6.168000000005122,"receive":0.04299999999784632,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.06900000000314321},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":53230.308,"startedDateTime":"2025-07-06T14:45:35.067Z","time":6.165999999997439,"request":{"method":"POST","url":"http://localhost:8000/api/v1/connections/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"69"}],"queryString":[],"headersSize":-1,"bodySize":69,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"b97bd958c3b54b93d559eaf938e9e069eb5711c5.json"}},"response":{"status":201,"statusText":"Created","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"166"},{"name":"content-type","value":"application/json"}],"content":{"size":166,"mimeType":"application/json","_sha1":"754bf9b7c371acd34415d343e5c6cacea4a8b8ac.json"},"headersSize":-1,"bodySize":166,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.06999999999970896,"wait":6.139999999999418,"receive":0.02599999999802094,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.05099999999947613},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":53230.456,"startedDateTime":"2025-07-06T14:45:35.067Z","time":6.542999999997846,"request":{"method":"POST","url":"http://localhost:8000/api/v1/connections/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"70"}],"queryString":[],"headersSize":-1,"bodySize":70,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"59ee8064fad1a5c83eff4c772590c9eece2d7d0a.json"}},"response":{"status":201,"statusText":"Created","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"165"},{"name":"content-type","value":"application/json"}],"content":{"size":165,"mimeType":"application/json","_sha1":"60dc74414456f8808755689044812829229bd031.json"},"headersSize":-1,"bodySize":165,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.5910000000003492,"wait":5.561999999998079,"receive":0.021000000000640284,"dns":0.40099999999802094,"connect":0.559000000001106,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":53230.396,"startedDateTime":"2025-07-06T14:45:35.067Z","time":6.936999999998079,"request":{"method":"POST","url":"http://localhost:8000/api/v1/connections/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"},{"name":"content-type","value":"application/json"},{"name":"content-length","value":"70"}],"queryString":[],"headersSize":-1,"bodySize":70,"postData":{"mimeType":"application/json","text":"","params":[],"_sha1":"b7c350d1053f499dfcab5e2020e6208dd4343ed9.json"}},"response":{"status":201,"statusText":"Created","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"165"},{"name":"content-type","value":"application/json"}],"content":{"size":165,"mimeType":"application/json","_sha1":"275e7c7f6a4baa8ecb9820e1c83787006188456c.json"},"headersSize":-1,"bodySize":165,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.5969999999942956,"wait":6.027000000001863,"receive":0.03500000000349246,"dns":0.3459999999977299,"connect":0.5289999999949941,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_frameref":"frame@7c40d707465c4b75dee9e5154d9ae2a6","_monotonicTime":53245.352,"startedDateTime":"2025-07-06T14:45:35.082Z","time":3.3729999999999993,"request":{"method":"GET","url":"http://localhost:3000/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"Accept","value":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"},{"name":"Accept-Encoding","value":"gzip, deflate, br, zstd"},{"name":"Accept-Language","value":"en-US"},{"name":"Connection","value":"keep-alive"},{"name":"Host","value":"localhost:3000"},{"name":"Sec-Fetch-Dest","value":"document"},{"name":"Sec-Fetch-Mode","value":"navigate"},{"name":"Sec-Fetch-Site","value":"none"},{"name":"Sec-Fetch-User","value":"?1"},{"name":"Upgrade-Insecure-Requests","value":"1"},{"name":"User-Agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"sec-ch-ua","value":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"HeadlessChrome\";v=\"138\""},{"name":"sec-ch-ua-mobile","value":"?0"},{"name":"sec-ch-ua-platform","value":"\"Windows\""},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-isolation","value":"enabled"},{"name":"x-test-worker-id","value":"27"}],"queryString":[],"headersSize":749,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"Accept-Ranges","value":"bytes"},{"name":"Access-Control-Allow-Headers","value":"*"},{"name":"Access-Control-Allow-Methods","value":"*"},{"name":"Access-Control-Allow-Origin","value":"*"},{"name":"Connection","value":"keep-alive"},{"name":"Content-Length","value":"674"},{"name":"Content-Type","value":"text/html; charset=utf-8"},{"name":"Date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"ETag","value":"W/\"2a2-d+UIh9BpXbcZyToMQ2q0u2Vkiwc\""},{"name":"Keep-Alive","value":"timeout=5"},{"name":"Vary","value":"Accept-Encoding"},{"name":"X-Powered-By","value":"Express"}],"content":{"size":674,"mimeType":"text/html; charset=utf-8","compression":0,"_sha1":"77e50887d0695db719c93a0c436ab4bb65648b07.html"},"headersSize":373,"bodySize":674,"redirectURL":"","_transferSize":1047},"cache":{},"timings":{"dns":0.003,"connect":0.137,"ssl":1.386,"send":0,"wait":1.39,"receive":0.457},"pageref":"page@e14a07905c003d9bbc40b7420a22b30f","serverIPAddress":"[::1]","_serverPort":3000,"_securityDetails":{}}}
{"type":"resource-snapshot","snapshot":{"_frameref":"frame@7c40d707465c4b75dee9e5154d9ae2a6","_monotonicTime":53249.433,"startedDateTime":"2025-07-06T14:45:35.086Z","time":19.542,"request":{"method":"GET","url":"http://localhost:3000/static/js/bundle.js","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"Accept","value":"*/*"},{"name":"Accept-Encoding","value":"gzip, deflate, br, zstd"},{"name":"Accept-Language","value":"en-US"},{"name":"Connection","value":"keep-alive"},{"name":"Host","value":"localhost:3000"},{"name":"Referer","value":"http://localhost:3000/"},{"name":"Sec-Fetch-Dest","value":"script"},{"name":"Sec-Fetch-Mode","value":"no-cors"},{"name":"Sec-Fetch-Site","value":"same-origin"},{"name":"User-Agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"sec-ch-ua","value":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"HeadlessChrome\";v=\"138\""},{"name":"sec-ch-ua-mobile","value":"?0"},{"name":"sec-ch-ua-platform","value":"\"Windows\""},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-isolation","value":"enabled"},{"name":"x-test-worker-id","value":"27"}],"queryString":[],"headersSize":623,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"Accept-Ranges","value":"bytes"},{"name":"Access-Control-Allow-Headers","value":"*"},{"name":"Access-Control-Allow-Methods","value":"*"},{"name":"Access-Control-Allow-Origin","value":"*"},{"name":"Connection","value":"keep-alive"},{"name":"Content-Encoding","value":"br"},{"name":"Content-Type","value":"application/javascript; charset=utf-8"},{"name":"Date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"ETag","value":"W/\"245f7b-/+2KF+JnqQpNPg5X9L7eGnBeubI\""},{"name":"Keep-Alive","value":"timeout=5"},{"name":"Transfer-Encoding","value":"chunked"},{"name":"Vary","value":"Accept-Encoding"},{"name":"X-Powered-By","value":"Express"}],"content":{"size":-1,"mimeType":"application/javascript; charset=utf-8","compression":0},"headersSize":418,"bodySize":459484,"redirectURL":"","_transferSize":459902},"cache":{},"timings":{"dns":-1,"connect":-1,"ssl":-1,"send":0,"wait":16.193,"receive":3.349},"pageref":"page@e14a07905c003d9bbc40b7420a22b30f","serverIPAddress":"[::1]","_serverPort":3000,"_securityDetails":{}}}
{"type":"resource-snapshot","snapshot":{"_frameref":"frame@7c40d707465c4b75dee9e5154d9ae2a6","_monotonicTime":53350.43,"startedDateTime":"2025-07-06T14:45:35.187Z","time":4.215,"request":{"method":"GET","url":"http://localhost:8000/api/v1/units/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"Accept","value":"application/json, text/plain, */*"},{"name":"Accept-Encoding","value":"gzip, deflate, br, zstd"},{"name":"Accept-Language","value":"en-US"},{"name":"Connection","value":"keep-alive"},{"name":"Host","value":"localhost:8000"},{"name":"Referer","value":"http://localhost:3000/"},{"name":"Sec-Fetch-Dest","value":"empty"},{"name":"Sec-Fetch-Mode","value":"cors"},{"name":"Sec-Fetch-Site","value":"same-site"},{"name":"User-Agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"sec-ch-ua","value":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"HeadlessChrome\";v=\"138\""},{"name":"sec-ch-ua-mobile","value":"?0"},{"name":"sec-ch-ua-platform","value":"\"Windows\""},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-isolation","value":"enabled"},{"name":"x-test-worker-id","value":"27"}],"queryString":[],"headersSize":641,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"content-length","value":"607"},{"name":"content-type","value":"application/json"},{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"}],"content":{"size":607,"mimeType":"application/json","compression":0,"_sha1":"ad2b685749d6e01cd886bbf041a3f16a1ef18bbf.json"},"headersSize":126,"bodySize":607,"redirectURL":"","_transferSize":733},"cache":{},"timings":{"dns":0.002,"connect":0.148,"ssl":1.189,"send":0,"wait":2.586,"receive":0.29},"pageref":"page@e14a07905c003d9bbc40b7420a22b30f","serverIPAddress":"[::1]","_serverPort":8000,"_securityDetails":{}}}
{"type":"resource-snapshot","snapshot":{"_frameref":"frame@7c40d707465c4b75dee9e5154d9ae2a6","_monotonicTime":53350.359,"startedDateTime":"2025-07-06T14:45:35.187Z","time":4.6659999999999995,"request":{"method":"GET","url":"http://localhost:8000/api/v1/entities/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"Accept","value":"application/json, text/plain, */*"},{"name":"Accept-Encoding","value":"gzip, deflate, br, zstd"},{"name":"Accept-Language","value":"en-US"},{"name":"Connection","value":"keep-alive"},{"name":"Host","value":"localhost:8000"},{"name":"Referer","value":"http://localhost:3000/"},{"name":"Sec-Fetch-Dest","value":"empty"},{"name":"Sec-Fetch-Mode","value":"cors"},{"name":"Sec-Fetch-Site","value":"same-site"},{"name":"User-Agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"sec-ch-ua","value":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"HeadlessChrome\";v=\"138\""},{"name":"sec-ch-ua-mobile","value":"?0"},{"name":"sec-ch-ua-platform","value":"\"Windows\""},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-isolation","value":"enabled"},{"name":"x-test-worker-id","value":"27"}],"queryString":[],"headersSize":644,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"content-length","value":"1732"},{"name":"content-type","value":"application/json"},{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"}],"content":{"size":1732,"mimeType":"application/json","compression":0,"_sha1":"049a62e4e55a4135b4e40203d3e4949bcd3b65d7.json"},"headersSize":127,"bodySize":1732,"redirectURL":"","_transferSize":1859},"cache":{},"timings":{"dns":0.002,"connect":0.244,"ssl":1.297,"send":0,"wait":2.828,"receive":0.295},"pageref":"page@e14a07905c003d9bbc40b7420a22b30f","serverIPAddress":"[::1]","_serverPort":8000,"_securityDetails":{}}}
{"type":"resource-snapshot","snapshot":{"_frameref":"frame@7c40d707465c4b75dee9e5154d9ae2a6","_monotonicTime":53350.49,"startedDateTime":"2025-07-06T14:45:35.187Z","time":2.327,"request":{"method":"GET","url":"http://localhost:8000/api/v1/units/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"Accept","value":"application/json, text/plain, */*"},{"name":"Accept-Encoding","value":"gzip, deflate, br, zstd"},{"name":"Accept-Language","value":"en-US"},{"name":"Connection","value":"keep-alive"},{"name":"Host","value":"localhost:8000"},{"name":"Referer","value":"http://localhost:3000/"},{"name":"Sec-Fetch-Dest","value":"empty"},{"name":"Sec-Fetch-Mode","value":"cors"},{"name":"Sec-Fetch-Site","value":"same-site"},{"name":"User-Agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"sec-ch-ua","value":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"HeadlessChrome\";v=\"138\""},{"name":"sec-ch-ua-mobile","value":"?0"},{"name":"sec-ch-ua-platform","value":"\"Windows\""},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-isolation","value":"enabled"},{"name":"x-test-worker-id","value":"27"}],"queryString":[],"headersSize":641,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"content-length","value":"607"},{"name":"content-type","value":"application/json"},{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"}],"content":{"size":607,"mimeType":"application/json","compression":0,"_sha1":"ad2b685749d6e01cd886bbf041a3f16a1ef18bbf.json"},"headersSize":126,"bodySize":607,"redirectURL":"","_transferSize":733},"cache":{},"timings":{"dns":-1,"connect":-1,"ssl":-1,"send":0,"wait":2.009,"receive":0.318},"pageref":"page@e14a07905c003d9bbc40b7420a22b30f","serverIPAddress":"[::1]","_serverPort":8000,"_securityDetails":{}}}
{"type":"resource-snapshot","snapshot":{"_frameref":"frame@7c40d707465c4b75dee9e5154d9ae2a6","_monotonicTime":53350.463,"startedDateTime":"2025-07-06T14:45:35.187Z","time":2.2729999999999997,"request":{"method":"GET","url":"http://localhost:8000/api/v1/entities/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"Accept","value":"application/json, text/plain, */*"},{"name":"Accept-Encoding","value":"gzip, deflate, br, zstd"},{"name":"Accept-Language","value":"en-US"},{"name":"Connection","value":"keep-alive"},{"name":"Host","value":"localhost:8000"},{"name":"Referer","value":"http://localhost:3000/"},{"name":"Sec-Fetch-Dest","value":"empty"},{"name":"Sec-Fetch-Mode","value":"cors"},{"name":"Sec-Fetch-Site","value":"same-site"},{"name":"User-Agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"sec-ch-ua","value":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"HeadlessChrome\";v=\"138\""},{"name":"sec-ch-ua-mobile","value":"?0"},{"name":"sec-ch-ua-platform","value":"\"Windows\""},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-isolation","value":"enabled"},{"name":"x-test-worker-id","value":"27"}],"queryString":[],"headersSize":644,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"content-length","value":"1732"},{"name":"content-type","value":"application/json"},{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"}],"content":{"size":1732,"mimeType":"application/json","compression":0,"_sha1":"049a62e4e55a4135b4e40203d3e4949bcd3b65d7.json"},"headersSize":127,"bodySize":1732,"redirectURL":"","_transferSize":1859},"cache":{},"timings":{"dns":-1,"connect":-1,"ssl":-1,"send":0,"wait":2.001,"receive":0.272},"pageref":"page@e14a07905c003d9bbc40b7420a22b30f","serverIPAddress":"[::1]","_serverPort":8000,"_securityDetails":{}}}
{"type":"resource-snapshot","snapshot":{"_frameref":"frame@7c40d707465c4b75dee9e5154d9ae2a6","_monotonicTime":66118.57,"startedDateTime":"2025-07-06T14:45:47.955Z","time":8.559,"request":{"method":"GET","url":"http://localhost:8000/api/v1/compare/?from=483&to=487&unit=1","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"Accept","value":"application/json, text/plain, */*"},{"name":"Accept-Encoding","value":"gzip, deflate, br, zstd"},{"name":"Accept-Language","value":"en-US"},{"name":"Connection","value":"keep-alive"},{"name":"Host","value":"localhost:8000"},{"name":"Referer","value":"http://localhost:3000/"},{"name":"Sec-Fetch-Dest","value":"empty"},{"name":"Sec-Fetch-Mode","value":"cors"},{"name":"Sec-Fetch-Site","value":"same-site"},{"name":"User-Agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"sec-ch-ua","value":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"HeadlessChrome\";v=\"138\""},{"name":"sec-ch-ua-mobile","value":"?0"},{"name":"sec-ch-ua-platform","value":"\"Windows\""},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-isolation","value":"enabled"},{"name":"x-test-worker-id","value":"27"}],"queryString":[{"name":"from","value":"483"},{"name":"to","value":"487"},{"name":"unit","value":"1"}],"headersSize":643,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"content-length","value":"825"},{"name":"content-type","value":"application/json"},{"name":"date","value":"Sun, 06 Jul 2025 14:45:47 GMT"},{"name":"server","value":"uvicorn"}],"content":{"size":825,"mimeType":"application/json","compression":0,"_sha1":"182769e55e011b0771b153d6146b98483d3e492c.json"},"headersSize":126,"bodySize":825,"redirectURL":"","_transferSize":951},"cache":{},"timings":{"dns":0.003,"connect":0.13,"ssl":1.328,"send":0,"wait":6.848,"receive":0.25},"pageref":"page@e14a07905c003d9bbc40b7420a22b30f","serverIPAddress":"[::1]","_serverPort":8000,"_securityDetails":{}}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":66742.316,"startedDateTime":"2025-07-06T14:45:48.579Z","time":3.8770000000076834,"request":{"method":"DELETE","url":"http://localhost:8000/api/v1/entities/483","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:48 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"content":{"size":41,"mimeType":"application/json","_sha1":"f3bc11351f85225c37b8b0d7d402c26ec4665a8f.json"},"headersSize":-1,"bodySize":41,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.7309999999997672,"wait":2.764999999999418,"receive":0.06700000001001172,"dns":0.40099999999802094,"connect":0.6440000000002328,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":66773.734,"startedDateTime":"2025-07-06T14:45:48.610Z","time":7.478000000002794,"request":{"method":"DELETE","url":"http://localhost:8000/api/v1/entities/484","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:48 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"content":{"size":41,"mimeType":"application/json","_sha1":"f3bc11351f85225c37b8b0d7d402c26ec4665a8f.json"},"headersSize":-1,"bodySize":41,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.15699999999196734,"wait":7.418999999994412,"receive":0.0590000000083819,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.11899999999150168},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":66808.475,"startedDateTime":"2025-07-06T14:45:48.645Z","time":7.817000000010012,"request":{"method":"DELETE","url":"http://localhost:8000/api/v1/entities/485","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:48 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"content":{"size":41,"mimeType":"application/json","_sha1":"f3bc11351f85225c37b8b0d7d402c26ec4665a8f.json"},"headersSize":-1,"bodySize":41,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.13899999999557622,"wait":7.763000000006286,"receive":0.05400000000372529,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.09900000000197906},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":66843.71,"startedDateTime":"2025-07-06T14:45:48.680Z","time":7.649999999994179,"request":{"method":"DELETE","url":"http://localhost:8000/api/v1/entities/487","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:48 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"content":{"size":41,"mimeType":"application/json","_sha1":"f3bc11351f85225c37b8b0d7d402c26ec4665a8f.json"},"headersSize":-1,"bodySize":41,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.14800000000104774,"wait":7.586999999999534,"receive":0.0629999999946449,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.10699999998905696},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":66878.723,"startedDateTime":"2025-07-06T14:45:48.715Z","time":7.387000000002445,"request":{"method":"DELETE","url":"http://localhost:8000/api/v1/entities/486","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:48 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"content":{"size":41,"mimeType":"application/json","_sha1":"f3bc11351f85225c37b8b0d7d402c26ec4665a8f.json"},"headersSize":-1,"bodySize":41,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.1200000000098953,"wait":7.338000000003376,"receive":0.04899999999906868,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.08400000000256114},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":66913.449,"startedDateTime":"2025-07-06T14:45:48.750Z","time":5.177999999999884,"request":{"method":"DELETE","url":"http://localhost:8000/api/v1/entities/488","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:48 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"content":{"size":41,"mimeType":"application/json","_sha1":"f3bc11351f85225c37b8b0d7d402c26ec4665a8f.json"},"headersSize":-1,"bodySize":41,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.14400000000023283,"wait":5.105999999999767,"receive":0.07200000000011642,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.10399999999208376},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":66919.742,"startedDateTime":"2025-07-06T14:45:48.756Z","time":0.7920000000012806,"request":{"method":"GET","url":"http://localhost:8000/api/v1/entities","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:48 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/entities/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/entities/","_transferSize":-1},"cache":{},"timings":{"send":0.10300000000279397,"wait":0.7649999999994179,"receive":0.027000000001862645,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.07499999999708962},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":66920.822,"startedDateTime":"2025-07-06T14:45:48.758Z","time":4.563999999998487,"request":{"method":"GET","url":"http://localhost:8000/api/v1/entities/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:48 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"1039"},{"name":"content-type","value":"application/json"}],"content":{"size":1039,"mimeType":"application/json","_sha1":"1ea3db92a73f04d920d6eb8ec1702be6e9264ad2.json"},"headersSize":-1,"bodySize":1039,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.5899999999965075,"wait":3.5819999999948777,"receive":0.03700000001117587,"dns":0.3959999999933643,"connect":0.5489999999990687,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":66925.944,"startedDateTime":"2025-07-06T14:45:48.763Z","time":0.8040000000037253,"request":{"method":"GET","url":"http://localhost:8000/api/v1/entities","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:48 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/entities/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/entities/","_transferSize":-1},"cache":{},"timings":{"send":0.09700000000884756,"wait":0.7859999999927823,"receive":0.01800000001094304,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.07300000000395812},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":66926.993,"startedDateTime":"2025-07-06T14:45:48.764Z","time":3.7559999999939464,"request":{"method":"GET","url":"http://localhost:8000/api/v1/entities/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:48 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"1039"},{"name":"content-type","value":"application/json"}],"content":{"size":1039,"mimeType":"application/json","_sha1":"1ea3db92a73f04d920d6eb8ec1702be6e9264ad2.json"},"headersSize":-1,"bodySize":1039,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.4949999999953434,"wait":2.9400000000023283,"receive":0.0639999999984866,"dns":0.2989999999990687,"connect":0.4529999999940628,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":66931.938,"startedDateTime":"2025-07-06T14:45:48.769Z","time":0.41100000000733417,"request":{"method":"GET","url":"http://localhost:8000/api/v1/entities","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":307,"statusText":"Temporary Redirect","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:48 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"0"},{"name":"location","value":"http://localhost:8000/api/v1/entities/"}],"content":{"size":0,"mimeType":"x-unknown"},"headersSize":-1,"bodySize":0,"redirectURL":"http://localhost:8000/api/v1/entities/","_transferSize":-1},"cache":{},"timings":{"send":0.07999999998719431,"wait":0.39300000001094304,"receive":0.017999999996391125,"dns":-1,"connect":-1,"ssl":-1,"blocked":0.0610000000015134},"_apiRequest":true}}
{"type":"resource-snapshot","snapshot":{"_monotonicTime":66932.584,"startedDateTime":"2025-07-06T14:45:48.769Z","time":2.188000000009197,"request":{"method":"GET","url":"http://localhost:8000/api/v1/entities/","httpVersion":"HTTP/1.1","cookies":[],"headers":[{"name":"user-agent","value":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"},{"name":"accept","value":"*/*"},{"name":"accept-encoding","value":"gzip,deflate,br"},{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"}],"queryString":[],"headersSize":-1,"bodySize":0},"response":{"status":200,"statusText":"OK","httpVersion":"1.1","cookies":[],"headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:48 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"1039"},{"name":"content-type","value":"application/json"}],"content":{"size":1039,"mimeType":"application/json","_sha1":"1ea3db92a73f04d920d6eb8ec1702be6e9264ad2.json"},"headersSize":-1,"bodySize":1039,"redirectURL":"","_transferSize":-1},"cache":{},"timings":{"send":0.48199999998905696,"wait":1.4200000000128057,"receive":0.03999999999359716,"dns":0.2960000000020955,"connect":0.4320000000006985,"ssl":-1,"blocked":-1},"_apiRequest":true,"serverIPAddress":"::1","_serverPort":8000}}
