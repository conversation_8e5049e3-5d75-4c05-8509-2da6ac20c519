{"version":8,"type":"context-options","origin":"library","browserName":"chromium","options":{"noDefaultViewport":false,"viewport":{"width":1280,"height":720},"ignoreHTTPSErrors":true,"javaScriptEnabled":true,"bypassCSP":true,"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36","locale":"en-US","extraHTTPHeaders":[{"name":"x-test-backend-url","value":"http://localhost:8000"},{"name":"x-test-worker-id","value":"27"},{"name":"x-test-isolation","value":"enabled"}],"offline":false,"deviceScaleFactor":1,"isMobile":false,"hasTouch":false,"colorScheme":"light","acceptDownloads":"accept","baseURL":"http://localhost:3000","recordVideo":{"dir":"/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/latest-e2e-results.json/.playwright-artifacts-27","size":{"width":800,"height":450}},"serviceWorkers":"allow","selectorEngines":[],"testIdAttributeName":"data-testid"},"platform":"darwin","wallTime":1751813134959,"monotonicTime":53122.653,"sdkLanguage":"javascript","testIdAttributeName":"data-testid","contextId":"browser-context@6fdd745886521d3811f396a022e979a2","title":"comparisons.spec.ts:229 › Entity Comparisons and Pathfinding › should calculate complex multi-hop paths"}
{"type":"before","callId":"call@6","startTime":53124.148,"class":"BrowserContext","method":"newPage","params":{},"stepId":"pw:api@8","beforeSnapshot":"before@call@6"}
{"type":"event","time":53175.137,"class":"BrowserContext","method":"page","params":{"pageId":"page@e14a07905c003d9bbc40b7420a22b30f"}}
{"type":"after","callId":"call@6","endTime":53175.18,"result":{"page":"<Page>"},"afterSnapshot":"after@call@6"}
{"type":"before","callId":"call@9","startTime":53180.644,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities","method":"GET","timeout":8000},"stepId":"pw:api@9","beforeSnapshot":"before@call@9"}
{"type":"log","callId":"call@9","time":53185.558,"message":"→ GET http://localhost:8000/api/v1/entities"}
{"type":"log","callId":"call@9","time":53185.571,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@9","time":53185.574,"message":"  accept: */*"}
{"type":"log","callId":"call@9","time":53185.576,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@9","time":53185.58,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@9","time":53185.585,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@9","time":53185.588,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@9","time":53189.356,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@9","time":53189.365,"message":"  date: Sun, 06 Jul 2025 14:45:34 GMT"}
{"type":"log","callId":"call@9","time":53189.37,"message":"  server: uvicorn"}
{"type":"log","callId":"call@9","time":53189.373,"message":"  content-length: 0"}
{"type":"log","callId":"call@9","time":53189.375,"message":"  location: http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@9","time":53189.635,"message":"→ GET http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@9","time":53189.638,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@9","time":53189.64,"message":"  accept: */*"}
{"type":"log","callId":"call@9","time":53189.641,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@9","time":53189.643,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@9","time":53189.644,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@9","time":53189.645,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@9","time":53193.982,"message":"← 200 OK"}
{"type":"log","callId":"call@9","time":53193.986,"message":"  date: Sun, 06 Jul 2025 14:45:34 GMT"}
{"type":"log","callId":"call@9","time":53193.988,"message":"  server: uvicorn"}
{"type":"log","callId":"call@9","time":53193.99,"message":"  content-length: 1732"}
{"type":"log","callId":"call@9","time":53193.992,"message":"  content-type: application/json"}
{"type":"after","callId":"call@9","endTime":53194.05,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/","status":200,"statusText":"OK","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:34 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"1732"},{"name":"content-type","value":"application/json"}],"fetchUid":"8a180e4e4b4d1e4bffb477d776425ce5"}},"afterSnapshot":"after@call@9"}
{"type":"before","callId":"call@12","startTime":53198.592,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities/480","method":"DELETE","timeout":8000},"stepId":"pw:api@10","beforeSnapshot":"before@call@12"}
{"type":"before","callId":"call@14","startTime":53198.778,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities/481","method":"DELETE","timeout":8000},"stepId":"pw:api@11","beforeSnapshot":"before@call@14"}
{"type":"before","callId":"call@16","startTime":53198.912,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities/478","method":"DELETE","timeout":8000},"stepId":"pw:api@12","beforeSnapshot":"before@call@16"}
{"type":"before","callId":"call@18","startTime":53199.031,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities/479","method":"DELETE","timeout":8000},"stepId":"pw:api@13","beforeSnapshot":"before@call@18"}
{"type":"before","callId":"call@20","startTime":53199.146,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities/477","method":"DELETE","timeout":8000},"stepId":"pw:api@14","beforeSnapshot":"before@call@20"}
{"type":"before","callId":"call@22","startTime":53199.258,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities/482","method":"DELETE","timeout":8000},"stepId":"pw:api@15","beforeSnapshot":"before@call@22"}
{"type":"log","callId":"call@12","time":53199.596,"message":"→ DELETE http://localhost:8000/api/v1/entities/480"}
{"type":"log","callId":"call@12","time":53199.6,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@12","time":53199.601,"message":"  accept: */*"}
{"type":"log","callId":"call@12","time":53199.603,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@12","time":53199.604,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@12","time":53199.605,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@12","time":53199.606,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@14","time":53199.799,"message":"→ DELETE http://localhost:8000/api/v1/entities/481"}
{"type":"log","callId":"call@14","time":53199.801,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@14","time":53199.803,"message":"  accept: */*"}
{"type":"log","callId":"call@14","time":53199.808,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@14","time":53199.81,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@14","time":53199.811,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@14","time":53199.812,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@16","time":53199.889,"message":"→ DELETE http://localhost:8000/api/v1/entities/478"}
{"type":"log","callId":"call@16","time":53199.893,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@16","time":53199.894,"message":"  accept: */*"}
{"type":"log","callId":"call@16","time":53199.895,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@16","time":53199.896,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@16","time":53199.898,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@16","time":53199.899,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@18","time":53199.989,"message":"→ DELETE http://localhost:8000/api/v1/entities/479"}
{"type":"log","callId":"call@18","time":53199.992,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@18","time":53199.993,"message":"  accept: */*"}
{"type":"log","callId":"call@18","time":53199.994,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@18","time":53199.995,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@18","time":53199.998,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@18","time":53199.999,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@20","time":53200.066,"message":"→ DELETE http://localhost:8000/api/v1/entities/477"}
{"type":"log","callId":"call@20","time":53200.069,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@20","time":53200.07,"message":"  accept: */*"}
{"type":"log","callId":"call@20","time":53200.071,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@20","time":53200.072,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@20","time":53200.074,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@20","time":53200.075,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@22","time":53200.186,"message":"→ DELETE http://localhost:8000/api/v1/entities/482"}
{"type":"log","callId":"call@22","time":53200.189,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@22","time":53200.191,"message":"  accept: */*"}
{"type":"log","callId":"call@22","time":53200.192,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@22","time":53200.193,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@22","time":53200.194,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@22","time":53200.195,"message":"  x-test-isolation: enabled"}
{"type":"screencast-frame","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":53206.798,"frameSwapWallTime":1751813135042.3901}
{"type":"log","callId":"call@12","time":53208.778,"message":"← 200 OK"}
{"type":"log","callId":"call@12","time":53208.781,"message":"  date: Sun, 06 Jul 2025 14:45:34 GMT"}
{"type":"log","callId":"call@12","time":53208.783,"message":"  server: uvicorn"}
{"type":"log","callId":"call@12","time":53208.783,"message":"  content-length: 41"}
{"type":"log","callId":"call@12","time":53208.784,"message":"  content-type: application/json"}
{"type":"after","callId":"call@12","endTime":53208.818,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/480","status":200,"statusText":"OK","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:34 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"fetchUid":"60a18f850a624eeacee948d968f12561"}},"afterSnapshot":"after@call@12"}
{"type":"log","callId":"call@16","time":53209.064,"message":"← 200 OK"}
{"type":"log","callId":"call@16","time":53209.066,"message":"  date: Sun, 06 Jul 2025 14:45:35 GMT"}
{"type":"log","callId":"call@16","time":53209.068,"message":"  server: uvicorn"}
{"type":"log","callId":"call@16","time":53209.068,"message":"  content-length: 41"}
{"type":"log","callId":"call@16","time":53209.069,"message":"  content-type: application/json"}
{"type":"after","callId":"call@16","endTime":53209.087,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/478","status":200,"statusText":"OK","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"fetchUid":"4a1b32f413e96d9737434dc681fb3d82"}},"afterSnapshot":"after@call@16"}
{"type":"log","callId":"call@20","time":53209.295,"message":"← 200 OK"}
{"type":"log","callId":"call@20","time":53209.297,"message":"  date: Sun, 06 Jul 2025 14:45:35 GMT"}
{"type":"log","callId":"call@20","time":53209.298,"message":"  server: uvicorn"}
{"type":"log","callId":"call@20","time":53209.299,"message":"  content-length: 41"}
{"type":"log","callId":"call@20","time":53209.299,"message":"  content-type: application/json"}
{"type":"after","callId":"call@20","endTime":53209.315,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/477","status":200,"statusText":"OK","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"fetchUid":"135bb80e06552b1b63808b81d2b33f0f"}},"afterSnapshot":"after@call@20"}
{"type":"log","callId":"call@18","time":53209.489,"message":"← 200 OK"}
{"type":"log","callId":"call@18","time":53209.492,"message":"  date: Sun, 06 Jul 2025 14:45:35 GMT"}
{"type":"log","callId":"call@18","time":53209.493,"message":"  server: uvicorn"}
{"type":"log","callId":"call@18","time":53209.494,"message":"  content-length: 41"}
{"type":"log","callId":"call@18","time":53209.495,"message":"  content-type: application/json"}
{"type":"after","callId":"call@18","endTime":53209.509,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/479","status":200,"statusText":"OK","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"fetchUid":"d0289c5ae0f2a7a8bde10d7047ac6b8f"}},"afterSnapshot":"after@call@18"}
{"type":"log","callId":"call@22","time":53209.699,"message":"← 200 OK"}
{"type":"log","callId":"call@22","time":53209.701,"message":"  date: Sun, 06 Jul 2025 14:45:35 GMT"}
{"type":"log","callId":"call@22","time":53209.702,"message":"  server: uvicorn"}
{"type":"log","callId":"call@22","time":53209.702,"message":"  content-length: 41"}
{"type":"log","callId":"call@22","time":53209.704,"message":"  content-type: application/json"}
{"type":"after","callId":"call@22","endTime":53209.718,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/482","status":200,"statusText":"OK","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"fetchUid":"8d2977d6cbb00573f4dd5e7267452615"}},"afterSnapshot":"after@call@22"}
{"type":"log","callId":"call@14","time":53209.908,"message":"← 200 OK"}
{"type":"log","callId":"call@14","time":53209.91,"message":"  date: Sun, 06 Jul 2025 14:45:35 GMT"}
{"type":"log","callId":"call@14","time":53209.91,"message":"  server: uvicorn"}
{"type":"log","callId":"call@14","time":53209.911,"message":"  content-length: 41"}
{"type":"log","callId":"call@14","time":53209.912,"message":"  content-type: application/json"}
{"type":"after","callId":"call@14","endTime":53209.93,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/481","status":200,"statusText":"OK","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"fetchUid":"c25e2ab08b568e8a6109992ee2bb2257"}},"afterSnapshot":"after@call@14"}
{"type":"before","callId":"call@24","startTime":53210.529,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities","method":"GET","timeout":8000},"stepId":"pw:api@16","beforeSnapshot":"before@call@24"}
{"type":"log","callId":"call@24","time":53210.869,"message":"→ GET http://localhost:8000/api/v1/entities"}
{"type":"log","callId":"call@24","time":53210.871,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@24","time":53210.872,"message":"  accept: */*"}
{"type":"log","callId":"call@24","time":53210.873,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@24","time":53210.874,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@24","time":53210.875,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@24","time":53210.876,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@24","time":53211.567,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@24","time":53211.57,"message":"  date: Sun, 06 Jul 2025 14:45:35 GMT"}
{"type":"log","callId":"call@24","time":53211.572,"message":"  server: uvicorn"}
{"type":"log","callId":"call@24","time":53211.573,"message":"  content-length: 0"}
{"type":"log","callId":"call@24","time":53211.574,"message":"  location: http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@24","time":53211.757,"message":"→ GET http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@24","time":53211.76,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@24","time":53211.761,"message":"  accept: */*"}
{"type":"log","callId":"call@24","time":53211.762,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@24","time":53211.763,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@24","time":53211.764,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@24","time":53211.765,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@24","time":53213.147,"message":"← 200 OK"}
{"type":"log","callId":"call@24","time":53213.15,"message":"  date: Sun, 06 Jul 2025 14:45:35 GMT"}
{"type":"log","callId":"call@24","time":53213.152,"message":"  server: uvicorn"}
{"type":"log","callId":"call@24","time":53213.154,"message":"  content-length: 1039"}
{"type":"log","callId":"call@24","time":53213.155,"message":"  content-type: application/json"}
{"type":"after","callId":"call@24","endTime":53213.189,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/","status":200,"statusText":"OK","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"1039"},{"name":"content-type","value":"application/json"}],"fetchUid":"82d021cacc3c274ee272da3cb01eec42"}},"afterSnapshot":"after@call@24"}
{"type":"before","callId":"call@27","startTime":53216.018,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities","method":"POST","jsonData":"{\"name\":\"Human YDMXA\",\"unit_id\":1}","timeout":8000},"stepId":"pw:api@17","beforeSnapshot":"before@call@27"}
{"type":"before","callId":"call@29","startTime":53216.229,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities","method":"POST","jsonData":"{\"name\":\"Ball YDMXB\",\"unit_id\":1}","timeout":8000},"stepId":"pw:api@18","beforeSnapshot":"before@call@29"}
{"type":"before","callId":"call@31","startTime":53216.361,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities","method":"POST","jsonData":"{\"name\":\"Build YDMXC\",\"unit_id\":1}","timeout":8000},"stepId":"pw:api@19","beforeSnapshot":"before@call@31"}
{"type":"before","callId":"call@33","startTime":53216.478,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities","method":"POST","jsonData":"{\"name\":\"Car YDMXD\",\"unit_id\":1}","timeout":8000},"stepId":"pw:api@20","beforeSnapshot":"before@call@33"}
{"type":"before","callId":"call@35","startTime":53216.589,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities","method":"POST","jsonData":"{\"name\":\"Eleph YDMXE\",\"unit_id\":1}","timeout":8000},"stepId":"pw:api@21","beforeSnapshot":"before@call@35"}
{"type":"before","callId":"call@37","startTime":53216.695,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities","method":"POST","jsonData":"{\"name\":\"Mouse YDMXF\",\"unit_id\":1}","timeout":8000},"stepId":"pw:api@22","beforeSnapshot":"before@call@37"}
{"type":"log","callId":"call@27","time":53218.242,"message":"→ POST http://localhost:8000/api/v1/entities"}
{"type":"log","callId":"call@27","time":53218.247,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@27","time":53218.248,"message":"  accept: */*"}
{"type":"log","callId":"call@27","time":53218.249,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@27","time":53218.25,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@27","time":53218.251,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@27","time":53218.252,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@27","time":53218.252,"message":"  content-type: application/json"}
{"type":"log","callId":"call@27","time":53218.253,"message":"  content-length: 34"}
{"type":"log","callId":"call@29","time":53218.454,"message":"→ POST http://localhost:8000/api/v1/entities"}
{"type":"log","callId":"call@29","time":53218.457,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@29","time":53218.458,"message":"  accept: */*"}
{"type":"log","callId":"call@29","time":53218.459,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@29","time":53218.46,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@29","time":53218.46,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@29","time":53218.461,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@29","time":53218.462,"message":"  content-type: application/json"}
{"type":"log","callId":"call@29","time":53218.463,"message":"  content-length: 33"}
{"type":"log","callId":"call@31","time":53218.554,"message":"→ POST http://localhost:8000/api/v1/entities"}
{"type":"log","callId":"call@31","time":53218.556,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@31","time":53218.557,"message":"  accept: */*"}
{"type":"log","callId":"call@31","time":53218.557,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@31","time":53218.558,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@31","time":53218.559,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@31","time":53218.56,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@31","time":53218.56,"message":"  content-type: application/json"}
{"type":"log","callId":"call@31","time":53218.561,"message":"  content-length: 34"}
{"type":"log","callId":"call@33","time":53218.636,"message":"→ POST http://localhost:8000/api/v1/entities"}
{"type":"log","callId":"call@33","time":53218.638,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@33","time":53218.639,"message":"  accept: */*"}
{"type":"log","callId":"call@33","time":53218.64,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@33","time":53218.64,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@33","time":53218.641,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@33","time":53218.642,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@33","time":53218.643,"message":"  content-type: application/json"}
{"type":"log","callId":"call@33","time":53218.643,"message":"  content-length: 32"}
{"type":"log","callId":"call@35","time":53218.712,"message":"→ POST http://localhost:8000/api/v1/entities"}
{"type":"log","callId":"call@35","time":53218.713,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@35","time":53218.714,"message":"  accept: */*"}
{"type":"log","callId":"call@35","time":53218.715,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@35","time":53218.716,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@35","time":53218.716,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@35","time":53218.717,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@35","time":53218.718,"message":"  content-type: application/json"}
{"type":"log","callId":"call@35","time":53218.719,"message":"  content-length: 34"}
{"type":"log","callId":"call@37","time":53218.806,"message":"→ POST http://localhost:8000/api/v1/entities"}
{"type":"log","callId":"call@37","time":53218.808,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@37","time":53218.809,"message":"  accept: */*"}
{"type":"log","callId":"call@37","time":53218.809,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@37","time":53218.81,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@37","time":53218.811,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@37","time":53218.812,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@37","time":53218.812,"message":"  content-type: application/json"}
{"type":"log","callId":"call@37","time":53218.818,"message":"  content-length: 34"}
{"type":"log","callId":"call@27","time":53219.096,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@27","time":53219.099,"message":"  date: Sun, 06 Jul 2025 14:45:35 GMT"}
{"type":"log","callId":"call@27","time":53219.1,"message":"  server: uvicorn"}
{"type":"log","callId":"call@27","time":53219.101,"message":"  content-length: 0"}
{"type":"log","callId":"call@27","time":53219.102,"message":"  location: http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@27","time":53219.358,"message":"→ POST http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@27","time":53219.36,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@27","time":53219.361,"message":"  accept: */*"}
{"type":"log","callId":"call@27","time":53219.362,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@27","time":53219.363,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@27","time":53219.364,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@27","time":53219.365,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@27","time":53219.365,"message":"  content-type: application/json"}
{"type":"log","callId":"call@27","time":53219.366,"message":"  content-length: 34"}
{"type":"log","callId":"call@33","time":53219.826,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@33","time":53219.829,"message":"  date: Sun, 06 Jul 2025 14:45:35 GMT"}
{"type":"log","callId":"call@33","time":53219.83,"message":"  server: uvicorn"}
{"type":"log","callId":"call@33","time":53219.831,"message":"  content-length: 0"}
{"type":"log","callId":"call@33","time":53219.832,"message":"  location: http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@31","time":53219.93,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@31","time":53219.932,"message":"  date: Sun, 06 Jul 2025 14:45:35 GMT"}
{"type":"log","callId":"call@31","time":53219.933,"message":"  server: uvicorn"}
{"type":"log","callId":"call@31","time":53219.934,"message":"  content-length: 0"}
{"type":"log","callId":"call@31","time":53219.935,"message":"  location: http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@35","time":53220.017,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@35","time":53220.019,"message":"  date: Sun, 06 Jul 2025 14:45:35 GMT"}
{"type":"log","callId":"call@35","time":53220.02,"message":"  server: uvicorn"}
{"type":"log","callId":"call@35","time":53220.02,"message":"  content-length: 0"}
{"type":"log","callId":"call@35","time":53220.021,"message":"  location: http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@29","time":53220.118,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@29","time":53220.12,"message":"  date: Sun, 06 Jul 2025 14:45:35 GMT"}
{"type":"log","callId":"call@29","time":53220.121,"message":"  server: uvicorn"}
{"type":"log","callId":"call@29","time":53220.121,"message":"  content-length: 0"}
{"type":"log","callId":"call@29","time":53220.122,"message":"  location: http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@37","time":53220.266,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@37","time":53220.268,"message":"  date: Sun, 06 Jul 2025 14:45:35 GMT"}
{"type":"log","callId":"call@37","time":53220.269,"message":"  server: uvicorn"}
{"type":"log","callId":"call@37","time":53220.269,"message":"  content-length: 0"}
{"type":"log","callId":"call@37","time":53220.27,"message":"  location: http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@33","time":53220.446,"message":"→ POST http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@33","time":53220.454,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@33","time":53220.456,"message":"  accept: */*"}
{"type":"log","callId":"call@33","time":53220.456,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@33","time":53220.457,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@33","time":53220.458,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@33","time":53220.459,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@33","time":53220.46,"message":"  content-type: application/json"}
{"type":"log","callId":"call@33","time":53220.46,"message":"  content-length: 32"}
{"type":"log","callId":"call@31","time":53220.537,"message":"→ POST http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@31","time":53220.539,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@31","time":53220.54,"message":"  accept: */*"}
{"type":"log","callId":"call@31","time":53220.541,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@31","time":53220.542,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@31","time":53220.542,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@31","time":53220.543,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@31","time":53220.544,"message":"  content-type: application/json"}
{"type":"log","callId":"call@31","time":53220.545,"message":"  content-length: 34"}
{"type":"log","callId":"call@35","time":53220.597,"message":"→ POST http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@35","time":53220.598,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@35","time":53220.599,"message":"  accept: */*"}
{"type":"log","callId":"call@35","time":53220.6,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@35","time":53220.601,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@35","time":53220.602,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@35","time":53220.602,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@35","time":53220.603,"message":"  content-type: application/json"}
{"type":"log","callId":"call@35","time":53220.604,"message":"  content-length: 34"}
{"type":"log","callId":"call@29","time":53220.646,"message":"→ POST http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@29","time":53220.648,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@29","time":53220.649,"message":"  accept: */*"}
{"type":"log","callId":"call@29","time":53220.649,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@29","time":53220.65,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@29","time":53220.651,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@29","time":53220.652,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@29","time":53220.652,"message":"  content-type: application/json"}
{"type":"log","callId":"call@29","time":53220.653,"message":"  content-length: 33"}
{"type":"log","callId":"call@37","time":53220.791,"message":"→ POST http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@37","time":53220.792,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@37","time":53220.793,"message":"  accept: */*"}
{"type":"log","callId":"call@37","time":53220.794,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@37","time":53220.794,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@37","time":53220.795,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@37","time":53220.796,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@37","time":53220.796,"message":"  content-type: application/json"}
{"type":"log","callId":"call@37","time":53220.797,"message":"  content-length: 34"}
{"type":"log","callId":"call@27","time":53225.437,"message":"← 201 Created"}
{"type":"log","callId":"call@27","time":53225.44,"message":"  date: Sun, 06 Jul 2025 14:45:35 GMT"}
{"type":"log","callId":"call@27","time":53225.441,"message":"  server: uvicorn"}
{"type":"log","callId":"call@27","time":53225.442,"message":"  content-length: 115"}
{"type":"log","callId":"call@27","time":53225.443,"message":"  content-type: application/json"}
{"type":"after","callId":"call@27","endTime":53225.48,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/","status":201,"statusText":"Created","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"115"},{"name":"content-type","value":"application/json"}],"fetchUid":"6304b20b6362218c5e4b43d5f0d62bdc"}},"afterSnapshot":"after@call@27"}
{"type":"log","callId":"call@33","time":53225.902,"message":"← 201 Created"}
{"type":"log","callId":"call@33","time":53225.904,"message":"  date: Sun, 06 Jul 2025 14:45:35 GMT"}
{"type":"log","callId":"call@33","time":53225.905,"message":"  server: uvicorn"}
{"type":"log","callId":"call@33","time":53225.906,"message":"  content-length: 113"}
{"type":"log","callId":"call@33","time":53225.906,"message":"  content-type: application/json"}
{"type":"after","callId":"call@33","endTime":53225.925,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/","status":201,"statusText":"Created","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"113"},{"name":"content-type","value":"application/json"}],"fetchUid":"514d738afea81f461d3fed3d4409bcba"}},"afterSnapshot":"after@call@33"}
{"type":"log","callId":"call@35","time":53226.195,"message":"← 201 Created"}
{"type":"log","callId":"call@35","time":53226.197,"message":"  date: Sun, 06 Jul 2025 14:45:35 GMT"}
{"type":"log","callId":"call@35","time":53226.198,"message":"  server: uvicorn"}
{"type":"log","callId":"call@35","time":53226.199,"message":"  content-length: 115"}
{"type":"log","callId":"call@35","time":53226.199,"message":"  content-type: application/json"}
{"type":"after","callId":"call@35","endTime":53226.211,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/","status":201,"statusText":"Created","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"115"},{"name":"content-type","value":"application/json"}],"fetchUid":"b0db34987e66e63e396867c25db46789"}},"afterSnapshot":"after@call@35"}
{"type":"log","callId":"call@31","time":53226.29,"message":"← 201 Created"}
{"type":"log","callId":"call@31","time":53226.291,"message":"  date: Sun, 06 Jul 2025 14:45:35 GMT"}
{"type":"log","callId":"call@31","time":53226.292,"message":"  server: uvicorn"}
{"type":"log","callId":"call@31","time":53226.293,"message":"  content-length: 115"}
{"type":"log","callId":"call@31","time":53226.294,"message":"  content-type: application/json"}
{"type":"after","callId":"call@31","endTime":53226.304,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/","status":201,"statusText":"Created","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"115"},{"name":"content-type","value":"application/json"}],"fetchUid":"f4c11aa4d2f10a86306acd2111788414"}},"afterSnapshot":"after@call@31"}
{"type":"log","callId":"call@37","time":53226.374,"message":"← 201 Created"}
{"type":"log","callId":"call@37","time":53226.376,"message":"  date: Sun, 06 Jul 2025 14:45:35 GMT"}
{"type":"log","callId":"call@37","time":53226.377,"message":"  server: uvicorn"}
{"type":"log","callId":"call@37","time":53226.377,"message":"  content-length: 115"}
{"type":"log","callId":"call@37","time":53226.378,"message":"  content-type: application/json"}
{"type":"after","callId":"call@37","endTime":53226.387,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/","status":201,"statusText":"Created","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"115"},{"name":"content-type","value":"application/json"}],"fetchUid":"752061c69a51cef03026e5ef9b5b0d7a"}},"afterSnapshot":"after@call@37"}
{"type":"log","callId":"call@29","time":53226.445,"message":"← 201 Created"}
{"type":"log","callId":"call@29","time":53226.447,"message":"  date: Sun, 06 Jul 2025 14:45:35 GMT"}
{"type":"log","callId":"call@29","time":53226.447,"message":"  server: uvicorn"}
{"type":"log","callId":"call@29","time":53226.448,"message":"  content-length: 114"}
{"type":"log","callId":"call@29","time":53226.449,"message":"  content-type: application/json"}
{"type":"after","callId":"call@29","endTime":53226.46,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/","status":201,"statusText":"Created","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"114"},{"name":"content-type","value":"application/json"}],"fetchUid":"a1e39abc41b2d4a101fe30462e3cf563"}},"afterSnapshot":"after@call@29"}
{"type":"before","callId":"call@45","startTime":53228.659,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/connections","method":"POST","jsonData":"{\"from_entity_id\":483,\"to_entity_id\":488,\"multiplier\":10,\"unit_id\":1}","timeout":8000},"stepId":"pw:api@23","beforeSnapshot":"before@call@45"}
{"type":"before","callId":"call@47","startTime":53228.788,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/connections","method":"POST","jsonData":"{\"from_entity_id\":488,\"to_entity_id\":486,\"multiplier\":50,\"unit_id\":1}","timeout":8000},"stepId":"pw:api@24","beforeSnapshot":"before@call@47"}
{"type":"before","callId":"call@49","startTime":53228.885,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/connections","method":"POST","jsonData":"{\"from_entity_id\":486,\"to_entity_id\":487,\"multiplier\":0.1,\"unit_id\":1}","timeout":8000},"stepId":"pw:api@25","beforeSnapshot":"before@call@49"}
{"type":"before","callId":"call@51","startTime":53228.979,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/connections","method":"POST","jsonData":"{\"from_entity_id\":484,\"to_entity_id\":485,\"multiplier\":0.3,\"unit_id\":2}","timeout":8000},"stepId":"pw:api@26","beforeSnapshot":"before@call@51"}
{"type":"log","callId":"call@45","time":53229.149,"message":"→ POST http://localhost:8000/api/v1/connections"}
{"type":"log","callId":"call@45","time":53229.15,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@45","time":53229.152,"message":"  accept: */*"}
{"type":"log","callId":"call@45","time":53229.152,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@45","time":53229.153,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@45","time":53229.154,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@45","time":53229.155,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@45","time":53229.155,"message":"  content-type: application/json"}
{"type":"log","callId":"call@45","time":53229.156,"message":"  content-length: 69"}
{"type":"log","callId":"call@47","time":53229.252,"message":"→ POST http://localhost:8000/api/v1/connections"}
{"type":"log","callId":"call@47","time":53229.253,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@47","time":53229.254,"message":"  accept: */*"}
{"type":"log","callId":"call@47","time":53229.255,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@47","time":53229.256,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@47","time":53229.256,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@47","time":53229.257,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@47","time":53229.258,"message":"  content-type: application/json"}
{"type":"log","callId":"call@47","time":53229.259,"message":"  content-length: 69"}
{"type":"log","callId":"call@49","time":53229.322,"message":"→ POST http://localhost:8000/api/v1/connections"}
{"type":"log","callId":"call@49","time":53229.323,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@49","time":53229.324,"message":"  accept: */*"}
{"type":"log","callId":"call@49","time":53229.325,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@49","time":53229.326,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@49","time":53229.326,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@49","time":53229.327,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@49","time":53229.328,"message":"  content-type: application/json"}
{"type":"log","callId":"call@49","time":53229.329,"message":"  content-length: 70"}
{"type":"log","callId":"call@51","time":53229.383,"message":"→ POST http://localhost:8000/api/v1/connections"}
{"type":"log","callId":"call@51","time":53229.385,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@51","time":53229.386,"message":"  accept: */*"}
{"type":"log","callId":"call@51","time":53229.386,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@51","time":53229.387,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@51","time":53229.388,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@51","time":53229.389,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@51","time":53229.389,"message":"  content-type: application/json"}
{"type":"log","callId":"call@51","time":53229.39,"message":"  content-length: 70"}
{"type":"log","callId":"call@45","time":53229.786,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@45","time":53229.79,"message":"  date: Sun, 06 Jul 2025 14:45:35 GMT"}
{"type":"log","callId":"call@45","time":53229.793,"message":"  server: uvicorn"}
{"type":"log","callId":"call@45","time":53229.794,"message":"  content-length: 0"}
{"type":"log","callId":"call@45","time":53229.795,"message":"  location: http://localhost:8000/api/v1/connections/"}
{"type":"log","callId":"call@45","time":53229.949,"message":"→ POST http://localhost:8000/api/v1/connections/"}
{"type":"log","callId":"call@45","time":53229.952,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@45","time":53229.953,"message":"  accept: */*"}
{"type":"log","callId":"call@45","time":53229.954,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@45","time":53229.954,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@45","time":53229.955,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@45","time":53229.956,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@45","time":53229.957,"message":"  content-type: application/json"}
{"type":"log","callId":"call@45","time":53229.957,"message":"  content-length: 69"}
{"type":"log","callId":"call@47","time":53230.111,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@47","time":53230.113,"message":"  date: Sun, 06 Jul 2025 14:45:35 GMT"}
{"type":"log","callId":"call@47","time":53230.114,"message":"  server: uvicorn"}
{"type":"log","callId":"call@47","time":53230.115,"message":"  content-length: 0"}
{"type":"log","callId":"call@47","time":53230.116,"message":"  location: http://localhost:8000/api/v1/connections/"}
{"type":"log","callId":"call@49","time":53230.193,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@49","time":53230.195,"message":"  date: Sun, 06 Jul 2025 14:45:35 GMT"}
{"type":"log","callId":"call@49","time":53230.195,"message":"  server: uvicorn"}
{"type":"log","callId":"call@49","time":53230.196,"message":"  content-length: 0"}
{"type":"log","callId":"call@49","time":53230.197,"message":"  location: http://localhost:8000/api/v1/connections/"}
{"type":"log","callId":"call@51","time":53230.266,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@51","time":53230.267,"message":"  date: Sun, 06 Jul 2025 14:45:35 GMT"}
{"type":"log","callId":"call@51","time":53230.268,"message":"  server: uvicorn"}
{"type":"log","callId":"call@51","time":53230.269,"message":"  content-length: 0"}
{"type":"log","callId":"call@51","time":53230.27,"message":"  location: http://localhost:8000/api/v1/connections/"}
{"type":"log","callId":"call@47","time":53230.358,"message":"→ POST http://localhost:8000/api/v1/connections/"}
{"type":"log","callId":"call@47","time":53230.359,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@47","time":53230.36,"message":"  accept: */*"}
{"type":"log","callId":"call@47","time":53230.361,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@47","time":53230.362,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@47","time":53230.362,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@47","time":53230.363,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@47","time":53230.364,"message":"  content-type: application/json"}
{"type":"log","callId":"call@47","time":53230.365,"message":"  content-length: 69"}
{"type":"log","callId":"call@49","time":53230.441,"message":"→ POST http://localhost:8000/api/v1/connections/"}
{"type":"log","callId":"call@49","time":53230.443,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@49","time":53230.444,"message":"  accept: */*"}
{"type":"log","callId":"call@49","time":53230.444,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@49","time":53230.445,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@49","time":53230.446,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@49","time":53230.447,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@49","time":53230.447,"message":"  content-type: application/json"}
{"type":"log","callId":"call@49","time":53230.448,"message":"  content-length: 70"}
{"type":"log","callId":"call@51","time":53230.491,"message":"→ POST http://localhost:8000/api/v1/connections/"}
{"type":"log","callId":"call@51","time":53230.493,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@51","time":53230.494,"message":"  accept: */*"}
{"type":"log","callId":"call@51","time":53230.494,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@51","time":53230.495,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@51","time":53230.496,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@51","time":53230.497,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@51","time":53230.497,"message":"  content-type: application/json"}
{"type":"log","callId":"call@51","time":53230.498,"message":"  content-length: 70"}
{"type":"log","callId":"call@45","time":53236.255,"message":"← 201 Created"}
{"type":"log","callId":"call@45","time":53236.257,"message":"  date: Sun, 06 Jul 2025 14:45:35 GMT"}
{"type":"log","callId":"call@45","time":53236.258,"message":"  server: uvicorn"}
{"type":"log","callId":"call@45","time":53236.259,"message":"  content-length: 166"}
{"type":"log","callId":"call@45","time":53236.26,"message":"  content-type: application/json"}
{"type":"after","callId":"call@45","endTime":53236.288,"result":{"response":{"url":"http://localhost:8000/api/v1/connections/","status":201,"statusText":"Created","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"166"},{"name":"content-type","value":"application/json"}],"fetchUid":"4e1df591e2a66a6bac94830d96a72a04"}},"afterSnapshot":"after@call@45"}
{"type":"log","callId":"call@47","time":53236.583,"message":"← 201 Created"}
{"type":"log","callId":"call@47","time":53236.584,"message":"  date: Sun, 06 Jul 2025 14:45:35 GMT"}
{"type":"log","callId":"call@47","time":53236.585,"message":"  server: uvicorn"}
{"type":"log","callId":"call@47","time":53236.586,"message":"  content-length: 166"}
{"type":"log","callId":"call@47","time":53236.587,"message":"  content-type: application/json"}
{"type":"after","callId":"call@47","endTime":53236.599,"result":{"response":{"url":"http://localhost:8000/api/v1/connections/","status":201,"statusText":"Created","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"166"},{"name":"content-type","value":"application/json"}],"fetchUid":"5e85dea0e04a415885f18e7037ab231e"}},"afterSnapshot":"after@call@47"}
{"type":"log","callId":"call@51","time":53236.66,"message":"← 201 Created"}
{"type":"log","callId":"call@51","time":53236.662,"message":"  date: Sun, 06 Jul 2025 14:45:35 GMT"}
{"type":"log","callId":"call@51","time":53236.662,"message":"  server: uvicorn"}
{"type":"log","callId":"call@51","time":53236.663,"message":"  content-length: 165"}
{"type":"log","callId":"call@51","time":53236.664,"message":"  content-type: application/json"}
{"type":"after","callId":"call@51","endTime":53236.675,"result":{"response":{"url":"http://localhost:8000/api/v1/connections/","status":201,"statusText":"Created","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"165"},{"name":"content-type","value":"application/json"}],"fetchUid":"63603eb826f91ff3e3e8195efad96dc8"}},"afterSnapshot":"after@call@51"}
{"type":"log","callId":"call@49","time":53237.096,"message":"← 201 Created"}
{"type":"log","callId":"call@49","time":53237.098,"message":"  date: Sun, 06 Jul 2025 14:45:35 GMT"}
{"type":"log","callId":"call@49","time":53237.098,"message":"  server: uvicorn"}
{"type":"log","callId":"call@49","time":53237.1,"message":"  content-length: 165"}
{"type":"log","callId":"call@49","time":53237.101,"message":"  content-type: application/json"}
{"type":"after","callId":"call@49","endTime":53237.114,"result":{"response":{"url":"http://localhost:8000/api/v1/connections/","status":201,"statusText":"Created","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:35 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"165"},{"name":"content-type","value":"application/json"}],"fetchUid":"1ffaeec4ca6a421507b84520ed535337"}},"afterSnapshot":"after@call@49"}
{"type":"before","callId":"call@57","startTime":53242.979,"class":"Frame","method":"goto","params":{"url":"/","timeout":15000,"waitUntil":"load"},"stepId":"pw:api@27","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","beforeSnapshot":"before@call@57"}
{"type":"frame-snapshot","snapshot":{"callId":"call@57","snapshotName":"before@call@57","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"about:blank","html":["HTML",{},["HEAD",{},["BASE",{"href":"about:blank"}]],["BODY"]],"viewport":{"width":1280,"height":720},"timestamp":53244.219,"wallTime":1751813135081,"collectionTime":0.5,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@57","time":53244.739,"message":"navigating to \"http://localhost:3000/\", waiting until \"load\""}
{"type":"screencast-frame","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":53261.105,"frameSwapWallTime":1751813135096.942}
{"type":"console","messageType":"info","text":"%cDownload the React DevTools for a better development experience: https://reactjs.org/link/react-devtools font-weight:bold","args":[{"preview":"%cDownload the React DevTools for a better development experience: https://reactjs.org/link/react-devtools","value":"%cDownload the React DevTools for a better development experience: https://reactjs.org/link/react-devtools"},{"preview":"font-weight:bold","value":"font-weight:bold"}],"location":{"url":"http://localhost:3000/static/js/bundle.js","lineNumber":38990,"columnNumber":20},"time":53312.078,"pageId":"page@e14a07905c003d9bbc40b7420a22b30f"}
{"type":"after","callId":"call@57","endTime":53343.699,"result":{"response":"<Response>"},"afterSnapshot":"after@call@57"}
{"type":"screencast-frame","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":53343.985,"frameSwapWallTime":1751813135175.7158}
{"type":"console","messageType":"warning","text":"⚠️ React Router Future Flag Warning: React Router will begin wrapping state updates in `React.startTransition` in v7. You can use the `v7_startTransition` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_starttransition.","args":[{"preview":"⚠️ React Router Future Flag Warning: React Router will begin wrapping state updates in `React.startTransition` in v7. You can use the `v7_startTransition` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_starttransition.","value":"⚠️ React Router Future Flag Warning: React Router will begin wrapping state updates in `React.startTransition` in v7. You can use the `v7_startTransition` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_starttransition."}],"location":{"url":"http://localhost:3000/static/js/bundle.js","lineNumber":42276,"columnNumber":12},"time":53348.983,"pageId":"page@e14a07905c003d9bbc40b7420a22b30f"}
{"type":"console","messageType":"warning","text":"⚠️ React Router Future Flag Warning: Relative route resolution within Splat routes is changing in v7. You can use the `v7_relativeSplatPath` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath.","args":[{"preview":"⚠️ React Router Future Flag Warning: Relative route resolution within Splat routes is changing in v7. You can use the `v7_relativeSplatPath` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath.","value":"⚠️ React Router Future Flag Warning: Relative route resolution within Splat routes is changing in v7. You can use the `v7_relativeSplatPath` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath."}],"location":{"url":"http://localhost:3000/static/js/bundle.js","lineNumber":42276,"columnNumber":12},"time":53349.04,"pageId":"page@e14a07905c003d9bbc40b7420a22b30f"}
{"type":"console","messageType":"log","text":"API Request: GET /entities/ undefined","args":[{"preview":"API Request:","value":"API Request:"},{"preview":"GET","value":"GET"},{"preview":"/entities/","value":"/entities/"},{"preview":"undefined"}],"location":{"url":"http://localhost:3000/static/js/bundle.js","lineNumber":54565,"columnNumber":10},"time":53349.211,"pageId":"page@e14a07905c003d9bbc40b7420a22b30f"}
{"type":"console","messageType":"log","text":"API Request: GET /units/ undefined","args":[{"preview":"API Request:","value":"API Request:"},{"preview":"GET","value":"GET"},{"preview":"/units/","value":"/units/"},{"preview":"undefined"}],"location":{"url":"http://localhost:3000/static/js/bundle.js","lineNumber":54565,"columnNumber":10},"time":53349.272,"pageId":"page@e14a07905c003d9bbc40b7420a22b30f"}
{"type":"console","messageType":"log","text":"API Request: GET /entities/ undefined","args":[{"preview":"API Request:","value":"API Request:"},{"preview":"GET","value":"GET"},{"preview":"/entities/","value":"/entities/"},{"preview":"undefined"}],"location":{"url":"http://localhost:3000/static/js/bundle.js","lineNumber":54565,"columnNumber":10},"time":53349.304,"pageId":"page@e14a07905c003d9bbc40b7420a22b30f"}
{"type":"console","messageType":"log","text":"API Request: GET /units/ undefined","args":[{"preview":"API Request:","value":"API Request:"},{"preview":"GET","value":"GET"},{"preview":"/units/","value":"/units/"},{"preview":"undefined"}],"location":{"url":"http://localhost:3000/static/js/bundle.js","lineNumber":54565,"columnNumber":10},"time":53349.329,"pageId":"page@e14a07905c003d9bbc40b7420a22b30f"}
{"type":"frame-snapshot","snapshot":{"callId":"call@57","snapshotName":"after@call@57","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},["HEAD",{},["BASE",{"href":"http://localhost:3000/"}],"\n    ",["META",{"charset":"utf-8"}],"\n    ",["LINK",{"rel":"icon","href":"/favicon.ico"}],"\n    ",["META",{"name":"viewport","content":"width=device-width, initial-scale=1"}],"\n    ",["META",{"name":"theme-color","content":"#000000"}],"\n    ",["META",{"name":"description","content":"SIMILE - Compare entities based on measurable relationships"}],"\n    ",["LINK",{"rel":"apple-touch-icon","href":"/logo192.png"}],"\n    ",["LINK",{"rel":"manifest","href":"/manifest.json"}],"\n    ",["TITLE",{},"SIMILE"],"\n  ",["STYLE",{},"body {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9pbmRleC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxTQUFTO0VBQ1Q7O2NBRVk7RUFDWixtQ0FBbUM7RUFDbkMsa0NBQWtDO0FBQ3BDOztBQUVBO0VBQ0U7YUFDVztBQUNiIiwic291cmNlc0NvbnRlbnQiOlsiYm9keSB7XG4gIG1hcmdpbjogMDtcbiAgZm9udC1mYW1pbHk6IC1hcHBsZS1zeXN0ZW0sIEJsaW5rTWFjU3lzdGVtRm9udCwgJ1NlZ29lIFVJJywgJ1JvYm90bycsICdPeHlnZW4nLFxuICAgICdVYnVudHUnLCAnQ2FudGFyZWxsJywgJ0ZpcmEgU2FucycsICdEcm9pZCBTYW5zJywgJ0hlbHZldGljYSBOZXVlJyxcbiAgICBzYW5zLXNlcmlmO1xuICAtd2Via2l0LWZvbnQtc21vb3RoaW5nOiBhbnRpYWxpYXNlZDtcbiAgLW1vei1vc3gtZm9udC1zbW9vdGhpbmc6IGdyYXlzY2FsZTtcbn1cblxuY29kZSB7XG4gIGZvbnQtZmFtaWx5OiBzb3VyY2UtY29kZS1wcm8sIE1lbmxvLCBNb25hY28sIENvbnNvbGFzLCAnQ291cmllciBOZXcnLFxuICAgIG1vbm9zcGFjZTtcbn0iXSwic291cmNlUm9vdCI6IiJ9 */"],["STYLE",{},"/* Reset and base styles */\n* {\n  box-sizing: border-box;\n  margin: 0;\n  padding: 0;\n}\n\nbody {\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  background-color: #f5f5f5;\n}\n\n.App {\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n}\n\n/* Navigation */\n.navigation {\n  background-color: #282c34;\n  padding: 1rem 2rem;\n  color: white;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.nav-brand .brand-link {\n  color: white;\n  text-decoration: none;\n}\n\n.nav-brand h1 {\n  font-size: 1.8rem;\n  margin-bottom: 0.2rem;\n}\n\n.nav-brand p {\n  font-size: 0.9rem;\n  color: #61dafb;\n}\n\n.nav-links {\n  display: flex;\n  gap: 1.5rem;\n}\n\n.nav-link {\n  color: white;\n  text-decoration: none;\n  padding: 0.5rem 1rem;\n  border-radius: 4px;\n  transition: background-color 0.2s;\n}\n\n.nav-link:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n}\n\n.nav-link.active {\n  background-color: #61dafb;\n  color: #282c34;\n}\n\n/* Main content */\n.main-content {\n  flex: 1 1;\n  padding: 2rem;\n  max-width: 1200px;\n  margin: 0 auto;\n  width: 100%;\n}\n\n/* Manager headers */\n.manager-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 2rem;\n  padding-bottom: 1rem;\n  border-bottom: 2px solid #e0e0e0;\n}\n\n.manager-header h2 {\n  color: #333;\n  font-size: 1.8rem;\n}\n\n/* Buttons */\n.btn-primary, .btn-secondary, .btn-select, .btn-edit, .btn-delete {\n  padding: 0.5rem 1rem;\n  border-radius: 4px;\n  border: none;\n  cursor: pointer;\n  font-size: 0.9rem;\n  transition: background-color 0.2s;\n}\n\n.btn-primary {\n  background-color: #007bff;\n  color: white;\n}\n\n.btn-primary:hover:not(:disabled) {\n  background-color: #0056b3;\n}\n\n.btn-secondary {\n  background-color: #6c757d;\n  color: white;\n}\n\n.btn-secondary:hover:not(:disabled) {\n  background-color: #545b62;\n}\n\n.btn-select {\n  background-color: #28a745;\n  color: white;\n}\n\n.btn-edit {\n  background-color: #ffc107;\n  color: #212529;\n}\n\n.btn-delete {\n  background-color: #dc3545;\n  color: white;\n}\n\n.btn-delete:hover:not(:disabled) {\n  background-color: #c82333;\n}\n\nbutton:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n/* Cards and grids */\n.entity-grid, .connections-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 1rem;\n}\n\n.entity-card, .connection-card {\n  background: white;\n  border-radius: 8px;\n  padding: 1.5rem;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  border: 1px solid #e0e0e0;\n}\n\n.entity-card h4 {\n  margin-bottom: 0.5rem;\n  color: #333;\n}\n\n.entity-id {\n  color: #666;\n  font-size: 0.8rem;\n  margin-bottom: 1rem;\n}\n\n.entity-actions {\n  display: flex;\n  gap: 0.5rem;\n  flex-wrap: wrap;\n}\n\n.connection-relationship {\n  margin-bottom: 1rem;\n  font-size: 1.1rem;\n  line-height: 1.4;\n}\n\n.entity-name {\n  font-weight: bold;\n  color: #007bff;\n}\n\n.multiplier {\n  font-weight: bold;\n  color: #28a745;\n  font-size: 1.2em;\n}\n\n.relationship-text, .times-text {\n  color: #666;\n  margin: 0 0.3rem;\n}\n\n.unit-text {\n  color: #6c757d;\n  font-style: italic;\n}\n\n.connection-details {\n  display: flex;\n  gap: 1rem;\n  margin-bottom: 1rem;\n  font-size: 0.8rem;\n  color: #666;\n}\n\n/* Forms */\n.entity-form, .connection-form, .comparison-form {\n  background: white;\n  border-radius: 8px;\n  padding: 2rem;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  border: 1px solid #e0e0e0;\n}\n\n/* Form validation states */\n.form-input {\n  transition: border-color 0.2s, box-shadow 0.2s;\n}\n\n.form-input.valid {\n  border-color: #28a745;\n  box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.25);\n}\n\n.form-input.invalid {\n  border-color: #dc3545;\n  box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.25);\n}\n\n.validation-success {\n  color: #28a745;\n  font-weight: 500;\n  margin-left: 0.5rem;\n}\n\n.form-group {\n  margin-bottom: 1rem;\n}\n\n.form-row {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n}\n\n.form-group label {\n  display: block;\n  margin-bottom: 0.5rem;\n  font-weight: 500;\n  color: #333;\n}\n\n.form-group input, .form-group select {\n  width: 100%;\n  padding: 0.5rem;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  font-size: 1rem;\n}\n\n.form-group input:focus, .form-group select:focus {\n  outline: none;\n  border-color: #007bff;\n  box-shadow: 0 0 0 2px rgba(0,123,255,0.25);\n}\n\n.form-help {\n  display: block;\n  margin-top: 0.25rem;\n  color: #666;\n  font-size: 0.8rem;\n}\n\n.form-actions {\n  display: flex;\n  gap: 1rem;\n  margin-top: 1.5rem;\n}\n\n.form-note {\n  margin-top: 1rem;\n  padding: 1rem;\n  background-color: #f8f9fa;\n  border-radius: 4px;\n  border-left: 4px solid #007bff;\n}\n\n/* Previews */\n.connection-preview, .comparison-preview {\n  background-color: #f8f9fa;\n  padding: 1rem;\n  border-radius: 4px;\n  margin-bottom: 1.5rem;\n  border-left: 4px solid #28a745;\n}\n\n/* Results */\n.comparison-result {\n  background: white;\n  border-radius: 8px;\n  padding: 2rem;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  border: 1px solid #e0e0e0;\n  margin-top: 2rem;\n}\n\n.main-result {\n  text-align: center;\n  margin-bottom: 2rem;\n}\n\n.result-statement {\n  font-size: 1.5rem;\n  line-height: 1.6;\n  padding: 1rem;\n  background-color: #f8f9fa;\n  border-radius: 8px;\n  border: 2px solid #28a745;\n}\n\n/* Template-style result statement */\n.result-statement-template {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 3rem;\n  font-weight: 300;\n  line-height: 1.5;\n  padding: 3rem 2rem;\n  text-align: center;\n  letter-spacing: 0.02em;\n  margin-bottom: 2rem;\n}\n\n.template-prefix {\n  color: #d2691e; /* Orange/brown color */\n  margin-right: 0.3rem;\n}\n\n.template-from-count {\n  color: #333;\n  font-weight: 400;\n  border-bottom: 2px solid #333;\n  padding: 0.1rem 0.3rem;\n  margin: 0 0.4rem;\n}\n\n.template-from-entity {\n  color: #008B8B; /* Teal */\n  font-weight: 400;\n  border-bottom: 2px solid #333;\n  padding: 0.1rem 0.3rem;\n  margin: 0 0.4rem;\n}\n\n.template-relationship {\n  color: #333;\n  font-weight: 400;\n  margin: 0 0.4rem;\n}\n\n.template-multiplier {\n  color: #333;\n  font-weight: 400;\n  border-bottom: 2px solid #333;\n  padding: 0.1rem 0.3rem;\n  margin: 0 0.4rem;\n}\n\n.template-to-entity {\n  color: #808080; /* Gray */\n  font-weight: 400;\n  border-bottom: 2px solid #333;\n  padding: 0.1rem 0.3rem;\n  margin: 0 0.4rem;\n}\n\n.template-suffix {\n  color: #D2691E; /* Orange color */\n  font-weight: 400;\n  font-size: 3.2rem;\n  margin-left: 0.4rem;\n}\n\n.calculation-path {\n  margin-bottom: 2rem;\n}\n\n.calculation-path h4 {\n  margin-bottom: 1rem;\n  color: #333;\n}\n\n.path-steps {\n  background-color: #f8f9fa;\n  padding: 1rem;\n  border-radius: 8px;\n  margin-bottom: 1rem;\n}\n\n.path-step {\n  display: flex;\n  align-items: center;\n  margin-bottom: 0.5rem;\n}\n\n.step-number {\n  background-color: #007bff;\n  color: white;\n  width: 24px;\n  height: 24px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 0.8rem;\n  margin-right: 1rem;\n  flex-shrink: 0;\n}\n\n.step-content {\n  flex: 1 1;\n}\n\n.step-arrow {\n  text-align: center;\n  color: #666;\n  margin: 0.5rem 0;\n  font-size: 1.2rem;\n}\n\n.calculation-formula {\n  background-color: white;\n  padding: 1rem;\n  border-radius: 4px;\n  border: 1px solid #ddd;\n}\n\n.result-details {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n  padding-top: 1rem;\n  border-top: 1px solid #e0e0e0;\n}\n\n.detail-item {\n  color: #666;\n}\n\n/* Loading and error states */\n.loading {\n  text-align: center;\n  padding: 2rem;\n  color: #666;\n}\n\n.error {\n  background-color: #f8d7da;\n  color: #721c24;\n  padding: 1rem;\n  border-radius: 4px;\n  border: 1px solid #f5c6cb;\n}\n\n.error-message {\n  background-color: #f8d7da;\n  color: #721c24;\n  padding: 0.75rem;\n  border-radius: 4px;\n  border: 1px solid #f5c6cb;\n  margin-bottom: 1rem;\n}\n\n.no-data {\n  text-align: center;\n  padding: 2rem;\n  color: #666;\n}\n\n/* Responsive design */\n@media (max-width: 768px) {\n  .navigation {\n    flex-direction: column;\n    gap: 1rem;\n  }\n  \n  .nav-links {\n    gap: 1rem;\n  }\n  \n  .main-content {\n    padding: 1rem;\n  }\n  \n  .manager-header {\n    flex-direction: column;\n    gap: 1rem;\n    align-items: stretch;\n  }\n  \n  .entity-grid, .connections-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .form-row {\n    grid-template-columns: 1fr;\n  }\n  \n  .form-actions {\n    flex-direction: column;\n  }\n  \n  .result-details {\n    grid-template-columns: 1fr;\n  }\n  \n  .result-statement-template {\n    font-size: 2rem;\n    padding: 2rem 1rem;\n    line-height: 1.4;\n  }\n  \n  .result-statement-template .template-suffix {\n    font-size: 2.2rem;\n  }\n}\n\n/* Skeleton loading animations */\n.skeleton {\n  background: linear-gradient(\n    90deg,\n    #f0f0f0 25%,\n    #e0e0e0 50%,\n    #f0f0f0 75%\n  );\n  background-size: 200% 100%;\n  animation: skeleton-loading 1.5s infinite ease-in-out;\n  border-radius: 4px;\n  margin-bottom: 0.5rem;\n}\n\n@keyframes skeleton-loading {\n  0% {\n    background-position: 200% 0;\n  }\n  100% {\n    background-position: -200% 0;\n  }\n}\n\n.skeleton-card {\n  opacity: 0.7;\n}\n\n.skeleton-form {\n  opacity: 0.7;\n}\n\n.skeleton-form .skeleton-form-title {\n  margin-bottom: 1rem;\n}\n\n.skeleton-form .skeleton-label {\n  margin-bottom: 0.5rem;\n}\n\n.skeleton-form .skeleton-input {\n  margin-bottom: 1rem;\n}\n\n.skeleton-list {\n  opacity: 0.7;\n}\n\n.skeleton-list .skeleton-list-title {\n  margin-bottom: 1rem;\n}\n\n.skeleton-list-item {\n  margin-bottom: 1rem;\n  padding: 0.5rem 0;\n}\n\n.skeleton-list-item .skeleton:last-child {\n  margin-bottom: 0;\n}\n\n/* Loading states */\n.loading-overlay {\n  position: relative;\n}\n\n.loading-overlay::after {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(255, 255, 255, 0.7);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 8px;\n  z-index: 10;\n}\n\n.loading-spinner {\n  width: 24px;\n  height: 24px;\n  border: 2px solid #f3f3f3;\n  border-top: 2px solid #007bff;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* Error Boundary Styles */\n.error-boundary, .api-error-fallback {\n  background: white;\n  border: 1px solid #f5c6cb;\n  border-radius: 8px;\n  padding: 2rem;\n  margin: 1rem;\n  text-align: center;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n}\n\n.error-boundary h2, .api-error-fallback h3 {\n  color: #721c24;\n  margin-bottom: 1rem;\n}\n\n.error-boundary p, .api-error-fallback p {\n  color: #721c24;\n  margin-bottom: 1rem;\n}\n\n.api-error-fallback .error-icon {\n  font-size: 3rem;\n  margin-bottom: 1rem;\n}\n\n.api-error-fallback .error-message {\n  font-weight: 600;\n  font-size: 1.1rem;\n}\n\n.api-error-fallback .error-suggestion {\n  color: #6c757d;\n  font-size: 0.9rem;\n}\n\n.error-actions {\n  display: flex;\n  gap: 1rem;\n  justify-content: center;\n  margin-top: 1.5rem;\n}\n\n.error-details {\n  margin-top: 1.5rem;\n  text-align: left;\n  background: #f8f9fa;\n  border: 1px solid #dee2e6;\n  border-radius: 4px;\n  padding: 1rem;\n}\n\n.error-details summary {\n  cursor: pointer;\n  font-weight: 600;\n  margin-bottom: 0.5rem;\n}\n\n.error-details pre {\n  background: #ffffff;\n  border: 1px solid #e9ecef;\n  border-radius: 4px;\n  padding: 0.5rem;\n  overflow-x: auto;\n  font-size: 0.8rem;\n  margin: 0.5rem 0;\n}\n\n/* Template Form Styles */\n.template-form {\n  background: white;\n  border-radius: 8px;\n  padding: 3rem;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  border: 1px solid #e0e0e0;\n  margin-top: 1rem;\n}\n\n.template-sentence {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 3rem;\n  font-weight: 300;\n  line-height: 1.5;\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 0.6rem;\n  justify-content: center;\n  margin-bottom: 2rem;\n  letter-spacing: 0.02em;\n}\n\n.template-prefix {\n  color: #D2691E; /* Orange color to match template */\n  font-weight: 400;\n}\n\n.template-suffix {\n  color: #D2691E; /* Orange color to match template */\n  font-weight: 400;\n  font-size: 3.2rem;\n}\n\n.template-relationship {\n  color: #333;\n  font-weight: normal;\n}\n\n.template-input {\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 3rem;\n  font-weight: 300;\n  border: none !important;\n  border-bottom: 2px solid #333 !important;\n  border-radius: 0 !important;\n  padding: 0.1rem 0.2rem !important;\n  text-align: center;\n  min-width: 100px;\n  background: transparent !important;\n  outline: none !important;\n  box-shadow: none !important;\n  -webkit-box-shadow: none !important;\n  -moz-box-shadow: none !important;\n  appearance: none !important;\n}\n\n.template-input:focus {\n  outline: none;\n  border-bottom-color: #007bff;\n  box-shadow: none;\n}\n\n.template-count {\n  color: #333;\n  font-weight: 300;\n  width: 120px;\n  border: none !important;\n  border-bottom: 2px solid #333 !important;\n  background: transparent !important;\n  background-color: transparent !important;\n  margin: 0 0.1rem;\n  outline: none !important;\n  box-shadow: none !important;\n  -webkit-box-shadow: none !important;\n  appearance: none !important;\n  padding: 0 0.2rem !important;\n  line-height: 1.2 !important;\n  vertical-align: baseline !important;\n}\n\n.template-measure {\n  color: #333;\n  font-weight: 300;\n  min-width: 150px;\n  border: none !important;\n  border-bottom: 2px solid #333 !important;\n  background: transparent !important;\n  background-color: transparent !important;\n  margin: 0 0.1rem;\n  outline: none !important;\n  box-shadow: none !important;\n  -webkit-box-shadow: none !important;\n  appearance: none !important;\n  padding: 0 0.2rem !important;\n  line-height: 1.2 !important;\n  vertical-align: baseline !important;\n}\n\n.template-calculated-value {\n  color: #333;\n  font-weight: 300;\n  border-bottom: 2px solid #333;\n  min-width: 100px;\n  text-align: center;\n  padding: 0.1rem 0.3rem;\n  background: transparent;\n  display: inline-block;\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-size: 3rem;\n  margin: 0 0.2rem;\n}\n\n.template-dropdown {\n  position: relative;\n  display: inline-block;\n}\n\n/* Seamless entity input styling */\n.template-entity-input {\n  font-size: 3rem !important;\n  font-family: 'Helvetica Neue', 'Arial', sans-serif !important;\n  font-weight: 300 !important;\n  border: none !important;\n  border-bottom: 2px solid #333 !important;\n  border-radius: 0 !important;\n  border-top: none !important;\n  border-left: none !important;\n  border-right: none !important;\n  background: transparent !important;\n  background-color: transparent !important;\n  min-width: 180px;\n  text-align: center;\n  outline: none !important;\n  padding: 0 0.2rem !important;\n  margin: 0 0.1rem !important;\n  box-shadow: none !important;\n  -webkit-box-shadow: none !important;\n  -moz-box-shadow: none !important;\n  appearance: none !important;\n  line-height: 1.2 !important;\n  vertical-align: baseline !important;\n}\n\n/* Alternative: Contenteditable span approach */\n.template-entity-editable {\n  font-size: 3rem;\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n  font-weight: 300;\n  border-bottom: 2px solid #333;\n  background: transparent;\n  min-width: 180px;\n  text-align: center;\n  outline: none;\n  padding: 0 0.2rem;\n  margin: 0 0.1rem;\n  display: inline-block;\n  line-height: 1.2;\n  vertical-align: baseline;\n  cursor: text;\n}\n\n.template-entity-editable:focus {\n  border-bottom-color: #007bff;\n}\n\n.template-entity-editable:empty:before {\n  content: attr(data-placeholder);\n  color: #ccc;\n  font-style: italic;\n}\n\n.template-entity-input.from-entity {\n  color: #008B8B !important; /* Teal for first entity */\n}\n\n.template-entity-input.to-entity {\n  color: #808080 !important; /* Gray for second entity */\n}\n\n/* Make autocomplete dropdown match the input styling */\n.template-entity-input.autocomplete-input {\n  background: transparent !important;\n  box-shadow: none !important;\n  border: none !important;\n  border-bottom: 2px solid #333 !important;\n}\n\n.template-entity-input:focus {\n  border-bottom-color: #333 !important;\n  box-shadow: none !important;\n}\n\n/* Style placeholder text to match */\n.template-entity-input::placeholder {\n  color: #ccc !important;\n  font-weight: 300 !important;\n  font-style: italic !important;\n}\n\n.template-actions {\n  text-align: center;\n  margin-top: 1rem;\n}\n\n/* Additional underline styling */\n.template-input:hover {\n  border-bottom-color: #666;\n}\n\n.template-entity-input:focus {\n  border-bottom-color: #007bff !important;\n}\n\n.template-relationship {\n  color: #333;\n  font-weight: 400;\n  font-family: 'Helvetica Neue', 'Arial', sans-serif;\n}\n\n/* Template form responsive design */\n@media (max-width: 768px) {\n  .template-sentence {\n    font-size: 2rem;\n    justify-content: flex-start;\n    line-height: 1.4;\n    gap: 0.4rem;\n  }\n  \n  .template-input {\n    font-size: 2rem;\n    min-width: 80px;\n  }\n  \n  .template-count {\n    width: 90px;\n  }\n  \n  .template-measure {\n    min-width: 120px;\n  }\n  \n  .template-entity-input {\n    font-size: 2rem !important;\n    min-width: 140px;\n    font-weight: 300 !important;\n    padding: 0.1rem 0.2rem !important;\n  }\n  \n  .template-calculated-value {\n    font-size: 2rem;\n    font-weight: 300;\n    min-width: 80px;\n    padding: 0.1rem 0.2rem;\n  }\n  \n  .template-count {\n    font-weight: 300;\n    width: 100px;\n  }\n  \n  .template-measure {\n    font-weight: 300;\n    min-width: 120px;\n  }\n  \n  .template-suffix {\n    font-size: 2.2rem;\n  }\n}\n\n/* AutoComplete Styles */\n.autocomplete {\n  position: relative;\n  display: inline-block;\n  width: 100%;\n}\n\n.autocomplete-input {\n  width: 100%;\n  padding: 0.5rem;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  font-size: 1rem;\n}\n\n.autocomplete-input:focus {\n  outline: none;\n  border-color: #007bff;\n  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n/* Additional AutoComplete styling for template */\n.template-dropdown .autocomplete {\n  display: inline-block;\n  position: relative;\n}\n\n.template-dropdown .autocomplete-dropdown {\n  font-size: 1rem;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\n  font-weight: 400;\n  z-index: 1001;\n  min-width: 200px;\n  max-width: 300px;\n  border-radius: 4px;\n  margin-top: 0.5rem;\n}\n\n.autocomplete-dropdown {\n  position: absolute;\n  top: 100%;\n  left: 0;\n  right: 0;\n  background: white;\n  border: 1px solid #ddd;\n  border-top: none;\n  border-radius: 0 0 4px 4px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  max-height: 200px;\n  overflow-y: auto;\n  z-index: 1000;\n}\n\n.autocomplete-option {\n  padding: 0.5rem;\n  cursor: pointer;\n  border-bottom: 1px solid #f0f0f0;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.autocomplete-option:last-child {\n  border-bottom: none;\n}\n\n.autocomplete-option:hover,\n.autocomplete-option.highlighted {\n  background-color: #f8f9fa;\n}\n\n.autocomplete-option.highlighted {\n  background-color: #e3f2fd;\n}\n\n.option-name {\n  font-weight: 500;\n}\n\n.option-id {\n  font-size: 0.8rem;\n  color: #6c757d;\n}\n\n.autocomplete-no-results {\n  padding: 0.5rem;\n  color: #6c757d;\n  font-style: italic;\n  text-align: center;\n}\n\n/* AutoComplete enhanced states */\n.autocomplete-input-wrapper {\n  position: relative;\n  display: flex;\n  align-items: center;\n}\n\n.autocomplete-loading, \n.autocomplete-confirmed {\n  position: absolute;\n  right: 0.5rem;\n  font-size: 0.9rem;\n  pointer-events: none;\n}\n\n.autocomplete-confirmed {\n  color: #28a745;\n  font-weight: bold;\n}\n\n.autocomplete-loading {\n  animation: pulse 1.5s infinite;\n}\n\n@keyframes pulse {\n  0%, 100% { opacity: 1; }\n  50% { opacity: 0.5; }\n}\n\n.autocomplete.loading .autocomplete-input {\n  background-color: #f8f9fa;\n  cursor: wait;\n}\n\n.autocomplete.confirmed .autocomplete-input {\n  border-color: #28a745;\n  background-color: #f8fff8;\n}\n\n.autocomplete.error .autocomplete-input {\n  border-color: #dc3545;\n  background-color: #fff5f5;\n}\n\n.autocomplete-error {\n  color: #dc3545;\n  font-size: 0.875rem;\n  margin-top: 0.25rem;\n  padding-left: 0.5rem;\n}\n\n/* EditableSpan color styling */\n.template-entity-editable.from-entity {\n  color: #008B8B; /* Teal for first entity */\n}\n\n.template-entity-editable.to-entity {\n  color: #808080; /* Gray for second entity */\n}\n\n.template-entity-editable.placeholder {\n  color: #ccc;\n  font-style: italic;\n}\n\n.template-entity-editable:focus {\n  border-bottom-color: #333;\n  outline: none;\n}\n\n/* Remove input number controls */\ninput[type=\"number\"].template-count::-webkit-outer-spin-button,\ninput[type=\"number\"].template-count::-webkit-inner-spin-button {\n  -webkit-appearance: none;\n  margin: 0;\n}\n\ninput[type=\"number\"].template-count {\n  -moz-appearance: textfield;\n}\n\n/* Remove select dropdown styling */\nselect.template-measure {\n  background-image: none;\n  padding-right: 0.5rem;\n}\n\nselect.template-measure::-ms-expand {\n  display: none;\n}\n\n/* Ultra-seamless entity input styling */\n.template-entity-seamless {\n  font-size: 3rem !important;\n  font-family: 'Helvetica Neue', 'Arial', sans-serif !important;\n  font-weight: 300 !important;\n  border: none !important;\n  border-bottom: 2px solid #333 !important;\n  border-top: none !important;\n  border-left: none !important;\n  border-right: none !important;\n  background: transparent !important;\n  background-color: transparent !important;\n  background-image: none !important;\n  min-width: 180px;\n  text-align: center;\n  outline: none !important;\n  padding: 0 0.2rem !important;\n  margin: 0 0.1rem !important;\n  box-shadow: none !important;\n  -webkit-box-shadow: none !important;\n  -moz-box-shadow: none !important;\n  appearance: none !important;\n  line-height: 1.2 !important;\n  vertical-align: baseline !important;\n  border-radius: 0 !important;\n}\n\n.template-entity-seamless.autocomplete-input {\n  font-size: 3rem !important;\n  font-family: 'Helvetica Neue', 'Arial', sans-serif !important;\n  font-weight: 300 !important;\n  border: none !important;\n  border-bottom: 2px solid #333 !important;\n  background: transparent !important;\n  box-shadow: none !important;\n  outline: none !important;\n}\n\n.template-entity-seamless.from-entity {\n  color: #008B8B !important;\n}\n\n.template-entity-seamless.to-entity {\n  color: #808080 !important;\n}\n\n.template-entity-seamless:focus {\n  border: none !important;\n  border-bottom: 2px solid #333 !important;\n  box-shadow: none !important;\n  outline: none !important;\n}\n\n.template-entity-wrapper {\n  display: inline-block;\n  position: relative;\n}\n\n.template-entity-wrapper .autocomplete {\n  display: inline-block;\n  width: auto;\n}\n\n.template-entity-wrapper .autocomplete-dropdown {\n  font-size: 1rem;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;\n  font-weight: 400;\n}\n\n/* Ultra-seamless input styling - complete invisibility except underline */\n.template-input-seamless {\n  font-size: 3rem !important;\n  font-family: 'Helvetica Neue', 'Arial', sans-serif !important;\n  font-weight: 300 !important;\n  \n  /* Completely remove all borders and backgrounds */\n  border: none !important;\n  border-top: none !important;\n  border-left: none !important;\n  border-right: none !important;\n  border-bottom: 2px solid #333 !important;\n  border-radius: 0 !important;\n  \n  background: transparent !important;\n  background-color: transparent !important;\n  background-image: none !important;\n  \n  /* Remove all shadows and outlines */\n  box-shadow: none !important;\n  -webkit-box-shadow: none !important;\n  -moz-box-shadow: none !important;\n  outline: none !important;\n  \n  /* Remove browser default styling */\n  appearance: none !important;\n  \n  /* Positioning and sizing */\n  min-width: 180px;\n  max-width: 200px;\n  text-align: center;\n  padding: 0 0.2rem !important;\n  margin: 0 0.1rem !important;\n  line-height: 1.2 !important;\n  vertical-align: baseline !important;\n}\n\n/* Color variations */\n.template-input-seamless.from-entity {\n  color: #008B8B !important; /* Teal */\n}\n\n.template-input-seamless.to-entity {\n  color: #808080 !important; /* Gray */\n}\n\n/* Focus state - keep the same styling */\n.template-input-seamless:focus {\n  border: none !important;\n  border-bottom: 2px solid #333 !important;\n  background: transparent !important;\n  box-shadow: none !important;\n  outline: none !important;\n}\n\n/* Placeholder styling */\n.template-input-seamless::placeholder {\n  color: #ccc !important;\n  font-style: italic !important;\n  font-weight: 300 !important;\n}\n\n/* Remove any autofill styling */\n.template-input-seamless:-webkit-autofill,\n.template-input-seamless:-webkit-autofill:hover,\n.template-input-seamless:-webkit-autofill:focus {\n  -webkit-box-shadow: 0 0 0 1000px transparent inset !important;\n  -webkit-text-fill-color: inherit !important;\n  background-color: transparent !important;\n  background: transparent !important;\n}\n/*# sourceMappingURL=data:application/json;base64,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 */"]],"\n  ",["BODY",{},"\n    ","\n    ",["DIV",{"id":"root"},["DIV",{"class":"App"},["NAV",{"class":"navigation"},["DIV",{"class":"nav-brand"},["A",{"class":"brand-link","data-testid":"nav-brand-link","href":"/"},["H1",{},"SIMILE"],["P",{},"Entity Comparison System"]]],["DIV",{"class":"nav-links"},["A",{"class":"nav-link active","data-testid":"nav-compare-link","href":"/"},"Compare"],["A",{"class":"nav-link ","data-testid":"nav-entities-link","href":"/entities"},"Entities"],["A",{"class":"nav-link ","data-testid":"nav-connections-link","href":"/connections"},"Connections"]]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},["DIV",{"class":"manager-header"},["H2",{},"Entity Comparison"]],["DIV",{"class":"comparison-content"},["DIV",{"class":"skeleton-form"},["DIV",{"class":"skeleton skeleton-form-title","style":"width: 200px; height: 1.5rem;"}],["DIV",{"class":"form-row"},["DIV",{"class":"form-group"},["DIV",{"class":"skeleton skeleton-label","style":"width: 80px; height: 1rem;"}],["DIV",{"class":"skeleton skeleton-input","style":"width: 100%; height: 2.5rem;"}]],["DIV",{"class":"form-group"},["DIV",{"class":"skeleton skeleton-label","style":"width: 80px; height: 1rem;"}],["DIV",{"class":"skeleton skeleton-input","style":"width: 100%; height: 2.5rem;"}]],["DIV",{"class":"form-group"},["DIV",{"class":"skeleton skeleton-label","style":"width: 80px; height: 1rem;"}],["DIV",{"class":"skeleton skeleton-input","style":"width: 100%; height: 2.5rem;"}]]],["DIV",{"class":"form-actions"},["DIV",{"class":"skeleton ","style":"width: 100px; height: 2.5rem;"}],["DIV",{"class":"skeleton ","style":"width: 80px; height: 2.5rem;"}]]]]]]]],"\n  \n"]],"viewport":{"width":1280,"height":720},"timestamp":53351.145,"wallTime":1751813135187,"collectionTime":0.3999999910593033,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@59","startTime":53352.826,"title":"Wait for load state \"networkidle\"","class":"Page","method":"waitForEventInfo","params":{"info":{"waitId":"64116106d7837f135083ebeba59d432a","phase":"before","event":""}},"stepId":"pw:api@28","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","beforeSnapshot":"before@call@59"}
{"type":"console","messageType":"log","text":"API Response: GET /units/ 200 [Object, Object, Object, Object, Object]","args":[{"preview":"API Response:","value":"API Response:"},{"preview":"GET","value":"GET"},{"preview":"/units/","value":"/units/"},{"preview":"200","value":200},{"preview":"[Object, Object, Object, Object, Object]"}],"location":{"url":"http://localhost:3000/static/js/bundle.js","lineNumber":54575,"columnNumber":10},"time":53353.746,"pageId":"page@e14a07905c003d9bbc40b7420a22b30f"}
{"type":"frame-snapshot","snapshot":{"callId":"call@59","snapshotName":"before@call@59","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,66]],"viewport":{"width":1280,"height":720},"timestamp":53353.932,"wallTime":1751813135190,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"console","messageType":"log","text":"API Response: GET /entities/ 200 [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]","args":[{"preview":"API Response:","value":"API Response:"},{"preview":"GET","value":"GET"},{"preview":"/entities/","value":"/entities/"},{"preview":"200","value":200},{"preview":"[Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]"}],"location":{"url":"http://localhost:3000/static/js/bundle.js","lineNumber":54575,"columnNumber":10},"time":53354.078,"pageId":"page@e14a07905c003d9bbc40b7420a22b30f"}
{"type":"console","messageType":"log","text":"API Response: GET /units/ 200 [Object, Object, Object, Object, Object]","args":[{"preview":"API Response:","value":"API Response:"},{"preview":"GET","value":"GET"},{"preview":"/units/","value":"/units/"},{"preview":"200","value":200},{"preview":"[Object, Object, Object, Object, Object]"}],"location":{"url":"http://localhost:3000/static/js/bundle.js","lineNumber":54575,"columnNumber":10},"time":53356.588,"pageId":"page@e14a07905c003d9bbc40b7420a22b30f"}
{"type":"console","messageType":"log","text":"API Response: GET /entities/ 200 [Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]","args":[{"preview":"API Response:","value":"API Response:"},{"preview":"GET","value":"GET"},{"preview":"/entities/","value":"/entities/"},{"preview":"200","value":200},{"preview":"[Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, Object]"}],"location":{"url":"http://localhost:3000/static/js/bundle.js","lineNumber":54575,"columnNumber":10},"time":53356.732,"pageId":"page@e14a07905c003d9bbc40b7420a22b30f"}
{"type":"screencast-frame","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":53369.089,"frameSwapWallTime":1751813135204.7852}
{"type":"screencast-frame","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":53373.394,"frameSwapWallTime":1751813135208.984}
{"type":"log","callId":"call@59","time":53862.145,"message":"  \"networkidle\" event fired"}
{"type":"after","callId":"call@59","endTime":53862.163,"afterSnapshot":"after@call@59"}
{"type":"before","callId":"call@63","startTime":53862.451,"title":"Wait for load state \"networkidle\"","class":"Page","method":"waitForEventInfo","params":{"info":{"waitId":"0a05e030cfa02249f4be4deb5ae95c6f","phase":"before","event":""}},"stepId":"pw:api@29","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","beforeSnapshot":"before@call@63"}
{"type":"log","callId":"call@63","time":53862.561,"message":"  not waiting, \"networkidle\" event already fired"}
{"type":"after","callId":"call@63","endTime":53862.566,"afterSnapshot":"after@call@63"}
{"type":"before","callId":"call@67","startTime":53862.699,"class":"Frame","method":"waitForSelector","params":{"selector":"nav","timeout":10000},"stepId":"pw:api@30","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","beforeSnapshot":"before@call@67"}
{"type":"frame-snapshot","snapshot":{"callId":"call@59","snapshotName":"after@call@59","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[2,23]],[[2,24]],["BODY",{},[[2,25]],[[2,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[2,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[2,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},["H3",{},"Compare Entities"],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},["SPAN",{"class":"template-prefix"},"Did you know that"],["INPUT",{"__playwright_value_":"1","type":"number","class":"template-input template-count","min":"1","value":"1"}],["INPUT",{"__playwright_value_":"","type":"text","placeholder":"entity","class":"template-input-seamless from-entity","list":"from-entities","data-testid":"from-entity-input","value":""}],["DATALIST",{"id":"from-entities"},["OPTION",{"__playwright_selected_":"false","value":"A"}],["OPTION",{"__playwright_selected_":"false","value":"B"}],["OPTION",{"__playwright_selected_":"false","value":"Ball YDMXB"}],["OPTION",{"__playwright_selected_":"false","value":"Build YDMXC"}],["OPTION",{"__playwright_selected_":"false","value":"C"}],["OPTION",{"__playwright_selected_":"false","value":"Car YDMXD"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W D AAB D"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W D AAB Z"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W D AAC R"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W D AAC Z"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W D AAD L"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W D AAD V"}],["OPTION",{"__playwright_selected_":"false","value":"Eleph YDMXE"}],["OPTION",{"__playwright_selected_":"false","value":"Human YDMXA"}],["OPTION",{"__playwright_selected_":"false","value":"Mouse YDMXF"}]],["SPAN",{"class":"template-relationship"},"is as"],["SELECT",{"class":"template-input template-measure"},["OPTION",{"__playwright_selected_":"true","value":""},"measure"],["OPTION",{"__playwright_selected_":"false","value":"5"},"big"],["OPTION",{"__playwright_selected_":"false","value":"1"},"tall"],["OPTION",{"__playwright_selected_":"false","value":"2"},"heavy"],["OPTION",{"__playwright_selected_":"false","value":"4"},"long"],["OPTION",{"__playwright_selected_":"false","value":"3"},"voluminous"]],["SPAN",{"class":"template-relationship"},"as"],["SPAN",{"class":"template-calculated-value","data-testid":"comparison-result"},"?"],["INPUT",{"__playwright_value_":"","type":"text","placeholder":"entity","class":"template-input-seamless to-entity","list":"to-entities","data-testid":"to-entity-input","value":""}],["DATALIST",{"id":"to-entities"},["OPTION",{"__playwright_selected_":"false","value":"A"}],["OPTION",{"__playwright_selected_":"false","value":"B"}],["OPTION",{"__playwright_selected_":"false","value":"Ball YDMXB"}],["OPTION",{"__playwright_selected_":"false","value":"Build YDMXC"}],["OPTION",{"__playwright_selected_":"false","value":"C"}],["OPTION",{"__playwright_selected_":"false","value":"Car YDMXD"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W D AAB D"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W D AAB Z"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W D AAC R"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W D AAC Z"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W D AAD L"}],["OPTION",{"__playwright_selected_":"false","value":"Cleanu W D AAD V"}],["OPTION",{"__playwright_selected_":"false","value":"Eleph YDMXE"}],["OPTION",{"__playwright_selected_":"false","value":"Human YDMXA"}],["OPTION",{"__playwright_selected_":"false","value":"Mouse YDMXF"}]],["SPAN",{"class":"template-suffix"},"?"]],["DIV",{"class":"template-actions"},["BUTTON",{"type":"button","class":"btn-secondary"},"Clear"]]]]]]]]],[[2,64]]]],"viewport":{"width":1280,"height":720},"timestamp":53863.053,"wallTime":1751813135699,"collectionTime":0.29999999701976776,"resourceOverrides":[],"isMainFrame":true}}
{"type":"frame-snapshot","snapshot":{"callId":"call@63","snapshotName":"before@call@63","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,72]],"viewport":{"width":1280,"height":720},"timestamp":53863.159,"wallTime":1751813135700,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"frame-snapshot","snapshot":{"callId":"call@63","snapshotName":"after@call@63","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[2,72]],"viewport":{"width":1280,"height":720},"timestamp":53863.273,"wallTime":1751813135700,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"frame-snapshot","snapshot":{"callId":"call@67","snapshotName":"before@call@67","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[3,72]],"viewport":{"width":1280,"height":720},"timestamp":53863.422,"wallTime":1751813135700,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@67","time":53863.862,"message":"waiting for locator('nav') to be visible"}
{"type":"log","callId":"call@67","time":53871.844,"message":"  locator resolved to visible <nav class=\"navigation\">…</nav>"}
{"type":"after","callId":"call@67","endTime":53873.392,"result":{"element":"<ElementHandle>"},"afterSnapshot":"after@call@67"}
{"type":"frame-snapshot","snapshot":{"callId":"call@67","snapshotName":"after@call@67","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[4,72]],"viewport":{"width":1280,"height":720},"timestamp":53876.228,"wallTime":1751813135713,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@69","startTime":53877.598,"class":"Frame","method":"waitForSelector","params":{"selector":"[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"] >> nth=0","strict":true,"timeout":10000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@31","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","beforeSnapshot":"before@call@69"}
{"type":"frame-snapshot","snapshot":{"callId":"call@69","snapshotName":"before@call@69","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[5,72]],"viewport":{"width":1280,"height":720},"timestamp":53877.897,"wallTime":1751813135714,"collectionTime":0.19999998807907104,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@69","time":53878.118,"message":"waiting for locator('[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"]').first() to be visible"}
{"type":"log","callId":"call@69","time":53879.048,"message":"  locator resolved to visible <input value=\"\" type=\"text\" placeholder=\"entity\" list=\"from-entities\" data-testid=\"from-entity-input\" class=\"template-input-seamless from-entity\"/>"}
{"type":"after","callId":"call@69","endTime":53879.06,"result":{},"afterSnapshot":"after@call@69"}
{"type":"frame-snapshot","snapshot":{"callId":"call@69","snapshotName":"after@call@69","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[6,72]],"viewport":{"width":1280,"height":720},"timestamp":53879.321,"wallTime":1751813135716,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@71","startTime":53880.06,"class":"Frame","method":"click","params":{"selector":"[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"] >> nth=0","strict":true,"timeout":8000},"stepId":"pw:api@32","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","beforeSnapshot":"before@call@71"}
{"type":"frame-snapshot","snapshot":{"callId":"call@71","snapshotName":"before@call@71","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[7,72]],"viewport":{"width":1280,"height":720},"timestamp":53880.325,"wallTime":1751813135717,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@71","time":53880.652,"message":"waiting for locator('[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"]').first()"}
{"type":"log","callId":"call@71","time":53881.577,"message":"  locator resolved to <input value=\"\" type=\"text\" placeholder=\"entity\" list=\"from-entities\" data-testid=\"from-entity-input\" class=\"template-input-seamless from-entity\"/>"}
{"type":"log","callId":"call@71","time":53881.877,"message":"attempting click action"}
{"type":"log","callId":"call@71","time":53882.005,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"log","callId":"call@71","time":53890.488,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@71","time":53890.492,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@71","time":53890.699,"message":"  done scrolling"}
{"type":"input","callId":"call@71","point":{"x":853.98,"y":358.18},"inputSnapshot":"input@call@71"}
{"type":"frame-snapshot","snapshot":{"callId":"call@71","snapshotName":"input@call@71","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[10,23]],[[10,24]],["BODY",{},[[10,25]],[[10,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[10,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[10,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[8,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[8,3]],[[8,4]],["INPUT",{"__playwright_value_":"","__playwright_target__":"call@71","type":"text","placeholder":"entity","class":"template-input-seamless from-entity","list":"from-entities","data-testid":"from-entity-input","value":""}],[[8,21]],[[8,23]],[[8,36]],[[8,38]],[[8,40]],[[8,41]],[[8,57]],[[8,59]]],[[8,63]]]]]]]]],[[10,64]]]],"viewport":{"width":1280,"height":720},"timestamp":53891.401,"wallTime":1751813135728,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@71","time":53892.179,"message":"  performing click action"}
{"type":"log","callId":"call@71","time":53902.033,"message":"  click action done"}
{"type":"log","callId":"call@71","time":53902.039,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@71","time":53902.145,"message":"  navigations have finished"}
{"type":"after","callId":"call@71","endTime":53902.191,"point":{"x":853.98,"y":358.18},"afterSnapshot":"after@call@71"}
{"type":"frame-snapshot","snapshot":{"callId":"call@71","snapshotName":"after@call@71","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,10]],"viewport":{"width":1280,"height":720},"timestamp":53902.533,"wallTime":1751813135739,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@73","startTime":53903.298,"class":"Frame","method":"fill","params":{"selector":"[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"] >> nth=0","strict":true,"value":"","timeout":8000},"stepId":"pw:api@33","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","beforeSnapshot":"before@call@73"}
{"type":"frame-snapshot","snapshot":{"callId":"call@73","snapshotName":"before@call@73","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[2,10]],"viewport":{"width":1280,"height":720},"timestamp":53903.567,"wallTime":1751813135740,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@73","time":53903.775,"message":"waiting for locator('[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"]').first()"}
{"type":"log","callId":"call@73","time":53904.54,"message":"  locator resolved to <input value=\"\" type=\"text\" placeholder=\"entity\" list=\"from-entities\" data-testid=\"from-entity-input\" class=\"template-input-seamless from-entity\"/>"}
{"type":"log","callId":"call@73","time":53904.797,"message":"  fill(\"\")"}
{"type":"log","callId":"call@73","time":53904.801,"message":"attempting fill action"}
{"type":"input","callId":"call@73","inputSnapshot":"input@call@73"}
{"type":"frame-snapshot","snapshot":{"callId":"call@73","snapshotName":"input@call@73","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[13,23]],[[13,24]],["BODY",{},[[13,25]],[[13,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[13,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[13,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[11,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[11,3]],[[11,4]],["INPUT",{"__playwright_value_":"","__playwright_target__":"call@73","type":"text","placeholder":"entity","class":"template-input-seamless from-entity","list":"from-entities","data-testid":"from-entity-input","value":""}],[[11,21]],[[11,23]],[[11,36]],[[11,38]],[[11,40]],[[11,41]],[[11,57]],[[11,59]]],[[11,63]]]]]]]]],[[13,64]]]],"viewport":{"width":1280,"height":720},"timestamp":53905.103,"wallTime":1751813135742,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@73","time":53905.154,"message":"  waiting for element to be visible, enabled and editable"}
{"type":"after","callId":"call@73","endTime":53906.106,"afterSnapshot":"after@call@73"}
{"type":"frame-snapshot","snapshot":{"callId":"call@73","snapshotName":"after@call@73","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,10]],"viewport":{"width":1280,"height":720},"timestamp":53906.324,"wallTime":1751813135743,"collectionTime":0,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@75","startTime":53906.84,"class":"Frame","method":"waitForSelector","params":{"selector":"[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"] >> nth=0","strict":true,"timeout":8000,"state":"attached","omitReturnValue":true},"stepId":"pw:api@34","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","beforeSnapshot":"before@call@75"}
{"type":"frame-snapshot","snapshot":{"callId":"call@75","snapshotName":"before@call@75","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[2,10]],"viewport":{"width":1280,"height":720},"timestamp":53924.073,"wallTime":1751813135761,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@75","time":53924.346,"message":"waiting for locator('[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"]').first()"}
{"type":"log","callId":"call@75","time":53925.325,"message":"  locator resolved to visible <input value=\"\" type=\"text\" placeholder=\"entity\" list=\"from-entities\" data-testid=\"from-entity-input\" class=\"template-input-seamless from-entity\"/>"}
{"type":"after","callId":"call@75","endTime":53925.349,"result":{},"afterSnapshot":"after@call@75"}
{"type":"frame-snapshot","snapshot":{"callId":"call@75","snapshotName":"after@call@75","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[3,10]],"viewport":{"width":1280,"height":720},"timestamp":53925.683,"wallTime":1751813135762,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@77","startTime":53926.35,"class":"Frame","method":"fill","params":{"selector":"[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"] >> nth=0","strict":true,"value":"Human","timeout":8000},"stepId":"pw:api@35","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","beforeSnapshot":"before@call@77"}
{"type":"frame-snapshot","snapshot":{"callId":"call@77","snapshotName":"before@call@77","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[4,10]],"viewport":{"width":1280,"height":720},"timestamp":53926.623,"wallTime":1751813135763,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@77","time":53926.766,"message":"waiting for locator('[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"]').first()"}
{"type":"log","callId":"call@77","time":53927.409,"message":"  locator resolved to <input value=\"\" type=\"text\" placeholder=\"entity\" list=\"from-entities\" data-testid=\"from-entity-input\" class=\"template-input-seamless from-entity\"/>"}
{"type":"log","callId":"call@77","time":53927.667,"message":"  fill(\"Human\")"}
{"type":"log","callId":"call@77","time":53927.669,"message":"attempting fill action"}
{"type":"input","callId":"call@77","inputSnapshot":"input@call@77"}
{"type":"frame-snapshot","snapshot":{"callId":"call@77","snapshotName":"input@call@77","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[18,23]],[[18,24]],["BODY",{},[[18,25]],[[18,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[18,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[18,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[16,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[16,3]],[[16,4]],["INPUT",{"__playwright_value_":"","__playwright_target__":"call@77","type":"text","placeholder":"entity","class":"template-input-seamless from-entity","list":"from-entities","data-testid":"from-entity-input","value":""}],[[16,21]],[[16,23]],[[16,36]],[[16,38]],[[16,40]],[[16,41]],[[16,57]],[[16,59]]],[[16,63]]]]]]]]],[[18,64]]]],"viewport":{"width":1280,"height":720},"timestamp":53928.036,"wallTime":1751813135765,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@77","time":53928.081,"message":"  waiting for element to be visible, enabled and editable"}
{"type":"after","callId":"call@77","endTime":53929.781,"afterSnapshot":"after@call@77"}
{"type":"frame-snapshot","snapshot":{"callId":"call@77","snapshotName":"after@call@77","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[19,23]],[[19,24]],["BODY",{},[[19,25]],[[19,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[19,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[19,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[17,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[17,3]],[[17,4]],["INPUT",{"__playwright_value_":"Human","__playwright_scroll_left_":"15","__playwright_target__":"call@77","type":"text","placeholder":"entity","class":"template-input-seamless from-entity","list":"from-entities","data-testid":"from-entity-input","value":"Human"}],[[17,21]],[[17,23]],[[17,36]],[[17,38]],[[17,40]],[[17,41]],[[17,57]],[[17,59]]],[[17,63]]]]]]]]],[[19,64]]]],"viewport":{"width":1280,"height":720},"timestamp":53930.258,"wallTime":1751813135767,"collectionTime":0.10000000894069672,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@79","startTime":53930.797,"class":"Frame","method":"waitForSelector","params":{"selector":".autocomplete-dropdown","strict":true,"timeout":3000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@36","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","beforeSnapshot":"before@call@79"}
{"type":"frame-snapshot","snapshot":{"callId":"call@79","snapshotName":"before@call@79","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,10]],"viewport":{"width":1280,"height":720},"timestamp":53931.01,"wallTime":1751813135768,"collectionTime":0.10000000894069672,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@79","time":53931.123,"message":"waiting for locator('.autocomplete-dropdown') to be visible"}
{"type":"screencast-frame","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":53931.572,"frameSwapWallTime":1751813135767.0742}
{"type":"screencast-frame","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":53945.895,"frameSwapWallTime":1751813135781.403}
{"type":"screencast-frame","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":54450.194,"frameSwapWallTime":1751813136283.231}
{"type":"screencast-frame","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":54949.345,"frameSwapWallTime":1751813136782.896}
{"type":"screencast-frame","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":55450.505,"frameSwapWallTime":1751813137283.746}
{"type":"screencast-frame","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":55949.616,"frameSwapWallTime":1751813137782.981}
{"type":"screencast-frame","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":56448.815,"frameSwapWallTime":1751813138282.04}
{"type":"after","callId":"call@79","endTime":56932.033,"error":{"message":"Timeout 3000ms exceeded.","stack":"TimeoutError: Timeout 3000ms exceeded.\n    at ProgressController.run (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/progress.js:76:26)\n    at Frame.waitForSelector (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/frames.js:615:23)\n    at FrameDispatcher.waitForSelector (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/dispatchers/frameDispatcher.js:82:116)\n    at FrameDispatcher._handleCommand (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/dispatchers/dispatcher.js:88:40)\n    at DispatcherConnection.dispatch (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/dispatchers/dispatcher.js:309:39)","name":"TimeoutError"},"afterSnapshot":"after@call@79"}
{"type":"frame-snapshot","snapshot":{"callId":"call@79","snapshotName":"after@call@79","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[2,10]],"viewport":{"width":1280,"height":720},"timestamp":56933.286,"wallTime":1751813138770,"collectionTime":0.4000000059604645,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@81","startTime":56935.251,"class":"Frame","method":"waitForSelector","params":{"selector":".autocomplete-dropdown >> nth=0","strict":true,"timeout":3000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@37","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","beforeSnapshot":"before@call@81"}
{"type":"frame-snapshot","snapshot":{"callId":"call@81","snapshotName":"before@call@81","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[3,10]],"viewport":{"width":1280,"height":720},"timestamp":56935.62,"wallTime":1751813138772,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@81","time":56935.73,"message":"waiting for locator('.autocomplete-dropdown').first() to be visible"}
{"type":"screencast-frame","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":56945.836,"frameSwapWallTime":1751813138781.405}
{"type":"screencast-frame","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":57448.58,"frameSwapWallTime":1751813139282.585}
{"type":"screencast-frame","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":57947.768,"frameSwapWallTime":1751813139781.847}
{"type":"screencast-frame","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":58449.948,"frameSwapWallTime":1751813140283.0232}
{"type":"screencast-frame","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":58945.845,"frameSwapWallTime":1751813140781.322}
{"type":"screencast-frame","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":59449.602,"frameSwapWallTime":1751813141283.218}
{"type":"after","callId":"call@81","endTime":59935.813,"error":{"message":"Timeout 3000ms exceeded.","stack":"TimeoutError: Timeout 3000ms exceeded.\n    at ProgressController.run (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/progress.js:76:26)\n    at Frame.waitForSelector (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/frames.js:615:23)\n    at FrameDispatcher.waitForSelector (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/dispatchers/frameDispatcher.js:82:116)\n    at FrameDispatcher._handleCommand (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/dispatchers/dispatcher.js:88:40)\n    at DispatcherConnection.dispatch (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/dispatchers/dispatcher.js:309:39)","name":"TimeoutError"},"afterSnapshot":"after@call@81"}
{"type":"frame-snapshot","snapshot":{"callId":"call@81","snapshotName":"after@call@81","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[4,10]],"viewport":{"width":1280,"height":720},"timestamp":59936.363,"wallTime":1751813141773,"collectionTime":0,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@83","startTime":59937.154,"class":"Frame","method":"queryCount","params":{"selector":"datalist option"},"stepId":"pw:api@38","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","beforeSnapshot":"before@call@83"}
{"type":"frame-snapshot","snapshot":{"callId":"call@83","snapshotName":"before@call@83","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[5,10]],"viewport":{"width":1280,"height":720},"timestamp":59937.396,"wallTime":1751813141774,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"after","callId":"call@83","endTime":59938.04,"result":{"value":30},"afterSnapshot":"after@call@83"}
{"type":"frame-snapshot","snapshot":{"callId":"call@83","snapshotName":"after@call@83","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[6,10]],"viewport":{"width":1280,"height":720},"timestamp":59938.315,"wallTime":1751813141775,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@85","startTime":59938.823,"class":"Frame","method":"queryCount","params":{"selector":"datalist option >> internal:has-text=\"Human YDMXA\"i"},"stepId":"pw:api@39","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","beforeSnapshot":"before@call@85"}
{"type":"frame-snapshot","snapshot":{"callId":"call@85","snapshotName":"before@call@85","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[7,10]],"viewport":{"width":1280,"height":720},"timestamp":59939.094,"wallTime":1751813141776,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"after","callId":"call@85","endTime":59939.796,"result":{"value":0},"afterSnapshot":"after@call@85"}
{"type":"frame-snapshot","snapshot":{"callId":"call@85","snapshotName":"after@call@85","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[8,10]],"viewport":{"width":1280,"height":720},"timestamp":59940.03,"wallTime":1751813141777,"collectionTime":0.10000000894069672,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@87","startTime":59940.594,"class":"Frame","method":"fill","params":{"selector":"[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"] >> nth=0","strict":true,"value":"Human YDMXA","timeout":8000},"stepId":"pw:api@40","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","beforeSnapshot":"before@call@87"}
{"type":"frame-snapshot","snapshot":{"callId":"call@87","snapshotName":"before@call@87","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[9,10]],"viewport":{"width":1280,"height":720},"timestamp":59940.793,"wallTime":1751813141777,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@87","time":59940.914,"message":"waiting for locator('[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"]').first()"}
{"type":"log","callId":"call@87","time":59941.506,"message":"  locator resolved to <input type=\"text\" value=\"Human\" placeholder=\"entity\" list=\"from-entities\" data-testid=\"from-entity-input\" class=\"template-input-seamless from-entity\"/>"}
{"type":"log","callId":"call@87","time":59941.713,"message":"  fill(\"Human YDMXA\")"}
{"type":"log","callId":"call@87","time":59941.716,"message":"attempting fill action"}
{"type":"input","callId":"call@87","inputSnapshot":"input@call@87"}
{"type":"frame-snapshot","snapshot":{"callId":"call@87","snapshotName":"input@call@87","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[29,23]],[[29,24]],["BODY",{},[[29,25]],[[29,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[29,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[29,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[27,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[27,3]],[[27,4]],["INPUT",{"__playwright_value_":"Human","__playwright_scroll_left_":"15","__playwright_target__":"call@87","type":"text","placeholder":"entity","class":"template-input-seamless from-entity","list":"from-entities","data-testid":"from-entity-input","value":"Human"}],[[27,21]],[[27,23]],[[27,36]],[[27,38]],[[27,40]],[[27,41]],[[27,57]],[[27,59]]],[[27,63]]]]]]]]],[[29,64]]]],"viewport":{"width":1280,"height":720},"timestamp":59942.015,"wallTime":1751813141779,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@87","time":59942.05,"message":"  waiting for element to be visible, enabled and editable"}
{"type":"after","callId":"call@87","endTime":59943.553,"afterSnapshot":"after@call@87"}
{"type":"frame-snapshot","snapshot":{"callId":"call@87","snapshotName":"after@call@87","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[30,23]],[[30,24]],["BODY",{},[[30,25]],[[30,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[30,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[30,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[28,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[28,3]],[[28,4]],["INPUT",{"__playwright_value_":"Human YDMXA","__playwright_scroll_left_":"182","__playwright_target__":"call@87","type":"text","placeholder":"entity","class":"template-input-seamless from-entity","list":"from-entities","data-testid":"from-entity-input","value":"Human YDMXA"}],[[28,21]],[[28,23]],[[28,36]],[[28,38]],[[28,40]],[[28,41]],["DATALIST",{"id":"to-entities"},[[28,42]],[[28,43]],[[28,44]],[[28,45]],[[28,46]],[[28,47]],[[28,48]],[[28,49]],[[28,50]],[[28,51]],[[28,52]],[[28,53]],[[28,54]],[[28,56]]],[[28,59]]],[[28,63]]]]]]]]],[[30,64]]]],"viewport":{"width":1280,"height":720},"timestamp":59944.157,"wallTime":1751813141780,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@89","startTime":59944.679,"class":"Page","method":"keyboardPress","params":{"key":"Tab"},"stepId":"pw:api@41","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","beforeSnapshot":"before@call@89"}
{"type":"frame-snapshot","snapshot":{"callId":"call@89","snapshotName":"before@call@89","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,11]],"viewport":{"width":1280,"height":720},"timestamp":59944.937,"wallTime":1751813141782,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"screencast-frame","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":59945.506,"frameSwapWallTime":1751813141780.979}
{"type":"after","callId":"call@89","endTime":59945.838,"afterSnapshot":"after@call@89"}
{"type":"frame-snapshot","snapshot":{"callId":"call@89","snapshotName":"after@call@89","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[32,23]],[[32,24]],["BODY",{},[[32,25]],[[32,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[32,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[32,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[30,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[30,3]],[[30,4]],["INPUT",{"__playwright_value_":"Human YDMXA","__playwright_target__":"call@87","type":"text","placeholder":"entity","class":"template-input-seamless from-entity","list":"from-entities","data-testid":"from-entity-input","value":"Human YDMXA"}],[[30,21]],[[30,23]],[[30,36]],[[30,38]],[[30,40]],[[30,41]],[[2,1]],[[30,59]]],[[30,63]]]]]]]]],[[32,64]]]],"viewport":{"width":1280,"height":720},"timestamp":59946.12,"wallTime":1751813141783,"collectionTime":0,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@91","startTime":59946.781,"class":"Frame","method":"inputValue","params":{"selector":"[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"] >> nth=0","strict":true,"timeout":8000},"stepId":"pw:api@42","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","beforeSnapshot":"before@call@91"}
{"type":"frame-snapshot","snapshot":{"callId":"call@91","snapshotName":"before@call@91","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,10]],"viewport":{"width":1280,"height":720},"timestamp":59947.117,"wallTime":1751813141784,"collectionTime":0.10000000894069672,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@91","time":59947.719,"message":"waiting for locator('[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"]').first()"}
{"type":"log","callId":"call@91","time":59948.689,"message":"  locator resolved to <input type=\"text\" value=\"Human YDMXA\" placeholder=\"entity\" list=\"from-entities\" data-testid=\"from-entity-input\" class=\"template-input-seamless from-entity\"/>"}
{"type":"after","callId":"call@91","endTime":59948.715,"result":{"value":"Human YDMXA"},"afterSnapshot":"after@call@91"}
{"type":"frame-snapshot","snapshot":{"callId":"call@91","snapshotName":"after@call@91","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[34,23]],[[34,24]],["BODY",{},[[34,25]],[[34,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[34,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[34,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[32,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[32,3]],[[32,4]],["INPUT",{"__playwright_value_":"Human YDMXA","__playwright_target__":"call@91","type":"text","placeholder":"entity","class":"template-input-seamless from-entity","list":"from-entities","data-testid":"from-entity-input","value":"Human YDMXA"}],[[32,21]],[[32,23]],[[32,36]],[[32,38]],[[32,40]],[[32,41]],[[4,1]],[[32,59]]],[[32,63]]]]]]]]],[[34,64]]]],"viewport":{"width":1280,"height":720},"timestamp":59949.171,"wallTime":1751813141786,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@93","startTime":59950.01,"class":"Frame","method":"inputValue","params":{"selector":"[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"] >> nth=0","strict":true,"timeout":8000},"stepId":"pw:api@43","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","beforeSnapshot":"before@call@93"}
{"type":"frame-snapshot","snapshot":{"callId":"call@93","snapshotName":"before@call@93","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,10]],"viewport":{"width":1280,"height":720},"timestamp":59950.364,"wallTime":1751813141787,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@93","time":59950.601,"message":"waiting for locator('[data-testid=\"from-entity-input\"], input.from-entity, input[list=\"from-entities\"]').first()"}
{"type":"log","callId":"call@93","time":59951.006,"message":"  locator resolved to <input type=\"text\" value=\"Human YDMXA\" placeholder=\"entity\" list=\"from-entities\" data-testid=\"from-entity-input\" class=\"template-input-seamless from-entity\"/>"}
{"type":"after","callId":"call@93","endTime":59951.014,"result":{"value":"Human YDMXA"},"afterSnapshot":"after@call@93"}
{"type":"frame-snapshot","snapshot":{"callId":"call@93","snapshotName":"after@call@93","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[36,23]],[[36,24]],["BODY",{},[[36,25]],[[36,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[36,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[36,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[34,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[34,3]],[[34,4]],["INPUT",{"__playwright_value_":"Human YDMXA","__playwright_target__":"call@93","type":"text","placeholder":"entity","class":"template-input-seamless from-entity","list":"from-entities","data-testid":"from-entity-input","value":"Human YDMXA"}],[[34,21]],[[34,23]],[[34,36]],[[34,38]],[[34,40]],[[34,41]],[[6,1]],[[34,59]]],[[34,63]]]]]]]]],[[36,64]]]],"viewport":{"width":1280,"height":720},"timestamp":59951.305,"wallTime":1751813141788,"collectionTime":0.10000000894069672,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@95","startTime":59952.233,"class":"Frame","method":"click","params":{"selector":"[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"] >> nth=0","strict":true,"timeout":8000},"stepId":"pw:api@44","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","beforeSnapshot":"before@call@95"}
{"type":"frame-snapshot","snapshot":{"callId":"call@95","snapshotName":"before@call@95","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,10]],"viewport":{"width":1280,"height":720},"timestamp":59952.625,"wallTime":1751813141789,"collectionTime":0.10000000894069672,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@95","time":59952.804,"message":"waiting for locator('[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"]').first()"}
{"type":"log","callId":"call@95","time":59953.597,"message":"  locator resolved to <input value=\"\" type=\"text\" list=\"to-entities\" placeholder=\"entity\" data-testid=\"to-entity-input\" class=\"template-input-seamless to-entity\"/>"}
{"type":"log","callId":"call@95","time":59953.863,"message":"attempting click action"}
{"type":"log","callId":"call@95","time":59953.881,"message":"  waiting for element to be visible, enabled and stable"}
{"type":"screencast-frame","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":59962.902,"frameSwapWallTime":1751813141798.5078}
{"type":"log","callId":"call@95","time":59973.759,"message":"  element is visible, enabled and stable"}
{"type":"log","callId":"call@95","time":59973.763,"message":"  scrolling into view if needed"}
{"type":"log","callId":"call@95","time":59973.881,"message":"  done scrolling"}
{"type":"input","callId":"call@95","point":{"x":834.89,"y":442.37},"inputSnapshot":"input@call@95"}
{"type":"frame-snapshot","snapshot":{"callId":"call@95","snapshotName":"input@call@95","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[38,23]],[[38,24]],["BODY",{},[[38,25]],[[38,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[38,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[38,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[36,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[36,3]],[[36,4]],[[2,0]],[[36,21]],[[36,23]],[[36,36]],[[36,38]],[[36,40]],["INPUT",{"__playwright_value_":"","__playwright_target__":"call@95","type":"text","placeholder":"entity","class":"template-input-seamless to-entity","list":"to-entities","data-testid":"to-entity-input","value":""}],[[8,1]],[[36,59]]],[[36,63]]]]]]]]],[[38,64]]]],"viewport":{"width":1280,"height":720},"timestamp":59974.473,"wallTime":1751813141811,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@95","time":59974.838,"message":"  performing click action"}
{"type":"log","callId":"call@95","time":59975.834,"message":"  click action done"}
{"type":"log","callId":"call@95","time":59975.836,"message":"  waiting for scheduled navigations to finish"}
{"type":"log","callId":"call@95","time":59975.923,"message":"  navigations have finished"}
{"type":"after","callId":"call@95","endTime":59975.953,"point":{"x":834.89,"y":442.37},"afterSnapshot":"after@call@95"}
{"type":"frame-snapshot","snapshot":{"callId":"call@95","snapshotName":"after@call@95","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,10]],"viewport":{"width":1280,"height":720},"timestamp":59976.155,"wallTime":1751813141813,"collectionTime":0,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@97","startTime":59976.707,"class":"Frame","method":"fill","params":{"selector":"[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"] >> nth=0","strict":true,"value":"","timeout":8000},"stepId":"pw:api@45","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","beforeSnapshot":"before@call@97"}
{"type":"frame-snapshot","snapshot":{"callId":"call@97","snapshotName":"before@call@97","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[2,10]],"viewport":{"width":1280,"height":720},"timestamp":59976.911,"wallTime":1751813141814,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@97","time":59977.06,"message":"waiting for locator('[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"]').first()"}
{"type":"log","callId":"call@97","time":59977.593,"message":"  locator resolved to <input value=\"\" type=\"text\" list=\"to-entities\" placeholder=\"entity\" data-testid=\"to-entity-input\" class=\"template-input-seamless to-entity\"/>"}
{"type":"log","callId":"call@97","time":59977.732,"message":"  fill(\"\")"}
{"type":"log","callId":"call@97","time":59977.734,"message":"attempting fill action"}
{"type":"input","callId":"call@97","inputSnapshot":"input@call@97"}
{"type":"frame-snapshot","snapshot":{"callId":"call@97","snapshotName":"input@call@97","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[41,23]],[[41,24]],["BODY",{},[[41,25]],[[41,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[41,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[41,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[39,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[39,3]],[[39,4]],[[5,0]],[[39,21]],[[39,23]],[[39,36]],[[39,38]],[[39,40]],["INPUT",{"__playwright_value_":"","__playwright_target__":"call@97","type":"text","placeholder":"entity","class":"template-input-seamless to-entity","list":"to-entities","data-testid":"to-entity-input","value":""}],[[11,1]],[[39,59]]],[[39,63]]]]]]]]],[[41,64]]]],"viewport":{"width":1280,"height":720},"timestamp":59977.949,"wallTime":1751813141815,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@97","time":59977.974,"message":"  waiting for element to be visible, enabled and editable"}
{"type":"after","callId":"call@97","endTime":59978.472,"afterSnapshot":"after@call@97"}
{"type":"frame-snapshot","snapshot":{"callId":"call@97","snapshotName":"after@call@97","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,10]],"viewport":{"width":1280,"height":720},"timestamp":59978.654,"wallTime":1751813141815,"collectionTime":0,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@99","startTime":59979.127,"class":"Frame","method":"waitForSelector","params":{"selector":"[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"] >> nth=0","strict":true,"timeout":8000,"state":"attached","omitReturnValue":true},"stepId":"pw:api@46","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","beforeSnapshot":"before@call@99"}
{"type":"frame-snapshot","snapshot":{"callId":"call@99","snapshotName":"before@call@99","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[2,10]],"viewport":{"width":1280,"height":720},"timestamp":59979.286,"wallTime":1751813141816,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@99","time":59979.378,"message":"waiting for locator('[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"]').first()"}
{"type":"log","callId":"call@99","time":59979.808,"message":"  locator resolved to visible <input value=\"\" type=\"text\" list=\"to-entities\" placeholder=\"entity\" data-testid=\"to-entity-input\" class=\"template-input-seamless to-entity\"/>"}
{"type":"after","callId":"call@99","endTime":59979.818,"result":{},"afterSnapshot":"after@call@99"}
{"type":"frame-snapshot","snapshot":{"callId":"call@99","snapshotName":"after@call@99","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[3,10]],"viewport":{"width":1280,"height":720},"timestamp":59979.984,"wallTime":1751813141817,"collectionTime":0.10000000894069672,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@101","startTime":59980.431,"class":"Frame","method":"fill","params":{"selector":"[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"] >> nth=0","strict":true,"value":"Mouse","timeout":8000},"stepId":"pw:api@47","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","beforeSnapshot":"before@call@101"}
{"type":"frame-snapshot","snapshot":{"callId":"call@101","snapshotName":"before@call@101","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[4,10]],"viewport":{"width":1280,"height":720},"timestamp":59980.59,"wallTime":1751813141817,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@101","time":59980.694,"message":"waiting for locator('[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"]').first()"}
{"type":"log","callId":"call@101","time":59981.097,"message":"  locator resolved to <input value=\"\" type=\"text\" list=\"to-entities\" placeholder=\"entity\" data-testid=\"to-entity-input\" class=\"template-input-seamless to-entity\"/>"}
{"type":"log","callId":"call@101","time":59981.267,"message":"  fill(\"Mouse\")"}
{"type":"log","callId":"call@101","time":59981.269,"message":"attempting fill action"}
{"type":"input","callId":"call@101","inputSnapshot":"input@call@101"}
{"type":"frame-snapshot","snapshot":{"callId":"call@101","snapshotName":"input@call@101","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[46,23]],[[46,24]],["BODY",{},[[46,25]],[[46,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[46,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[46,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[44,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[44,3]],[[44,4]],[[10,0]],[[44,21]],[[44,23]],[[44,36]],[[44,38]],[[44,40]],["INPUT",{"__playwright_value_":"","__playwright_target__":"call@101","type":"text","placeholder":"entity","class":"template-input-seamless to-entity","list":"to-entities","data-testid":"to-entity-input","value":""}],[[16,1]],[[44,59]]],[[44,63]]]]]]]]],[[46,64]]]],"viewport":{"width":1280,"height":720},"timestamp":59981.456,"wallTime":1751813141818,"collectionTime":0.10000000894069672,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@101","time":59981.478,"message":"  waiting for element to be visible, enabled and editable"}
{"type":"after","callId":"call@101","endTime":59982.411,"afterSnapshot":"after@call@101"}
{"type":"frame-snapshot","snapshot":{"callId":"call@101","snapshotName":"after@call@101","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[47,23]],[[47,24]],["BODY",{},[[47,25]],[[47,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[47,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[47,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[45,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[45,3]],[[45,4]],[[11,0]],[[45,21]],[[45,23]],[[45,36]],[[45,38]],[[45,40]],["INPUT",{"__playwright_value_":"Mouse","__playwright_scroll_left_":"9","__playwright_target__":"call@101","type":"text","placeholder":"entity","class":"template-input-seamless to-entity","list":"to-entities","data-testid":"to-entity-input","value":"Mouse"}],[[17,1]],[[45,59]]],[[45,63]]]]]]]]],[[47,64]]]],"viewport":{"width":1280,"height":720},"timestamp":59982.64,"wallTime":1751813141819,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@103","startTime":59983.055,"class":"Frame","method":"waitForSelector","params":{"selector":".autocomplete-dropdown","strict":true,"timeout":3000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@48","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","beforeSnapshot":"before@call@103"}
{"type":"frame-snapshot","snapshot":{"callId":"call@103","snapshotName":"before@call@103","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,10]],"viewport":{"width":1280,"height":720},"timestamp":59983.223,"wallTime":1751813141820,"collectionTime":0.10000000894069672,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@103","time":59983.296,"message":"waiting for locator('.autocomplete-dropdown') to be visible"}
{"type":"screencast-frame","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":59996.413,"frameSwapWallTime":1751813141832.0798}
{"type":"screencast-frame","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":60498.11,"frameSwapWallTime":1751813142332.159}
{"type":"screencast-frame","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":60998.022,"frameSwapWallTime":1751813142831.6418}
{"type":"screencast-frame","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":61498.919,"frameSwapWallTime":1751813143332.953}
{"type":"screencast-frame","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":61999.228,"frameSwapWallTime":1751813143833.253}
{"type":"screencast-frame","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":62501.322,"frameSwapWallTime":1751813144332.628}
{"type":"after","callId":"call@103","endTime":62984.065,"error":{"message":"Timeout 3000ms exceeded.","stack":"TimeoutError: Timeout 3000ms exceeded.\n    at ProgressController.run (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/progress.js:76:26)\n    at Frame.waitForSelector (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/frames.js:615:23)\n    at FrameDispatcher.waitForSelector (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/dispatchers/frameDispatcher.js:82:116)\n    at FrameDispatcher._handleCommand (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/dispatchers/dispatcher.js:88:40)\n    at DispatcherConnection.dispatch (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/dispatchers/dispatcher.js:309:39)","name":"TimeoutError"},"afterSnapshot":"after@call@103"}
{"type":"frame-snapshot","snapshot":{"callId":"call@103","snapshotName":"after@call@103","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[2,10]],"viewport":{"width":1280,"height":720},"timestamp":62986.11,"wallTime":1751813144822,"collectionTime":0.7000000029802322,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@105","startTime":62987.932,"class":"Frame","method":"waitForSelector","params":{"selector":".autocomplete-dropdown >> nth=0","strict":true,"timeout":3000,"state":"visible","omitReturnValue":true},"stepId":"pw:api@49","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","beforeSnapshot":"before@call@105"}
{"type":"frame-snapshot","snapshot":{"callId":"call@105","snapshotName":"before@call@105","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[3,10]],"viewport":{"width":1280,"height":720},"timestamp":62988.789,"wallTime":1751813144825,"collectionTime":0.3999999910593033,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@105","time":62989.1,"message":"waiting for locator('.autocomplete-dropdown').first() to be visible"}
{"type":"screencast-frame","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":62995.676,"frameSwapWallTime":1751813144831.329}
{"type":"screencast-frame","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":63501.281,"frameSwapWallTime":1751813145334.031}
{"type":"screencast-frame","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":64001.024,"frameSwapWallTime":1751813145833.876}
{"type":"screencast-frame","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":64500.396,"frameSwapWallTime":1751813146333.369}
{"type":"screencast-frame","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":64998.607,"frameSwapWallTime":1751813146832.259}
{"type":"screencast-frame","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":65498.979,"frameSwapWallTime":1751813147332.4448}
{"type":"after","callId":"call@105","endTime":65988.696,"error":{"message":"Timeout 3000ms exceeded.","stack":"TimeoutError: Timeout 3000ms exceeded.\n    at ProgressController.run (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/progress.js:76:26)\n    at Frame.waitForSelector (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/frames.js:615:23)\n    at FrameDispatcher.waitForSelector (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/dispatchers/frameDispatcher.js:82:116)\n    at FrameDispatcher._handleCommand (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/dispatchers/dispatcher.js:88:40)\n    at DispatcherConnection.dispatch (/Users/<USER>/Documents/PROJECTS/HDS/SIMILE/simile-web-app/frontend/node_modules/playwright-core/lib/server/dispatchers/dispatcher.js:309:39)","name":"TimeoutError"},"afterSnapshot":"after@call@105"}
{"type":"frame-snapshot","snapshot":{"callId":"call@105","snapshotName":"after@call@105","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[4,10]],"viewport":{"width":1280,"height":720},"timestamp":65990.75,"wallTime":1751813147827,"collectionTime":0.5,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@107","startTime":65993.483,"class":"Frame","method":"queryCount","params":{"selector":"datalist option"},"stepId":"pw:api@50","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","beforeSnapshot":"before@call@107"}
{"type":"frame-snapshot","snapshot":{"callId":"call@107","snapshotName":"before@call@107","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[5,10]],"viewport":{"width":1280,"height":720},"timestamp":65994.35,"wallTime":1751813147831,"collectionTime":0.29999999701976776,"resourceOverrides":[],"isMainFrame":true}}
{"type":"after","callId":"call@107","endTime":65995.436,"result":{"value":29},"afterSnapshot":"after@call@107"}
{"type":"frame-snapshot","snapshot":{"callId":"call@107","snapshotName":"after@call@107","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[6,10]],"viewport":{"width":1280,"height":720},"timestamp":65996.052,"wallTime":1751813147833,"collectionTime":0.19999998807907104,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@109","startTime":65997.062,"class":"Frame","method":"queryCount","params":{"selector":"datalist option >> internal:has-text=\"Mouse YDMXF\"i"},"stepId":"pw:api@51","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","beforeSnapshot":"before@call@109"}
{"type":"screencast-frame","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":65997.262,"frameSwapWallTime":1751813147831.517}
{"type":"frame-snapshot","snapshot":{"callId":"call@109","snapshotName":"before@call@109","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[7,10]],"viewport":{"width":1280,"height":720},"timestamp":65997.396,"wallTime":1751813147834,"collectionTime":0.19999998807907104,"resourceOverrides":[],"isMainFrame":true}}
{"type":"after","callId":"call@109","endTime":65997.907,"result":{"value":0},"afterSnapshot":"after@call@109"}
{"type":"frame-snapshot","snapshot":{"callId":"call@109","snapshotName":"after@call@109","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[8,10]],"viewport":{"width":1280,"height":720},"timestamp":65998.226,"wallTime":1751813147835,"collectionTime":0,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@111","startTime":65998.819,"class":"Frame","method":"fill","params":{"selector":"[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"] >> nth=0","strict":true,"value":"Mouse YDMXF","timeout":8000},"stepId":"pw:api@52","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","beforeSnapshot":"before@call@111"}
{"type":"frame-snapshot","snapshot":{"callId":"call@111","snapshotName":"before@call@111","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[9,10]],"viewport":{"width":1280,"height":720},"timestamp":65999.083,"wallTime":1751813147836,"collectionTime":0,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@111","time":65999.194,"message":"waiting for locator('[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"]').first()"}
{"type":"log","callId":"call@111","time":65999.848,"message":"  locator resolved to <input type=\"text\" value=\"Mouse\" list=\"to-entities\" placeholder=\"entity\" data-testid=\"to-entity-input\" class=\"template-input-seamless to-entity\"/>"}
{"type":"log","callId":"call@111","time":66000.103,"message":"  fill(\"Mouse YDMXF\")"}
{"type":"log","callId":"call@111","time":66000.107,"message":"attempting fill action"}
{"type":"input","callId":"call@111","inputSnapshot":"input@call@111"}
{"type":"frame-snapshot","snapshot":{"callId":"call@111","snapshotName":"input@call@111","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[57,23]],[[57,24]],["BODY",{},[[57,25]],[[57,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[57,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[57,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[55,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[55,3]],[[55,4]],[[21,0]],[[55,21]],[[55,23]],[[55,36]],[[55,38]],[[55,40]],["INPUT",{"__playwright_value_":"Mouse","__playwright_scroll_left_":"9","__playwright_target__":"call@111","type":"text","placeholder":"entity","class":"template-input-seamless to-entity","list":"to-entities","data-testid":"to-entity-input","value":"Mouse"}],[[27,1]],[[55,59]]],[[55,63]]]]]]]]],[[57,64]]]],"viewport":{"width":1280,"height":720},"timestamp":66000.462,"wallTime":1751813147837,"collectionTime":0,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@111","time":66000.549,"message":"  waiting for element to be visible, enabled and editable"}
{"type":"after","callId":"call@111","endTime":66003.643,"afterSnapshot":"after@call@111"}
{"type":"frame-snapshot","snapshot":{"callId":"call@111","snapshotName":"after@call@111","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[58,23]],[[58,24]],["BODY",{},[[58,25]],[[58,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[58,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[58,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[56,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[56,3]],[[56,4]],[[22,0]],[[56,21]],[[56,23]],[[56,36]],[[56,38]],[[56,40]],["INPUT",{"__playwright_value_":"Mouse YDMXF","__playwright_scroll_left_":"172","__playwright_target__":"call@111","type":"text","placeholder":"entity","class":"template-input-seamless to-entity","list":"to-entities","data-testid":"to-entity-input","value":"Mouse YDMXF"}],[[28,1]],[[56,59]]],[[56,63]]]]]]]]],[[58,64]]]],"viewport":{"width":1280,"height":720},"timestamp":66004.072,"wallTime":1751813147841,"collectionTime":0.10000000894069672,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@113","startTime":66005.111,"class":"Page","method":"keyboardPress","params":{"key":"Tab"},"stepId":"pw:api@53","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","beforeSnapshot":"before@call@113"}
{"type":"frame-snapshot","snapshot":{"callId":"call@113","snapshotName":"before@call@113","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,10]],"viewport":{"width":1280,"height":720},"timestamp":66005.422,"wallTime":1751813147842,"collectionTime":0.19999998807907104,"resourceOverrides":[],"isMainFrame":true}}
{"type":"after","callId":"call@113","endTime":66006.28,"afterSnapshot":"after@call@113"}
{"type":"frame-snapshot","snapshot":{"callId":"call@113","snapshotName":"after@call@113","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[60,23]],[[60,24]],["BODY",{},[[60,25]],[[60,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[60,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[60,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[58,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[58,3]],[[58,4]],[[24,0]],[[58,21]],[[58,23]],[[58,36]],[[58,38]],[[58,40]],["INPUT",{"__playwright_value_":"Mouse YDMXF","__playwright_target__":"call@111","type":"text","placeholder":"entity","class":"template-input-seamless to-entity","list":"to-entities","data-testid":"to-entity-input","value":"Mouse YDMXF"}],[[30,1]],[[58,59]]],[[58,63]]]]]]]]],[[60,64]]]],"viewport":{"width":1280,"height":720},"timestamp":66009.043,"wallTime":1751813147843,"collectionTime":0,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@115","startTime":66009.675,"class":"Frame","method":"inputValue","params":{"selector":"[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"] >> nth=0","strict":true,"timeout":8000},"stepId":"pw:api@54","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","beforeSnapshot":"before@call@115"}
{"type":"frame-snapshot","snapshot":{"callId":"call@115","snapshotName":"before@call@115","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,10]],"viewport":{"width":1280,"height":720},"timestamp":66010.08,"wallTime":1751813147847,"collectionTime":0.20000000298023224,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@115","time":66010.304,"message":"waiting for locator('[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"]').first()"}
{"type":"log","callId":"call@115","time":66011.067,"message":"  locator resolved to <input type=\"text\" list=\"to-entities\" value=\"Mouse YDMXF\" placeholder=\"entity\" data-testid=\"to-entity-input\" class=\"template-input-seamless to-entity\"/>"}
{"type":"after","callId":"call@115","endTime":66011.082,"result":{"value":"Mouse YDMXF"},"afterSnapshot":"after@call@115"}
{"type":"frame-snapshot","snapshot":{"callId":"call@115","snapshotName":"after@call@115","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[62,23]],[[62,24]],["BODY",{},[[62,25]],[[62,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[62,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[62,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[60,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[60,3]],[[60,4]],[[26,0]],[[60,21]],[[60,23]],[[60,36]],[[60,38]],[[60,40]],["INPUT",{"__playwright_value_":"Mouse YDMXF","__playwright_target__":"call@115","type":"text","placeholder":"entity","class":"template-input-seamless to-entity","list":"to-entities","data-testid":"to-entity-input","value":"Mouse YDMXF"}],[[32,1]],[[60,59]]],[[60,63]]]]]]]]],[[62,64]]]],"viewport":{"width":1280,"height":720},"timestamp":66011.349,"wallTime":1751813147848,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@117","startTime":66011.899,"class":"Frame","method":"inputValue","params":{"selector":"[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"] >> nth=0","strict":true,"timeout":8000},"stepId":"pw:api@55","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","beforeSnapshot":"before@call@117"}
{"type":"frame-snapshot","snapshot":{"callId":"call@117","snapshotName":"before@call@117","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,10]],"viewport":{"width":1280,"height":720},"timestamp":66012.125,"wallTime":1751813147849,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@117","time":66012.305,"message":"waiting for locator('[data-testid=\"to-entity-input\"], input.to-entity, input[list=\"to-entities\"]').first()"}
{"type":"screencast-frame","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":66012.502,"frameSwapWallTime":1751813147847.904}
{"type":"log","callId":"call@117","time":66012.659,"message":"  locator resolved to <input type=\"text\" list=\"to-entities\" value=\"Mouse YDMXF\" placeholder=\"entity\" data-testid=\"to-entity-input\" class=\"template-input-seamless to-entity\"/>"}
{"type":"after","callId":"call@117","endTime":66012.669,"result":{"value":"Mouse YDMXF"},"afterSnapshot":"after@call@117"}
{"type":"frame-snapshot","snapshot":{"callId":"call@117","snapshotName":"after@call@117","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[64,23]],[[64,24]],["BODY",{},[[64,25]],[[64,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[64,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[64,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[62,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[62,3]],[[62,4]],[[28,0]],[[62,21]],[[62,23]],[[62,36]],[[62,38]],[[62,40]],["INPUT",{"__playwright_value_":"Mouse YDMXF","__playwright_target__":"call@117","type":"text","placeholder":"entity","class":"template-input-seamless to-entity","list":"to-entities","data-testid":"to-entity-input","value":"Mouse YDMXF"}],[[34,1]],[[62,59]]],[[62,63]]]]]]]]],[[64,64]]]],"viewport":{"width":1280,"height":720},"timestamp":66012.924,"wallTime":1751813147850,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@119","startTime":66013.512,"class":"Frame","method":"selectOption","params":{"selector":".template-measure, select.template-input","strict":true,"options":[{"label":"tall"}],"timeout":8000},"stepId":"pw:api@56","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","beforeSnapshot":"before@call@119"}
{"type":"frame-snapshot","snapshot":{"callId":"call@119","snapshotName":"before@call@119","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,10]],"viewport":{"width":1280,"height":720},"timestamp":66013.752,"wallTime":1751813147850,"collectionTime":0.10000000894069672,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@119","time":66013.915,"message":"waiting for locator('.template-measure, select.template-input')"}
{"type":"log","callId":"call@119","time":66014.5,"message":"  locator resolved to <select class=\"template-input template-measure\">…</select>"}
{"type":"log","callId":"call@119","time":66014.779,"message":"attempting select option action"}
{"type":"input","callId":"call@119","inputSnapshot":"input@call@119"}
{"type":"frame-snapshot","snapshot":{"callId":"call@119","snapshotName":"input@call@119","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[66,23]],[[66,24]],["BODY",{},[[66,25]],[[66,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[66,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[66,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[64,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[64,3]],[[64,4]],[[30,0]],[[64,21]],[[64,23]],["SELECT",{"__playwright_target__":"call@119","class":"template-input template-measure"},[[64,25]],[[64,27]],[[64,29]],[[64,31]],[[64,33]],[[64,35]]],[[64,38]],[[64,40]],[[2,0]],[[36,1]],[[64,59]]],[[64,63]]]]]]]]],[[66,64]]]],"viewport":{"width":1280,"height":720},"timestamp":66015.092,"wallTime":1751813147852,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@119","time":66015.141,"message":"  waiting for element to be visible and enabled"}
{"type":"log","callId":"call@119","time":66016.075,"message":"  selected specified option(s)"}
{"type":"after","callId":"call@119","endTime":66016.107,"result":{"values":["1"]},"afterSnapshot":"after@call@119"}
{"type":"frame-snapshot","snapshot":{"callId":"call@119","snapshotName":"after@call@119","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[67,23]],[[67,24]],["BODY",{},[[67,25]],[[67,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[67,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[67,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[65,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[65,3]],[[65,4]],[[31,0]],[[65,21]],[[65,23]],["SELECT",{"__playwright_target__":"call@119","class":"template-input template-measure"},["OPTION",{"__playwright_selected_":"false","value":""},[[65,24]]],[[65,27]],["OPTION",{"__playwright_selected_":"true","value":"1"},[[65,28]]],[[65,31]],[[65,33]],[[65,35]]],[[65,38]],[[65,40]],[[3,0]],[[37,1]],[[65,59]]],[[65,63]]]]]]]]],[[67,64]]]],"viewport":{"width":1280,"height":720},"timestamp":66016.496,"wallTime":1751813147853,"collectionTime":0.29999999701976776,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@121","startTime":66016.951,"class":"Frame","method":"waitForSelector","params":{"selector":".template-measure, select.template-input","strict":true,"timeout":8000,"state":"attached","omitReturnValue":true},"stepId":"pw:api@57","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","beforeSnapshot":"before@call@121"}
{"type":"frame-snapshot","snapshot":{"callId":"call@121","snapshotName":"before@call@121","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,12]],"viewport":{"width":1280,"height":720},"timestamp":66017.153,"wallTime":1751813147854,"collectionTime":0.10000000894069672,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@121","time":66017.255,"message":"waiting for locator('.template-measure, select.template-input')"}
{"type":"log","callId":"call@121","time":66017.735,"message":"  locator resolved to visible <select class=\"template-input template-measure\">…</select>"}
{"type":"after","callId":"call@121","endTime":66017.748,"result":{},"afterSnapshot":"after@call@121"}
{"type":"frame-snapshot","snapshot":{"callId":"call@121","snapshotName":"after@call@121","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[2,12]],"viewport":{"width":1280,"height":720},"timestamp":66017.939,"wallTime":1751813147855,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@123","startTime":66018.897,"class":"Page","method":"waitForEventInfo","params":{"info":{"waitId":"cda09082369f6c7632b25ce8ebf4b4c0","phase":"before","event":"response"}},"stepId":"pw:api@58","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","beforeSnapshot":"before@call@123"}
{"type":"before","callId":"call@126","startTime":66019.067,"class":"Frame","method":"waitForFunction","params":{"expression":"() => {\n      const calculatedValue = document.querySelector('[data-testid=\"comparison-result\"], .template-calculated-value');\n      return calculatedValue && calculatedValue.textContent !== '?' && calculatedValue.textContent !== '...';\n    }","isFunction":true,"arg":{"value":{"o":[{"k":"timeout","v":{"n":10000}}],"id":1},"handles":[]},"timeout":8000},"stepId":"pw:api@59","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","beforeSnapshot":"before@call@126"}
{"type":"frame-snapshot","snapshot":{"callId":"call@123","snapshotName":"before@call@123","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[3,12]],"viewport":{"width":1280,"height":720},"timestamp":66019.164,"wallTime":1751813147856,"collectionTime":0.09999999403953552,"resourceOverrides":[],"isMainFrame":true}}
{"type":"frame-snapshot","snapshot":{"callId":"call@126","snapshotName":"before@call@126","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[4,12]],"viewport":{"width":1280,"height":720},"timestamp":66019.251,"wallTime":1751813147856,"collectionTime":0,"resourceOverrides":[],"isMainFrame":true}}
{"type":"screencast-frame","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":66028.653,"frameSwapWallTime":1751813147864.296}
{"type":"console","messageType":"log","text":"API Request: GET /compare/ undefined","args":[{"preview":"API Request:","value":"API Request:"},{"preview":"GET","value":"GET"},{"preview":"/compare/","value":"/compare/"},{"preview":"undefined"}],"location":{"url":"http://localhost:3000/static/js/bundle.js","lineNumber":54565,"columnNumber":10},"time":66118.139,"pageId":"page@e14a07905c003d9bbc40b7420a22b30f"}
{"type":"console","messageType":"log","text":"API Response: GET /compare/ 200 {from_entity: Object, to_entity: Object, unit: Object, multiplier: 50.000, path: Array(3)}","args":[{"preview":"API Response:","value":"API Response:"},{"preview":"GET","value":"GET"},{"preview":"/compare/","value":"/compare/"},{"preview":"200","value":200},{"preview":"{from_entity: Object, to_entity: Object, unit: Object, multiplier: 50.000, path: Array(3)}"}],"location":{"url":"http://localhost:3000/static/js/bundle.js","lineNumber":54575,"columnNumber":10},"time":66126.492,"pageId":"page@e14a07905c003d9bbc40b7420a22b30f"}
{"type":"screencast-frame","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":66128.442,"frameSwapWallTime":1751813147964.171}
{"type":"after","callId":"call@126","endTime":66142.188,"result":{"handle":"<JSHandle>"},"afterSnapshot":"after@call@126"}
{"type":"frame-snapshot","snapshot":{"callId":"call@126","snapshotName":"after@call@126","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":["HTML",{"lang":"en"},[[72,23]],[[72,24]],["BODY",{},[[72,25]],[[72,26]],["DIV",{"id":"root"},["DIV",{"class":"App"},[[72,40]],["MAIN",{"class":"main-content"},["DIV",{"class":"comparison-manager"},[[72,43]],["DIV",{"class":"comparison-content"},["DIV",{"class":"comparison-form"},[[70,1]],["DIV",{"class":"template-form"},["DIV",{"class":"template-sentence"},[[70,3]],[[70,4]],[[36,0]],[[70,21]],[[70,23]],[[5,2]],[[70,38]],["SPAN",{"class":"template-calculated-value","data-testid":"comparison-result"},"50.0"],[[8,0]],[[42,1]],[[70,59]]],[[70,63]]]],["DIV",{"class":"result-section"},["DIV",{"class":"comparison-result"},["DIV",{"class":"main-result"},["DIV",{"class":"result-statement-template"},["SPAN",{"class":"template-prefix"},"Did you know that"],["SPAN",{"class":"template-from-count"},"1"],["SPAN",{"class":"template-from-entity"},"Human YDMXA"],["SPAN",{"class":"template-relationship"},"is as tall as"],["SPAN",{"class":"template-multiplier"},"50"],["SPAN",{"class":"template-to-entity"},"Mouse YDMXF"],["SPAN",{"class":"template-suffix"},"?"]]],["DIV",{"class":"calculation-path"},["H4",{},"Calculation Path:"],["DIV",{"class":"path-steps"},["DIV",{"class":"path-step"},["DIV",{"class":"step-number"},"1"],["DIV",{"class":"step-content"},["SPAN",{"class":"entity-name"},"Human YDMXA"],["SPAN",{"class":"relationship-text"},"is"],["SPAN",{"class":"multiplier"},"10","x"],["SPAN",{"class":"entity-name"},"Ball YDMXB"]],["DIV",{"class":"step-arrow"},"↓"]],["DIV",{"class":"path-step"},["DIV",{"class":"step-number"},"2"],["DIV",{"class":"step-content"},["SPAN",{"class":"entity-name"},"Ball YDMXB"],["SPAN",{"class":"relationship-text"},"is"],["SPAN",{"class":"multiplier"},"50","x"],["SPAN",{"class":"entity-name"},"Build YDMXC"]],["DIV",{"class":"step-arrow"},"↓"]],["DIV",{"class":"path-step"},["DIV",{"class":"step-number"},"3"],["DIV",{"class":"step-content"},["SPAN",{"class":"entity-name"},"Build YDMXC"],["SPAN",{"class":"relationship-text"},"is"],["SPAN",{"class":"multiplier"},"0.1","x"],["SPAN",{"class":"entity-name"},"Mouse YDMXF"]]]],["DIV",{"class":"calculation-formula"},["H5",{},"Final Calculation:"],["P",{},"10 × 50 × 0.1"," = ","50"]]],["DIV",{"class":"result-details"},["DIV",{"class":"detail-item"},["STRONG",{},"From:"]," ","Human YDMXA"," (ID: ","483",")"],["DIV",{"class":"detail-item"},["STRONG",{},"To:"]," ","Mouse YDMXF"," (ID: ","487",")"],["DIV",{"class":"detail-item"},["STRONG",{},"Unit:"]," ","Length"," (","m",")"],["DIV",{"class":"detail-item"},["STRONG",{},"Path Length:"]," ","3"," hop","s"]]]]]]]]],[[72,64]]]],"viewport":{"width":1280,"height":720},"timestamp":66142.943,"wallTime":1751813147979,"collectionTime":0.4000000059604645,"resourceOverrides":[],"isMainFrame":true}}
{"type":"screencast-frame","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":66148.896,"frameSwapWallTime":1751813147984.458}
{"type":"before","callId":"call@128","startTime":66717.842,"class":"Page","method":"screenshot","params":{"timeout":5000,"type":"png","caret":"initial"},"stepId":"pw:api@61","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","beforeSnapshot":"before@call@128"}
{"type":"frame-snapshot","snapshot":{"callId":"call@128","snapshotName":"before@call@128","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[1,115]],"viewport":{"width":1280,"height":720},"timestamp":66718.472,"wallTime":1751813148555,"collectionTime":0.19999998807907104,"resourceOverrides":[],"isMainFrame":true}}
{"type":"log","callId":"call@128","time":66718.764,"message":"taking page screenshot"}
{"type":"log","callId":"call@128","time":66719.156,"message":"waiting for fonts to load..."}
{"type":"log","callId":"call@128","time":66719.504,"message":"fonts loaded"}
{"type":"screencast-frame","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","sha1":"<EMAIL>","width":1280,"height":720,"timestamp":66724.701,"frameSwapWallTime":1751813148560.2468}
{"type":"after","callId":"call@128","endTime":66740.368,"result":{"binary":"<Buffer>"},"afterSnapshot":"after@call@128"}
{"type":"frame-snapshot","snapshot":{"callId":"call@128","snapshotName":"after@call@128","pageId":"page@e14a07905c003d9bbc40b7420a22b30f","frameId":"frame@7c40d707465c4b75dee9e5154d9ae2a6","frameUrl":"http://localhost:3000/","doctype":"html","html":[[2,115]],"viewport":{"width":1280,"height":720},"timestamp":66740.804,"wallTime":1751813148577,"collectionTime":0.10000000894069672,"resourceOverrides":[],"isMainFrame":true}}
{"type":"before","callId":"call@130","startTime":66742.12,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities/483","method":"DELETE","timeout":8000},"stepId":"pw:api@63","beforeSnapshot":"before@call@130"}
{"type":"log","callId":"call@130","time":66742.492,"message":"→ DELETE http://localhost:8000/api/v1/entities/483"}
{"type":"log","callId":"call@130","time":66742.497,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@130","time":66742.499,"message":"  accept: */*"}
{"type":"log","callId":"call@130","time":66742.513,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@130","time":66742.515,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@130","time":66742.517,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@130","time":66742.518,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@130","time":66746.027,"message":"← 200 OK"}
{"type":"log","callId":"call@130","time":66746.03,"message":"  date: Sun, 06 Jul 2025 14:45:48 GMT"}
{"type":"log","callId":"call@130","time":66746.031,"message":"  server: uvicorn"}
{"type":"log","callId":"call@130","time":66746.032,"message":"  content-length: 41"}
{"type":"log","callId":"call@130","time":66746.033,"message":"  content-type: application/json"}
{"type":"after","callId":"call@130","endTime":66746.071,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/483","status":200,"statusText":"OK","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:48 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"fetchUid":"0da9b16624900a5499b7ba519f7b1b5a"}},"afterSnapshot":"after@call@130"}
{"type":"before","callId":"call@132","startTime":66773.485,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities/484","method":"DELETE","timeout":8000},"stepId":"pw:api@64","beforeSnapshot":"before@call@132"}
{"type":"log","callId":"call@132","time":66773.845,"message":"→ DELETE http://localhost:8000/api/v1/entities/484"}
{"type":"log","callId":"call@132","time":66773.849,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@132","time":66773.851,"message":"  accept: */*"}
{"type":"log","callId":"call@132","time":66773.852,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@132","time":66773.852,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@132","time":66773.853,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@132","time":66773.854,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@132","time":66781.458,"message":"← 200 OK"}
{"type":"log","callId":"call@132","time":66781.461,"message":"  date: Sun, 06 Jul 2025 14:45:48 GMT"}
{"type":"log","callId":"call@132","time":66781.462,"message":"  server: uvicorn"}
{"type":"log","callId":"call@132","time":66781.462,"message":"  content-length: 41"}
{"type":"log","callId":"call@132","time":66781.463,"message":"  content-type: application/json"}
{"type":"after","callId":"call@132","endTime":66781.491,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/484","status":200,"statusText":"OK","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:48 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"fetchUid":"03c589c977c5ffc1d83a085a4dd772d1"}},"afterSnapshot":"after@call@132"}
{"type":"before","callId":"call@134","startTime":66808.213,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities/485","method":"DELETE","timeout":8000},"stepId":"pw:api@65","beforeSnapshot":"before@call@134"}
{"type":"log","callId":"call@134","time":66808.57,"message":"→ DELETE http://localhost:8000/api/v1/entities/485"}
{"type":"log","callId":"call@134","time":66808.575,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@134","time":66808.576,"message":"  accept: */*"}
{"type":"log","callId":"call@134","time":66808.577,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@134","time":66808.578,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@134","time":66808.578,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@134","time":66808.579,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@134","time":66816.532,"message":"← 200 OK"}
{"type":"log","callId":"call@134","time":66816.536,"message":"  date: Sun, 06 Jul 2025 14:45:48 GMT"}
{"type":"log","callId":"call@134","time":66816.537,"message":"  server: uvicorn"}
{"type":"log","callId":"call@134","time":66816.538,"message":"  content-length: 41"}
{"type":"log","callId":"call@134","time":66816.539,"message":"  content-type: application/json"}
{"type":"after","callId":"call@134","endTime":66816.573,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/485","status":200,"statusText":"OK","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:48 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"fetchUid":"827a86c9d6224b1ebd6d9856200dbf2c"}},"afterSnapshot":"after@call@134"}
{"type":"before","callId":"call@136","startTime":66843.46,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities/487","method":"DELETE","timeout":8000},"stepId":"pw:api@66","beforeSnapshot":"before@call@136"}
{"type":"log","callId":"call@136","time":66843.814,"message":"→ DELETE http://localhost:8000/api/v1/entities/487"}
{"type":"log","callId":"call@136","time":66843.818,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@136","time":66843.82,"message":"  accept: */*"}
{"type":"log","callId":"call@136","time":66843.821,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@136","time":66843.822,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@136","time":66843.822,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@136","time":66843.823,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@136","time":66851.601,"message":"← 200 OK"}
{"type":"log","callId":"call@136","time":66851.603,"message":"  date: Sun, 06 Jul 2025 14:45:48 GMT"}
{"type":"log","callId":"call@136","time":66851.604,"message":"  server: uvicorn"}
{"type":"log","callId":"call@136","time":66851.605,"message":"  content-length: 41"}
{"type":"log","callId":"call@136","time":66851.606,"message":"  content-type: application/json"}
{"type":"after","callId":"call@136","endTime":66851.639,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/487","status":200,"statusText":"OK","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:48 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"fetchUid":"5cc20a670652c3f18a9e2173f6f9ca14"}},"afterSnapshot":"after@call@136"}
{"type":"before","callId":"call@138","startTime":66878.477,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities/486","method":"DELETE","timeout":8000},"stepId":"pw:api@67","beforeSnapshot":"before@call@138"}
{"type":"log","callId":"call@138","time":66878.805,"message":"→ DELETE http://localhost:8000/api/v1/entities/486"}
{"type":"log","callId":"call@138","time":66878.808,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@138","time":66878.81,"message":"  accept: */*"}
{"type":"log","callId":"call@138","time":66878.81,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@138","time":66878.811,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@138","time":66878.812,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@138","time":66878.813,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@138","time":66886.325,"message":"← 200 OK"}
{"type":"log","callId":"call@138","time":66886.328,"message":"  date: Sun, 06 Jul 2025 14:45:48 GMT"}
{"type":"log","callId":"call@138","time":66886.329,"message":"  server: uvicorn"}
{"type":"log","callId":"call@138","time":66886.33,"message":"  content-length: 41"}
{"type":"log","callId":"call@138","time":66886.33,"message":"  content-type: application/json"}
{"type":"after","callId":"call@138","endTime":66886.363,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/486","status":200,"statusText":"OK","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:48 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"fetchUid":"61b3e72a1cbae250bd2b2856cd9de6b3"}},"afterSnapshot":"after@call@138"}
{"type":"before","callId":"call@140","startTime":66913.191,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities/488","method":"DELETE","timeout":8000},"stepId":"pw:api@68","beforeSnapshot":"before@call@140"}
{"type":"log","callId":"call@140","time":66913.555,"message":"→ DELETE http://localhost:8000/api/v1/entities/488"}
{"type":"log","callId":"call@140","time":66913.56,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@140","time":66913.562,"message":"  accept: */*"}
{"type":"log","callId":"call@140","time":66913.563,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@140","time":66913.563,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@140","time":66913.564,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@140","time":66913.565,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@140","time":66918.904,"message":"← 200 OK"}
{"type":"log","callId":"call@140","time":66918.908,"message":"  date: Sun, 06 Jul 2025 14:45:48 GMT"}
{"type":"log","callId":"call@140","time":66918.909,"message":"  server: uvicorn"}
{"type":"log","callId":"call@140","time":66918.91,"message":"  content-length: 41"}
{"type":"log","callId":"call@140","time":66918.911,"message":"  content-type: application/json"}
{"type":"after","callId":"call@140","endTime":66918.956,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/488","status":200,"statusText":"OK","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:48 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"41"},{"name":"content-type","value":"application/json"}],"fetchUid":"1f6f693a22323187660b887fe2d61de6"}},"afterSnapshot":"after@call@140"}
{"type":"before","callId":"call@142","startTime":66919.552,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities","method":"GET","timeout":8000},"stepId":"pw:api@69","beforeSnapshot":"before@call@142"}
{"type":"log","callId":"call@142","time":66919.813,"message":"→ GET http://localhost:8000/api/v1/entities"}
{"type":"log","callId":"call@142","time":66919.816,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@142","time":66919.818,"message":"  accept: */*"}
{"type":"log","callId":"call@142","time":66919.818,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@142","time":66919.819,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@142","time":66919.82,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@142","time":66919.821,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@142","time":66920.774,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@142","time":66920.776,"message":"  date: Sun, 06 Jul 2025 14:45:48 GMT"}
{"type":"log","callId":"call@142","time":66920.777,"message":"  server: uvicorn"}
{"type":"log","callId":"call@142","time":66920.778,"message":"  content-length: 0"}
{"type":"log","callId":"call@142","time":66920.779,"message":"  location: http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@142","time":66920.887,"message":"→ GET http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@142","time":66920.889,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@142","time":66920.89,"message":"  accept: */*"}
{"type":"log","callId":"call@142","time":66920.891,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@142","time":66920.892,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@142","time":66920.893,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@142","time":66920.894,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@142","time":66925.106,"message":"← 200 OK"}
{"type":"log","callId":"call@142","time":66925.109,"message":"  date: Sun, 06 Jul 2025 14:45:48 GMT"}
{"type":"log","callId":"call@142","time":66925.11,"message":"  server: uvicorn"}
{"type":"log","callId":"call@142","time":66925.111,"message":"  content-length: 1039"}
{"type":"log","callId":"call@142","time":66925.112,"message":"  content-type: application/json"}
{"type":"after","callId":"call@142","endTime":66925.149,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/","status":200,"statusText":"OK","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:48 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"1039"},{"name":"content-type","value":"application/json"}],"fetchUid":"11bddbb1ebeb1e4e258450d1dc24a864"}},"afterSnapshot":"after@call@142"}
{"type":"before","callId":"call@145","startTime":66925.802,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities","method":"GET","timeout":8000},"stepId":"pw:api@70","beforeSnapshot":"before@call@145"}
{"type":"log","callId":"call@145","time":66926.01,"message":"→ GET http://localhost:8000/api/v1/entities"}
{"type":"log","callId":"call@145","time":66926.014,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@145","time":66926.016,"message":"  accept: */*"}
{"type":"log","callId":"call@145","time":66926.017,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@145","time":66926.018,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@145","time":66926.019,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@145","time":66926.02,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@145","time":66926.953,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@145","time":66926.955,"message":"  date: Sun, 06 Jul 2025 14:45:48 GMT"}
{"type":"log","callId":"call@145","time":66926.957,"message":"  server: uvicorn"}
{"type":"log","callId":"call@145","time":66926.957,"message":"  content-length: 0"}
{"type":"log","callId":"call@145","time":66926.958,"message":"  location: http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@145","time":66927.052,"message":"→ GET http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@145","time":66927.054,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@145","time":66927.055,"message":"  accept: */*"}
{"type":"log","callId":"call@145","time":66927.056,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@145","time":66927.057,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@145","time":66927.058,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@145","time":66927.059,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@145","time":66930.595,"message":"← 200 OK"}
{"type":"log","callId":"call@145","time":66930.598,"message":"  date: Sun, 06 Jul 2025 14:45:48 GMT"}
{"type":"log","callId":"call@145","time":66930.599,"message":"  server: uvicorn"}
{"type":"log","callId":"call@145","time":66930.6,"message":"  content-length: 1039"}
{"type":"log","callId":"call@145","time":66930.601,"message":"  content-type: application/json"}
{"type":"after","callId":"call@145","endTime":66930.647,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/","status":200,"statusText":"OK","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:48 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"1039"},{"name":"content-type","value":"application/json"}],"fetchUid":"b3def781a518ce034bbc4a05a9a61e17"}},"afterSnapshot":"after@call@145"}
{"type":"before","callId":"call@148","startTime":66931.747,"class":"APIRequestContext","method":"fetch","params":{"url":"http://localhost:8000/api/v1/entities","method":"GET","timeout":8000},"stepId":"pw:api@71","beforeSnapshot":"before@call@148"}
{"type":"log","callId":"call@148","time":66932.005,"message":"→ GET http://localhost:8000/api/v1/entities"}
{"type":"log","callId":"call@148","time":66932.008,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@148","time":66932.009,"message":"  accept: */*"}
{"type":"log","callId":"call@148","time":66932.01,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@148","time":66932.011,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@148","time":66932.011,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@148","time":66932.012,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@148","time":66932.542,"message":"← 307 Temporary Redirect"}
{"type":"log","callId":"call@148","time":66932.545,"message":"  date: Sun, 06 Jul 2025 14:45:48 GMT"}
{"type":"log","callId":"call@148","time":66932.546,"message":"  server: uvicorn"}
{"type":"log","callId":"call@148","time":66932.547,"message":"  content-length: 0"}
{"type":"log","callId":"call@148","time":66932.547,"message":"  location: http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@148","time":66932.648,"message":"→ GET http://localhost:8000/api/v1/entities/"}
{"type":"log","callId":"call@148","time":66932.649,"message":"  user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.7204.23 Safari/537.36"}
{"type":"log","callId":"call@148","time":66932.65,"message":"  accept: */*"}
{"type":"log","callId":"call@148","time":66932.651,"message":"  accept-encoding: gzip,deflate,br"}
{"type":"log","callId":"call@148","time":66932.652,"message":"  x-test-backend-url: http://localhost:8000"}
{"type":"log","callId":"call@148","time":66932.653,"message":"  x-test-worker-id: 27"}
{"type":"log","callId":"call@148","time":66932.655,"message":"  x-test-isolation: enabled"}
{"type":"log","callId":"call@148","time":66934.6,"message":"← 200 OK"}
{"type":"log","callId":"call@148","time":66934.602,"message":"  date: Sun, 06 Jul 2025 14:45:48 GMT"}
{"type":"log","callId":"call@148","time":66934.603,"message":"  server: uvicorn"}
{"type":"log","callId":"call@148","time":66934.604,"message":"  content-length: 1039"}
{"type":"log","callId":"call@148","time":66934.605,"message":"  content-type: application/json"}
{"type":"after","callId":"call@148","endTime":66934.634,"result":{"response":{"url":"http://localhost:8000/api/v1/entities/","status":200,"statusText":"OK","headers":[{"name":"date","value":"Sun, 06 Jul 2025 14:45:48 GMT"},{"name":"server","value":"uvicorn"},{"name":"content-length","value":"1039"},{"name":"content-type","value":"application/json"}],"fetchUid":"72ef7354aaf4be7999da6d8e56eea03e"}},"afterSnapshot":"after@call@148"}
