version: '3.8'

services:
  database:
    build: ./database
    container_name: simile-db
    environment:
      POSTGRES_DB: simile
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    security_opt:
      - label=disable

  backend:
    build: ./backend
    container_name: simile-api
    environment:
      PYTHONPATH: /app
      DATABASE_URL: postgresql+asyncpg://postgres:postgres@database:5432/simile
      CORS_ORIGINS: '["http://localhost:3000"]'
    ports:
      - "8000:8000"
    depends_on:
      database:
        condition: service_healthy
    security_opt:
      - label=disable

  frontend:
    build: ./frontend
    container_name: simile-ui
    environment:
      REACT_APP_API_URL: http://localhost:8000
    ports:
      - "3000:3000"
    depends_on:
      - backend
    security_opt:
      - label=disable

volumes:
  postgres_data: